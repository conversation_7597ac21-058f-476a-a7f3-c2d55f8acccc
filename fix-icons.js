#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 图标映射表：将错误的图标名称映射到正确的Element Plus图标
const iconMapping = {
  // 方向图标
  'el-icon-direction-down': 'ElIconDirectionDown',
  'el-icon-direction-up': 'ElIconDirectionUp',
  'el-icon-direction-left': 'ElIconArrowLeft',
  'el-icon-direction-right': 'ElIconArrowRight',
  'el-icon-caret-bottom': 'ElIconCaretBottom',
  'el-icon-arrow-left': 'ElIconArrowLeft',
  'el-icon-arrow-right': 'ElIconArrowRight',
  
  // 操作图标
  'el-icon-search': 'ElIconSearch',
  'el-icon-remove': 'ElIconRemove',
  'el-icon-circle-plus': 'ElIconCirclePlus',
  'el-icon-plus': 'ElIconPlus',
  'el-icon-edit': 'ElIconEdit',
  'el-icon-delete': 'ElIconDelete',
  'el-icon-close': 'ElIconClose',
  'el-icon-more': 'ElIconMore',
  'el-icon-link': 'ElIconLink',
  'el-icon-zoom-in': 'ElIconZoomIn',
  'el-icon-zoom-out': 'ElIconZoomOut',
  'el-icon-full-screen': 'ElIconFullScreen',
  
  // 应用图标
  'el-icon-vone-dashboard': 'ElIconVoneDashboard',
  'el-icon-vone-product': 'ElIconVoneProduct',
  'el-icon-vone-more': 'ElIconVoneMore',
  'el-icon-icon-system-edit': 'ElIconIconSystemEdit',
  'el-icon-webxitong': 'ElIconWebxitong',
  'el-icon-application-attachment': 'ElIconApplicationAttachment',
  'el-icon-application-member': 'ElIconApplicationMember',
  'el-icon-application-password-reset': 'ElIconApplicationPasswordReset',
  
  // 其他图标
  'el-icon-icon-gengduo': 'ElIconMore',
  'el-icon-icon-shanchu': 'ElIconDelete',
  'el-icon-tips-done': 'ElIconCheck',
  'el-icon-warning-outline': 'ElIconWarning',
  'el-icon-document': 'ElIconDocument',
  'el-icon-application-view-list': 'ElIconList',
};

// Element Plus 图标到导入名称的映射
const importMapping = {
  'ElIconDirectionDown': 'ArrowDown as ElIconDirectionDown',
  'ElIconDirectionUp': 'ArrowUp as ElIconDirectionUp', 
  'ElIconArrowLeft': 'ArrowLeft as ElIconArrowLeft',
  'ElIconArrowRight': 'ArrowRight as ElIconArrowRight',
  'ElIconCaretBottom': 'CaretBottom as ElIconCaretBottom',
  'ElIconSearch': 'Search as ElIconSearch',
  'ElIconRemove': 'Remove as ElIconRemove',
  'ElIconCirclePlus': 'CirclePlus as ElIconCirclePlus',
  'ElIconPlus': 'Plus as ElIconPlus',
  'ElIconEdit': 'Edit as ElIconEdit',
  'ElIconDelete': 'Delete as ElIconDelete',
  'ElIconClose': 'Close as ElIconClose',
  'ElIconMore': 'More as ElIconMore',
  'ElIconLink': 'Link as ElIconLink',
  'ElIconZoomIn': 'ZoomIn as ElIconZoomIn',
  'ElIconZoomOut': 'ZoomOut as ElIconZoomOut',
  'ElIconFullScreen': 'FullScreen as ElIconFullScreen',
  'ElIconCheck': 'Check as ElIconCheck',
  'ElIconWarning': 'Warning as ElIconWarning',
  'ElIconDocument': 'Document as ElIconDocument',
  'ElIconList': 'List as ElIconList',
  
  // 自定义图标（保持原样，因为这些可能是项目特有的）
  'ElIconVoneDashboard': 'VoneDashboard as ElIconVoneDashboard',
  'ElIconVoneProduct': 'VoneProduct as ElIconVoneProduct', 
  'ElIconVoneMore': 'VoneMore as ElIconVoneMore',
  'ElIconIconSystemEdit': 'IconSystemEdit as ElIconIconSystemEdit',
  'ElIconWebxitong': 'Webxitong as ElIconWebxitong',
  'ElIconApplicationAttachment': 'ApplicationAttachment as ElIconApplicationAttachment',
  'ElIconApplicationMember': 'ApplicationMember as ElIconApplicationMember',
  'ElIconApplicationPasswordReset': 'ApplicationPasswordReset as ElIconApplicationPasswordReset',
};

function fixIconsInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 修复模板中的图标使用
  for (const [oldIcon, newIcon] of Object.entries(iconMapping)) {
    const regex = new RegExp(`<${oldIcon}\\s*/>`, 'g');
    if (content.includes(`<${oldIcon}`)) {
      content = content.replace(regex, `<${newIcon} />`);
      modified = true;
      console.log(`Fixed ${oldIcon} -> ${newIcon} in ${filePath}`);
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content);
  }
  
  return modified;
}

// 查找所有Vue文件
const vueFiles = glob.sync('src/**/*.vue');

console.log(`Found ${vueFiles.length} Vue files to process...`);

let totalFixed = 0;
vueFiles.forEach(file => {
  if (fixIconsInFile(file)) {
    totalFixed++;
  }
});

console.log(`\nFixed icons in ${totalFixed} files.`);
console.log('\nNext steps:');
console.log('1. Check each file to ensure the correct imports are added');
console.log('2. Add missing icon imports to the script section');
console.log('3. Register the icons in the components section');
