import { gainTreeList } from '@/utils'
import { orgList } from '@/api/vone/base/org'
// 查询所有机构

const orgMixin = {
  data() {
    return {
      orgData: [],
    }
  },

  mounted() {
    this.getList()
  },
  methods: {
    // 查询所有机构
    async getList() {
      const res = await orgList()
      if (!res.isSuccess) {
        console.warn(res.msg)
        return
      }
      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
  },
}

export default orgMixin
