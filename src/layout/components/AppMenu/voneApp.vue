<template>
  <div class="person-main">
    <div class="fixedApp">
      <router-link v-if="hasDashboard" to="/dashboard">
        <div
          :class="[
            'person-svg',
            activeApp == 'dashboard' ? 'person-svg-active' : '',
          ]"
        >
          <svg-icon
            v-if="activeApp == 'dashboard'"
            icon-class="vone-dashboard-active"
          />
          <el-icon class="iconfont" style="font-size: 24px"
            ><el-icon-vone-dashboard
          /></el-icon>
          <span>工作台</span>
        </div>
      </router-link>
      <div v-for="(item, index) in fixedList" :key="index">
        <router-link :to="item.path">
          <div
            :class="[
              'person-svg',
              activeApp == item.menuCode ? 'person-svg-active' : '',
            ]"
          >
            <svg
              v-if="activeApp == item.menuCode"
              class="icon svg-icon"
              aria-hidden="true"
              style="font-size: 32px"
            >
              <use :xlink:href="`#${item.icon}`" />
            </svg>

            <svg
              v-else
              class="icon svg-icon"
              aria-hidden="true"
              style="font-size: 32px"
            >
              <use :xlink:href="`#${item.icon}-inactive`" />
            </svg>
            <!-- <i v-else :class="['iconfont', item.icon]" style="font-size: 24px" /> -->
            <span>{{ item.name }}</span>
          </div>
        </router-link>
      </div>
    </div>

    <el-popover
      v-model="visible"
      placement="right-end"
      width="320"
      trigger="click"
      popper-class="vone-popover"
    >
      <div class="vone-appbox">
        <div class="title">全部应用</div>
        <section class="sectionContent">
          <div v-if="!editFlag" class="projectbox">
            <el-input v-model="searchApp" placeholder="请输入内容">
              <el-icon class="el-input__icon"><el-icon-search /></el-icon>
            </el-input>

            <div v-for="(group, index) in otherList" :key="index">
              <el-row class="strongHeader">
                <strong>{{ group.groupName }}</strong>
              </el-row>

              <el-row>
                <el-col
                  v-for="(item, index) in group.children"
                  :key="index"
                  :span="8"
                >
                  <div style="cursor: pointer" @click="toPath(item)">
                    <svg
                      class="icon svg-icon"
                      aria-hidden="true"
                      style="font-size: 32px"
                    >
                      <use :xlink:href="`#${item.icon}`" />
                    </svg>
                    <span>{{ item.name }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          <div v-else style="overflow: hidden">
            <p>常驻应用</p>
            <draggable
              :list="fixedArray"
              :group="{ name: 'menu', put: true }"
              class="projectbox dragBox"
              filter=".undraggable"
              element="el-row"
              @change="change"
            >
              <el-col
                v-for="(items, idx) in fixedArray"
                :key="idx"
                :class="['app', fixedArray.length <= 1 && 'undraggable']"
                :span="6"
              >
                <svg
                  class="icon svg-icon"
                  aria-hidden="true"
                  style="font-size: 32px"
                >
                  <use :xlink:href="`#${items.icon}`" />
                </svg>
                <!-- <svg-icon :icon-class="items.icon+'-active'" /> -->
                <span>{{ items.name }}</span>
                <el-icon class="icons"><el-icon-remove /></el-icon>
              </el-col>
            </draggable>
            <p>其他应用</p>
            <draggable
              class="projectbox"
              :group="{ name: 'menu', put: true }"
              element="el-row"
              :list="otherArray"
            >
              <el-col
                v-for="items in otherArray"
                :key="items.id"
                :class="['app']"
                :span="6"
              >
                <!-- <svg-icon :icon-class="items.icon+'-active'" /> -->
                <svg
                  class="icon svg-icon"
                  aria-hidden="true"
                  style="font-size: 32px"
                >
                  <use :xlink:href="`#${items.icon}`" />
                </svg>

                <span>{{ items.name }}</span>
                <el-icon class="icons"><el-icon-circle-plus /></el-icon>
              </el-col>
            </draggable>
          </div>
        </section>
        <div class="footer">
          <div v-if="editFlag" class="buttonbox">
            <el-button @click="appCancel">取消</el-button>
            <el-button type="primary" @click="appOk">确定</el-button>
          </div>
          <a v-else @click="editApp"
            ><el-icon class="iconfont" style="margin-right: 4px"
              ><el-icon-icon-system-edit /></el-icon
            >编辑导航</a
          >
        </div>
      </div>
      <div slot="reference" class="moreApp" style="cursor: pointer">
        <!-- <svg-icon icon-class="vone-more" /> -->
        <el-icon class="iconfont" style="font-size: 24px"
          ><el-icon-vone-more
        /></el-icon>
        <span>更多</span>
      </div>
    </el-popover>
  </div>
</template>

<script>
import {
  VoneDashboard as ElIconVoneDashboard,
  Search as ElIconSearch,
  Remove as ElIconRemove,
  CirclePlus as ElIconCirclePlus,
  IconSystemEdit as ElIconIconSystemEdit,
  VoneMore as ElIconVoneMore,
} from '@element-plus/icons-vue'
import { getFixedMenu, holdMenu } from '@/api/user'
import draggable from 'vuedraggable'
import { getRouter } from '@/utils/auth'
export default {
  components: {
    draggable,
    ElIconVoneDashboard,
    ElIconSearch,
    ElIconRemove,
    ElIconCirclePlus,
    ElIconIconSystemEdit,
    ElIconVoneMore,
  },
  props: {
    activeApp: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      originOther: [],
      searchApp: '',
      drag: false,
      editFlag: false,
      activeNames: '',
      allAppList: [],
      fixedList: [],
      otherList: [],
      fixedArray: [],
      otherArray: [],
      visible: false,
      hasDashboard: getRouter().filter((item) => item.name == 'dashboard')
        ?.length,
      // hasDashboard: getRouter()[0] && getRouter()[0].name == 'dashboard'
    }
  },
  computed: {},
  watch: {
    visible(val) {
      if (!val) {
        this.editFlag = false
      }
    },
    searchApp: {
      handler(val) {
        // 如果搜索关键词为空，返回所有数据
        if (val == '') {
          this.otherList = this.originOther
        } else {
          // 返回包含关键词的数据
          this.otherList = this.otherList.filter((item) => {
            const reg = new RegExp(val)
            return (
              reg.test(item.groupName) ||
              reg.test(item.children.map((r) => r.name))
            )
          })
        }
      },
    },
  },
  mounted() {
    this.getMenu()
  },
  methods: {
    async toPath(item, type) {
      if (item.menuCode != this.$route?.meta?.activeApp) {
        this.$router.push({
          path: item.path,
        })
      } else if (
        item.menuCode == this.$route?.meta?.activeApp &&
        type != 'init'
      ) {
        return
      }
      if (this.fixedList.length < 4) {
        this.fixedList.push(item)
      } else {
        this.fixedList[4] = item
      }
      const res = await holdMenu(this.fixedList)
      if (!res.isSuccess) return
      this.visible = false
      this.getMenu()
    },
    getMenu() {
      const personAppList = []
      this.$store.state.user.appRouterMenu.map((item) => {
        if (item.name != 'dashboard' && item.name != '404') {
          personAppList.push({
            id: item.meta.menuId,
            menuCode: item.meta.code,
            icon: item.meta.icon,
            path: item.path,
            name: item.meta.title,
            sort: item.sort,
          })
        }
      })
      this.allAppList = personAppList

      getFixedMenu().then((res) => {
        if (res.isSuccess) {
          const data = res.data.map((item) => {
            return item.menuCode
          })
          const fixedData = []
          res.data.forEach((item) => {
            personAppList.forEach((items) => {
              if (item.menuCode == items.menuCode) {
                items.sort = item.sort
                fixedData.push(items)
              }
            })
          })
          this.fixedList = fixedData
          const whiteMenu = ['/dashboard', '/filter', '/exception']
          if (!whiteMenu.includes(this.$route?.path)) {
            const currentMenuApp = personAppList?.filter(
              (item) => item.menuCode == this.$route?.matched?.[0]?.meta?.code
            )?.[0]
            const currentMenuLength = this.fixedList.filter(
              (item) => item.menuCode == currentMenuApp?.menuCode
            )
            if (currentMenuApp) {
              if (!currentMenuLength?.length) {
                this.toPath(
                  {
                    id: currentMenuApp.id,
                    menuCode: currentMenuApp.menuCode,
                    icon: currentMenuApp.icon,
                    path: currentMenuApp.path,
                    name: currentMenuApp.name,
                    sort: currentMenuApp.sort,
                  },
                  'init'
                )
                return
              }
            }
          }

          const list = personAppList.filter((item) => {
            return !data.includes(item.menuCode)
          })
          const groupMap = {
            base: '基础应用',
            workflow: '基础应用',
            si: '基础应用',
            wiki: '基础应用',
            project: '协同管理',
            projectm: '协同管理',
            producm: '协同管理',
            reqm_center: '协同管理',
            productfit: '协同管理',
            release: '协同管理',
            code: '持续交付',
            pipeline: '持续交付',
            package: '持续交付',
            cmdb: '持续交付',
            dashboard: '协同管理',
            man_hour: '协同管理',
            testm_test_library: '协同管理',
          }
          list.forEach((element) => {
            element.groupName = groupMap[element.menuCode] || '外部应用'
          })

          const map = {}
          list.forEach((item) => {
            map[item.groupName] = map[item.groupName] || []
            map[item.groupName].push(item)
          })
          const sort = {
            协同管理: 1,
            持续交付: 2,
            度量中心: 3,
            基础应用: 4,
            外部应用: 5,
          }
          const DATA = Object.keys(map).map((orgName) => {
            return {
              groupName: orgName,
              children: map[orgName],
              sort: sort[orgName],
            }
          })
          DATA.sort((a, b) => {
            return a.sort > b.sort ? 1 : -1
          })
          this.otherList = DATA

          this.originOther = JSON.parse(JSON.stringify(DATA))
          this.fixedArray = JSON.parse(JSON.stringify(this.fixedList))
          this.otherArray = JSON.parse(JSON.stringify(list))
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    editApp() {
      this.editFlag = true
    },
    appCancel() {
      this.editFlag = false
    },
    appOk() {
      // if (this.fixedArray.length > 5) {
      //   return this.$message.warning('常驻应用最多只能有5个')
      // } else {
      holdMenu(this.fixedArray).then((res) => {
        if (res.isSuccess) {
          this.$message.success(res.msg)
          this.editFlag = false
          this.visible = false
          this.getMenu()
        } else {
          this.$message.warning(res.msg)
        }
      })
      // }
    },
    // evt里面有两个值，一个evt.added 和evt.removed  可以分别知道移动元素的ID和删除元素的ID
    change(evt) {
      const data = this.fixedArray
      data.map((item, index) => {
        item.sort = index
      })
    },
    removeApp(e) {
      this.otherArray.push(e)
      const data = this.fixedArray
      data.map((item, index) => {
        if (item.menuCode == e.menuCode) {
          data.splice(index, 1)
        }
      })
      data.map((item, index) => {
        item.sort = index
      })
    },
    addApp(e) {
      if (this.fixedArray.length == 5) {
        return this.$message.warning('常驻应用最多只能有5个')
      }

      this.fixedArray.push(e)
      const data = this.fixedArray
      this.otherArray.map((item, index) => {
        if (item.menuCode == e.menuCode) {
          this.otherArray.splice(index, 1)
        }
      })
      data.map((item, index) => {
        item.sort = index
      })
    },
  },
}
</script>

<style lang="scss" scoped>
// 左侧菜单样式
.person-main {
  height: auto;
  text-align: center;
  .person-svg {
    width: 64px;
    height: 64px;
    margin-bottom: 10px;
    padding-top: 10px;
    cursor: pointer;
  }
  .fixedApp {
    margin-right: -4px;
    padding-right: 4px;
    // height: calc(100vh - 250px);
    overflow-y: auto;
    overflow-x: hidden;
  }
  .iconfont,
  span {
    // 左侧菜单图标的颜色
    color: var(--auxiliary-font-color);
  }
  span {
    margin-top: 5px;
    display: block;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
  }
  .svg-icon {
    width: 24px;
    height: 24px;
    font-size: 24px;
    border: none;
  }

  .person-svg-active {
    width: 64px;
    height: 64px;
    background: var(--app-active-color);
    box-shadow: var(--app-active-shadow);
    border-radius: 6px;
    span {
      color: var(--main-theme-color, #3e7bfa);
      line-height: 1.5;
    }
    .svg-icon {
      // 左侧菜单图标选中的颜色
      color: #fff;
    }
  }
}

// 弹窗应用样式
.vone-appbox {
  .el-col:hover {
    background: var(--app-hover-color);
    box-shadow: 0px 4px 16px rgba(41, 82, 166, 0.16);
  }
  .title,
  .footer {
    height: 56px;
    line-height: 56px;
    padding: 0px 16px;
    color: var(--main-font-color);
  }
  .projectbox {
    padding: 12px;
  }
  .dragBox {
    border-bottom: 1px solid var(--disabled-bg-color);
  }
  .title {
    font-weight: 700;
    border-bottom: 1px solid var(--disabled-bg-color);
  }
  .footer {
    border-top: 1px solid var(--disabled-bg-color);
    text-align: center;
  }
  .line {
    height: 1px;
    width: 100%;
    background: var(--disabled-bg-color);
  }
  .buttonbox {
    text-align: right;
    :deep(.el-button) {
      min-width: 60px;
    }
  }
  p {
    text-align: left;
    margin: 12px;
  }

  .el-icon-remove.icons {
    color: #dc5564;
  }
  .el-icon-circle-plus.icons,
  .footer a {
    color: var(--main-theme-color, #3e7bfa);
  }
}

:deep(.popper__arrow) {
  display: none;
}
.projectbox {
  text-align: center;
  .el-col {
    height: 86px;
    padding-top: 16px;
  }
  .el-col:hover {
    border-radius: 6px;
  }

  .svg-icon {
    font-size: 36px;
  }
  span {
    display: block;
    margin: 4px 0px;
    line-height: 22px;
  }
  .app {
    position: relative;
  }
  .icons {
    font-size: 16px;
    position: absolute;
    top: 6px;
    right: 6px;
  }
}
// 暗色主题
.custom-theme-dark {
  .person-svg-active {
    span {
      color: #fff;
    }
  }
  .vone-appbox {
    .footer a {
      color: #fff;
    }
  }
}
</style>

<style lang="scss">
// 修改应用样式
.vone-popover {
  padding: 0px !important;
  &[x-placement^='right'] .popper__arrow {
    display: none;

    &::after {
      display: none;
    }
  }
}
.strongHeader {
  text-align: left;
  margin: 12px 0px 12px 12px;
}
.sectionContent {
  max-height: 446px;
  overflow-y: auto;
}
</style>
