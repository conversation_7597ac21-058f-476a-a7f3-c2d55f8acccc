<template>
  <div class="simple-dialog-overlay">
    <div class="simple-dialog">
      <h3>个人设置</h3>
      <p>个人设置对话框 - 简化版本</p>
      <div class="dialog-footer">
        <button>取消</button>
        <button>确定</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OwnSettingDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible'],
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style scoped>
.simple-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.simple-dialog {
  background: white;
  padding: 20px;
  border-radius: 4px;
  min-width: 300px;
  text-align: center;
}

.dialog-footer {
  margin-top: 20px;
}

.dialog-footer button {
  margin: 0 5px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: #f5f5f5;
  cursor: pointer;
}
</style>
