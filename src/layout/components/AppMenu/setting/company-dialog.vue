<template>
  <div>
    <el-dialog
      title="企业设置"
      width="40%"
      :model-value="visible"
      @update:model-value="$emit('update:visible', $event)"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="companyForm"
        v-loading="formLoading"
        :model="companyForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model="companyForm.name"
            placeholder="请输入产品名称"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="CopyRight" prop="description">
          <el-input
            v-model="companyForm.description"
            placeholder="请输入CopyRight"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="团队logo" prop="value">
          <el-image
            fit="contain"
            style="width: 100px; height: 100px"
            :src="companyForm.value"
            :preview-src-list="srcList"
          />
          <!-- <div class="logo">
              <input id="saveImage" type="file" class="shangchuan" @change="handleChange">
              <div> <i>支持上传JPG/PNG/SVG图片</i></div>

            </div> -->
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="saveInfo"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { apiBaseTenantPlatformConfig } from '@/api/vone/base/user'
// import storage from 'store'
import { apiBaseCompanySetting } from '@/api/vone/base/user'
import { getCompanyInfo } from '@/api/vone/base/index'

import { mapState } from 'vuex'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  ...mapState({
    user: (state) => state.user.user,
  }),
  data() {
    return {
      companyForm: {
        code: 'cloudbooster',
        description: '',
        value: '',
      },
      formLoading: false,
      rules: {
        name: [
          {
            required: true,
            message: '请输入企业名称',
          },
        ],
        // logo: [
        //   {
        //     required: true,
        //     message: '请上传企业LOGO'
        //   }
        // ]
      },
      saveLoading: false,
      srcList: [],
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.companyForm.resetFields()
    },
    async getInfo() {
      this.formLoading = true
      const res = await getCompanyInfo('cloudbooster')
      this.formLoading = false

      if (!res.isSuccess) {
        return
      }

      if (res.data) {
        this.$set(this.companyForm, 'name', res.data.name)
        this.$set(this.companyForm, 'description', res.data.description)
      } else {
        this.companyForm = {
          value: null,
          name: 'Vone价值流交付平台',
          description: '© 2021 cloudbooster.com',
        }
      }
    },
    changeInput() {
      // this.$set()
    },

    async saveInfo() {
      try {
        await this.$refs.companyForm.validate()
      } catch (e) {
        return
      }
      // const userInfo = storage.get('user')
      try {
        this.saveLoading = true
        const { isSuccess, msg } = await apiBaseCompanySetting({
          ...this.companyForm,
        })
        this.saveLoading = false
        if (!isSuccess) {
          return this.$message.error(msg)
        }
        this.$message.success('保存成功')
        this.onClose()
        this.$emit('success')

        await this.$store.dispatch('user/logout', this.user.id)
        this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        location.reload()
      } catch (e) {
        this.saveLoading = false
      }
    },

    handleChange(e) {
      const target = e.target || e.srcElement
      const fileImage = target.files[0]

      // 判断上传文件是否是图片类型：\w 的释义都是指包含大 小写字母数字和下划线 相当于([0-9a-zA-Z])
      if (!/image\/\w+/.test(fileImage.type)) {
        this.$message.error('请上传图片文件')
        return
      }

      const isLt2M = fileImage.size / 1024 / 1024 < 2
      if (isLt2M) {
        // uploadImgToBase64()返回一个Promise对象，通过.then()获取其数据。其中data.result是图片转成的base64值
        // this.uploadImgToBase64(fileImage.raw).then((data) => {
        //   this.companyForm.value = data.result
        // })

        // FileReader 对象允许Web应用程序异步读取存储在用户计算机上的文件（或原始数据缓冲区）的内容，使用 File 或 Blob 对象指定要读取的文件或数据
        const reader = new FileReader()
        // onload 事件会在页面或图像加载完成后立即发生
        reader.onload = (data) => {
          const res = data.target || data.srcElement
          // this.headInfo.src = res.result
          this.companyForm.value = res.result
        }
        // FileReader对象的readAsDataURL方法可以将读取到的文件编码成Data URL
        reader.readAsDataURL(fileImage)
        // 将上传的头像图片提交给后台
        // this.handleUploadHead()
      } else {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
    },
    uploadImgToBase64(file) {
      // 核心方法，将图片转成base64字符串形式
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function () {
          // 图片转base64完成后返回reader对象
          resolve(reader)
        }
        reader.onerror = reject
      })
    },
    beforeUpload(file) {
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 30px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
