<template>
  <div class="menu">
    <div class="menu-title">
      <img v-if="projectInfo.icon" :src="projectInfo.icon" class="icon-img" />
      <el-icon class="icon iconfont" style="color: #8791fa; margin-right: 4px"
        ><el-icon-vone-product
      /></el-icon>
      <span class="text-over"> {{ projectInfo.name }} </span>

      <el-icon class="iconfont"><el-icon-direction-down /></el-icon>
      <el-icon class="iconfont"><el-icon-direction-up /></el-icon>
    </div>
    <!-- <el-tag class="menu-title text-over" style="max-width:200px">
        <i class="iconfont el-icon-vone-projectm" />
        {{
          projectInfo.name
        }}

        <i v-if="!iconFlag" class="el-icon-caret-bottom" />
        <i v-if="iconFlag" class="el-icon-caret-top" />
      </el-tag> -->
    <el-select
      ref="select"
      v-model="productId"
      filterable
      placeholder="切换产品"
      @change="projectChange"
      @visible-change="(v) => visibleChange(v, 'select', selectClick)"
    >
      <el-option
        v-for="item in projectList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <div>
          <span style="margin-right: 4px">
            <img v-if="item.icon" :src="item.icon" class="icon-img" />
            <el-icon
              class="icon iconfont"
              style="font-weight: normal; color: #8791fa"
              ><el-icon-vone-product
            /></el-icon>
          </span>
          <span>{{ item.name }}</span>
        </div>
      </el-option>
    </el-select>
    <span class="solid" />
  </div>
</template>

<script>
import {
  VoneProduct as ElIconVoneProduct,
  DirectionDown as ElIconDirectionDown,
  DirectionUp as ElIconDirectionUp,
} from '@element-plus/icons-vue'

import { getProductDetail, apiProductNoPage } from '@/api/vone/product/index'

export default {
  components: {
    ElIconVoneProduct,
    ElIconDirectionDown,
    ElIconDirectionUp,
  },
  data() {
    return {
      productId: '',
      productCode: '',
      projectList: [],
      projectInfo: {},
      iconFlag: false,
      searchTitle: '',
    }
  },
  watch: {
    $route: function () {
      this.productCode = this.$route.params.productCode
      this.searchTitle = '查看全部'
      this.getProjectList()
      if (this.productCode) {
        this.getProjectInfo()
      }
    },
  },
  mounted() {
    this.productCode = this.$route.params.productCode
    this.productId = this.$route.params.productId

    this.searchTitle = '查看全部'
    this.getProjectList()
    if (this.productCode) {
      this.getProjectInfo()
    }
  },
  methods: {
    async getProjectInfo() {
      if (!this.productCode) {
        return
      }
      const { data, msg, isSuccess } = await getProductDetail(this.productId)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      if (data) {
        this.projectInfo = data
      }
    },
    async getProjectList() {
      const { isSuccess, data, msg } = await apiProductNoPage()
      if (!isSuccess) {
        return
      }
      this.projectList = data
    },

    projectChange(productId) {
      const code = this.projectList.find((item) => item.id == productId).code

      this.$router.push({
        name: 'product_count',
        params: { productCode: code, productId: productId },
      })

      this.getProjectInfo()
    },
    visibleChange(visible, refName, onClick) {
      this.iconFlag = visible
      if (visible) {
        const ref = this.$refs[refName]
        let popper = ref.$refs.popper
        if (popper.$el) popper = popper.$el
        if (
          !Array.from(popper.children).some(
            (v) => v.className === 'el-cascader-menu__list'
          )
        ) {
          const el = document.createElement('ul')
          el.className = 'el-cascader-menu__list'
          el.style = 'border-top: solid 1px #e4e7ed; padding:0; color: #606266;'
          el.innerHTML =
            `<li class="el-cascader-node color-info" style="height: 38px; line-height: 38px">

    <span class="el-cascader-node__label">` +
            this.searchTitle +
            `</span>
    <i class="el-icon-arrow-right el-cascader-node__postfix"/>
    </li>`
          popper.appendChild(el)
          el.onclick = () => {
            // 底部按钮的点击事件 点击后想触发的逻辑也可以直接写在这
            onClick && onClick()
            // 以下代码实现点击后弹层隐藏 不需要可以删掉
            // if (ref.toggleDropDownVisible) {
            //   ref.toggleDropDownVisible(false);
            // } else {
            //   ref.visible = false;
            // }
          }
        }
      }
    },
    selectClick() {
      this.$router.push({
        name: 'product_view',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
.icon {
  font-size: 20px;
}
.menu {
  position: relative;
  display: flex;
  align-items: center;
  height: 48px;
  margin-left: 5px;
  .menu-title {
    max-width: 214px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span {
      margin-right: 4px;
    }
    .iconfont {
      color: var(--font-second-color);
    }
    .text-over {
      min-width: 56rem;
    }
  }
  .el-select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    z-index: 99;
    cursor: pointer;
  }
  i {
    font-weight: normal;
    &.project-icon {
      font-size: 18px;
    }
  }
}

.solid {
  height: 16px;
  width: 1px;
  background: var(--solid-border-color);
}
</style>
