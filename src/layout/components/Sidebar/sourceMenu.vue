<template>
  <div class="menu">
    <div class="menu-title">
      <span>
        <el-icon class="icon iconfont" style="color: #409eff; margin-right: 4px"
          ><el-icon-webxitong
        /></el-icon>
      </span>
      <span class="text-over"> {{ systemInfo.name }} </span>

      <el-icon class="iconfont"><el-icon-direction-down /></el-icon>
      <el-icon class="iconfont"><el-icon-direction-up /></el-icon>
    </div>
    <el-select
      ref="select"
      v-model="systemInfoId"
      filterable
      placeholder="切换服务应用"
      @change="projectChange"
      @visible-change="(v) => visibleChange(v, 'select', selectClick)"
    >
      <el-option
        v-for="item in systemList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <div>
          <span>
            <el-icon class="iconfont" style="color: #409eff"
              ><el-icon-webxitong
            /></el-icon>
          </span>
          &nbsp;&nbsp;
          <span>{{ item.name }}</span>
        </div>
      </el-option>
    </el-select>
    <span class="solid" />
  </div>
</template>

<script>
import {
  Webxitong as ElIconWebxitong,
  DirectionDown as ElIconDirectionDown,
  DirectionUp as ElIconDirectionUp,
} from '@element-plus/icons-vue'

import {
  apiCmdbServerNoPage,
  apiBaseSelectBaseDatagById,
} from '@/api/vone/cmdb/server'

export default {
  components: {
    ElIconWebxitong,
    ElIconDirectionDown,
    ElIconDirectionUp,
  },
  data() {
    return {
      systemInfoId: '',
      systemId: '',
      systemList: [],
      systemInfo: {},

      iconFlag: false,
      searchTitle: '',
    }
  },
  watch: {
    $route: function () {
      this.systemId = this.$route.params.systemId
      this.systemInfoId = this.$route.params.systemId
      this.searchTitle = '查看全部'
      this.getSystemList()
      if (this.systemId) {
        this.getSystemInfo()
      }
    },
  },
  mounted() {
    this.systemId = this.$route.params.systemId
    this.systemInfoId = this.$route.params.systemId

    this.searchTitle = '查看全部'
    this.getSystemList()
    if (this.systemId) {
      this.getSystemInfo()
    }
  },
  methods: {
    async getSystemInfo() {
      if (!this.systemId) {
        return
      }
      const { data, msg, isSuccess } = await apiBaseSelectBaseDatagById(
        this.systemId
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      if (data) {
        this.systemInfo = data
      }
    },
    async getSystemList() {
      const { isSuccess, data, msg } = await apiCmdbServerNoPage()
      if (!isSuccess) {
        return
      }
      this.systemList = data
    },

    projectChange(systemInfoId) {
      // const code = this.systemList.find(
      //   (item) => item.id == systemInfoId
      // ).id

      this.$router.push({
        name: 'cmdb_server_set',
        params: { systemId: systemInfoId, type: this.$route.params.type },
      })
      this.$nextTick(() => {
        this.getSystemInfo()
      })
    },
    visibleChange(visible, refName, onClick) {
      this.iconFlag = visible
      if (visible) {
        const ref = this.$refs[refName]
        let popper = ref.$refs.popper
        if (popper.$el) popper = popper.$el
        if (
          !Array.from(popper.children).some(
            (v) => v.className === 'el-cascader-menu__list'
          )
        ) {
          const el = document.createElement('ul')
          el.className = 'el-cascader-menu__list'
          el.style = 'border-top: solid 1px #e4e7ed; padding:0; color: #606266;'
          el.innerHTML =
            `<li class="el-cascader-node color-info" style="height: 38px; line-height: 38px">

    <span class="el-cascader-node__label">` +
            this.searchTitle +
            `</span>
    <i class="el-icon-arrow-right el-cascader-node__postfix"/>
    </li>`
          popper.appendChild(el)
          el.onclick = () => {
            // 底部按钮的点击事件 点击后想触发的逻辑也可以直接写在这
            onClick && onClick()
            // 以下代码实现点击后弹层隐藏 不需要可以删掉
            // if (ref.toggleDropDownVisible) {
            //   ref.toggleDropDownVisible(false);
            // } else {
            //   ref.visible = false;
            // }
          }
        }
      }
    },
    selectClick() {
      this.$router.push({
        name: 'cmdb_server_view',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.icon {
  font-size: 20px;
}
.menu {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 5px;
  height: 48px;
  .menu-title {
    max-width: 214px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span {
      margin-right: 4px;
    }
    .iconfont {
      color: var(--font-second-color);
    }
    .text-over {
      min-width: 56rem;
    }
  }
  .el-select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    z-index: 99;
    cursor: pointer;
  }
  i {
    font-weight: normal;
    &.project-icon {
      font-size: 18px;
    }
  }
}

.solid {
  height: 16px;
  width: 1px;
  background: var(--solid-border-color);
}
</style>
