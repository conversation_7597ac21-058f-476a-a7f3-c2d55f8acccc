<template>
  <div class="menu">
    <div class="menu-title">
      <img v-if="projectInfo.icon" :src="projectInfo.icon" class="icon-img" />
      <svg v-else class="icon" style="margin-right: 4px" aria-hidden="true">
        <use xlink:href="#el-icon-vone-chanpinji" />
      </svg>
      <span class="text-over"> {{ projectInfo.name }} </span>

      <el-icon class="iconfont"><el-icon-direction-down /></el-icon>
      <el-icon class="icon iconfont"><el-icon-direction-up /></el-icon>
    </div>
    <!-- <div style="max-width:200px">
        <el-select ref="select" v-model="productfitId" filterable placeholder="切换产品" @change="projectChange" @visible-change="(v) => visibleChange(v, 'select', selectClick)">
          <el-option v-for="item in projectList" :key="item.id" class="text-over" :label="item.name" :value="item.id">
            <div>

              <span style="margin-right: 4px;">
                <img v-if="item.icon" :src="item.icon" class="icon-img">
                <svg v-else class="icon" aria-hidden="true">
                  <use xlink:href="#el-icon-vone-chanpinji" />
                </svg>
              </span>
              <span>{{ item.name }}</span>
            </div>
          </el-option>
        </el-select>
      </div> -->
    <span class="solid" />
  </div>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  DirectionUp as ElIconDirectionUp,
} from '@element-plus/icons'

import { onlyOneProductfit, getqueryList } from '@/api/vone/product/productfit'

export default {
  components: {
    ElIconDirectionDown,
    ElIconDirectionUp,
  },
  data() {
    return {
      productfitId: '',
      productfitCode: '',
      projectList: [],
      projectInfo: {},
      iconFlag: false,
      searchTitle: '',
    }
  },
  watch: {
    $route: function () {
      this.productfitId = this.$route.params.productfitId
      this.searchTitle = '查看全部'
    },
  },
  mounted() {
    this.productfitId = this.$route.params.productfitId
    this.searchTitle = '查看全部'
    // this.getProjectList()
    if (this.productfitId) {
      this.getProjectInfo()
    }
  },
  methods: {
    async getProjectInfo() {
      if (!this.productfitId) {
        return
      }
      const { data, msg, isSuccess } = await onlyOneProductfit(
        this.productfitId
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      if (data) {
        this.projectInfo = data
      }
    },
    async getProjectList() {
      const { isSuccess, data, msg } = await getqueryList()
      if (!isSuccess) {
        return
      }
      this.projectList = data
    },
    selectClick() {
      this.$router.push({
        name: 'productfit_view',
      })
    },
    projectChange(productfitId) {
      this.$router.push({
        name: 'productfit_count',
        params: { productfitId: productfitId },
      })
      this.getProjectInfo()
    },
    visibleChange(visible, refName, onClick) {
      this.iconFlag = visible
      if (visible) {
        const ref = this.$refs[refName]
        let popper = ref.$refs.popper
        if (popper.$el) popper = popper.$el
        if (
          !Array.from(popper.children).some(
            (v) => v.className === 'el-cascader-menu__list'
          )
        ) {
          const el = document.createElement('ul')
          el.className = 'el-cascader-menu__list'
          el.style = 'border-top: solid 1px #e4e7ed; padding:0; color: #606266;'
          el.innerHTML =
            `<li class="el-cascader-node color-info" style="height: 38px; line-height: 38px">

    <span class="el-cascader-node__label">` +
            this.searchTitle +
            `</span>
    <i class="el-icon-arrow-right el-cascader-node__postfix"/>
    </li>`
          popper.appendChild(el)
          el.onclick = () => {
            // 底部按钮的点击事件 点击后想触发的逻辑也可以直接写在这
            onClick && onClick()
            // 以下代码实现点击后弹层隐藏 不需要可以删掉
            // if (ref.toggleDropDownVisible) {
            //   ref.toggleDropDownVisible(false);
            // } else {
            //   ref.visible = false;
            // }
          }
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
.icon {
  font-size: 20px;
}
.menu {
  position: relative;
  display: flex;
  align-items: center;
  height: 48px;
  margin-left: 5px;
  .menu-title {
    max-width: 214px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span {
      margin-right: 4px;
    }
    .iconfont {
      color: var(--font-second-color);
    }
    .text-over {
      min-width: 56rem;
    }
  }
  .el-select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    z-index: 99;
    cursor: pointer;
  }
  i {
    font-weight: normal;
    &.project-icon {
      font-size: 18px;
    }
  }
}

.solid {
  height: 16px;
  width: 1px;
  background: var(--solid-border-color);
}
</style>
