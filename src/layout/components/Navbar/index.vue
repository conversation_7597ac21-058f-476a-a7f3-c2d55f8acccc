<template>
  <div class="navbar">
    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <div class="avatar-container">
        <div class="avatar-wrapper" @click="showDropdown = !showDropdown">
          <img :src="avatar + '?imageView2/1/w/80/h/80'" class="user-avatar" />
          <el-icon><ElIconCaretBottom /></el-icon>
        </div>
        <div v-if="showDropdown" class="dropdown-menu">
          <div class="dropdown-item" @click="goHome">Home</div>
          <div class="dropdown-item" @click="logout">Log Out</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { CaretBottom as ElIconCaretBottom } from '@element-plus/icons-vue'
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import { mapState } from 'vuex'

export default {
  components: {
    Breadcrumb,
    ElIconCaretBottom,
  },
  name: 'Navbar',
  data() {
    return {
      showDropdown: false,
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar']),
    ...mapState({
      user: (state) => state.user.user,
    }),
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    goHome() {
      this.showDropdown = false
      this.$router.push('/')
    },
    async logout() {
      this.showDropdown = false
      await this.$store.dispatch('user/logout', this.user.id)
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: var(--main-bg-color, #fff);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      position: relative;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        cursor: pointer;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 50px;
        right: 0;
        background: white;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 1000;
        min-width: 100px;

        .dropdown-item {
          padding: 8px 16px;
          cursor: pointer;
          color: #606266;
          font-size: 14px;

          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}
</style>
