<template>
  <transition name="el-zoom-in-top">
    <div :class="['v-lane-card', { isflag: item.flag }]">
      <div class="lane-card-title">
        <i
          :style="{ color: item.echoMap.type && item.echoMap.type.color }"
          :class="[
            'iconfont',
            `${item.echoMap.type && item.echoMap.type.icon}`,
          ]"
        />
        <el-tooltip
          :show-after="800"
          class="item"
          :content="title"
          placement="top-start"
        >
          <div class="lane-card-title-text" @click.stop.prevent="click">
            {{ title }}
          </div>
        </el-tooltip>
      </div>
      <div class="lane-card-code">
        <div v-show="calcDefault('计划完成时间')" class="lane-card-code-text">
          计划完成：{{ dayjs(date) || '--' }}
        </div>
      </div>

      <div class="lane-card-code">
        <div
          v-show="calcDefault('优先级')"
          class="lane-card-title-priorityCode"
        >
          <el-icon class="iconfont" style="color: #e8545b"
            ><el-icon-icon-dengji-jiaogao2
          /></el-icon>
          <!-- <i
            :style="{color: item.echoMap.priorityCode && item.echoMap.priorityCode.color}"
            :class="['iconfont', `${item.echoMap.priorityCode && item.echoMap.priorityCode.icon}`]"
          /> -->
        </div>
        <div v-show="calcDefault('编号')" class="lane-card-code-text">
          {{ code }}
        </div>
        <template v-if="type && type.indexOf('TASK') != -1">
          <div v-show="calcDefault('工时')" class="lane-card-code-types">
            工时: {{ item.estimateHour ? `${item.estimateHour}h` : '-' }}
          </div>
        </template>
        <template v-else>
          <div v-show="calcDefault('规模')" class="lane-card-code-types">
            规模: {{ item.estimatePoint ? item.estimatePoint : '-' }}
          </div>
        </template>
      </div>
      <div v-if="item.delay" v-show="calcDefault('延期')" class="delay">
        延期
      </div>
      <template v-if="customs.length > 0">
        <div v-for="ele in customs" :key="ele.key" class="lane-card-code">
          <template v-if="ele.name">
            <div
              v-if="ele.key === 'putBy' || ele.key === 'leadingBy'"
              class="lane-card-code-text"
            >
              <span>{{ ele.name + '：' }}</span>
              <vone-user-avatar
                height="20px"
                width="20px"
                :avatar-path="
                  item.echoMap[ele.key] && item.echoMap[ele.key].avatarPath
                "
                :name="item.echoMap[ele.key] && item.echoMap[ele.key].name"
              />
            </div>
            <div v-else-if="ele.key === 'planStime'">
              {{ ele.name + '：' + dayjs(item[ele.key]) || '--' }}
            </div>
            <div v-else-if="item.echoMap[ele.key]">
              {{ ele.name + '：' + item.echoMap[ele.key].name }}
            </div>
            <div v-else class="lane-card-code-text">
              {{ ele.name + '：' + (item[ele.key] || '--') }}
            </div>
          </template>
        </div>
      </template>
      <div class="lane-card-user">
        <div class="lane-card-user-main">
          <vone-user-avatar
            v-show="calcDefault('处理人')"
            height="20px"
            width="20px"
            :avatar-path="
              item.echoMap.handleBy && item.echoMap.handleBy.avatarPath
            "
            :name="item.echoMap.handleBy && item.echoMap.handleBy.name"
          />
          <el-tooltip
            :show-after="500"
            class="item"
            effect="dark"
            content="删除标志和备注"
            placement="top-start"
          >
            <el-icon class="iconfont flag-del"><el-icon-xiaoqi /></el-icon>
          </el-tooltip>
          <el-tooltip
            :show-after="500"
            class="item"
            effect="dark"
            content="添加标志和备注"
            placement="top-start"
          >
            <el-icon class="iconfont flag"><el-icon-xiaoqi /></el-icon>
          </el-tooltip>
        </div>
        <el-tooltip
          v-if="item.echoMap.stateCode"
          effect="dark"
          :content="item.echoMap.stateCode.name"
        >
          <div
            v-show="calcDefault('状态')"
            class="lane-card-code-tag"
            :style="{
              color: item.echoMap.stateCode.color,
              borderColor: item.echoMap.stateCode.color,
            }"
          >
            {{ item.echoMap.stateCode.name }}
          </div>
        </el-tooltip>
        <el-tooltip
          v-else-if="item.echoMap.state"
          effect="dark"
          :content="item.echoMap.stateCode.name"
        >
          <div
            v-show="calcDefault('状态')"
            class="lane-card-code-tag"
            :style="{
              color: item.echoMap.state.color,
              borderColor: item.echoMap.state.color,
            }"
          >
            {{ item.echoMap.state.name }}
          </div>
        </el-tooltip>
      </div>
      <div v-if="item.flag" class="lane-card-remark">
        <div class="lane-card-remark-text">
          <el-tooltip
            :show-after="800"
            class="item"
            effect="dark"
            :content="item.remark"
            placement="top-start"
          >
            <div slot="content" class="tooltip-text">{{ item.remark }}</div>

            <span class="text">{{ item.remark }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import {
  IconDengjiJiaogao2 as ElIconIconDengjiJiaogao2,
  Xiaoqi as ElIconXiaoqi,
} from '@element-plus/icons'

import dayjs from 'dayjs'
export default {
  components: {
    ElIconIconDengjiJiaogao2,
    ElIconXiaoqi,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    code: {
      type: String,
      default: '',
    },
    user: {
      type: String,
      default: '',
    },
    date: {
      type: String,
      default: '',
    },
    dateTooltip: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    item: {
      type: Object,
      default: () => {},
    },
    defaultFields: {
      type: Object,
      default: () => ({}),
    },
    customFields: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      customColors: [
        { color: '#f56c6c', percentage: 20 },
        { color: '#e6a23c', percentage: 40 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#1989fa', percentage: 80 },
        { color: '#6f7ad3', percentage: 100 },
      ],
    }
  },
  inject: ['lane'],
  computed: {
    classifyCode() {
      return (
        this.item.echoMap?.typeCode?.classify?.code ||
        this.item.classify?.code ||
        this.item.typeCode
      )
    },
    customs() {
      return this.customFields[this.classifyCode] || []
    },
  },
  methods: {
    flagClick(e, t) {
      this.$emit('flagClick', e, t)
    },
    calcDefault(name) {
      const fields = this.defaultFields[this.classifyCode] || []
      return fields.length > 0 ? fields.indexOf(name) > -1 : false
    },
    click() {
      this.$emit('click')
    },
    dayjs(date) {
      if (date != null) {
        return dayjs(date).format('YYYY-MM-DD HH:mm')
      } else {
        return null
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.v-lane-card {
  display: flex;
  flex-direction: column;
  background-color: var(--main-bg-color, #fff);
  border-radius: 4px;
  padding: 12px;
  gap: 8px;
  min-height: 130px;
  max-width: 250px;
  border: none;
  cursor: move;
  & + & {
    margin-top: 12px;
  }
  &:hover {
    .lane-card-user .flag {
      display: inline-block;
    }
  }
  .lane-card-title {
    line-height: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    i {
      font-size: 20px;
      margin-right: 6px;
    }
    &- {
      &text {
        font-size: 14px;
        width: 180px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2; /* 设置最大显示行数 */
      }
      // &priorityCode {}
    }
  }
  .lane-card-code,
  .lane-card-user {
    display: flex;
    font-size: 12px;
    align-items: center;
    height: 20px;
    gap: 0 4px;
    &- {
      &text,
      &types {
        display: flex;
        align-items: center;
        padding: 0 4px;
        color: #838a99;
        border-radius: 2px;
        background-color: #f2f3f5;
        height: 20px;
      }

      &main {
        width: 120px;
      }
      &time {
        width: calc(100% - 120px);
        text-align: right;
      }
      &tag {
        height: 18px;
        line-height: 16px;
        padding: 0 6px;
        border-radius: 2px;
        border: 1px solid;
        max-width: 90px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .delay {
    display: flex;
    align-items: center;
    padding: 0px 4px;
    width: fit-content;
    height: 20px;
    font-size: 12px;
    color: #ffffff;
    background: #e8545b;
    border-radius: 2px;
  }
  .lane-card-user {
    justify-content: space-between;
    .lane-card-user-main {
      display: flex;
      align-items: center;
    }
    .flag {
      display: none;
      color: #bcc2cb;
      margin-left: 9px;
      cursor: pointer;
    }
    .flag-del {
      color: #f27900;
      margin-left: 9px;
      cursor: pointer;
    }
  }
}
.isflag {
  background-color: #ffd152 !important;
  .lane-card-code-text,
  .lane-card-code-types {
    background: rgba(255, 255, 255, 0.4);
    color: #1d2129;
  }
  .lane-card-code {
    color: #1d2129 !important;
  }
  .lane-card-remark {
    border-top: 1px solid rgba(255, 255, 255, 0.4);
    width: 100%;
    padding-top: 6px;
    .lane-card-remark-text {
      padding: 6px 8px;
      font-size: 12px;
      line-height: 18px;
      background: rgba(255, 255, 255, 0.4);
      .text {
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
  }
}
.tooltip-text {
  max-width: 250px;
}
</style>
