<template>
  <div class="lane-item" :class="classname">
    <div class="lane-item__body">
      <draggable
        ref="draggable"
        ghost-class="ghost"
        filter=".lane-card-title-text"
        chosen-class="chosen"
        :disabled="disabled"
        :force-fallback="true"
        :scroll-speed="50"
        :scroll-sensitivity="200"
        :animation="200"
        class="lane-item__draggable"
        :data-id="value"
        :list="list"
        :group="group"
        :move="checkMove"
        @start="dragstart"
        @end="dragend"
        @change="change"
      >
        <slot />
      </draggable>
    </div>

    <div v-if="activeStatuses && activeStatuses.length > 0" class="status-list">
      <div
        v-for="v in activeStatuses"
        :key="v.typeCode + v.stateCode"
        :data-key="v.typeCode + '$' + v.stateCode"
        class="item-status"
        :class="{
          is_active: activeStatus === v.typeCode + '$' + v.stateCode,
        }"
        @mouseover="mouseover"
        @mouseleave="mouseLeave"
        @mouseup="mouseup"
      >
        <span v-if="stateCodeMap[v.stateCode]">{{
          stateCodeMap[v.stateCode].name
        }}</span>
        <span v-else> {{ v.stateCode }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import isArray from 'lodash/isArray'
import draggable from 'vuedraggable'
import classnames from 'classnames'
export default {
  components: {
    draggable,
  },
  props: {
    statuses: {
      type: Array,
      default: () => [],
    },
    stateCodeMap: {
      type: Object,
      default: () => ({}),
    },
    list: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [String, Number],
      default: '',
    },
    put: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    clone: Boolean,
  },
  data() {
    return {
      activeStatus: '',
      elKey: null,
      dragging: false,
    }
  },
  inject: ['lane'],
  provide() {
    return {
      laneItem: this,
    }
  },
  computed: {
    groupPut() {
      return (
        this.put &&
        !this.lane.disablePut &&
        (isArray(this.lane.colDragStates[this.value])
          ? this.lane.colDragStates[this.value]?.length > 0
          : true)
      )
    },
    group() {
      const group = {
        name: this.lane.group,
        put: this.groupPut,
      }
      if (this.clone) {
        group.pull = 'clone'
      }
      return group
    },
    activeStatuses() {
      const nextStates = this.lane.colDragStates[this.value]
      const { activeType, activeDragKey } = this.lane
      // 当前列存在相同类型的可流转状态且不是拖拽项
      return this.lane.activeType
        ? this.statuses.filter(
            (v) =>
              nextStates?.indexOf(v.stateCode) > -1 &&
              activeType === v.typeCode &&
              activeDragKey !== v.typeCode + '$' + v.stateCode
          )
        : this.statuses
    },
    classname() {
      return classnames({
        'lane-item__disable_put': this.lane.dragging && !this.groupPut,
        'lane-item__can_put': this.lane.dragging && this.groupPut,
        'is-active': this.lane.dragging && this.lane.activeId === this.value,
      })
    },
  },
  methods: {
    checkMove: function (evt) {
      return evt.draggedContext.element.name !== 'apple'
    },
    dragstart(e) {
      const { context } = this.$refs.draggable
      const data = e.item._underlying_vm_
      this.lane.activeId = e.from.dataset.id
      this.lane.activeCard = data
      this.lane.activeType = data.typeCode
      this.lane.activeDragKey = data.typeCode + '$' + data.stateCode
      this.lane.dragCtx = context
      this.lane.dragging = this.dragging = true
      this.lane.onDragStart({ ...context, laneItem: this.value })
    },
    dragend() {
      this.lane.dragging = this.dragging = false
      this.lane.dragCtx = undefined
    },
    mouseover(e) {
      const key = e.target.dataset.key
      // 当前拖拽状态
      this.activeStatus = key
    },
    mouseLeave() {
      this.activeStatus = ''
    },
    mouseup() {
      const nextStatus = this.activeStatus.split('$')[1]

      this.lane.$emit('updateCardStatus', {
        element: this.lane.activeCard,
        nextStatus: nextStatus,
      })
    },
    change({ added, removed }) {
      if (added) {
        this.lane.$emit('added', { ...added, laneItem: this.value })
      } else if (removed) {
        this.lane.$emit('removed', { ...removed, laneItem: this.value })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.lane-item {
  display: flex;
  flex-direction: column;
  flex: 0 0 266px;
  gap: 8px;
  border-radius: var(--border-radius-base);
  transition: var(--all-transition);
  box-shadow: var(--box-shadow-light);
  min-width: 266px;
  overflow-y: overlay;
  overflow-x: hidden;
  background: #eaedf0;
  height: calc(100vh - 242px);
  padding: 8px 8px;
  position: relative;
  & + & {
    margin-left: 16px;
  }
  &__ {
    &body {
      flex: 1;
    }
    &draggable {
      height: 100%;
    }
    &disable_put:not(.is-active) {
      &::after {
        content: '不能拖拽到此';
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: #838a99;
        font-weight: 400;
        background-color: #fafafa;
      }
      .lane-item__draggable {
        display: none;
      }
    }
    &can_put {
      border: 2px dashed var(--main-theme-color, #3e7bfa);
    }
  }
}
.status-list {
  display: none;
  width: 100%;
  height: 100%;
}
.item-status {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  width: 100%;
  min-height: 150px;
  border: 2px dashed #3e7bfa;
  background-color: #f0f7ff;

  &.is_active {
    background-color: #f0fff1;
    border-color: #2cc750;
  }
  & + & {
    margin-top: 4px;
  }
}
.lane-item__can_put {
  justify-content: center;
  gap: 0 4px;
  border: none;
  .status-list {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  &.is-active {
    .lane-item__body {
      flex: auto;
      max-height: 50%;
      overflow: auto;
    }
  }
  &:not(.is-active) {
    .lane-item__body {
      display: none;
    }
  }
}
</style>

<style lang="scss">
.custom-theme-light {
  .chosen {
    border: 1px solid var(--main-theme-color, #3e7bfa) !important;
    box-shadow: 0px 8px 16px rgba(96, 101, 112, 0.16) !important;
    background: var(--main-bg-color, #fff) !important;
    opacity: 1 !important;
    transform: rotate(3deg);
    -webkit-transform: rotate(3deg);
    -moz-transform: rotate(3deg);
  }
  .ghost {
    background-color: #f5f7fa !important;
    opacity: 0.5 !important;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    & + & {
      margin-top: 0 !important;
    }
  }
}
.custom-theme-dark {
  .chosen {
    border: 1px solid #2d69e6 !important;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.32) !important;
    background: #252933 !important;
    opacity: 1 !important;
    transform: rotate(3deg);
    -webkit-transform: rotate(3deg);
    -moz-transform: rotate(3deg);
  }
  .ghost {
    background-color: #3e4657 !important;
    opacity: 1 !important;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    & + & {
      margin-top: 0 !important;
    }
  }
  .lane-item {
    background: #333947;
  }
}
</style>
