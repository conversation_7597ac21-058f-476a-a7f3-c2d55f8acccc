<template>
  <div>
    <el-upload
      :ref="uploadRef"
      :accept="accept"
      :action="action"
      :data="fileOtherData"
      :file-list="fileList"
      :headers="headers"
      :limit="limit"
      :multiple="multiple"
      :before-upload="beforeUploads"
      :on-change="handleChange"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      :on-success="fileUploadSuccess"
      :class="className"
      :auto-upload="autoUpload"
      v-bind="$attrs"
    >
      <el-button
        v-if="isUpload && !type"
        size="small"
        :disabled="disabled"
        :icon="ElIconIconfont elIconEditUpload"
      >
        {{ fileTitle }}
      </el-button>
      <el-button
        v-if="isUpload && type"
        :type="type"
        size="small"
        :icon="ElIconIconfont elIconEditUpload"
      >
        {{ fileTitle }}
      </el-button>
      <div slot="tip" class="tips">{{ tip }}</div>
      <slot />
    </el-upload>
    <el-image-viewer
      v-if="dialogVisible"
      :z-index="4000"
      :on-close="
        () => {
          dialogVisible = false
        }
      "
      :url-list="imgList"
    />
  </div>
</template>

<script>
import { uploadFile, delFile, downloadFile } from '@/api/common'
import { download } from '@/utils'
import { getToken } from '@/utils/auth'
import SparkMD5 from 'spark-md5'
export default {
  name: 'Upload',
  components: {
    // 使用Element Plus的图片查看器
    // 'el-image-viewer': () => import('element-plus/es/components/image-viewer')
  },
  props: {
    uploadRef: {
      type: String,
      default: 'file',
    },
    fileTitle: {
      type: String,
      default: '上传',
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true,
    },
    // 是否上传文件
    isUpload: {
      type: Boolean,
      default: true,
    },
    // 最大允许上传个数
    limit: {
      type: Number,
      default: 20,
    },
    // 允许上传的文件类型
    accept: {
      type: String,
      default: '',
    },
    action: {
      type: String,
      default: `/api/base/base/file/anyone/upload`,
    },
    // 允许上传的文件大小 单位：字节
    acceptSize: {
      type: Number,
      default: null,
    },
    // 文件类型
    bizType: {
      type: String,
      default: 'BUG_FILE_UPLOAD',
    },
    storageType: {
      type: String,
      default: 'LOCAL',
    },
    // 校验规则
    rules: {
      type: Function,
      default: null,
    },
    // 回显的列表
    filesData: {
      type: Array,
      default: () => [],
    },
    tip: {
      type: String,
      default: undefined,
    },
    // 是否md5加密
    checkMd5: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    disabled: {
      // 上传按钮置灰
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: 'upload',
    },
    deleteType: {
      // 删除方式locality:本地、remote: 远程
      type: String,
      default: 'remote',
    },
    fileSize: {
      // 限制文件大小默认2GB
      type: Number,
      default: 2 * 1024 * 1024 * 1024,
    },
    isPaste: {
      // 是否支持粘贴上传
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 默认附件列表
      fileList: [],
      // 默认额外上传数据
      fileOtherData: {
        bizType: '',
        bucket: '',
        storageType: '',
      },
      // 已上传文件
      uploadFiles: [],
      dialogVisible: false,
      imgList: [],
      serverUrl: window.location.host,
    }
  },
  computed: {
    headers() {
      return {
        token: 'Bearer ' + getToken(),
      }
    },
  },
  watch: {
    filesData: {
      handler(val) {
        if (Array.isArray(val)) {
          this.uploadFiles = [...val]
          val.map((item) => (item.name = item?.name || item.originalFileName))
          this.fileList = [...val]
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.init()
    if (!this.isPaste) return
    document.addEventListener('paste', this.onPasteUpload)
  },
  beforeDestroy() {
    if (!this.isPaste) return
    document.removeEventListener('paste', this.onPasteUpload)
  },
  methods: {
    onPasteUpload(event) {
      const items = event.clipboardData && event.clipboardData.items
      let file = null
      if (items && items.length) {
        for (var i = 0; i < items.length; i++) {
          if (items[i].type.indexOf('image') !== -1) {
            file = items[i].getAsFile()
          }
        }
      }
      if (!file) {
        return
      }
      this.uploadImg(file)
    },
    // 附件初始化
    init() {
      this.fileOtherData.bizType = this.bizType
      this.fileOtherData.storageType = this.storageType
      // this.$refs[this.uploadRef].clearFiles()
    },
    // 初始化数据
    resetData() {
      this.uploadFiles = []
      this.fileList = []
    },
    beforeUploads(file) {
      const isLtAcceptSize = file.size > this.fileSize
      if (isLtAcceptSize) {
        this.$message.warning(
          '最大支持' +
            this.fileSize / (1024 * 1024) +
            'MB的文件，该文件超出限制'
        )
        return false
      }
      if (file) {
        const flag = this.fileList.filter(
          (item) => item.originalFileName == file.name
        )
        if (flag.length > 0) {
          this.$message.warning('已有相同名称的文件存在')
          return false
        }
      }
      if (typeof this.rules == 'function') {
        if (!this.rules(file)) {
          return false
        }
      }
    },
    async handleChange(file, fileList) {
      this.$emit('change', fileList, file)
      // this.fileList = [...this.fileList, ...fileList]
    },
    fileUploadSuccess(respones, file, fileList) {
      if (respones) {
        if (respones.isSuccess) {
          file.id = respones.data.id
          this.uploadFiles.push(respones.data)
          this.$emit('onSuccess', fileList)
          if (this.checkMd5) {
            this.computeMD5(file.raw)
          }
        } else {
          fileList.map((item, index) => {
            if (item.uid === file.uid) {
              fileList.splice(index, 1)
            }
          })
          this.$message.warning(respones.msg)
        }
      }
    },
    // 附件上传失败
    handleError() {
      this.$message.warning('附件上传失败，请重试')
    },
    async handlePreview(file) {
      if (file.id) {
        if (file.fileType && file.fileType.code === 'IMAGE') {
          this.dialogVisible = true
          this.imgList = [`/api/base/base/file/noToken/download/${file.id}`]
        } else {
          // download(file.name, await downloadFile(
          //   [file.id]
          // ))
          download(
            file.name,
            `http://${this.serverUrl}/api/base/base/file/noToken/download/${file.id}`,
            'link'
          )
        }
      }
    },
    beforeRemove(file) {
      if (file.id) {
        return this.$confirm('确定删除附件' + file.name + '?', '提示')
      }
    },
    // 文件超出个数限制时的钩子
    handleExceed() {
      this.$message.warning('当前最多允许上传' + this.limit + '个文件')
    },
    // 删除附件列表
    handleRemove(file) {
      if (file.id) {
        if (this.deleteType == 'locality') {
          this.uploadFiles.map((item, index) => {
            if (item.id === file.id) {
              this.uploadFiles.splice(index, 1)
            }
          })
          this.fileList.map((item, index) => {
            if (item.id === file.id) {
              this.fileList.splice(index, 1)
            }
          })
        } else {
          delFile([file.id]).then((res) => {
            if (res.isSuccess) {
              this.uploadFiles.map((item, index) => {
                if (item.id === file.id) {
                  this.uploadFiles.splice(index, 1)
                }
              })
              this.fileList.map((item, index) => {
                if (item.id === file.id) {
                  this.fileList.splice(index, 1)
                }
              })
            } else {
              this.$message.warning(res.msg)
            }
          })
        }
      }
      this.$emit('remove', file)
    },
    // 调用平台上传方法，处理粘贴事件
    uploadImg(file) {
      const fileForm = new FormData()
      fileForm.append(
        'file',
        file,
        file.name.split('.')[0] + new Date().valueOf() + '.png'
      )
      fileForm.append('bizType', this.bizType)
      fileForm.append('storageType', this.storageType)
      fileForm.append('bucket', '')
      uploadFile(fileForm).then((res) => {
        if (res.isSuccess) {
          this.uploadFiles.push(res.data)
          res.data.name = res.data.originalFileName
          this.fileList.push(res.data)
          this.$emit('change', this.fileList)
        }
      })
    },
    /**
     * 计算md5，实现断点续传及秒传
     * @param file
     */
    computeMD5(file) {
      const fileReader = new FileReader()
      // const time = new Date().getTime()
      const blobSlice =
        File.prototype.slice ||
        File.prototype.mozSlice ||
        File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 2 * 1024 * 1024 * 1024
      const chunks = Math.ceil(file.size / chunkSize)
      const spark = new SparkMD5.ArrayBuffer()
      let md5 = null
      loadNext()

      fileReader.onload = (e) => {
        spark.append(e.target.result)

        if (currentChunk < chunks) {
          currentChunk++
          loadNext()
        } else {
          md5 = spark.end()
          this.computeMD5Success(md5, file)
        }
      }

      fileReader.onerror = function () {
        this.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }

      function loadNext() {
        const start = currentChunk * chunkSize
        const end =
          start + chunkSize >= file.size ? file.size : start + chunkSize

        fileReader.readAsArrayBuffer(blobSlice.call(file, start, end))
      }
    },

    computeMD5Success(md5, file) {
      if (this.checkMd5 && this.uploadFiles.length > 0) {
        this.uploadFiles[0].fileMd5 = md5
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.hidebtn {
  :deep(.el-upload) {
    display: none;
  }
}
:deep(.avatar-uploader .el-upload) {
  background: var(--content-bg-disabled-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 24px;
    color: var(--font-second-color);
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: auto;
    display: block;
  }
}
.tips {
  font-size: 12px;
  line-height: 20px;
  color: var(--font-second-color);
}
</style>
