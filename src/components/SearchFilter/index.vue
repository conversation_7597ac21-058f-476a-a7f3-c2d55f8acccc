<template>
  <div
    ref="filterContent"
    class="filterContent"
    :style="{ marginBottom: filterFileds.length ? '10px' : '6px' }"
    v-bind="$attrs"
  >
    <div v-for="item in filterFileds" :key="item.prop" class="fliterBorder">
      <span class="label">
        {{ item.name }}
      </span>
      <span class="maxInput">
        <span v-if="item.type.code == 'INPUT'">
          {{ item.value }}
        </span>
        <span v-else-if="item.type.code == 'TREE'">
          <vone-tree-select
            v-model.trim="item.value"
            search-nested
            :tree-data="item.optionList"
            @select="changeSelect($event, item)"
          />
        </span>
        <span
          v-else-if="item.type.code == 'SELECT' && item.key == 'tagId'"
          class="dateValue"
        >
          <tagSelect v-model="item.value" multiple is-id :clearable="false" />
        </span>
        <span
          v-else-if="item.type.code == 'SELECT' || item.type.code == 'ICON'"
        >
          <el-dropdown
            trigger="click"
            :hide-on-click="false"
            @command="changeCommand($event, item)"
          >
            <span class="el-dropdown-link">
              <span>
                {{ getName(item) }}
              </span>
              <el-icon class="el-icon--right"><ElIconArrowDown /></el-icon>
            </span>
            <el-dropdown-menu slot="dropdown">
              <virtual-list
                :style="{ 'max-height': '458px', 'overflow-y': 'auto' }"
                data-key="id"
                :extra-props="{ item }"
                :data-sources="item.optionList || []"
                :data-component="dropMenu"
              />
              <!-- <el-dropdown-item v-for="i in item.optionList" :key="i.id" :command="item.valueType == 'id' ? i.id : i.code" class="dropdownMeun">
                  <span :class="getClass(item,i)">
                    {{ i.name }}
                  </span>
                  <i v-show="getClass(item,i)" class="iconfont el-icon-tips-done" />
                </el-dropdown-item> -->
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <span v-else-if="item.type.code == 'USER'" class="dateValue">
          <vone-remote-user
            v-model="item.value"
            :no-name="false"
            :multiple="item.multiple"
            :clearable="false"
            :collapse-tags="true"
            is-filter
            @removeTag="removeTag($event, item)"
            @filterChange="filterChange($event, item)"
          />
        </span>
        <span v-else-if="item.type.code == 'DATE'" class="dateValue">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) => dayjs(d, 'hh:mm:ss').toDate())
            "
            v-model="item.value"
            style="width: 100%"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @input="changeDate($event, item)"
          ></el-date-picker>
        </span>
      </span>
    </div>
  </div>
</template>

<script>
import { ArrowDown as ElIconArrowDown } from '@element-plus/icons-vue';
import * as dayjs from 'dayjs';
import tagSelect from '@/components/CustomEdit/components/tag-select.vue'
import dropMenu from './dropMenu.vue'
export default {
  components: {
    // dropMenu
tagSelect,
    ElIconArrowDown,
  },
  data() {

return {
  filterFileds: [],
dropMenu,
  dayjs,
}
},
props: {
defaultFileds: {
type: Array,
default: () => []
},
model: {
type: Object,
default: () => {}
},
extra: { // extradata里列表视图相关的数据
type: Object,
default: () => {}
}
},
computed: {
getName() {
return function(row) {
  if ((typeof row.value != 'boolean' && !row.value) || !row.optionList) return

  if (row.multiple) {
    const hJson = row.optionList.filter(item => row.value.indexOf(row.valueType == 'id' ? item.id : item.code) !== -1)
    return hJson?.map(r => r.name)?.join(' 、')
  } else if (row.valueType == 'id') {
    const name = row.optionList.find(j => j.id == row.value)?.name
    return name
  } else {
    const name = row.optionList.find(j => j.code == row.value)?.name
    return name
  }
}
},
getClass() {
return function(row, i) {
  if (row.valueType == 'id') {
    if (row.multiple) {
      return row.value.includes(i.id) ? 'activeNode' : null
    }
    return row.value == i.id ? 'activeNode' : null
  } else {
    if (row.multiple) {
      this.$set(i, 'isActive', row.value.includes(i.code))
      return row.value.includes(i.code) ? 'activeNode' : null
    }
    return row.value == i.code ? 'activeNode' : null
  }
}
}
},
watch: {
model: {
handler(val) {
  if (Object.keys(val)?.length > 0) {
    this.$nextTick(() => {
      const keys = Object.keys(val)
      const hJson = this.defaultFileds.filter(item => keys.indexOf(item.key) !== -1)
      this.refreshlist(hJson, this.extra.fixedView || [], val)
    })
  } else {
    this.filterFileds = []
    // this.$emit('update:extra', { ...this.extra, height: 0 })
  }
},
immediate: true
},
extra: {
handler(val) {
  if (val.fixedView?.length > 0) {
    this.$nextTick(() => {
      const keys = Object.keys(this.model)
      const hJson = this.defaultFileds.filter(item => keys.indexOf(item.key) !== -1)
      this.refreshlist(hJson, val.fixedView || [])
    })
  }
},
immediate: true
}
},
methods: {
removeTag(val, item) {
this.$set(this.model, item.key, item.value)
this.$emit('update:model', { ...this.model })
this.$emit('getTableData')
},
async  changeSelect(command, item) {
this.$set(this.model, item.key, command.id)
await this.$emit('update:model', { ...this.model })

this.$emit('getTableData', this.model)
},
async changeDate(command, item) {
// console.log(command, item, 'command, item')
// this.$set(this.model, item.key.start, command[0])
this.$set(this.model, item.key, command)
await this.$emit('update:model', { ...this.model })

this.$emit('getTableData', this.model)
},
async  filterChange(command, item) {
this.$set(this.model, item.key, command)
await this.$emit('update:model', { ...this.model })
this.$emit('getTableData', this.model)
},
async changeCommand(command, item) {
if (item.key == 'classify') {
  const keyIndex = this.filterFileds.findIndex(i => i.key === 'typeCodes')
  if (keyIndex != -1) {
    const obj = this.filterFileds[keyIndex]
    obj.value = []
    this.$set(this.filterFileds, keyIndex, obj)
    this.$set(this.model, 'typeCodes', [])
  }
}
if (item.multiple) {
  const oldValue = item.value

  if (item.value.includes(command)) {
    if (oldValue.length == 1) return
    oldValue.splice(oldValue.findIndex(e => e === command), 1)
  } else {
    oldValue.push(command)
  }
  this.$set(this.model, item.key, oldValue)
} else {
  this.$set(this.model, item.key, command)
}
await this.$emit('update:model', { ...this.model })
this.$emit('getTableData', this.model)
},
refreshlist(list, fixed, val) {
list.forEach(element => {
  element.multiple = element.multiple || false
  if (val) {
    element.value = element.type.code == 'DATE' ? [val[element.key].start, val[element.key].end] : val[element.key]
  }
})
const hJson = list.filter(
  (i, j) => fixed?.indexOf(i.key) !== -1
)
this.filterFileds = hJson
this.$nextTick(() => {
  const height = this.$refs?.filterContent?.offsetHeight == 0 ? 0 : this.$refs?.filterContent?.offsetHeight + 4
  if (!this.extra?.height || (this.extra?.height != height)) {
    this.$emit('update:extra', { ...this.extra, height: height })
  }
})
}
}
}
</script>

<style lang="scss" scoped>
.filterContent {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px 10px;
}
.fliterBorder {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0px 6px;
  display: flex;
  flex-direction: row;
  line-height: 20px;
  height: 20px;
  position: relative;
  /* flex-direction: column; */
  align-items: center;
  max-width: 430px;
  overflow: hidden;
}
.label {
  font-size: 12px;
  color: #777f8e;
  margin-right: 5px;
  flex-shrink: 0;
}
.maxInput {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #2c2e36;
  font-size: 12px;
  :deep(.el-dropdown) {
    display: inline;
  }
}

:deep() {
  .vue-treeselect {
    width: 150px;
    .vue-treeselect__x-container {
      display: none !important;
    }
    .vue-treeselect__control {
      border: none;
      display: flex;
    }
    .vue-treeselect__placeholder {
      line-height: 15px !important;
    }
    .vue-treeselect__input {
      line-height: 15px !important;
      height: 15px !important;
    }
  }
}
:deep(.el-input__inner) {
  border: none;
}

.el-dropdown-link {
  cursor: pointer;
  color: #2c2e36;
  font-size: 12px;
}
.activeNode {
  // background: #3E7BFA;;
  color: #3e7bfa;
}
.dropdownMeun {
  max-height: 300px !important;
  overflow-y: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    margin-right: 20px;
  }
  .iconfont {
    color: #3e7bfa;
  }
}

.dateValue {
  :deep(.el-input__inner) {
    background: none;
    height: 100% !important;
    font-size: 12px;
  }
  :deep(.el-range-editor--small .el-range-input) {
    font-size: 12px;
    color: #000;
  }
}

:deep(.vue-treeselect__single-value) {
  line-height: 15px;
  font-size: 12px;
}
:deep(.el-input--small .el-input__icon) {
  line-height: 20px;
}
:deep(.el-dropdown-menu) {
  max-height: 300px !important;
  overflow-y: auto;
}
// :deep(.el-input--small .el-input__inner) {
//   font-size: 12px !important;
// }
:deep(.el-select__tags) {
  display: flex;
  flex-wrap: nowrap;
}
:deep(.el-tag.el-tag--info) {
  background-color: #fff;
  border: none;
  color: #2c2e36;
}
:deep(.el-range-editor--small .el-range-separator) {
  font-size: 12px;
}
</style>
