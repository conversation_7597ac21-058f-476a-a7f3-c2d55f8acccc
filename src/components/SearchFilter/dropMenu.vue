<template>
  <el-dropdown-item
    :key="index"
    class="dropdownMeun"
    :command="item.valueType == 'id' ? source.id : source.code"
  >
    <span :class="getClass(item, source)">
      {{ source.name }}
    </span>

    <el-icon class="iconfont"><el-icon-tips-done /></el-icon>
  </el-dropdown-item>
</template>

<script>
import { TipsDone as ElIconTipsDone } from '@element-plus/icons'
export default {
  components: {
    ElIconTipsDone,
  },
  name: 'ItemComponent',
  props: {
    index: {
      // 每一行的索引
      type: Number,
      default: 0,
    },
    item: {
      type: Object,
      default: () => {},
    },
    source: {
      // 每一行的内容
      type: Object,
      default() {
        return {}
      },
    },
    label: {
      // 要显示的名称
      type: String,
      default: '',
    },
    value: {
      // 绑定的值
      type: String,
      default: '',
    },
  },
  computed: {
    getClass() {
      return function (row, i) {
        if (row.valueType == 'id') {
          if (row.multiple) {
            return row.value.includes(i.id) ? 'activeNode' : null
          }
          return row.value == i.id ? 'activeNode' : null
        } else {
          if (row.multiple) {
            this.$set(i, 'isActive', row.value.includes(i.code))
            // console.log(i, 'rowrowrow')
            return row.value.includes(i.code) ? 'activeNode' : null
          }
          return row.value == i.code ? 'activeNode' : null
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.filterContent {
  display: flex;
  flex-direction: row;
  /* flex-direction: column; */
  align-items: center;
  flex-wrap: wrap;
  align-content: flex-start; //排列格式
}
.fliterBorder {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0px 6px;
  display: flex;
  flex-direction: row;

  height: 20px;
  line-height: 20px;
  position: relative;
  /* flex-direction: column; */
  align-items: center;
  max-width: 430px;
}
.fliterBorder + .fliterBorder {
  margin-left: 10px;
}
.label {
  font-size: 12px;
  color: #777f8e;
  margin-right: 5px;
  flex-shrink: 0;
}
.maxInput {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #2c2e36;
}

.selectSection {
  :deep() {
    .vue-treeselect {
      width: 150px;
      .vue-treeselect__x-container {
        display: none !important;
      }
      .vue-treeselect__control {
        border: none;
        display: flex;
      }
      .vue-treeselect__placeholder {
        line-height: 15px !important;
      }
      .vue-treeselect__input {
        line-height: 15px !important;
        height: 15px !important;
      }
    }
  }
  :deep(.el-input__inner) {
    border: none;
  }
}
.el-dropdown-link {
  cursor: pointer;
  color: #2c2e36;
}
.activeNode {
  // background: #3E7BFA;;
  color: #3e7bfa;
}
.dropdownMeun {
  font-size: 12px;
  max-height: 300px !important;
  overflow-y: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    margin-right: 20px;
  }
  .iconfont {
    color: #3e7bfa;
  }
}

.dateValue {
  :deep(.el-input__inner) {
    background: none;
    height: 100% !important;
  }
}

:deep(.vue-treeselect__single-value) {
  line-height: 15px;
  font-size: 12px;
}
:deep(.el-input--small .el-input__icon) {
  line-height: 20px;
}
:deep(.el-dropdown-menu) {
  max-height: 300px !important;
  overflow-y: auto;
}
</style>
