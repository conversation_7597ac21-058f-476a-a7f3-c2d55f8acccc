<template>
  <el-card
    ref="echarts-card"
    class="vone-echarts-card"
    shadow="never"
    :style="{ height: height, width: width }"
  >
    <template v-if="title" slot="header">
      <span class="title"
        >{{ title }}
        <slot v-if="$slots.tips" name="tips" />
      </span>
      <span v-if="name" class="desc">{{ name }}</span>
      <span v-if="headerBox" class="headerBox">
        <slot name="headerBox" />
      </span>
    </template>
    <slot />
  </el-card>
</template>

<script>
import ResizeListener from 'element-resize-detector'
export default {
  name: 'VoneEchartsCard',
  props: {
    title: {
      type: String,
      default: null,
    },
    name: {
      type: String,
      default: null,
    },
    headerBox: {
      type: Boolean,
      default: false,
    },
    height: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '',
    },
    formInfo: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    this.addChartResizeListener()
  },
  beforeDestroy() {
    this.instance.uninstall(this.$el)
  },
  methods: {
    /**
     * 对chart元素尺寸进行监听，当发生变化时同步更新echart视图
     */
    addChartResizeListener() {
      this.instance = ResizeListener({
        strategy: 'scroll',
        callOnAdd: true,
      })
      this.instance.listenTo(this.$el, () => {
        this.$nextTick(() => {
          if (this.$refs['echarts-card']?.$children.length > 0) {
            const headDom = this.$el.querySelector('.el-card__header')
            const pageDom = this.$el.querySelector('.el-pagination')
            const reduceHeight =
              headDom && pageDom ? 95 : headDom || pageDom ? 58 : 26
            this.$refs['echarts-card'].$children[
              this.headerBox ? 1 : 0
            ].$el.style.height = this.$el.offsetHeight - reduceHeight + 'px'
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.vone-echarts-card {
  border: 1px solid var(--solid-border-color);
  // min-height: 188px;
  height: 100%;
  padding: 12px 0;
  box-shadow: var(--main-bg-shadow);
  :deep(.el-card__header) {
    padding: 0;
    height: 26px;
    line-height: 26px;
    border: none;
    width: 100%;
    .title {
      padding-left: 16px;
      font-weight: 700;
      font-size: 14px;
      // width: 50%;
      flex: 1;
    }
    .desc {
      color: var(--font-second-color);
      font-size: 12px;
      margin-left: 4px;
      // filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
    }
    .headerBox {
      // padding: 0 20px;
      margin-right: 16px;
      border-radius: 2px;
      font-size: 12px;
      text-align: center;
      cursor: pointer;
    }
  }
  :deep(.el-card__body) {
    padding: 0 16px;
  }
}
</style>
