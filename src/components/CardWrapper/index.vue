<template>
  <el-card
    class="card-wrapper"
    shadow="nerver"
    :body-style="{ padding: '12px 16px 16px' }"
  >
    <div class="card-wrapper-header">
      <div class="card-wrapper-header-title">
        {{ title }}

        <span v-if="num" class="numberCard">{{ num }}</span>
      </div>
      <slot name="header" />
      <slot name="actions" />
    </div>
    <div class="card-wrapper-content">
      <slot />
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'VoneCardWrapper',
  props: {
    title: {
      type: String,
      default: '',
    },
    num: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="scss" scoped>
.card-wrapper {
  margin-bottom: 12px;
  &- {
    &header {
      margin-top: 11px;
      height: 22px;
      line-height: 22px;
      display: flex;
      &- {
        &title {
          position: relative;
          flex: 1;
          font-size: 16px;
          font-weight: 600;
          &:before {
            display: block;
            content: '';
            background: var(--main-theme-color, #3e7bfa);
            position: absolute;
            top: 10px;
            width: 4px;
            height: 18px;
            top: 9px;
            left: -16px;
            border-radius: 1px;
          }
        }
      }
    }
    &content {
      margin-top: 16px;
    }
  }
  &:last-child {
    margin-bottom: 0;
  }
  .numberCard {
    padding: 4px 8px;
    border-radius: 50%;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
