<template>
  <div class="header">
    <div style="display: flex; flex: 1; align-items: center">
      <div class="back" @click="back">
        <el-icon class="iconfont"><el-icon-direction-back /></el-icon>
      </div>
      <div class="title">{{ title }}</div>
      <slot name="custom" />
    </div>
    <div class="toolbars">
      <slot name="toolbar" />
    </div>
  </div>
</template>

<script>
import { DirectionBack as ElIconDirectionBack } from '@element-plus/icons-vue'
export default {
  components: {
    ElIconDirectionBack,
  },
  name: 'VoneBack',
  props: {
    title: {
      type: String,
      default: null,
    },
  },
  data() {
    return {}
  },
  methods: {
    back() {
      // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
      if (this.$attrs?.onBack && typeof this.$attrs?.onBack == 'function') {
        this.$emit('back')
        return
      }
      this.$router.back()
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  height: 48px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  margin-top: -16px;
  margin-left: -16px;
  margin-right: -16px;
  padding: 8px 16px;
  border-radius: 4px 4px 0 0;

  .back {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 10px;
    width: 32px;
    height: 32px;
    background-color: #ffffff;
    box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
    border-radius: 100px;
    margin-right: 16px;
    cursor: pointer;
    i {
      color: var(--main-theme-color);
    }
  }
  .title {
    font-weight: 500;
    font-size: 16px;
    color: var(--font-main-color);
    user-select: none;
  }
}
</style>
