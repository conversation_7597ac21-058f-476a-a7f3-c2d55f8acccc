<template>
  <div class="code-editor">
    <div class="code-editor-layout" :style="layoutStyle">
      <!-- 编辑器容器 -->
      <div class="editor-container">
        <!-- 代码编辑器 -->
        <div v-show="currentView === 'editor'" class="editor-panel">
          <textarea ref="editor" />
        </div>
        <!-- Markdown预览 -->
        <div v-show="currentView === 'preview'" class="preview-panel">
          <div class="markdown-body" v-html="renderedMarkdown"></div>
        </div>
        <!-- 分屏模式 -->
        <div v-show="currentView === 'split'" class="split-panel">
          <div class="editor-half">
            <textarea ref="splitEditor" />
          </div>
          <div class="preview-half">
            <div class="markdown-body" v-html="renderedMarkdown"></div>
          </div>
        </div>
      </div>
    </div>
    <el-row
      v-if="!hideActions"
      type="flex"
      justify="space-between"
      align="middle"
      class="code-editor__actions"
    >
      <div>
        <span class="code-editor-filename">
          {{ name }}
        </span>
        <template v-if="value">
          <el-divider direction="vertical" />
          <span class="code-editor-filename"> {{ value.length }} 个字符 </span>
        </template>
        <template v-if="editor">
          <el-divider direction="vertical" />
          <span class="code-editor-filename"> {{ editor.doc.size }} 行 </span>
        </template>
      </div>
      <div>
        <!-- 视图切换按钮 -->
        <el-button-group v-if="isMarkdownMode" size="small" class="view-toggle">
          <el-button
            :type="currentView === 'editor' ? 'primary' : ''"
            @click="currentView = 'editor'"
          >
            编辑
          </el-button>
          <el-button
            :type="currentView === 'split' ? 'primary' : ''"
            @click="currentView = 'split'"
          >
            分屏
          </el-button>
          <el-button
            :type="currentView === 'preview' ? 'primary' : ''"
            @click="currentView = 'preview'"
          >
            预览
          </el-button>
        </el-button-group>
        <el-divider v-if="isMarkdownMode" direction="vertical" />
        <el-select
          v-model="mode"
          v-loading="modeLoading"
          size="mini"
          filterable
        >
          <el-option
            v-for="mode in modes"
            :key="mode.name"
            :label="mode.name"
            :value="mode.name"
          />
        </el-select>
        <el-divider direction="vertical" />
        <el-select
          v-model="theme"
          v-loading="themeLoading"
          size="mini"
          filterable
        >
          <el-option
            v-for="theme in themes"
            :key="theme"
            :label="theme"
            :value="theme"
          />
        </el-select>
      </div>
    </el-row>
  </div>
</template>

<script>
import CodeMirror from 'codemirror'
import 'codemirror/mode/meta'
import 'codemirror/lib/codemirror.css'
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/solarized.css'
// 引入代码自动提示插件
import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/mode/sql/sql.js'
import 'codemirror/addon/hint/anyword-hint'
import 'codemirror/addon/hint/css-hint'
import 'codemirror/addon/hint/javascript-hint'
import 'codemirror/addon/hint/html-hint'
import 'codemirror/addon/hint/xml-hint'
import 'codemirror/addon/hint/show-hint'
import 'codemirror/addon/hint/sql-hint'
import 'codemirror/addon/edit/closetag'
// 代码段折叠功能
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/foldgutter.css'

import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/comment-fold'
import 'codemirror/addon/fold/xml-fold.js'
import 'codemirror/addon/fold/indent-fold.js'
import 'codemirror/addon/fold/markdown-fold.js'
import 'codemirror/addon/fold/comment-fold.js'
// 自动括号匹配功能
import 'codemirror/addon/edit/matchbrackets'
// 高亮行功能
import 'codemirror/addon/selection/active-line'
import 'codemirror/addon/selection/selection-pointer'
// 小地图插件
import 'codemirror-minimap'
import 'codemirror-minimap/src/minimap.css'
// closebrackets
import 'codemirror/addon/edit/closebrackets.js'
// keyMap
import 'codemirror/mode/clike/clike.js'
import 'codemirror/addon/comment/comment.js'
import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/search/searchcursor.js'
import 'codemirror/addon/search/search.js'
import 'codemirror/keymap/emacs.js'

import assignIn from 'lodash/assignIn'
// Markdown 相关导入
import MarkdownIt from 'markdown-it'
import 'github-markdown-css/github-markdown-light.css'

// 动态加载Mode
const _importMode = (name) => require(`codemirror/mode/${name}/${name}.js`)
// 动态加载主题
const _importTheme = async (name) => {
  try {
    await import(/* @vite-ignore */ `codemirror/theme/${name}.css`)
  } catch (e) {
    console.warn(`Failed to load CodeMirror theme: ${name}`, e)
  }
}

// 获取所有主题
// const context = require.context('codemirror/theme')
const themeModules = import.meta.glob('/node_modules/codemirror/theme/*.css', {
  eager: false,
})
const themes = Object.keys(themeModules)
  .map((path) => {
    const match = path.match(/codemirror\/theme\/(.*)\.css$/)
    return match ? match[1] : null
  })
  .filter(Boolean)

const local_theme_key = 'CODE_MIRROR_THEME'

export default {
  model: {
    prop: 'value',
    event: 'input',
  },
  props: {
    // 编辑器设置
    options: {
      type: Object,
      default: () => ({}),
    },
    // 编辑器内容
    value: {
      type: String,
      default: '',
    },
    // 编辑器名称
    name: {
      type: String,
      default: '',
    },
    readOnly: Boolean, // 是否只读
    hideActions: Boolean, // 是否隐藏配置项
    modes: {
      // 自定义语言模式有哪些
      type: Array,
      default: () => CodeMirror.modeInfo,
    },
    themes: {
      type: Array,
      default: () => themes,
    },
  },
  data() {
    return {
      code: '',
      defaultOptions: {
        minimap: true, // 小地图
        smartIndent: true, // 是否智能缩进
        lineWrapping: true, // 长行换行
        autoCloseTags: true,
        mode: 'YAML',
        lineNumbers: true,
        // 在行槽中添加行号显示器、折叠器、语法检测器
        gutters: [
          'CodeMirror-linenumbers',
          'CodeMirror-foldgutter',
          'CodeMirror-lint-markers',
        ],
        foldGutter: true, // 启用行槽中的代码折叠
        styleActiveLine: true, // 高亮行功能
        matchBrackets: true, // 自动括号匹配功能
      },
      editor: null,
      splitEditor: null, // 分屏模式的编辑器
      // modes: CodeMirror.modeInfo,
      mode: 'YAML',
      modeLoading: false,
      // themes,
      theme: localStorage.getItem(local_theme_key) || 'idea',
      themeLoading: false,
      // Markdown 相关
      currentView: 'editor', // 'editor', 'preview', 'split'
      markdownRenderer: null,
    }
  },
  computed: {
    owner() {
      return this.$refs.editor
    },
    splitOwner() {
      return this.$refs.splitEditor
    },
    _options() {
      return assignIn(
        { readOnly: this.readOnly },
        this.defaultOptions,
        this.options,
        this.$attrs
      )
    },
    layoutStyle() {
      return {
        bottom: this._options.minimap ? '30px' : 0,
      }
    },
    isMarkdownMode() {
      return this.mode === 'Markdown' || this.mode === 'markdown'
    },
    renderedMarkdown() {
      if (!this.isMarkdownMode || !this.markdownRenderer) {
        return ''
      }
      try {
        return this.markdownRenderer.render(this.code || '')
      } catch (error) {
        console.error('Markdown rendering error:', error)
        return '<p>Markdown 渲染错误</p>'
      }
    },
  },
  watch: {
    value: {
      handler(value) {
        if (this.editor && value !== this.code) {
          this.editor.setValue(value)
          this.code = value
          this.editor.setSize('-webkit-fill-available', 'auto')
        }
        if (this.splitEditor && value !== this.code) {
          this.splitEditor.setValue(value)
          this.splitEditor.setSize('-webkit-fill-available', 'auto')
        }
      },
      immediate: true,
    },
    mode: 'modeChange',
    theme: 'themeChange',
    name: {
      handler(name) {
        if (!name) return
        const mode = CodeMirror.findModeByFileName(name)
        if (mode) {
          this.mode = mode.name || 'YAML'
        }
      },
      immediate: true,
    },
    readOnly(val) {
      if (this.editor) {
        this.editor.setOption('readOnly', val)
      }
      if (this.splitEditor) {
        this.splitEditor.setOption('readOnly', val)
      }
    },
    currentView: {
      handler(newView) {
        this.$nextTick(() => {
          if (newView === 'split' && !this.splitEditor) {
            this.initSplitEditor()
          }
          if (this.editor) {
            this.editor.refresh()
          }
          if (this.splitEditor) {
            this.splitEditor.refresh()
          }
        })
      },
    },
  },
  mounted() {
    // 初始化 Markdown 渲染器
    this.initMarkdownRenderer()

    this.editor = CodeMirror.fromTextArea(this.owner, this._options)
    this.editor.on('inputRead', (_, changeObj) => {
      if (changeObj.text == ' ') {
        return
      }
      this.editor.showHint({ completeSingle: false })
    })
    // 设置行宽
    if (this._options.minimap) {
      const charWidth = this.editor.defaultCharWidth()
      const basePadding = 4
      this.editor.on('renderLine', function (cm, line, elt) {
        const minimapWidth = document.querySelector(
          '.CodeMirror-minimap'
        ).clientWidth
        const off =
          CodeMirror.countColumn(line.text, null, cm.getOption('tabSize')) *
          charWidth
        elt.style.textIndent = '-' + off + 'px'
        elt.style.paddingLeft = basePadding + off + 'px'
        elt.style.paddingRight = minimapWidth + 'px'
      })
    }

    this.editor.refresh()
    if (this.value) {
      this.editor.setValue(this.value)
      this.editor.setSize('-webkit-fill-available', 'auto')
    }
    this.editor.on('change', (coder) => {
      this.code = coder.getValue()
      this.$emit('input', this.code)
    })
    this.$nextTick(() => {
      this.modeChange()
      this.themeChange()
    })
  },
  methods: {
    // 初始化 Markdown 渲染器
    initMarkdownRenderer() {
      this.markdownRenderer = new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true,
        breaks: true,
      })
    },

    // 初始化分屏编辑器
    initSplitEditor() {
      if (this.splitOwner && !this.splitEditor) {
        this.splitEditor = CodeMirror.fromTextArea(
          this.splitOwner,
          this._options
        )
        this.splitEditor.on('change', (coder) => {
          this.code = coder.getValue()
          this.$emit('input', this.code)
        })
        if (this.value) {
          this.splitEditor.setValue(this.value)
          this.splitEditor.setSize('-webkit-fill-available', 'auto')
        }
      }
    },

    async modeChange() {
      const _mode = CodeMirror.findModeByName(this.mode)
      if (!_mode || _mode.mode === 'null') {
        if (_mode) {
          const { mode } = _mode
          this.editor.setOption('mode', mode)
          if (this.splitEditor) {
            this.splitEditor.setOption('mode', mode)
          }
        }
        return
      }
      const { mode } = _mode
      this.modeLoading = true
      await _importMode(mode)
      this.editor.setOption('mode', mode)
      if (this.splitEditor) {
        this.splitEditor.setOption('mode', mode)
      }
      this.modeLoading = false

      // 如果切换到 Markdown 模式，默认显示分屏视图
      if (this.isMarkdownMode && this.currentView === 'editor') {
        this.currentView = 'split'
      }
    },

    async themeChange(v) {
      if (v) {
        localStorage.setItem(local_theme_key, v)
      }
      const { theme } = this
      this.themeLoading = true
      try {
        await _importTheme(theme)
        this.editor.setOption('theme', theme)
        if (this.splitEditor) {
          this.splitEditor.setOption('theme', theme)
        }
      } catch (error) {
        console.error('Failed to load theme:', theme, error)
      }
      this.themeLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.code-editor-layout {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  height: calc(100% - 27px);
  overflow-y: auto;
}

.editor-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.editor-panel {
  position: absolute;
  width: 100%;
  height: 100%;
}

.preview-panel {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  background-color: #fff;

  .markdown-body {
    max-width: none;
    box-sizing: border-box;
    min-width: 200px;
    margin: 0 auto;
    padding: 0;
  }
}

.split-panel {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;

  .editor-half {
    width: 50%;
    height: 100%;
    border-right: 1px solid #e1e4e8;
  }

  .preview-half {
    width: 50%;
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    background-color: #fff;

    .markdown-body {
      max-width: none;
      box-sizing: border-box;
      min-width: 200px;
      margin: 0;
      padding: 0;
    }
  }
}

.code-editor {
  position: relative;
  flex: 1;
  min-height: 360px;
  height: 100%;
  overflow: hidden;
  :deep() {
    .CodeMirror {
      position: absolute;
      height: 100%;
      width: 100%;
      font-family: Consolas, Lucida Console, Monaco, Andale Mono, Ubuntu Mono,
        monospace;
      text-align: initial;
    }
  }
  &__ {
    &actions {
      position: absolute;
      z-index: 99;
      width: 100%;
      padding: 0 6px;
      bottom: 0;
      left: 0;
      transition: all 0.3s;
      background-color: var(--main-bg-color, #fff);
      height: 30px;
      .code-editor-filename {
        color: #1d2129;
      }
      .view-toggle {
        margin-right: 8px;
        :deep() {
          .el-button {
            padding: 4px 8px;
            font-size: 12px;
          }
        }
      }
      :deep() {
        .el-select {
          width: 100px;
          .el-input__inner {
            border: 0;
            padding-right: 27px;
          }
        }
      }
    }
  }
}

.custom-theme-dark {
  .code-editor__actions {
    background-color: #252933;
    .code-editor-filename {
      color: #e6e9f0;
    }
  }

  .preview-panel,
  .preview-half {
    background-color: #1e1e1e;
    color: #d4d4d4;

    .markdown-body {
      color: #d4d4d4;
    }
  }
}
</style>
