// 全局组件注册
// 这个文件用于注册所有需要全局使用的组件

// 导入常用的全局组件
import SvgIcon from './SvgIcon/index.vue'
import Pagination from './Pagination/index.vue'
import SearchWrapper from './SearchWrapper/index.vue'
import PageWrapper from './PageWrapper/index.vue'
import CardWrapper from './CardWrapper/index.vue'
import DivWrapper from './DivWrapper/index.vue'
import EditWrapper from './EditWrapper/index.vue'
import Back from './Back/index.vue'
import Breadcrumb from './Breadcrumb/index.vue'
import Card from './Card/index.vue'
import Cards from './Cards/index.vue'
import Empty from './Empty/index.vue'
import UserAvatar from './UserAvatar/index.vue'
import Toolitip from './Toolitip/index.vue'
import Upload from './Upload/index.vue'
import FileUploader from './FileUploader/index.vue'
import ImportFile from './ImportFile/index.vue'
import IconSelect from './IconSelect/index.vue'
import RemoteUser from './RemoteUser/index.vue'
import SelectTag from './SelectTag/index.vue'
import SimpleAdd from './SimpleAdd/index.vue'
import CustomsimpleAdd from './CustomsimpleAdd/index.vue'
import CustomAdd from './CustomAdd/index.vue'
import CustomEdit from './CustomEdit/index.vue'
import CustomInfo from './CustomInfo/index.vue'
import Comment from './Comment/index.vue'
import Desc from './Desc/index.vue'
import EchartsCard from './EchartsCard/index.vue'
import Echarts from './Echarts/index.vue'
import Graph from './Graph/index.vue'
import GraphGroup from './GraphGroup/index.vue'
import ServerGraph from './ServerGraph/index.vue'
import BulletinBoard from './BulletinBoard/index.vue'
import CodeEdit from './CodeEdit/index.vue'
import Editor from './Editor/index.vue'
import EditorAomao from './EditorAomao/index.vue'
import OnlyOffice from './OnlyOffice/index.vue'
import WorkFlow from './WorkFlow/index.vue'
import FormRender from './FormRender/index.vue'
import SearchDynamic from './SearchDynamic/index.vue'
import SearchFilter from './SearchFilter/index.vue'
import AuthOrg from './AuthOrg/index.vue'
import TreeSelect from './TreeSelect/index.vue'
import VirtualTree from './VirtualTree/index.vue'
import Select from './Select/index.vue'
import EmbeddedPage from './EmbeddedPage/index.vue'
import RouterWrap from './RouterWrap/index.vue'

// Element Plus 自定义组件
import ElTransfer from './Element/Transfer/index.vue'
import ElTooltip from './Element/Tooltip/index.vue'
import ElSelect from './Element/Select/index.vue'
import ElPopover from './Element/Popover/index.vue'

// Lane 相关组件
import Lane from './Lane/index.js'

// Drawer 组件
import Drawer from './Drawer/index.js'

export default function setupComponents(app) {
  // 注册全局组件
  const components = [
    { name: 'SvgIcon', component: SvgIcon },
    { name: 'Pagination', component: Pagination },
    { name: 'SearchWrapper', component: SearchWrapper },
    { name: 'PageWrapper', component: PageWrapper },
    { name: 'CardWrapper', component: CardWrapper },
    { name: 'DivWrapper', component: DivWrapper },
    { name: 'EditWrapper', component: EditWrapper },
    { name: 'Back', component: Back },
    { name: 'Breadcrumb', component: Breadcrumb },
    { name: 'Card', component: Card },
    { name: 'Cards', component: Cards },
    { name: 'Empty', component: Empty },
    { name: 'UserAvatar', component: UserAvatar },
    { name: 'Toolitip', component: Toolitip },
    { name: 'Upload', component: Upload },
    { name: 'FileUploader', component: FileUploader },
    { name: 'ImportFile', component: ImportFile },
    { name: 'IconSelect', component: IconSelect },
    { name: 'RemoteUser', component: RemoteUser },
    { name: 'SelectTag', component: SelectTag },
    { name: 'SimpleAdd', component: SimpleAdd },
    { name: 'CustomsimpleAdd', component: CustomsimpleAdd },
    { name: 'CustomAdd', component: CustomAdd },
    { name: 'CustomEdit', component: CustomEdit },
    { name: 'CustomInfo', component: CustomInfo },
    { name: 'Comment', component: Comment },
    { name: 'Desc', component: Desc },
    { name: 'EchartsCard', component: EchartsCard },
    { name: 'Echarts', component: Echarts },
    { name: 'Graph', component: Graph },
    { name: 'GraphGroup', component: GraphGroup },
    { name: 'ServerGraph', component: ServerGraph },
    { name: 'BulletinBoard', component: BulletinBoard },
    { name: 'CodeEdit', component: CodeEdit },
    { name: 'Editor', component: Editor },
    { name: 'EditorAomao', component: EditorAomao },
    { name: 'OnlyOffice', component: OnlyOffice },
    { name: 'WorkFlow', component: WorkFlow },
    { name: 'FormRender', component: FormRender },
    { name: 'SearchDynamic', component: SearchDynamic },
    { name: 'SearchFilter', component: SearchFilter },
    { name: 'AuthOrg', component: AuthOrg },
    { name: 'TreeSelect', component: TreeSelect },
    { name: 'VirtualTree', component: VirtualTree },
    { name: 'Select', component: Select },
    { name: 'EmbeddedPage', component: EmbeddedPage },
    { name: 'RouterWrap', component: RouterWrap },
    // Element Plus 自定义组件
    { name: 'ElTransfer', component: ElTransfer },
    { name: 'ElTooltip', component: ElTooltip },
    { name: 'ElSelect', component: ElSelect },
    { name: 'ElPopover', component: ElPopover },
    // Lane 组件
    { name: 'Lane', component: Lane },
    // Drawer 组件
    { name: 'Drawer', component: Drawer },
  ]

  // 批量注册组件
  components.forEach(({ name, component }) => {
    if (component) {
      try {
        app.component(name, component)
      } catch (error) {
        console.warn(`⚠️ 组件 ${name} 注册失败:`, error.message)
      }
    }
  })

  console.log('✅ 全局组件注册完成')
}
