import { assign, isArray, mapKeys } from 'lodash'
const list2Tree = (list, { id = 'id', pId = 'pId' }) => {
  const map = list.reduce((r, v) => (r[v[id]] = { ...v }) && r, {})
  const nList = []
  Object.values(map).forEach((item) => {
    if (!item[pId]) {
      nList.push(item)
      return
    }
    const parent = map[item[pId]]
    if (parent) {
      parent.children = parent.children || []
      parent.children.push(item)
    } else {
      nList.push(item)
    }
  })
  return nList
}
const getNodeStyle = (node, nodeStyle, paletteData, single) => {
  const attrs = []
  const paletteItem = paletteData.find((d) => d.key === node.key)
  const di = assign({}, nodeStyle.di, paletteItem && paletteItem.di)
  if ('fill' in di) {
    attrs.push(`bioc:fill="${di.fill}"`)
  }
  if ('stroke' in di) {
    attrs.push(`bioc:stroke="${di.stroke}"`)
  }
  return attrs.join(' ')
}

const getEdgeId = (edge) => edge.id || `${edge.source}_${edge.target}`

export default function json2Bpmn(data = {}, nodeStyle, paletteData, single) {
  const xml = []
  xml.push(`<?xml version="1.0" encoding="UTF-8"?>`)
  xml.push(
    `<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" id="Definitions_07cjh6u" targetNamespace="http://bpmn.io/schema/bpmn" exporter="bpmn-js (https://demo.bpmn.io)" exporterVersion="7.3.0">`
  )
  xml.push(`<bpmn:process id="Process_0s8gqht" isExecutable="false" >`)
  if (data.nodes) {
    const nodeTree = list2Tree(data.nodes, { pId: 'pid' })
    const loop = (list) => {
      list.forEach((node) => {
        const attrs = []
        if (node.data) {
          mapKeys(
            node.data,
            (v, k) => v !== undefined && v !== null && attrs.push(`${k}="${v}"`)
          )
        }
        xml.push(
          `<${node.type} id="${node.id}" ${attrs.join(' ')} name="${
            node.name
          }" key="${node.key}">`
        )
        if (node.children) {
          loop(node.children)
        }
        xml.push(`</${node.type}>`)
      })
    }
    loop(nodeTree)
  }
  if (data.edges) {
    data.edges.forEach((edge) => {
      if (edge.isAnnotation) {
        xml.push(
          `<bpmn:association id="${getEdgeId(edge)}" sourceRef="${
            edge.source
          }" targetRef="${edge.target}" />`
        )
      } else {
        xml.push(
          `<bpmn:sequenceFlow id="${getEdgeId(edge)}" ${
            edge.name ? `name="${edge.name}"` : ''
          } sourceRef="${edge.source}" targetRef="${edge.target}" />`
        )
      }
    })
  }
  if (data.annotations) {
    data.annotations.forEach((an) => {
      xml.push(`<bpmn:textAnnotation id="${an.id}">`)
      xml.push(`<bpmn:text>${an.name || ''}</bpmn:text>`)
      xml.push(`</bpmn:textAnnotation>`)
    })
  }
  xml.push(`</bpmn:process>`)
  xml.push(`<bpmndi:BPMNDiagram id="BPMNDiagram_1">`)
  xml.push(`<bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0s8gqht" >`)
  if (data.nodes) {
    data.nodes.map((node) => {
      // bioc:stroke="#40a9ff" bioc:fill="#e6f7ff"
      xml.push(
        `<bpmndi:BPMNShape id="${node.id}_di" ${
          node.isExpanded ? `isExpanded="${node.isExpanded}"` : ''
        }  bpmnElement="${node.id}" ${getNodeStyle(
          node,
          nodeStyle,
          paletteData,
          single
        )}>`
      )
      xml.push(
        `<dc:Bounds x="${node.x}" y="${node.y}" width="${node.w}" height="${node.h}" />`
      )
      xml.push(`</bpmndi:BPMNShape>`)
    })
  }
  if (data.edges) {
    data.edges.forEach((edge) => {
      xml.push(
        `<bpmndi:BPMNEdge id="${getEdgeId(edge)}_di" bpmnElement="${getEdgeId(
          edge
        )}">`
      )
      const waypoints = JSON.parse(edge.waypoints)
      if (isArray(waypoints)) {
        waypoints.forEach((wp) => {
          xml.push(`<di:waypoint x="${wp.x}" y="${wp.y}" />`)
        })
      }
      xml.push(`</bpmndi:BPMNEdge>`)
    })
  }
  if (data.annotations) {
    data.annotations.map((an) => {
      xml.push(`<bpmndi:BPMNShape id="${an.id}_di" bpmnElement="${an.id}">`)
      xml.push(
        `<dc:Bounds x="${an.x}" y="${an.y}" width="${an.w}" height="${an.h}" />`
      )
      xml.push(`</bpmndi:BPMNShape>`)
    })
  }
  xml.push(`</bpmndi:BPMNPlane>`)
  xml.push(`</bpmndi:BPMNDiagram>`)
  xml.push(`</bpmn:definitions>`)
  return xml.join('')
}
