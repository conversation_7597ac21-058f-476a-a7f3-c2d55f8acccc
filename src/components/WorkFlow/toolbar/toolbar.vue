<template>
  <div ref="toolbar" class="bpmn-toolbar">
    <div class="row">
      <div class="col">
        <el-button
          v-if="!preview && !btnEdit"
          size="small"
          :icon="'el-icon-delete'"
          :disabled="!sharedState.selected.length"
          @click="remove"
        />
        <el-button
          v-if="!single && !preview"
          size="small"
          :icon="ElIconDocumentCopy"
          :disabled="!sharedState.selected.length"
          @click="copy"
        />
        <el-button-group v-if="!preview && !btnEdit">
          <el-button
            size="small"
            :icon="ElIconArrowLeft"
            :disabled="!canUndo"
            @click="undo"
          />
          <el-button
            size="small"
            :icon="ElIconArrowRight"
            :disabled="!canRedo"
            @click="redo"
          />
        </el-button-group>
        <el-button-group>
          <el-button size="small" :icon="ElIconZoomIn" @click="zoomAdd" />
          <el-button size="small" :icon="ElIconZoomOut" @click="zoomRemove" />
          <el-button
            size="small"
            :icon="ElIconCScaleToOriginal"
            @click="zoomCenter"
          />
        </el-button-group>
        <!-- <el-button-group>
            <el-button size="small" :disabled="disableAlign" @click="() => align('left')">
              <i class="iconfont icon-left" />
            </el-button>
            <el-button size="small" :disabled="disableAlign" @click="() => align('right')">
              <i class="iconfont icon-right" />
            </el-button>
            <el-button size="small" :disabled="disableAlign" @click="() => align('middle')">
              <i class="iconfont icon-middle" />
            </el-button>
            <el-button size="small" :disabled="disableAlign" @click="() => align('top')">
              <i class="iconfont icon-top" />
            </el-button>
            <el-button size="small" :disabled="disableAlign" @click="() => align('bottom')">
              <i class="iconfont icon-bottom" />
            </el-button>
            <el-button size="small" :disabled="disableAlign" @click="() => align('center')">
              <i class="iconfont icon-center" />
            </el-button>
          </el-button-group> -->
        <el-button
          size="small"
          :icon="full ? 'el-icon-full-screen' : 'el-icon-full-screen'"
          @click="doFull"
        />
        <!-- <el-popover v-if="!preview" trigger="hover" title="快捷键">
            <template>
              <table class="keyboard-table">
                <tbody>
                  <template v-for="item in keyboards">
                    <tr v-if="!item.hide" :key="item.label">
                      <td>{{ item.label }}</td>
                      <td>
                        <code v-if="typeof item.keyboard === 'string'">{{ item.keyboard }}</code>
                        <code v-for="key in item.keyboard" v-else :key="key">{{ key }}</code>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </template>
            <el-button slot="reference" size="small" icon="el-icon-warning-outline" shape="circle" />
          </el-popover> -->
        <span v-if="!btnEdit">
          <el-button
            v-for="item in actions"
            :key="item.className"
            size="small"
            :title="item.title"
            :icon="item.className"
            v-on="item.action"
          />
        </span>
      </div>
      <div class="col">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    main: {
      type: HTMLDivElement,
      default: () => HTMLDivElement,
    },
    single: {
      type: Boolean,
      default: false,
    },
    preview: {
      type: Boolean,
      default: false,
    },
    btnEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const actions = [
      {
        className: 'bpmn-icon-hand-tool',
        title: 'Activate the hand tool',
        action: {
          click: (event) => {
            this.sharedState.bpmnjs.get('handTool').activateHand(event)
          },
        },
      },
      {
        className: 'bpmn-icon-lasso-tool',
        title: 'Activate the lasso tool',
        hidden: this.preview,
        action: {
          click: (event) => {
            this.sharedState.bpmnjs.get('lassoTool').activateSelection(event)
          },
        },
      },
      {
        className: 'bpmn-icon-space-tool',
        title: 'Activate the create/remove space tool',
        hidden: this.preview,
        action: {
          click: (event) => {
            this.sharedState.bpmnjs.get('spaceTool').activateSelection(event)
          },
        },
      },
      {
        className: 'bpmn-icon-connection-multi',
        title: 'Activate the global connect tool',
        hidden: this.preview,
        action: {
          click: (event) => {
            this.sharedState.bpmnjs.get('globalConnect').toggle(event)
          },
        },
      },
    ]

    const keyboards = [
      {
        label: 'Undo',
        keyboard: ['Ctrl + Z'],
      },
      {
        label: 'Redo',
        keyboard: ['Ctrl + Y', 'Ctrl + Shift + Z'],
      },
      {
        label: 'Copy',
        keyboard: 'Ctrl + C',
        hide: this.single,
      },
      {
        label: 'Paste',
        keyboard: 'Ctrl + V',
        hide: this.single,
      },
      {
        label: 'Select All',
        keyboard: 'Ctrl + A',
      },
      {
        label: 'Scrolling (Vertical)',
        keyboard: 'Ctrl + Scrolling',
      },
      {
        label: 'Scrolling (Horizontal)',
        keyboard: 'Ctrl + ⇧ + Scrolling',
      },
      {
        label: 'Direct Editing',
        keyboard: 'E',
      },
      {
        label: 'Hand Tool',
        keyboard: 'H',
      },
      {
        label: 'Lasso Tool',
        keyboard: 'L',
      },
      {
        label: 'Space Tool',
        keyboard: 'S',
      },
      {
        label: 'Search',
        keyboard: 'Ctrl + F',
      },
    ]
    return {
      canRedo: false,
      canUndo: false,
      full: false,
      keyboards,
      actions: actions.filter((a) => !a.hidden),
    }
  },
  inject: ['sharedState'],
  computed: {
    disableAlign() {
      const filteredElements = this.sharedState.selected.filter((element) => {
        return !(element.waypoints || element.host || element.labelTarget)
      })
      return filteredElements.length < 2
    },
  },
  created() {
    this.bpmnjs = this.sharedState.bpmnjs
    this.canvas = this.bpmnjs.get('canvas')
    if (!this.preview) {
      this.command = this.bpmnjs.get('commandStack')

      this.bpmnjs.on('commandStack.changed', () => {
        this.canRedo = this.command.canRedo()
        this.canUndo = this.command.canUndo()
      })
    }
  },
  methods: {
    async save() {
      this.$emit('save')
    },
    undo() {
      this.command && this.command.undo()
    },
    redo() {
      this.command && this.command.redo()
    },
    doFull() {
      if (!this.full) this.main.requestFullscreen()
      else document.exitFullscreen()
      this.full = !this.full
    },
    zoomAdd() {
      const z = this.canvas.zoom()
      this.canvas.zoom(z + 0.2)
    },
    zoomRemove() {
      const z = this.canvas.zoom()
      this.canvas.zoom(z - 0.2)
    },
    zoomCenter() {
      this.canvas.zoom('fit-viewport', true)
    },
    remove() {
      this.sharedState.bpmnjs.get('editorActions').trigger('removeSelection')
    },
    align(position) {
      const elements = this.sharedState.selected
      this.sharedState.bpmnjs.get('alignElements').trigger(elements, position)
    },
    copy() {
      const editorActions = this.sharedState.bpmnjs.get('editorActions')
      editorActions.trigger('copy')
      editorActions.trigger('paste')
    },
  },
}
</script>

<style lang="scss" scoped>
.bpmn-toolbar {
  position: relative;
  z-index: 2;
  padding: 11px 20px;
  background-color: var(--main-bg-color, #fff);
  border-bottom: 1px solid var(--disabled-bg-color, #ebeef5);
  // box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);

  .el-button {
    min-width: 40px;
  }
}
.keyboard-table {
  td {
    padding: 3px 6px;
  }
  code {
    background-color: var(--hover-bg-color, #f5f6fa);
    padding: 3px 6px;
    border-radius: 4px;
    & + code {
      margin-left: 6px;
    }
  }
}
.row {
  display: flex;
  justify-content: space-between;
  .col {
    display: flex;
    & > * + * {
      margin-left: 6px;
    }
  }
}
</style>
