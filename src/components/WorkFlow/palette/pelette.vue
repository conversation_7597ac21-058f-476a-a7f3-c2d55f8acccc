<template>
  <el-card :header="title">
    <template v-if="isGroupLayout">
      <template v-for="group in actions().filter((g) => !g.name)">
        <div
          v-for="(item, i) in group.nodes"
          :key="i"
          class="palette-item"
          :style="itemStyle(item)"
          draggable="true"
          :title="item.name"
          v-on="item.action"
        >
          <template v-if="$scopedSlots['palette-item']">
            <slot name="palette-item" :item="item" />
          </template>
          <template v-else>
            {{ item.name }}
          </template>
        </div>
      </template>
      <el-collapse accordion>
        <el-collapse-item
          v-for="group in actions().filter((g) => g.name)"
          :key="group.name"
        >
          <template slot="title">
            <div
              :title="group.name"
              style="
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
            >
              {{ group.name }}
            </div>
          </template>
          <vone-empty
            v-if="
              group.nodes.length === 0 ||
              !group.nodes.filter((r) => r.name).length
            "
            class="my-0"
          />
          <template v-else>
            <div
              v-for="(item, i) in group.nodes"
              :key="i"
              class="palette-item"
              :style="itemStyle(item)"
              draggable="true"
              :title="item.name"
              v-on="item.action"
            >
              <template v-if="$scopedSlots['palette-item']">
                <slot name="palette-item" :item="item" />
              </template>
              <template v-else>
                {{ item.name }}
              </template>
            </div>
          </template>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template v-else>
      <div
        v-for="(item, i) in actions()"
        :key="i"
        class="palette-item"
        :style="itemStyle(item)"
        draggable="true"
        :title="item.name"
        v-on="item.action"
      >
        <template v-if="$scopedSlots['palette-item']">
          <slot name="palette-item" :item="item" />
        </template>
        <template v-else>
          {{ item.name }}
        </template>
      </div>
    </template>
  </el-card>
</template>

<script>
import assign from 'lodash/assign'
import mapKeys from 'lodash/mapKeys'
import { getBusinessObject, isActivity } from '../utils/ModelUtil'
import pick from 'lodash/pick'
import values from 'lodash/values'
export default {
  inject: ['sharedState'],
  props: {
    title: {
      type: String,
      default: undefined,
    },
    data: {
      type: Array,
      default: () => [],
    },
    nodeStyle: {
      type: Object,
      default: () => ({}),
    },
    single: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    create() {
      return this.sharedState.bpmnjs.get('create')
    },
    elementFactory() {
      return this.sharedState.bpmnjs.get('elementFactory')
    },
    elementRegistry() {
      return this.sharedState.bpmnjs.get('elementRegistry')
    },
    isGroupLayout() {
      return this.data.some((item) => item.group)
    },
  },
  mounted() {
    this.sharedState.bpmnjs.on('elements.changed', (e) => this.$forceUpdate())
    this.sharedState.bpmnjs.on('import.parse.complete', (e) =>
      this.$forceUpdate()
    )
  },
  methods: {
    itemStyle({ di = {} }) {
      const _di = assign({}, this.nodeStyle.di, di)
      const style = {}
      if ('fill' in _di) {
        style.backgroundColor = _di.fill
      }
      if ('stroke' in _di) {
        style.borderColor = _di.stroke
        style.color = _di.stroke
      }
      return style
    },
    createListener(item) {
      return (event) => {
        const opts = assign(
          { type: 'j:Base' },
          this.nodeStyle,
          pick(item, ['type', 'height', 'width'])
        )
        opts.di = assign({}, this.nodeStyle.di, item.di)
        const shape = this.elementFactory.createShape(opts)
        if ('isExpanded' in item) {
          shape.businessObject.di.isExpanded = item.isExpanded
        }
        const bs = getBusinessObject(shape)
        bs.set('name', item.name)
        bs.set('key', item.key)
        if (item.data) {
          mapKeys(item.data, (v, k) => {
            if (k == 'id') {
              bs.set('_id', v)
              return
            }
            bs.set(k, v)
          })
        }
        this.create.start(event, shape)
      }
    },
    actions() {
      let all = this.data
      if (this.single) {
        const ids = this.elementRegistry
          .filter(isActivity)
          .map((n) => getBusinessObject(n).get('key') + '')
        all = this.data.filter(
          (item) => item.single === false || !ids.includes(item.key + '')
        )
      }

      const actions = all.map((item) => {
        return {
          ...item,
          action: {
            dragstart: this.createListener(item),
            click: this.createListener(item),
          },
        }
      })

      if (this.isGroupLayout) {
        const groupMap = this.data.reduce(
          (r, v) => ((r[v.group] = { name: v.group, nodes: [] }) || 1) && r,
          {}
        )
        actions.forEach((ac) => {
          if (groupMap[ac.group]) groupMap[ac.group].nodes.push(ac)
        })
        return values(groupMap)
      }

      return actions
    },
  },
}
</script>

<style lang="scss" scoped>
.el-card {
  width: 200px;
  border-radius: 0;
  border: 0;
  border-right: 1px solid var(--disabled-bg-color, #ebeef5);
  display: flex;
  flex-direction: column;
  :deep() {
    .el-collapse {
      margin: -16px;
      &:not(:first-child) {
        margin-top: 16px;
      }
    }
    .el-card__body {
      height: 100%;
      overflow: auto;
    }
  }
}
.palette-item {
  text-align: center;
  margin-bottom: 6px;
  border: 1px solid var(--disabled-bg-color, #ebeef5);
  transition: all 0.3s;
  border-radius: 10px;
  height: 30px;
  line-height: 30px;
  width: 100px;
  font-size: 12px;
  &:hover {
    transition: all 0.1s;
    background: #efefef;
  }
}
</style>
