import InputItem from './Control/Input'
import SelectItem from './Control/Select'
import DateItem from './Control/Date'
import InputNumber from './Control/InputNumber'
export default {
  name: 'ItemRender',
  props: {
    type: {
      type: String,
      default: 'input',
    },
    config: Object,
  },
  render: function (h) {
    switch (this.type) {
      case 'input':
        return InputItem(h, this.config)
      case 'select':
        return SelectItem(h, this.config)
      case 'date':
        return DateItem(h, this.config)
      case 'number':
        return InputNumber(h, this.config)
      default:
        return ''
    }
  },
}
