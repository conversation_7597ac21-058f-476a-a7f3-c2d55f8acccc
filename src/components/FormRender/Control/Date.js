export default function dateItem(h, config) {
  const { model, key, props, listeners, placeholder, type } = config
  const pps = props || {}
  const on = listeners || {}
  return h('el-date-picker', {
    props: {
      ...pps,
      value: model[key],
    },
    attrs: {
      placeholder,
      type: type || 'date',
    },
    on: {
      ...on,
      input: (val) => {
        model[key] = val
      },
    },
  })
}
