export default function selectItem(h, config) {
  const {
    model,
    key,
    props,
    listeners,
    optionsList,
    optionConfig,
    placeholder,
    syncConfig,
  } = config

  let optionLabel = 'label'
  let optionValue = 'value'
  if (optionConfig) {
    if (optionConfig.label) {
      optionLabel = optionConfig.label
    }
    if (optionConfig.value) {
      optionValue = optionConfig.value
    }
  }

  const opts = optionsList
  let ops = []
  if (opts.length > 0) {
    ops = opts.map((option) => {
      if (option.isSlot) {
        return h(
          'el-option',
          {
            key: option[optionValue],
            props: {
              label: option[optionLabel] || 'label',
              value: option[optionValue] || 'id',
              disabled: option.disabled,
            },
          },
          [
            h('el-icon', {
              class: option.icon,
              style: {
                color: option.color,
                paddingRight: '10px',
              },
            }),
            h(
              'span',
              {
                style: {},
              },
              option[optionLabel]
            ),
          ]
        )
      } else {
        return h('el-option', {
          key: option[optionValue],
          props: {
            label: option[optionLabel] || 'label',
            value: option[optionValue] || 'id',
            disabled: option.disabled,
          },
        })
      }
    })
  }

  const on = listeners || {}
  let pps = props || {}
  if (syncConfig) {
    pps = {
      ...pps,
      ...syncConfig(),
    }
  }

  return h(
    'el-select',
    {
      props: {
        placeholder,
        ...pps,
        value: model[key],
      },
      on: {
        ...on,
        change: (val) => {
          model[key] = val
          if (on.change) {
            on.change(val)
          }
        },
      },
    },
    ops
  )
}
