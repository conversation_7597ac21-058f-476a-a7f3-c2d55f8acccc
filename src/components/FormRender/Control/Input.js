export default function inputItem(h, config) {
  const { model, key } = config
  const { props, listeners, placeholder, atrrProps } = config
  const on = listeners || {}
  const pps = props || {}
  const attr = atrrProps || {}
  return h('el-input', {
    props: {
      ...pps,
      value: model[key],
      disabled: pps.disabled,
    },
    attrs: {
      placeholder,
      ...attr,
    },
    on: {
      ...on,
      // 实现双向绑定
      input: (val) => {
        model[key] = val
      },
    },
  })
}
