<template>
  <el-form ref="formRender" :model="model" label-position="top">
    <el-row>
      <!-- <draggable v-model="dragList" v-bind="{ animation:500 }" @update="datadragEnd"> -->
      <div v-for="item in dragList" :key="item.key">
        <el-col
          :span="
            item.key == 'name' ||
            item.key == 'description' ||
            item.key == 'files'
              ? 24
              : 6
          "
        >
          <!-- <transition-group> -->
          <el-form-item
            :label="item.name"
            :prop="item.key"
            :rules="item.rules || []"
          >
            <item-render
              :type="item.type"
              :config="{
                placeholder: item.placeholder || '',
                ...item.config,
                model,
                key: item.key,
              }"
            />
          </el-form-item>
          <!-- </transition-group> -->
        </el-col>
      </div>
      <!-- </draggable> -->
    </el-row>
  </el-form>
</template>

<script>
import ItemRender from './ItemRender'

export default {
  components: {
    ItemRender,
  },
  props: {
    // el-form model属性
    model: {
      type: Object,
      default: () => {},
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dragList: this.list,
    }
  },
  mounted() {},
  methods: {
    datadragEnd(eve) {
      this.$emit('update:list', this.dragList)
    },
  },
}
</script>
