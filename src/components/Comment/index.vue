<template>
  <div class="comment-box">
    <div
      class="comment-list"
      :style="{
        height: rowsnum == 1 ? 'calc(100% - 50px)' : 'calc(100% - 192px)',
      }"
    >
      <div
        v-for="(e, index) in commentList"
        :key="index"
        class="comment-list-item"
      >
        <div>
          <vone-user-avatar
            :avatar-path="e.echoMap.fromUser.avatarPath"
            :avatar-type="true"
            :show-name="false"
          />
        </div>
        <div class="comment-content">
          <div style="width: 100%">
            <span class="name">{{ e.echoMap.fromUser.name }}</span>
            <span v-if="e.toUser" class="reply">回复</span>
            <span v-if="e.toUser" class="name">{{
              e.echoMap.toUser.name
            }}</span>

            <!-- <el-dropdown class="operation" trigger="click" @command="handleCommand($event, e)">
                <span class="el-dropdown-link">
                  <i class="el-icon-more" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="a">回复</el-dropdown-item>
                  <el-dropdown-item v-if="e.createdBy==user" command="b">删除评论</el-dropdown-item>
                  <el-dropdown-item v-if="e.createdBy==user" command="c">编辑内容</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown> -->
          </div>
          <p
            class="commandtext"
            @click="commandclick($event)"
            v-html="e.textvalue"
          />
          <span class="time">{{ e.createTime }}</span>
          <span v-if="e.edited" class="status">已编辑</span>
          <div class="handleBox">
            <a @click="handleCommand('a', e)">回复</a>
            <a @click="handleCommand('c', e)">编辑</a>
            <a @click="handleCommand('b', e)">删除</a>
          </div>
          <div class="imgbox" style="padding: 0">
            <ul class="el-upload-list--picture-card">
              <li
                v-for="item in e.files"
                :key="item.id"
                class="img-list__item is-ready"
              >
                <div>
                  <img
                    :src="'/api/base/base/file/noToken/download/' + item.id"
                    alt=""
                    class="img-list__item-thumbnail"
                  />
                  <span class="el-upload-list__item-actions">
                    <span @click="getimg(item)"
                      ><el-icon><el-icon-zoom-in /></el-icon
                    ></span>
                  </span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <vone-empty v-if="commentList.length < 1" />
    </div>

    <div
      ref="textareabox"
      class="comment-edit"
      :style="{ padding: rowsnum == 1 ? '6px' : '16px' }"
    >
      <div class="textarea-box" @keyup.delete="deleteFn">
        <el-input
          v-model="value"
          resize="none"
          class="textarea-style"
          :rows="rowsnum"
          :prefix-icon="'el-icon-edit'"
          type="textarea"
          :placeholder="replytext"
          maxlength="300"
          @input="inputchang"
        />
        <div v-if="rowsnum === 3" class="imgbox">
          <ul class="el-upload-list--picture-card">
            <li
              v-for="item in uploadFiles"
              :key="item.id"
              class="img-list__item is-ready"
            >
              <div>
                <img
                  :src="'/api/base/base/file/noToken/download/' + item.id"
                  alt=""
                  class="img-list__item-thumbnail"
                />
                <span class="el-upload-list__item-actions">
                  <span @click="getimg(item)"
                    ><el-icon><el-icon-zoom-in /></el-icon
                  ></span>
                  <span @click="handleRemove(item)"
                    ><el-icon class="iconfont"
                      ><el-icon-application-delete /></el-icon
                  ></span>
                </span>
              </div>
            </li>
          </ul>
        </div>
        <div v-show="rowsnum === 3" class="toolbar">
          <div>
            <el-upload
              :show-file-list="false"
              :file-list="fileList"
              multiple
              accept=".jpeg,.png,.jpg,.bmp,.gif"
              :headers="headers"
              :data="fileOtherData"
              :before-upload="beforeUploads"
              :on-success="fileUploadSuccess"
              action="/api/base/base/file/anyone/upload"
            >
              <el-button type="text"
                ><el-icon class="iconfont"
                  ><el-icon-application-picture /></el-icon
              ></el-button>
            </el-upload>
            <el-popover
              ref="popoverRef"
              placement="top"
              width="200"
              trigger="click"
              @show="showCommitSelector('userSelector')"
            >
              <div>
                <selector
                  ref="userSelector"
                  datatype="user"
                  :project-id="projectId"
                  placeholder="搜索成员"
                  @selectedChang="selectedChang"
                />
              </div>
              <el-button slot="reference" type="text"
                ><el-icon class="iconfont"><el-icon-edit-character-a /></el-icon
              ></el-button>
            </el-popover>
            <el-popover
              ref="workpopoverRef"
              placement="top"
              trigger="click"
              @show="showCommitSelector('workSelector')"
            >
              <div>
                <selector
                  ref="workSelector"
                  datatype="work"
                  searchtype
                  placeholder="搜索工作项"
                  @selectedChang="workselectedChang"
                />
              </div>
              <el-button slot="reference" type="text"
                ><el-icon class="iconfont"><el-icon-edit-character-b /></el-icon
              ></el-button>
            </el-popover>
          </div>
          <span class="textsizi">{{ value.length }}/300</span>
        </div>
      </div>
      <div v-show="rowsnum === 3" class="sendstyle">
        <div>
          <el-switch
            v-model="switchvalue"
            :width="28"
            :height="16"
            active-color="#699DFF"
          />
          <span class="tips">提示相关人员查看</span>
        </div>
        <div>
          <el-button v-if="isreply" class="miniBtn" @click="cancel"
            >取消</el-button
          >
          <el-button class="miniBtn" type="primary" @click="send">{{
            buttontext
          }}</el-button>
        </div>
      </div>
      <el-image-viewer
        v-show="dialogVisible"
        id="imageviewer"
        :z-index="4000"
        :on-close="
          () => {
            dialogVisible = false
          }
        "
        :url-list="imgList"
      />
    </div>
  </div>
</template>

<script>
import { uploadFile, delFile } from '@/api/common'
import {
  getcomment,
  sendcomment,
  deletecomment,
  editcomment,
  getProjecCheck,
} from '@/api/vone/project/comment'

import { getToken } from '@/utils/auth'
import selector from './selector.vue'

const pathType = {
  IDEA: 'reqm_center_idea_view',
  ISSUE: 'reqm-center-require-list',
  BUG: 'project_defect',
  TASK: 'project_task',
  RISK: 'project_risk_view',
}
export default {
  components: {
    // 使用Element Plus的图片查看器
    // 'el-image-viewer': () => import('element-plus/es/components/image-viewer'),
    selector,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    titleContent: {
      type: String,
      default: '',
    },
    sourceId: {
      type: String,
      default: '',
    },
    sourceType: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: undefined,
    },
    height: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      sourceIds: this.sourceId,
      value: '',
      rowsnum: 1,
      valuenum: 0,
      isshow: false,
      switchvalue: false,
      fileList: [],
      fileOtherData: {
        bizType: 'BUG_FILE_UPLOAD',
        bucket: '',
      },
      uploadFiles: [],
      dialogVisible: false,
      imgList: [],
      commentList: [],
      objform: {
        description: '',
        files: [],
        noticeUsers: {
          allRelated: false,
          mentioneds: [],
        },
        sourceId: this.sourceIds,
        sourceType: this.sourceType,
        toUser: '',
      },
      namelist: [],
      worklist: [],
      isreply: false,
      replytext: '请输入评论',
      buttontext: '发送',
      user: this.$store.state.user.user.id,
    }
  },
  computed: {
    headers() {
      return {
        token: 'Bearer ' + getToken(),
      }
    },
  },
  watch: {
    sourceId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.sourceIds = newVal
        this.getworkitemFn()
      }
    },
  },
  beforeDestroy() {
    document.removeEventListener('paste', this.onPasteUpload)
    document.removeEventListener('click', this.eidtclick)
  },
  mounted() {
    document.addEventListener('paste', this.onPasteUpload)
    document.addEventListener('click', this.eidtclick)
    this.getworkitemFn()
    // this.$bus.$on('commentChange', (e) => {
    //   this.sourceIds = e
    //   this.getworkitemFn()
    // })
  },
  methods: {
    // 查询评论人员或工作项
    showCommitSelector(ref) {
      this.$refs[ref]?.getInitData()
    },
    commentChange(e) {
      this.sourceIds = e
      this.getworkitemFn()
    },
    eidtclick(e) {
      console.log(
        this.$refs.textareabox?.contains(e.target),
        document.getElementById('imageviewer')?.contains(e.target)
      )
      const refClick = this.$refs.textareabox?.contains(e.target)
      const imageviewerClick = document
        .getElementById('imageviewer')
        ?.contains(e.target)
      if (refClick) {
        this.rowsnum = 3
      } else {
        if (!imageviewerClick) {
          this.rowsnum = 1
        }
      }
    },
    async getProjecCheckFn(id) {
      const { isSuccess, data, msg } = await getProjecCheck(id)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      return data
    },
    async commandclick(e) {
      if (e.target.nodeName === 'A') {
        if (e.target.getAttribute('data-type') === 'work') {
          const data = e.target.dataset

          if (!data.projectid && data.code == 'undefined') {
            this.$message.warning('暂不支持跳转')
            return
          }
          if (data.projectid) {
            if (await this.getProjecCheckFn({ projectId: data.projectid })) {
              this.$router.push({
                name: pathType[data.code],
                params: {
                  projectKey: data.projectcode,
                  projectTypeCode: data.projecttypecode,
                  id: data.projectid,
                  type: 'comment',
                  businessId: data.uid,
                },
              })
            } else {
              this.$message.warning('您未在该工作项所属项目中，无法查看')
            }
          } else {
            this.$router.push({
              name: pathType[data.code],
              params: {
                projectKey: data.projectcode,
                projectTypeCode: data.projecttypecode,
                id: data.projectid,
                type: 'comment',
                businessId: data.uid,
              },
            })
          }
        }
      }
    },
    reset() {
      this.value = ''
      this.uploadFiles = []
      this.objform = {
        description: '',
        files: [],
        noticeUsers: {
          allRelated: false,
          mentioneds: [],
        },
        sourceId: this.sourceIds,
        sourceType: this.sourceType,
        toUser: '',
      }
      this.replytext = '请输入评论'
      this.buttontext = '发送'
    },
    cancel() {
      this.isreply = false
      this.reset()
    },
    selectedChang(e) {
      this.value += `@${e.name} `
      this.namelist.push(e)
      this.$refs.popoverRef.doClose()
    },
    workselectedChang(e) {
      this.value += `#${e.code} `
      this.worklist.push(e)
      this.$refs.workpopoverRef.doClose()
    },
    deleteFn(e) {},
    inputchang(e) {
      this.value = e
    },
    onPasteUpload(event) {
      const items = event.clipboardData && event.clipboardData.items
      let file = null
      if (items && items.length) {
        for (var i = 0; i < items.length; i++) {
          if (items[i].type.indexOf('image') !== -1) {
            file = items[i].getAsFile()
          }
        }
      }
      if (!file) {
        return
      }
      this.uploadImg(file)
    },
    uploadImg(file) {
      console.log('进来了')
      const fileForm = new FormData()
      fileForm.append(
        'file',
        file,
        file.name.split('.')[0] + new Date().valueOf() + '.png'
      )
      fileForm.append('bizType', 'BUG_FILE_UPLOAD')
      fileForm.append('bucket', '')
      console.log(123)

      uploadFile(fileForm).then((res) => {
        console.log(fileForm)
        if (res.isSuccess) {
          this.uploadFiles.push(res.data)
          res.data.name = res.data.originalFileName
          this.fileList.push(res.data)
        }
      })
    },
    beforeUploads(file) {
      const acceptSize = 2 * 1024 * 1024 * 1024
      const isLtAcceptSize = file.size > acceptSize
      if (isLtAcceptSize) {
        this.$message.warning(
          '最大支持2GB的文件，该文件' + file.name + '超出限制'
        )
        return false
      }
      if (file) {
        const flag = this.uploadFiles.filter(
          (item) => item.originalFileName == file.name
        )
        if (flag.length > 0) {
          this.$message.warning('已有相同名称的文件存在')
          return false
        }
      }
    },
    fileUploadSuccess(respones, file, fileList) {
      if (respones) {
        if (respones.isSuccess) {
          file.id = respones.data.id
          this.uploadFiles.push(respones.data)
        } else {
          fileList.map((item, index) => {
            if (item.uid === file.uid) {
              fileList.splice(index, 1)
            }
          })
          this.$message.warning(respones.msg)
        }
      }
    },
    handleRemove(file) {
      if (file.id) {
        delFile([file.id]).then((res) => {
          if (res.isSuccess) {
            this.uploadFiles.map((item, index) => {
              if (item.id === file.id) {
                this.uploadFiles.splice(index, 1)
              }
            })
            this.fileList.map((item, index) => {
              if (item.id === file.id) {
                this.fileList.splice(index, 1)
              }
            })
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
    getimg(e) {
      this.dialogVisible = true
      this.imgList = [`/api/base/base/file/noToken/download/${e.id}`]
    },
    async handleCommand(e, item) {
      switch (e) {
        case 'a':
          this.rowsnum = 3
          this.isreply = true
          this.replytext = `回复：${item.echoMap.fromUser.name}`
          this.objform.toUser = item.createdBy
          break
        case 'b':
          try {
            await this.$confirm(`确定要删除该条评论吗?`, '删除', {
              type: 'warning',
              closeOnClickModal: false,
              customClass: 'delConfirm',
            })

            const res = await deletecomment(item.id)
            if (!res.isSuccess) {
              this.$message.warning(res.msg)
              return
            } else {
              this.$message.success('删除成功')
              this.getworkitemFn()
            }
          } catch (e) {
            return
          }
          break
        case 'c':
          this.rowsnum = 3
          this.isreply = true
          this.replytext = '请输入评论'
          this.buttontext = '保存'
          this.objform = {
            id: item.id,
            description: '',
            files: item.files,
            noticeUsers: {
              allRelated: item.noticeUsers.allRelated,
              mentioneds: item.noticeUsers.mentioneds,
            },
            sourceId: this.sourceIds,
            sourceType: this.sourceType,
            fromUser: item.fromUser,
            toUser: item.toUser,
          }
          this.value = this.editdatahandle(item.description)
          this.uploadFiles = item.files
          this.switchvalue = item.switchvalue
          break
      }
    },
    editdatahandle(e) {
      const that = this
      var text = e
      if (text) {
        var patt1 = /\[(.+?)\]/g
        text = text.replace(patt1, function (e, s1) {
          const items = s1.split('|')
          if (s1.substr(0, 1) === '@') {
            that.namelist.push({
              name: items[2],
              userId: items[1],
            })
          } else if (s1.substr(0, 1) === '#') {
            that.worklist.push({
              code: items[2],
              id: items[1],
              sourceType: {
                code: items[3],
              },
            })
          }
          return items[0]
        })
      }
      return text
    },
    datahandle(e) {
      var text = e
      if (text) {
        var patt1 = /[\n\r]/g
        var patt2 = /\[(.+?)\]/g
        text = text.replace(patt1, '<br>')
        text = text.replace(patt2, function (e, s1) {
          const items = s1.split('|')
          if (s1.substr(0, 1) === '@') {
            return `<a data-uid="${items[1]}" data-type="name" data-type="name">${items[0]}</a>`
          } else if (s1.substr(0, 1) === '#') {
            if (items.length === 7) {
              return `<a data-type="work" data-uid="${items[1]}" data-code="${items[3]}" data-projectCode="${items[4]}" data-projectTypeCode="${items[5]}" data-projectId="${items[6]}">${items[0]}</a>`
            } else {
              return `<a data-type="work" data-uid="${items[1]}" data-code="${items[3]}" data-projectCode="" data-projectTypeCode="" data-projectId="">${items[0]}</a>`
            }
          }
        })
      }
      return text
    },
    dataformhandle(e) {
      const that = this
      that.objform.noticeUsers.mentioneds = []
      var text = e

      if (text) {
        var patt1 = /@(\S+)\s/g
        var patt2 = /#(\S+)\s/g
        text = text.replace(patt1, function (e, s1) {
          const items = that.namelist.find((item) => {
            return item.name === s1
          })
          items && that.objform.noticeUsers.mentioneds.push(items?.id)
          return items ? `[${e}|${items?.id}|${s1}]` : e
        })
        text = text.replace(patt2, function (e, s1) {
          const items = that.worklist.find((item) => {
            return item.code === s1
          })

          if (items?.echoMap?.projectId) {
            return `[${e}|${items?.id}|${s1}|${items?.sourceType.code}|${items?.echoMap?.projectId.code}|${items?.echoMap?.projectId.typeCode}|${items?.echoMap?.projectId.id}]`
          } else {
            return items
              ? `[${e}|${items?.id}|${s1}|${items?.sourceType.code}]`
              : e
          }
        })
      }
      return text
    },
    async getworkitemFn() {
      if (!this.sourceId) {
        return
      }
      const res = await getcomment({
        source_id: this.sourceIds,
        source_type: this.sourceType,
      })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.commentList = res.data.filter((e) => {
        e.textvalue = this.datahandle(e.description)
        return !e.deleted
      })
    },
    async send() {
      if (!this.value && this.uploadFiles.length < 1) return
      this.objform.description = this.dataformhandle(this.value)
      this.objform.files = this.uploadFiles
      this.objform.noticeUsers.allRelated = this.switchvalue
      this.objform.sourceId = this.sourceIds
      if (!this.objform.id) {
        const res = await sendcomment(this.objform)
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
      } else {
        const res = await editcomment(this.objform)
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
      }
      this.reset()
      this.getworkitemFn()
    },
  },
}
</script>

<style lang="scss" scoped>
.comment-box {
  display: flex;
  height: calc(90vh - 235px - 48px);
  overflow-y: auto;
  position: relative;
  .comment-list {
    flex: 1;
    height: 1px;
    overflow-y: auto;
    overflow-x: hidden;
    flex-direction: column;
    padding: 16px;
    height: calc(100% - 50px);

    .comment-list-item {
      display: flex;
      padding: 4px 0;
      .comment-content {
        width: 100%;
        padding: 4px 8px;
        border-radius: 4px;
        span,
        p {
          font-size: 14px;
          line-height: 22px;
        }
        .name {
          color: #202124;
        }
        .time {
          font-size: 12px;
          color: var(--font-second-color);
        }
        .status {
          margin-left: 8px;
          font-size: 12px;
          color: var(--font-second-color);
        }
        .reply {
          margin: 0 4px;
          color: #8a8f99;
        }
        p {
          margin: 4px 0;
          // padding-right: 56px;
        }
        .operation {
          float: right;
          height: 22px;
          line-height: 22px;
          background: #f5f6fa;
          color: #8a8f99;
          border: none;
          padding: 0 7px;
          border-radius: 2px;
        }
      }
      &:last-child {
        border: none;
      }
    }
  }
  .imgbox {
    // max-width: 300px;
    padding: 0 16px;
    // max-height: 66px;
    overflow-y: auto;
    .img-list__item {
      overflow: hidden;
      background-color: #f5f5f5;
      border: none;
      border-radius: 2px;
      box-sizing: border-box;
      width: 60px;
      height: 60px;
      margin: 0 8px 8px 0;
      display: inline-block;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      font-size: 14px;
      color: #606266;
      line-height: 1.8;
      position: relative;
      .img-list__item-thumbnail {
        width: 100%;
        height: 100%;
      }
    }
    .imglist {
      color: #888;
      display: flex;
      padding: 0 10px;
      border-radius: 2px;
      justify-content: space-between;
      align-items: center;
      :deep(.el-button) {
        padding: 0;
        height: 22px;
      }
      &:hover {
        background: #f3f3f3;
      }
    }
  }
  .comment-edit {
    border-top: 1px solid #ebeef5;
    padding: 16px;
    width: 100%;
    position: absolute;
    bottom: 0px;
    background: #fff;

    // margin:0px -16px;
    .textarea-box {
      width: 100%;
      border: 1px solid #c1c8d6;
      box-sizing: border-box;
      border-radius: 2px;
      .textarea-style {
        :deep(.el-textarea__inner) {
          border: none;
        }
      }

      .toolbar {
        padding: 0 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        > div {
          display: flex;
          :deep(.el-button) {
            min-width: min-content;
            margin-left: 0;
            margin-right: 10px;
            padding: 0;
          }
        }
        :deep(.el-button) {
          padding: 0;
        }
        .textsizi {
          color: #aeb5c2;
          font-size: 14px;
          line-height: 32px;
        }
        .iconfont {
          color: var(--main-theme-color, #3e7bfa);
        }
      }
    }
    .sendstyle {
      margin: 16px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      > div {
        display: flex;
        align-items: center;
        span {
          margin-left: 10px;
        }
      }
    }
  }
}
.commandtext {
  color: var(--font-main-color);

  :deep(a) {
    color: var(--main-theme-color);
  }
}
.handleBox {
  height: 24px;
  line-height: 24px;
  display: none;
  a {
    margin-right: 16px;
    font-size: 12px;
    font-weight: 500;
    color: var(--main-theme-color);
  }
}
.tips {
  color: var(--font-second-color);
  font-size: 12px;
}
.comment-content:hover {
  background-color: var(--content-bg-hover-color);
  .handleBox {
    display: block;
  }
}
.el-button + .el-button {
  margin-left: 12px !important;
}
</style>
