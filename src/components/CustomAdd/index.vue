<template>
  <div class="drawerBox">
    <el-dialog
      :title="title"
      top="6vh"
      v-model="visible"
      :before-close="onClose"
      :modal="true"
      size="md"
      :close-on-click-modal="false"
    >
      <div class="colBox">
        <el-skeleton :loading="pageLoading" style="width: 100%" animated>
          <template slot="template">
            <el-skeleton-item class="skeleton-item_lable" />
            <el-skeleton-item />
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <div v-for="index in 3" :key="index" style="width: 190px">
                <el-skeleton-item style="width: 50%; margin-bottom: 14px" />
                <el-skeleton-item style="width: 90%" />
              </div>
            </div>
            <el-skeleton-item class="skeleton-item_lable" />
            <el-skeleton-item />
            <el-skeleton-item class="skeleton-item_lable" />
            <el-skeleton-item />
          </template>
        </el-skeleton>
        <h4 style="margin-top: 0px">基本信息</h4>
        <!-- 固定属性有['名称',"类型",'处理人','时间','标签','描述','附件'] -->
        <el-form
          v-if="fixedProperty.length"
          ref="fixdForm"
          :model="fixdForm"
          label-position="top"
          :rules="fixdFormRules"
        >
          <el-row :gutter="12">
            <div v-for="item in fixedProperty" :key="item.id">
              <el-col
                :span="
                  item.key == 'name' ||
                  item.key == 'description' ||
                  item.key == 'files' ||
                  item.key == 'tagId'
                    ? 24
                    : 8
                "
              >
                <el-form-item :label="item.name" :prop="item.key">
                  <!-- 人员组件----处理人 -->
                  <vone-remote-user
                    v-if="item.type == 'USER' || item.type == 'PROJECTUSER'"
                    v-model="fixdForm[item.key]"
                    :project-id="
                      item.key == 'leadingBy' ||
                      item.key == 'putBy' ||
                      item.key == 'handleBy'
                        ? projectId
                        : ''
                    "
                  />

                  <!-- 标签 -->
                  <tagSelect
                    v-else-if="item.type == 'SELECT' && item.key == 'tagId'"
                    v-model="fixdForm[item.key]"
                    multiple
                  />
                  <!-- 日期组件 -->
                  <el-date-picker
                    :shortcuts="
                      pickerOptions(item.key) &&
                      pickerOptions(item.key).shortcuts
                    "
                    :disabled-date="
                      pickerOptions(item.key) &&
                      pickerOptions(item.key).disabledDate
                    "
                    :cell-class-name="
                      pickerOptions(item.key) &&
                      pickerOptions(item.key).cellClassName
                    "
                    v-else-if="item.type == 'DATE'"
                    v-model="fixdForm[item.key]"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    default-time="9:00:00"
                    :placeholder="item.placeholder"
                  ></el-date-picker>

                  <!-- 输入框 -->
                  <el-input
                    v-else-if="item.type == 'INPUT'"
                    v-model="fixdForm[item.key]"
                    :placeholder="item.placeholder"
                    maxlength="200"
                  />
                  <!-- 输入文本框 -->
                  <el-input
                    v-else-if="item.type == 'TEXTAREA'"
                    v-model="fixdForm[item.key]"
                    type="textarea"
                    :placeholder="item.placeholder"
                  />

                  <!-- 下拉单选框 -->
                  <el-select
                    v-else-if="item.type == 'SELECT' && !item.multiple"
                    v-model="fixdForm[item.key]"
                    :placeholder="item.placeholder"
                    :disabled="item.disabled"
                    @focus="setOptionWidth"
                    @change="changeSelect(item.key, fixdForm[item.key])"
                  >
                    <el-option
                      v-for="i in item.options"
                      :key="i.id"
                      :label="i.name"
                      :value="
                        item.key == 'sourceCode' ||
                        item.key == 'typeCode' ||
                        item.key == 'priorityCode' ||
                        item.key == 'stateCode' ||
                        item.key == 'envCode'
                          ? i.code
                          : i.id
                      "
                      :style="{ width: selectOptionWidth }"
                    >
                      <span v-if="item.key == 'ideaId'">
                        {{ `${i.code}  ${i.name}` }}
                      </span>
                    </el-option>
                  </el-select>

                  <!-- 下拉多选框 -->
                  <el-select
                    v-else-if="item.type == 'SELECT' && item.multiple"
                    v-model="fixdForm[item.key]"
                    :placeholder="item.placeholder"
                    multiple
                    @focus="setOptionWidth"
                  >
                    <el-option
                      v-for="i in item.options"
                      :key="i.id"
                      :label="i.name"
                      :value="i.code"
                      :style="{ width: selectOptionWidth }"
                    />
                  </el-select>

                  <!-- 文本编辑器 -->

                  <span v-else-if="item.type == 'EDITOR'">
                    <vone-editor
                      ref="editor"
                      v-model="fixdForm[item.key]"
                      @input.native="
                        eventDisposalRangeChange(fixdForm[item.key])
                      "
                    />
                  </span>
                  <!-- 文件 -->

                  <vone-upload
                    v-else-if="item.type == 'FILE'"
                    ref="uploadFile"
                    :biz-type="fileMap[typeCode]"
                    :files-data="fixdForm.files"
                  />
                </el-form-item>
              </el-col>
            </div>
          </el-row>
        </el-form>
        <el-divider v-if="customList.length" />

        <h4 v-if="customList.length">基本属性</h4>

        <el-form
          v-if="customList.length"
          ref="form"
          :model="form"
          label-position="top"
          :rules="formRules"
        >
          <el-row type="flex" :gutter="24" class="row-box">
            <el-col v-for="item in customList" :key="item.id" :span="12">
              <el-form-item :label="item.name" :prop="item.key">
                <!-- 平台人员组件 -->
                <vone-remote-user
                  v-if="item.type == 'USER'"
                  v-model="form[item.key]"
                  :project-id="
                    item.key == 'leadingBy' ||
                    item.key == 'putBy' ||
                    item.key == 'handleBy'
                      ? projectId
                      : ''
                  "
                  :multiple="item.multiple"
                />
                <!-- 项目人员组件 -->
                <projectRemoteUser
                  v-if="item.type == 'PROJECTUSER'"
                  v-model="form[item.key]"
                  :multiple="item.multiple"
                />
                <!-- 整数 -->
                <el-input-number
                  v-else-if="item.type == 'INT'"
                  v-model="form[item.key]"
                  :min="0.1"
                  :max="1000"
                  :precision="item.precision"
                  controls-position="right"
                  style="width: 100%"
                  :placeholder="item.placeholder"
                />

                <!-- 文件 -->
                <vone-upload
                  v-else-if="item.type == 'FILE'"
                  ref="formUploadFile"
                  :files-data="form[item.key]"
                  :biz-type="fileMap[typeCode]"
                />

                <!-- 日期组件 -->
                <el-date-picker
                  :default-time="
                    `${item.defaultTime ? item.defaultTime : '00:00'}:00`.map(
                      (d) => dayjs(d, 'hh:mm:ss').toDate()
                    )
                  "
                  :shortcuts="
                    pickerOptions(item.key) && pickerOptions(item.key).shortcuts
                  "
                  :disabled-date="
                    pickerOptions(item.key) &&
                    pickerOptions(item.key).disabledDate
                  "
                  :cell-class-name="
                    pickerOptions(item.key) &&
                    pickerOptions(item.key).cellClassName
                  "
                  v-else-if="item.type == 'DATE'"
                  v-model="form[item.key]"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :placeholder="item.placeholder"
                ></el-date-picker>
                <!-- 输入框 -->
                <el-input
                  v-else-if="item.type == 'INPUT'"
                  v-model="form[item.key]"
                  :placeholder="item.placeholder"
                  maxlength="200"
                  show-word-limit
                />
                <!-- 超链接 -->
                <el-input
                  v-else-if="item.type == 'LINK'"
                  v-model="form[item.key]"
                  :placeholder="item.placeholder"
                />
                <!-- 组织机构 -->
                <vone-tree-select
                  v-else-if="item.type == 'ORG'"
                  v-model="form[item.key]"
                  search-nested
                  :tree-data="orgData"
                  placeholder="请选择机构"
                  :multiple="item.multiple"
                />
                <!-- 输入文本域 -->
                <el-input
                  v-else-if="item.type == 'TEXTAREA'"
                  v-model="form[item.key]"
                  autosize
                  type="textarea"
                  :placeholder="item.placeholder"
                />
                <template
                  v-else-if="
                    item.type == 'SELECT' &&
                    item.key != 'productModuleFunctionId'
                  "
                >
                  <!-- 远程搜索下拉 -->
                  <el-select
                    v-if="
                      item.key == 'requirementId' ||
                      item.key == 'planId' ||
                      item.key == 'projectId' ||
                      item.key == 'bugId'
                    "
                    v-model="form[item.key]"
                    remote
                    :placeholder="item.placeholder"
                    :multiple="item.multiple"
                    clearable
                    filterable
                    :disabled="!item.isUpdate"
                    :remote-method="(e) => remoteMethod(e, item.key)"
                    @focus="setOptionWidth"
                  >
                    <el-option
                      v-for="i in item.options"
                      :key="i.id"
                      :label="i.name"
                      :value="i.id"
                      :style="{ width: selectOptionWidth }"
                    >
                      {{ i.code }}
                      {{ i.name }}
                    </el-option>
                  </el-select>
                  <!-- 下拉单选框 -->
                  <el-select
                    v-else
                    v-model="form[item.key]"
                    :placeholder="item.placeholder"
                    clearable
                    filterable
                    :multiple="item.multiple"
                    :disabled="!item.isUpdate"
                    @focus="setOptionWidth"
                    @change="changeSelect"
                  >
                    <el-option
                      v-for="i in item.options"
                      :key="i.id"
                      :label="i.name"
                      :value="
                        item.key == 'sourceCode' ||
                        item.key == 'typeCode' ||
                        item.key == 'priorityCode'
                          ? i.code
                          : i.id
                      "
                      :style="{ width: selectOptionWidth }"
                    >
                      <span v-if="item.key == 'ideaId'">{{
                        `${i.code}  ${i.name}`
                      }}</span>

                      <span v-if="item.key == 'productId'">
                        {{ i.name }}
                        <!-- <span v-if="i.echoMap" style="float:right">
                            <el-tag v-if="i.echoMap.isHost" type="success">
                              主
                            </el-tag>
                            <el-tag v-if="i.echoMap.isHost == false" type="warning">
                              辅
                            </el-tag>
                          </span> -->
                      </span>
                    </el-option>
                  </el-select>
                  <!-- 下拉多选框 -->
                  <!-- <el-select v-else-if="item.type == 'SELECT' && item.multiple&&item.key!='productModuleFunctionId'" v-model="customForm[item.key]" :placeholder="item.placeholder" multiple clearable filterable :disabled="!item.isUpdate" @focus="setOptionWidth">
                                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.name" :style="{ width: selectOptionWidth }" />
                                  </el-select> -->
                </template>
                <vone-tree-select
                  v-else-if="
                    item.type == 'SELECT' &&
                    item.key == 'productModuleFunctionId'
                  "
                  v-model="form[item.key]"
                  :disabled="item.disabled"
                  search-nested
                  :tree-data="item.options"
                  placeholder="请选择"
                />
                <!-- 下拉多选框 -->
                <!-- <el-select v-else-if="item.type == 'SELECT' && item.multiple &&item.key!='productModuleFunctionId'" v-model="form[item.key]" :placeholder="item.placeholder" multiple clearable filterable>
                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.name" />
                  </el-select> -->
                <dataSelect
                  v-else-if="item.type == 'LINKED'"
                  text-info="add"
                  :model="form[item.key]"
                  :config="item"
                  :placeholder="item.placeholder"
                  @change="dataSelectChange($event, item.key)"
                />
                <div v-else-if="item.type == 'QUOTE'">
                  <!-- <vone-user-avatar v-if="item.quoteType === 'user'" :avatar-path="item.user.avatarPath" :avatar-type="true" height="22px" width="22px" :name="item.user.name" :show-name="true" /> -->
                  <vone-remote-user
                    v-if="item.quoteType === 'user'"
                    v-model="form[item.key]"
                    disabled
                  />
                  <el-input
                    v-else
                    v-model="form[item.key]"
                    disabled
                    placeholder=""
                  />
                </div>
              </el-form-item>

              <!-- </div> -->
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div slot="footer" class="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="saveInfo"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
// index.js 的相对路径
import index from './form' // 名字可以任取
export default index
</script>

<style lang="scss" scoped>
/* //index.css 的相对路径 */
@use './form.scss' as *;
</style>
