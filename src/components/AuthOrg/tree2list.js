import { forEach, isArray } from 'lodash'
const treeLoop = (tree, key = 'children', cb, parent) => {
  forEach(tree, (item) => {
    cb(item, parent)
    if (key in item && isArray(item[key])) {
      treeLoop(item[key], key, cb, item)
    }
  })
}

const tree2list = (tree, key = 'children', cb) => {
  if (typeof key === 'function') {
    cb = key
    key = 'children'
  }
  const list = []
  treeLoop(tree, key, (item, parent) => {
    typeof cb === 'function' && cb(item, parent)
    list.push(item)
  })
  return list
}

export { tree2list }
