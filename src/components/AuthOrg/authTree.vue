<template>
  <div v-loading="loading">
    <el-tree
      ref="tree"
      :data="jTreeData"
      check-strictly
      highlight-current
      default-expand-all
      show-checkbox
      node-key="id"
      :props="defaultProps"
      @check="treeCheck"
    />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import { tree2list } from './tree2list'
import concat from 'lodash/concat'
export default {
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Array,
      default: () => [],
    },
    disabledValue: {
      type: Array,
      default: () => [],
    },
    isStrictly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultProps: {
        label: 'name',
      },
      jTreeData: [],
    }
  },
  watch: {
    treeData: 'initTreeData',
    value: 'getTreeData',
  },
  methods: {
    initTreeData() {
      this.jTreeData = cloneDeep(this.treeData)
      tree2list(this.jTreeData, (item, parent) => {
        item.disabled = this.disabledValue.indexOf(item.id) > -1
        Object.defineProperties(item, {
          $pid: {
            value: parent ? parent.id : undefined,
            writable: false,
          },
          $parents: {
            value: parent ? concat(parent.$parents, parent.id) : [],
            writable: false,
          },
        })
      })
      this.getTreeData()
    },
    getTreeData() {
      this.$nextTick(() => {
        this.$emit('input', this.value)
        this.$refs.tree.setCheckedKeys(this.value)
      })
    },
    treeCheck(node, { checkedNodes, checkedKeys }) {
      const check = checkedKeys.indexOf(node.id) > -1
      const parents = node.$parents || []
      if (check) {
        parents.map((e) => {
          this.$refs.tree.setChecked(e, true)
        })
      }
      this.childNodesChange(node, check)
      this.$emit('input', this.$refs.tree.getCheckedKeys())
    },
    childNodesChange(node, check) {
      const children = node.children
      if (!this.isStrictly && check) return
      if (children && children.length > 0) {
        children.map((e) => {
          this.$refs.tree.setChecked(e.id, check)
          this.childNodesChange(e, check)
        })
      }
    },
  },
}
</script>
