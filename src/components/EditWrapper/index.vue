<template>
  <div
    class="edit-wrapper"
    :style="{ paddingBottom: showFooter ? '52px' : '' }"
  >
    <slot />
    <div v-if="showFooter" class="edit-wrapper-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'VoneEditWrapper',
  props: {
    showFooter: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-wrapper {
  height: 100%;
  .edit-wrapper-footer {
    position: fixed;
    background: var(--main-bg-color, #fff);
    bottom: 0;
    left: 72px;
    text-align: center;
    height: 64px;
    line-height: 64px;
    width: calc(100% - 72px);
    z-index: 10;
  }
}
.custom-theme-dark {
  .edit-wrapper-footer {
    background-color: #252933;
  }
}
</style>
