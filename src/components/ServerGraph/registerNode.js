import G6 from '@antv/g6'
// const colors = {
//   B: '#5B8FF9',
//   R: '#F46649',
//   Y: '#EEBC20',
//   G: '#5BD8A6',
//   DI: '#A7A7A7'
// }

const story = require('@/assets/graph/icon-story.svg')
const nginx = require('@/assets/graph/nginx-1.svg')
const centos = require('@/assets/graph/Centos.svg')
const microsoft = require('@/assets/graph/microsoft.svg')
const java = require('@/assets/graph/JVM.svg')
const weblogic = require('@/assets/graph/weblogic.svg')
const websphere = require('@/assets/graph/websphere.svg')
const linux = require('@/assets/graph/linux.svg')
const others = require('@/assets/graph/others.svg')
const mysql = require('@/assets/graph/mysql-6.svg')
const db2 = require('@/assets/graph/db2.svg')
const oracle = require('@/assets/graph/oracle.svg')
const tomcat = require('@/assets/graph/tomcat.svg')

const customNode = {
  init() {
    G6.registerNode(
      'flow-rect',
      {
        shapeType: 'flow-rect',
        draw(cfg, group) {
          const {
            name = '',
            hostOs = '',
            appComponentsType = '',
            env = {},
            depth,
          } = cfg
          // 节点配置数据
          const rectConfig = {
            width: 240,
            height: depth < 2 ? 60 : 100,
            lineWidth: 1,
            fontSize: 12,
            fill: '#fff',
            radius: 4,
            stroke: '#CED4D9',
            opacity: 1,
          }
          // 节点容器
          const rect = group.addShape('rect', {
            attrs: {
              x: -rectConfig.width / 2,
              y: -rectConfig.height / 2,
              shadowColor: 'rgba(41, 82, 166, 0.16)',
              shadowBlur: 4,
              ...rectConfig,
            },
            draggable: true,
          })
          // 获取最外层容器坐标宽高
          const rectBBox = rect.getBBox()
          if (depth > 1) {
            let img = story
            if (depth === 2) {
              img =
                hostOs === 'CentOS'
                  ? centos
                  : hostOs === 'linux'
                  ? linux
                  : hostOs === 'microsoft'
                  ? microsoft
                  : others
            } else if (depth === 3) {
              if (appComponentsType) {
                img =
                  appComponentsType.indexOf('JVM') > -1
                    ? java
                    : appComponentsType.indexOf('Nginx') > -1
                    ? nginx
                    : appComponentsType.indexOf('ApacheTomcat') > -1
                    ? tomcat
                    : appComponentsType.indexOf('WebLogic') > -1
                    ? weblogic
                    : appComponentsType.indexOf('WebSphere') > -1
                    ? websphere
                    : others
              } else {
                img =
                  name.indexOf('MySQL') > -1
                    ? mysql
                    : name.indexOf('Oracle') > -1
                    ? oracle
                    : db2
              }
            }
            // 图标
            group.addShape('image', {
              attrs: {
                x: 12 + rectBBox.x,
                y: rectBBox.y / 2 + 10,
                width: 30,
                height: 30,
                img: img,
              },
              draggable: true,
              name: 'image-shape',
            })
          }
          // 标题
          group.addShape('text', {
            attrs: {
              x: depth < 2 ? -12 : 0,
              y: depth < 2 ? 28 + rectBBox.y : 42 + rectBBox.y,
              text: name.length > 18 ? name.substr(0, 18) + '...' : name,
              fontSize: 16,
              fontWeight: 700,
              fill: '#000',
              textAlign: 'center',
              textBaseline: 'bottom',
            },
            draggable: true,
            name: 'name-shape',
          })
          // 环境标签
          if (depth === 1) {
            group.addShape('text', {
              attrs: {
                x: -12,
                y: 48 + rectBBox.y,
                textAlign: 'center',
                textBaseline: 'bottom',
                text: env.code,
                fontSize: 14,
                fill: '#8A8F99',
                opacity: 0.85,
              },
              draggable: true,
              name: 'env-shape',
            })
          }
          if (depth > 1) {
            group.addShape('text', {
              attrs: {
                x: 0,
                y: 60 + rectBBox.y,
                textAlign: 'center',
                textBaseline: 'bottom',
                text:
                  depth === 2 ? hostOs : depth === 3 ? appComponentsType : '',
                fontSize: 14,
                fill: '#8A8F99',
                opacity: 0.85,
              },
              draggable: true,
              name: 'code-shape',
            })
          }

          this.drawLinkPoints(cfg, group)
          return rect
        },
        update(cfg, item) {
          const group = item.getContainer()
          this.updateLinkPoints(cfg, group)
        },
        setState(name, value, item) {},
        getAnchorPoints() {
          return [
            [0, 0.5],
            [1, 0.5],
          ]
        },
      },
      'rect'
    )
  },
}

export default customNode
