<template>
  <div>
    <el-form
      ref="form"
      :rules="rules"
      :model="model"
      label-width="0"
      @submit="save"
    >
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item prop="name" @keydown.native="lisEsc">
            <div class="input-layout">
              <div>
                <el-input
                  ref="input"
                  :model-value="value"
                  :placeholder="placeholder"
                  @input="(v) => $emit('input', v)"
                  @keydown.native="inputKeyUp"
                />
              </div>
            </div>
          </el-form-item>
        </el-col>
        <!-- 传入组件 -->
        <slot />
      </el-row>
    </el-form>
    <div class="minbtns">
      <el-button
        class="miniBtn"
        style="margin-left: 8px"
        size="mini"
        @click="$emit('cancel')"
        >取消</el-button
      >
      <el-button
        class="miniBtn"
        style="margin-left: 8px"
        type="primary"
        size="mini"
        :loading="loading"
        @click.stop="save"
      >
        确定
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 绑定表单数据
    model: {
      type: Object,
      default: () => {},
    },
    label: {
      type: String,
      default: '',
    },
    noFile: Boolean,
    hideFull: Boolean,
    disabledShift: Boolean,
    loading: Boolean,
    // 输入框绑定值
    value: {
      type: String,
      default: '',
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    inputWidth: {
      type: [Number, String],
      default: 240,
    },
    rules: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      fileAni: false,
      // 创建标题未填时的边框样式
      showBorder: false,
    }
  },
  computed: {
    placeholder() {
      return !this.disabledShift
        ? `${this.label || ''}（按回车快速创建）`
        : `${this.label || ''}`
    },
  },
  watch: {
    value(value) {
      if (value) {
        this.showBorder = false
      }
    },
  },
  mounted() {
    this.$refs.input.focus()
  },
  methods: {
    reset() {
      // this.$refs.form.reset()
    },
    open() {
      this.$emit('open')
      this.$emit('cancel')
    },
    inputKeyUp(e) {
      if (!this.disabledShift) {
        if (e.keyCode === 13 && e.shiftKey) {
          e.preventDefault()
          this.open()
        }
        if (e.keyCode === 13) {
          e.preventDefault()
          this.save()
        }
      }
    },
    lisEsc(e) {
      if (e.keyCode === 27) {
        this.$emit('cancel')
      }
    },
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (error) {
        return
      }
      if (!this.value) {
        this.showBorder = true
        return
      } else {
        this.showBorder = false
      }
      this.$emit('submit')
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-button + .el-button) {
  margin: 0px;
}
.minbtns {
  text-align: right;
  :deep(.el-button + .el-button) {
    margin: -16px 0px 12px 12px;
  }
}
</style>
