<template>
  <div
    :class="['list-view', currentAppMenu.length > 0 ? 'has-top-menu' : '']"
    :style="viewStyle"
  >
    <div v-if="$slots.toolbar" class="card-toolbar-wrapper">
      <slot name="toolbar" />
    </div>
    <template v-if="groupView">
      <vone-empty v-if="data && data.length === 0" />
      <div v-else class="card-list">
        <el-row v-for="(list, key) in data" :key="key" :gutter="gutter">
          <template>
            <el-col
              v-for="(row, $index) in list"
              :key="$index"
              :style="{ 'margin-bottom': '16px', width: `${100 / rowCount}%` }"
              vone-cards-item
              v-bind="{ index: $index, row }"
            >
              <slot v-bind="{ $index, row }" />
            </el-col>
          </template>
        </el-row>
      </div>
    </template>
    <template v-else>
      <vone-empty v-if="data.records && data.records.length === 0" />
      <div v-else class="card-list">
        <el-row :class="{ hidePage: hidePage }" :gutter="gutter">
          <template>
            <el-col
              v-for="(row, $index) in data.records"
              :key="$index"
              :style="{
                'margin-bottom': $index < lastNums ? '16px' : '0px',
                width: `${100 / rowCount}%`,
              }"
              vone-cards-item
              v-bind="{ index: $index, row }"
            >
              <slot v-bind="{ $index, row }" />
            </el-col>
          </template>
        </el-row>
      </div>
    </template>
    <div
      v-if="data.records && data.records.length > 0 && !hidePage"
      class="pagination"
    >
      <el-pagination
        :total="(data.total && data.total * 1) || data.total"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="->,total, sizes, prev, pager, next, jumper"
        :page-sizes="pageSizesConfig || pageSizes"
        :pager-count="5"
        @size-change="paginationChange"
        @current-change="paginationChange"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    height: {
      type: [String, Number],
      default: '',
    },
    data: {
      default: () => {},
      type: Object,
    },
    gutter: {
      type: Number,
      default: 16,
    },
    rowCount: {
      type: Number,
      default: 3,
    },
    hidePage: {
      type: Boolean,
      default: false,
    },
    isSpecial: {
      type: Boolean,
      default: false,
    },
    pageSizesConfig: {
      type: Array,
      default: () => null,
    },
    groupView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentPage: 1,
      pageSize: this.isSpecial ? 9 : 12,
      pageSizes: this.isSpecial ? [9, 18, 45, 90] : [12, 32, 64, 96],
    }
  },
  computed: {
    // 计算最后一行的个数
    lastNums() {
      const len = this.data?.records?.length
      const remainder = len % this.rowCount
      return remainder === 0 ? len - this.rowCount : len - remainder
    },
    viewStyle() {
      const style = {}
      if (this.height) {
        if (typeof this.height === 'string') {
          style.height = this.height
        } else {
          style.height = this.height + 'px'
        }
      }
      return style
    },
    ...mapGetters(['currentAppMenu']),
  },
  methods: {
    // 暴露出去pageData order
    exportTableQueryData() {
      return {
        sort: 'createTime',
        order: 'descending',
        current: this.currentPage,
        size: this.pageSize,
      }
    },
    paginationChange() {
      this.$emit('updateData')
    },
  },
}
</script>

<style lang="scss" scoped>
.empty {
  height: calc(100% - 86px) !important;
}
.card-toolbar-wrapper {
  display: flex;
  width: 100%;
  border-radius: 6px;
  align-items: center;
  margin-bottom: 16px;
  background-color: var(--main-bg-color, #fff);
}

.list-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  // padding: 5px 5px 12px 5px;

  overflow-y: hidden;
  overflow-x: hidden;
  // position: relative;
  .pagination {
    padding-top: 10px;
    position: absolute;
    bottom: 16px;
    right: 16px;
    background-color: #fff;
  }

  .card-list {
    overflow-y: auto;
    overflow-x: hidden;
    // height: 100%;
    flex-grow: 1;
    // &::-webkit-scrollbar {
    //   margin-left: -8px;
    // }
  }

  .hidePage {
    height: calc(100% - 72px);
  }
}
.has-top-menu {
  height: calc(100vh - 154px);
}
</style>
