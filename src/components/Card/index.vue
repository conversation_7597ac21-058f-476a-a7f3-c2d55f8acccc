<template>
  <el-card class="vone-card" shadow="hover" @click="clickCard">
    <slot slot="header" name="header" />

    <el-row type="flex" align="middle">
      <el-row v-if="$slots.icon" class="vone-card__icon">
        <slot name="icon" />
      </el-row>
      <el-tooltip
        :show-after="1000"
        v-if="$slots.title && titleContent"
        effect="dark"
        :content="titleContent"
        placement="top-start"
      >
        <div class="vone-card__title">
          <slot name="title" />
          <slot name="focus" />
          <slot name="title_1" />
        </div>

        <div class="vone-card__tags">
          <slot name="tagboxs" />
        </div>
      </el-tooltip>

      <div v-else-if="$slots.title" class="vone-card__title">
        <slot name="title" />
        <slot name="focus" />
        <slot class="title2" name="title_1" />
      </div>
      <div v-else-if="title" class="vone-card__title">{{ title }}</div>
      <slot name="tags" />
      <div class="vone-card__tags">
        <slot name="tagboxs" />
      </div>
    </el-row>
    <slot />
    <el-row
      v-if="filtedActions.length"
      type="flex"
      justify="space-between"
      align="middle"
      class="vone-card__footer"
      @click.stop
    >
      <div class="desc">
        <slot name="desc" />
      </div>
      <el-row
        v-if="showMenu"
        type="flex"
        justify="end"
        align="middle"
        class="vone-card__actions"
      >
        <div
          v-for="(action, index) in showedActions"
          :key="index"
          class="vone-card__actions_item"
        >
          <el-tooltip :content="action.text" placement="top">
            <el-button
              :key="action.text"
              class="actionBtn"
              :type="action.type"
              :loading="isLoading(action)"
              :disabled="isDisabled(action)"
              @click="actionOnClick(action)"
            >
              <div style="display: flex; align-items: center">
                <svg-icon v-if="action.svg" :icon-class="action.svg" />
                <i
                  v-else-if="action.icon.indexOf('el') === -1"
                  class="iconfont"
                  :class="action.icon"
                />
                <i
                  v-else-if="action.icon"
                  class="iconfont"
                  :class="action.icon"
                />
                <span v-else>{{ action.text }}</span>
              </div>
            </el-button>
          </el-tooltip>
          <el-divider
            v-if="index < showedActions.length - 1"
            :key="index"
            class="divider"
            direction="vertical"
          />
        </div>
        <el-divider
          v-if="dropdownActions"
          class="divider"
          direction="vertical"
        />
        <el-dropdown
          v-if="dropdownActions"
          trigger="click"
          @command="(e) => e && e()"
        >
          <el-tooltip content="操作" placement="top">
            <el-button :icon="ElIconMore" class="actionBtn" />
          </el-tooltip>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(action, index) in dropdownActions"
              :key="index"
              :command="() => actionOnClick(action)"
              :disabled="isDisabled(action)"
            >
              <svg-icon
                v-if="action.svg"
                :icon-class="action.svg"
                style="margin-right: 5px"
              />
              <i
                v-else-if="action.icon.indexOf('el') === -1"
                class="iconfont"
                :class="action.icon"
              />
              <i v-else-if="action.icon" :class="action.icon" />
              {{ action.text }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-row>
    </el-row>
  </el-card>
</template>

<script>
import { More as ElIconMore } from '@element-plus/icons-vue'
// 最多显示个数
const MAX_ACTIONS_NUM = 3

export default {
  data() {
    return {
      ElIconMore,
    }
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    actions: {
      type: Array,
      default: () => [],
    },
    titleContent: {
      type: String,
      default: '',
    },
    // 右下角操作显示最大数
    actionsNum: {
      type: Number,
      default: 3,
    },
    splitMenu: {
      type: Boolean,
      default: false,
    },
    propRow: {
      type: Object,
      default: () => ({}),
    },
    showMenu: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    owner() {
      let parent = this.$parent
      while (parent && !('vone-cards-item' in parent.$attrs)) {
        parent = parent.$parent
      }
      return parent
    },
    rowData() {
      if (!this.owner) return { row: this.propRow }
      const { index, row } = this.owner.$attrs
      return { row, $index: index }
    },
    filtedActions() {
      return this.actions.filter(({ hidden }) => {
        if (typeof hidden === 'function') return !hidden(this.rowData)
        return !hidden
      })
    },
    showedActions() {
      const nums = this.actionsNum || MAX_ACTIONS_NUM
      return this.filtedActions.length > nums
        ? this.filtedActions.slice(0, nums)
        : this.filtedActions
    },
    dropdownActions() {
      const nums = this.actionsNum || MAX_ACTIONS_NUM
      return this.filtedActions.length > nums
        ? this.filtedActions.slice(nums)
        : null
    },
  },
  methods: {
    clickCard(e) {
      this.$emit('click', e)
    },
    actionOnClick({ onClick }) {
      // if (this.owner) {
      onClick(this.rowData)
      // } else {
      //   onClick()
      // }
    },
    isDisabled({ disabled }) {
      if (typeof disabled === 'function') {
        disabled = disabled(this.rowData)
      }
      return !!disabled
    },
    isLoading({ loading }) {
      if (typeof loading === 'function') {
        loading = loading(this.rowData)
      }
      return !!loading
    },
  },
}
</script>

<style lang="scss" scoped>
.vone-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
    .vone-card__icon {
      transition: all 0.3s;
      transform: scale(1.15);
      img {
        width: 32px;
      }
    }
    .vone-card__title {
      transition: all 0.3s;
      transform: scale(1.03);
    }
  }
  &__ {
    &has-click {
      cursor: pointer;
    }
    &title {
      flex: 1;
      min-width: 100px;
      :deep(.title-box) {
        width: 100%;
        overflow: hidden;
        .title {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: var(--font-main-color);
          font-weight: 500;
        }
      }
    }
    &tags {
      position: absolute;
      right: 0px;
    }
    &icon {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
      text-align: center;
      width: 32px;
      height: 32px;
      margin-right: 8px;
      &,
      & :deep(i) {
        font-size: 32px;
        line-height: 50px;
      }
    }
    &footer {
      margin: 16px -16px -16px;
      border-top: 1px solid var(--solid-border-color);
      height: 44px;
      padding: 10px 16px;
    }
    &actions {
      :deep() {
        .el-button,
        .el-dropdown,
        .el-dropdown .el-button {
          border: none;
          background: none;
          padding-left: 0;
          padding-right: 0;
          min-width: unset;
          color: var(--font-second-color);
          &:hover {
            background: none;
          }
        }
        .actionBtn {
          display: flex;
          align-items: center;
          &:hover {
            color: #3e7bfa;
          }
        }
      }
    }
  }
}
.vone-card__actions {
  display: flex;
  align-items: center;
  .vone-card__actions_item {
    display: flex;
    align-items: center;
  }
  .svg-icon {
    width: 16px;
    height: 16px;
  }
  .divider {
    margin: 0 8px;
    background-color: var(--solid-border-color);
  }
}
</style>
