<template>
  <header>
    <div class="toolbar">
      <div class="toolbar_search">
        <slot name="custom" />
        <template>
          <slot name="search" />
        </template>
        <template>
          <slot name="moreSearch" />
        </template>
        <template>
          <slot name="tools" />
        </template>
      </div>
      <div class="toolbar_actions">
        <slot name="actions" />
      </div>
    </div>
    <div v-if="$slots.fliter" class="toolbar_filter">
      <slot name="fliter" />
    </div>
  </header>
</template>

<script>
export default {
  name: 'SearchWrapper',

  mounted() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  margin: -16px -16px 10px -16px;
  padding: 0 16px;
  border-bottom: 1px solid #f2f3f5;
  .toolbar_search {
    display: flex;
    align-items: center;
    flex: 1;
  }
}
.toolbar_actions {
  display: flex;
}
// .toolbar_filter{
//   display: flex;
//   margin-bottom: 10px;
// }
</style>
