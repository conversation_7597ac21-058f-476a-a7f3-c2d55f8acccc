<template>
  <section class="bulletin-board-container">
    <aside>
      <header>{{ leftTitle }}</header>
      <div class="container">
        <slot name="aside" />
      </div>
    </aside>

    <div class="main">
      <draggable
        v-model="localList"
        class="container"
        ghost-class="dragging-class"
        :disabled="!isfirstDraggable"
        @change="handleFirstSort"
      >
        <lane
          v-for="(first, index) in localList"
          :key="first.id"
          :lane-index="index"
          :is-show-first-toolbar="isShowFirstToolbar"
          :first="first"
          :first-create-command-title="firstCreateCommandTitle"
          :second-dialog-title="secondDialogTitle"
          style="flex: none"
          @create-second="$emit('create-second', $event)"
          @drop-item="$emit('drop-item', $event)"
          @remove-first="list.splice(index, 1)"
          @remove-second="$emit('remove-second', $event)"
          @remove-item="$emit('remove-item', $event)"
          @change-first-name="$emit('change-first-name', $event)"
          @change-second-name="$emit('change-second-name', $event)"
          @change-second-sort="$emit('change-second-sort', $event)"
        >
          <slot name="second-dialog" />
        </lane>
      </draggable>
    </div>

    <!-- 一级分类 -->
    <el-dialog
      v-model="firstDialogVisible"
      :title="firstDialogTitle"
      :close-on-click-modal="false"
      width="30%"
    >
      <slot name="first-dialog" />
      <div slot="footer">
        <el-button @click="firstDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleFirstSure">确定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import draggable from 'vuedraggable'
import lane from './lane.vue'

export default {
  components: { draggable, lane },
  props: {
    /**
     * 左区域标题
     */
    leftTitle: {
      type: String,
      default: '',
    },
    /**
     * 右区域标题
     */
    rightTitle: {
      type: String,
      default: '',
    },
    /**
     * 是否显示一级分类工具栏
     */
    isShowFirstToolbar: {
      type: Boolean,
      default: true,
    },
    /**
     * 一级分类是否可以拖拽
     */
    isfirstDraggable: {
      type: Boolean,
      default: true,
    },
    /**
     * 一级分类菜单新增标题
     */
    firstCreateCommandTitle: {
      type: String,
      default: '新增',
    },
    /**
     * 一级分类弹窗标题
     */
    firstDialogTitle: {
      type: String,
      default: '新增',
    },
    /**
     * 二级分类弹窗标题
     */
    secondDialogTitle: {
      type: String,
      default: '新增',
    },
    /**
     * 集合数据
     * [{id,name,children}]
     */
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      firstDialogVisible: false,
      localList: [],
    }
  },
  watch: {
    list: {
      handler(newVal) {
        this.localList = [...newVal]
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleFirstSort() {
      this.$emit('change-first-sort', this.localList)
      this.$emit('update:list', this.localList)
    },
    handleFirstSure() {
      const { list } = this
      this.$emit('create-first', (param) => {
        if ('id' in param && 'name' in param) {
          // 构建数据结构
          param.children = param.children || []
          param.isRename = false
          list.push(param)
          this.firstDialogVisible = false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.bulletin-board-container {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  > aside,
  > .main {
    > header {
      flex: 0 0 40px;
      display: flex;
      align-items: center;
      padding-left: 16px;
      border-bottom: 1px solid #ebeef5;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      color: #202124;
    }
  }
  > aside {
    width: 250px;
    height: 100%;
    border-right: 1px solid #ebeef5;
    display: flex;
    flex-direction: column;
    > .container {
      flex: 1;
      overflow-y: auto;
    }
  }
  > .main {
    flex: 1;
    overflow: auto;
    height: 100%;
    > .container {
      width: 100%;
      height: 100%;
      display: flex;
      padding: 16px;
      column-gap: 16px;
    }
  }
  .dragging-class {
    border: 2px solid #3e7bfa;
    box-shadow: 0px 4px 16px rgba(41, 82, 166, 0.16);
    border-radius: 2px;
  }
}
</style>
