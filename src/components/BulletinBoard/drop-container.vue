<template>
  <div
    class="bulletin-board-drop-container"
    @dragover.prevent
    @drop.prevent="handleDropItem($event, category)"
  >
    <div
      v-if="category.children.length === 0"
      style="color: rgb(200, 200, 200); font-size: 12rem; text-align: center"
    >
      请拖拽至此
    </div>

    <div
      v-for="(item, index) in category.children.filter((item) => item.visible)"
      v-else
      :key="item.id"
      class="item"
    >
      <span>
        <i
          :class="item.icon"
          :style="{ color: item.iconColor }"
          style="font-size: 12rem"
        />
        {{ item.name }}
        <el-icon class="iconfont"><el-icon-icon-shanchu /></el-icon>
      </span>
    </div>
  </div>
</template>

<script>
import { IconShanchu as ElIconIconShanchu } from '@element-plus/icons-vue'
export default {
  components: {
    ElIconIconShanchu,
  },
  props: {
    /**
     * 操作的分类：second
     */
    category: {
      type: Object,
      default: null,
    },
  },
  methods: {
    handleDropItem(e, category) {
      this.$emit('drop-item', {
        e,
        callback: (param) => {
          if ('id' in param && 'name' in param) {
            category.children.push(param)
          } else {
            this.$message.error('必须包含id、name')
          }
        },
      })
    },
    remove(index, item) {
      this.$confirm(`确定删除【${item.name}】？`, '删除', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }).then(() => {
        this.$emit('remove-item', {
          item,
          callback: () => {
            this.category.children.splice(index, 1)
          },
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.bulletin-board-drop-container {
  padding: 10px 16px;
  background: #fff;
  .item {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0px;
    }
    height: 32px !important;
    line-height: 32px !important;
    span {
      display: inline-block;
      padding: 0px 8px;
      background: #f5f6fa;
      border-radius: 2px;
      i {
        color: #8a8f99;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }
}
</style>
