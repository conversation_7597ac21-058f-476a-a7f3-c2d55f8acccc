// CustomInfo 组件的逻辑文件
import { apiAlmGetInfo } from '@/api/vone/project/issue'
import { getUser } from '@/utils/auth'
import storage from 'store'
import dayjs from 'dayjs'

export default {
  name: 'CustomInfo',
  props: {
    // 组件属性定义
    issueId: {
      type: [String, Number],
      default: null
    },
    issueType: {
      type: String,
      default: ''
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    // 其他可能的属性
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 组件数据
      issueInfo: {},
      loading: false,
      user: getUser(),
      dayjs
    }
  },
  computed: {
    // 计算属性
    currentProject() {
      return storage.get('currentProject') || {}
    }
  },
  watch: {
    // 监听器
    issueId: {
      handler(newVal) {
        if (newVal) {
          this.loadIssueInfo()
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 组件挂载后的逻辑
    this.init()
  },
  methods: {
    // 组件方法
    init() {
      if (this.issueId) {
        this.loadIssueInfo()
      }
    },
    
    async loadIssueInfo() {
      if (!this.issueId) return
      
      this.loading = true
      try {
        const response = await apiAlmGetInfo(this.issueId)
        if (response.code === 200) {
          this.issueInfo = response.data || {}
        }
      } catch (error) {
        console.error('加载问题信息失败:', error)
        this.$message.error('加载问题信息失败')
      } finally {
        this.loading = false
      }
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    
    // 其他可能需要的方法
    refresh() {
      this.loadIssueInfo()
    }
  }
}
