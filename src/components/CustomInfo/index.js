// CustomInfo 组件的逻辑文件
import { apiAlmGetInfo } from '@/api/vone/project/issue'
import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject
} from '@/api/vone/base/customForm'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { getSourceById } from '@/api/vone/base/source'

import dataSelect from '@/components/CustomEdit/commonTab/components/data-select.vue'
// 处理-连接拼接成驼峰命名
function toHump(str) {
  const reg = /-(\w)/g
  return str.replace(reg, function ($0, $1) {
    return $1.toUpperCase()
  })
}
// 处理首字母大写
function upperFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
// 读取当前文件夹下components文件夹下.vue文件
const requireComponents = import.meta.glob('./commonTab/components/*.vue', { eager: true })
const componentsObj = {}
Object.entries(requireComponents).forEach(([path, module]) => {
  const fileName = path.split('/').pop().replace(/\.vue$/, '')
  const componentName = upperFirst(toHump(fileName))
  componentsObj[componentName] = module.default || module
})

import activeTab from '../CustomEdit/commonTab/active.vue'
import _ from 'lodash'
import workTime from '../CustomEdit/commonTab/work-time.vue'
import activityRecord from '../CustomEdit/commonTab/activity-record.vue'

export default {
  name: 'CustomInfo',
  props: {
    // 组件属性定义
    issueId: {
      type: [String, Number],
      default: null
    },
    issueType: {
      type: String,
      default: ''
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    // 其他可能的属性
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 组件数据
      issueInfo: {},
      loading: false,
      user: getUser(),
      dayjs
    }
  },
  computed: {
    // 计算属性
    currentProject() {
      return storage.get('currentProject') || {}
    }
  },
  watch: {
    // 监听器
    issueId: {
      handler(newVal) {
        if (newVal) {
          this.loadIssueInfo()
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 组件挂载后的逻辑
    this.init()
  },
  methods: {
    // 组件方法
    init() {
      if (this.issueId) {
        this.loadIssueInfo()
      }
    },
    
    async loadIssueInfo() {
      if (!this.issueId) return
      
      this.loading = true
      try {
        const response = await apiAlmGetInfo(this.issueId)
        if (response.code === 200) {
          this.issueInfo = response.data || {}
        }
      } catch (error) {
        console.error('加载问题信息失败:', error)
        this.$message.error('加载问题信息失败')
      } finally {
        this.loading = false
      }
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    
    // 其他可能需要的方法
    refresh() {
      this.loadIssueInfo()
    }
  }
}
