<template>
  <div class="virtual-tree-placeholder">
    <el-alert
      title="VirtualTree 组件暂时不可用"
      type="warning"
      description="该组件在Vue3升级过程中遇到兼容性问题，已暂时禁用。"
      show-icon
      :closable="false"
    />
    <p class="mt-20">
      <strong>建议：</strong>可以使用Element Plus的Tree组件作为替代方案。
    </p>
  </div>
</template>

<script>
export default {
  name: 'VirtualTree',
}
</script>

<style scoped>
.virtual-tree-placeholder {
  padding: 20px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  min-height: 200px;
}
.mt-20 {
  margin-top: 20px;
}
</style>
