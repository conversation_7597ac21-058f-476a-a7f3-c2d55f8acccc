<template>
  <div v-loading="loading" class="active-style">
    <follow
      :source-id="sourceId"
      :source-type="sourceType"
      :project-id="projectId"
      :view-type="viewType"
    />
    <div v-if="data.length > 0">
      <div v-for="item in data" :key="item.id" class="activeStl">
        <ul class="list">
          <li>
            <span v-if="item.echoMap.createBy">
              <vone-user-avatar
                :avatar-path="item.echoMap.createBy.avatarPath"
              />
            </span>
            <span class="itemTitle text-over">
              <span v-if="item.echoMap && item.echoMap.createBy">
                {{ item.echoMap.createBy.name }}
              </span>
              <span v-else>
                <svg
                  class="icon svg-icon"
                  aria-hidden="true"
                  style="font-size: 26px"
                >
                  <use xlink:href="#el-icon-vone-setting-active" />
                </svg>

                系统流转
              </span>

              <span class="desc">在{{ item.day }}前</span></span
            >
          </li>
        </ul>
        <div
          style="
            margin-left: 32px;
            line-height: 22px;
            color: var(--font-main-color);
          "
        >
          <span>
            <!-- <span v-if="item.echoMap && item.echoMap.createBy">
              {{ item.echoMap.createBy.name }}
            </span>
            <span v-else>
              系统流转
            </span> -->
            将状态变更为</span
          >

          <el-tag
            v-if="item.echoMap && item.echoMap.targetStateCode"
            class="status"
            type="danger"
          >
            {{ item.echoMap.targetStateCode.name }}
          </el-tag>
        </div>
      </div>
    </div>

    <vone-empty v-else />
  </div>
</template>

<script>
import follow from './follow.vue'
import {
  findRequirementByIdCode,
  findTaskByIdCode,
  findBugByIdCode,
  findIdeaByIdCode,
} from '@/api/vone/project/issue'
import dayjs from 'dayjs'
export default {
  name: 'Active',
  components: {
    follow,
  },
  props: {
    sourceId: {
      type: String,
      default: '',
    },
    sourceType: {
      type: String,
      default: '',
    },
    viewType: {
      // 判断是详情页面
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: undefined,
    },
    formId: {
      type: String,
      default: undefined,
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      data: [],
      loading: false,
    }
  },
  mounted() {
    if (this.type == 'ISSUE') {
      this.getIssue() // 需求
    } else if (this.type == 'BUG') {
      this.getDefect()
    } else if (this.type == 'TASK') {
      this.getTask()
    } else if (this.type == 'IDEA') {
      this.getIdea()
    }

    // this.getCurrentTime()
  },
  methods: {
    async getIdea() {
      this.loading = true
      await findIdeaByIdCode(this.formId).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.data.forEach((item) => {
            item.day = dayjs(item.createTime).fromNow(true)
          })
          this.loading = false
        }
      })
    },
    async getIssue() {
      this.loading = true
      await findRequirementByIdCode(this.formId).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.data.forEach((item) => {
            item.day = dayjs(item.createTime).fromNow(true)
          })
          this.loading = false
        }
      })
    },
    async getDefect() {
      this.loading = true
      await findBugByIdCode(this.formId).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.data.forEach((item) => {
            item.day = dayjs(item.createTime).fromNow(true)
          })
        }
        this.loading = false
      })
    },
    async getTask() {
      this.loading = true
      await findTaskByIdCode(this.formId).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.data.forEach((item) => {
            item.day = dayjs(item.createTime).fromNow(true)
          })
          this.loading = false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.active-style {
  height: calc(90vh - 235px - 48px);
  overflow-y: auto;
}
.activeStl {
  padding: 16px;
  .list {
    margin-right: 4px;
    li {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      height: 34px;
      width: 100%;
      cursor: pointer;
      i {
        color: #333;
        margin-right: 8px;
      }
      .itemTitle {
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        color: #000;
        line-height: 18px;
        max-width: calc(100% - 105px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .error {
        color: #ea6362;
        background-color: #ffeded;
      }
    }

    &::-webkit-scrollbar {
      width: 6px;
    }
  }
  .status {
    margin-left: 4px;
  }
}
.desc {
  font-size: 12px;
  color: var(--font-second-color);
  margin-left: 12px;
}
</style>
