<template>
  <div>
    <el-table
      v-show="tableData.length != 0"
      ref="tableData"
      class="vone-table voneNobg-table"
      :data="tableData"
    >
      <template>
        <el-table-column
          prop="code"
          label="名称"
          show-overflow-tooltip
          min-width="250"
          fixed="left"
          class-name="name_col"
        >
          <template slot-scope="scope">
            <a class="table_title" @click="toRouter(scope.row)">
              <span
                v-if="
                  tableData[scope.$index].echoMap &&
                  tableData[scope.$index].echoMap.typeCode
                "
              >
                <i
                  :class="`iconfont ${
                    tableData[scope.$index].echoMap.typeCode.icon
                  }`"
                  :style="{
                    color: `${
                      tableData[scope.$index].echoMap.typeCode
                        ? tableData[scope.$index].echoMap.typeCode.color
                        : '#ccc'
                    }`,
                  }"
                />
              </span>
              {{ scope.row.code }} - {{ scope.row.name }}
            </a>
          </template>
        </el-table-column>

        <el-table-column
          prop="handleBy"
          label="处理人"
          show-overflow-tooltip
          width="100"
        >
          <template slot-scope="scope">
            <span>
              <vone-user-avatar
                :avatar-path="
                  getUserInfo(scope.row)
                    ? getUserInfo(scope.row).avatarPath
                    : ''
                "
                :name="
                  getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                "
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="stateCode"
          label="状态"
          show-overflow-tooltip
          width="100"
        >
          <template slot-scope="scope">
            <el-tag
              v-if="tableData[scope.$index].echoMap.stateCode"
              type="success"
            >
              {{ tableData[scope.$index].echoMap.stateCode.name }}
            </el-tag>

            <el-tag
              v-else-if="tableData[scope.$index].echoMap.state"
              type="success"
            >
              {{ tableData[scope.$index].echoMap.state.name }}
            </el-tag>

            <el-tag v-else type="success">
              {{ scope.row.stateCode }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="planEtime"
          label="计划完成时间"
          show-overflow-tooltip
          width="170"
        >
          <template slot-scope="scope">
            {{ scope.row.planEtime || '未设置' }}
          </template>
        </el-table-column>

        <el-table-column v-if="!isParent" fixed="right" label="操作" width="80">
          <template slot-scope="scope">
            <div style="display: flex">
              <el-button
                style="padding: 0 10px; min-width: 20px"
                type="text"
                :disabled="
                  scope.row.stateCode == 'DONE' ||
                  !$permission('project_issue_edit')
                "
                :icon="ElIconIconfont elIconApplicationEdit"
                @click="gotoEditI(scope.row)"
              />
              <el-divider direction="vertical" />
              <el-button
                style="padding: 0 10px; min-width: 20px"
                type="text"
                :icon="ElIconIconfont elIconIconLineJiechuguanlian"
                @click="deleteRelation(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <vone-empty v-show="tableData.length == 0" />
  </div>
</template>

<script>
import { apiAlmDeleteDepend } from '@/api/vone/project/issue'

export default {
  name: 'NeedTable',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    projectId: {
      type: String,
      default: '',
    },
    issueId: {
      type: String,
      default: '',
    },
    isParent: {
      // 判断是否为父需求，父需求没有解除关联关系的按钮
      type: Boolean,
      default: false,
    },
    typeCode: {
      type: String,
      default: '',
    },
    allProjectId: {
      // 所有项目
      type: Array,
      default: () => [],
    },
    relationType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    },
  },
  methods: {
    // 跳转至项目
    toProjectIssue(val, type) {
      console.log(this.typeCode)
      // 判断有没有项目权限,没有就去需求中心
      if (val.projectId && !this.allProjectId.includes(val.projectId)) {
        // this.$confirm('当前登陆账号没有该项目的查看权限,将跳转至需求中心, 是否继续?', '提示', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
        //   this.toRequireCenter(val)
        // }).catch(() => {

        // })
        this.$message.warning(
          '当前登陆账号没有该项目的查看权限,请联系项目经理授权'
        )
        return
      }
      const types = {
        ISSUE: 'issue',
        BUG: 'defect',
      }
      const newpage = this.$router.resolve({
        path: `/project/${types[this.typeCode]}/${val.echoMap.projectId.code}/${
          val.echoMap.projectId.code
        }/${val.projectId}`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId,
          type: type,
        },
      })
      window.open(newpage.href, '_blank')
    },
    // 跳转至需求中心
    toRequireCenter(val) {
      const newpage = this.$router.resolve({
        path: `/reqmcenter/require/requireList`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId,
        },
      })
      window.open(newpage.href, '_blank')
    },
    // 跳转至项目下缺陷
    toProjectBug(val) {
      const newpage = this.$router.resolve({
        path: `/project/defect/${val.echoMap.projectId.code}/${val.echoMap.projectId.code}/${val.projectId}`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId,
        },
      })
      window.open(newpage.href, '_blank')
    },
    gotoEditI(val) {
      this.toProjectIssue(val, 'edit')
    },
    toRouter(val) {
      // 关联意向.跳转到需求中心下意向
      if (this.typeCode == 'IDEA') {
        if (!this.$permission('reqm_center_idea_view')) {
          this.$message.warning(
            '当前登录账户没有查看【需求中心】-【用户需求】的权限,请联系管理员授权'
          )
          return
        }
        const newpage = this.$router.resolve({
          path: `/reqmcenter/idea`,
          query: {
            showDialog: true,
            queryId: val.id,
            rowTypeCode: val.typeCode,
            stateCode: val.stateCode,
          },
        })
        window.open(newpage.href, '_blank')
        return
      }
      // 关联需求,跳转到项目下需求或者需求中心下需求
      if (this.typeCode == 'ISSUE') {
        const activeApp = this.$route.meta.activeApp
        if (activeApp == 'project') {
          this.toProjectIssue(val, 'info')
        } else {
          this.toRequireCenter(val)
        }
        return
      }
      // 关联需求,跳转到项目下需求或者需求中心下需求
      if (this.typeCode == 'BUG') {
        // 1.在项目下,如果当前登陆账号有该条缺陷的所属项目的权限,就跳转到项目下,如果没有该项目的权限,就提示用户

        if (val.projectId && this.allProjectId.includes(val.projectId)) {
          this.toProjectBug(val)
          return
        }

        if (val.projectId && !this.allProjectId.includes(val.projectId)) {
          this.$message.warning(
            '当前登录账户没有查看该项目权限的权限,请联系管理员授权'
          )
          return
        }
      }
    },
    // 删除依赖关系
    async deleteRelation(row) {
      this.$confirm(
        `你确定要取消关联 【${row.name}】的关联关系吗?`,
        '取消关联',
        {
          confirmButtonText: '确认',
          type: 'warning',
          closeOnClickModal: false,
        }
      )
        .then(async () => {
          const { isSuccess, msg } = await apiAlmDeleteDepend(row.relationId)
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)

          this.$emit('success')
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.i-text {
  color: var(--main-theme-color);
  cursor: pointer;
}
.formBox {
  padding: 10px;
  // height: 300px;
  // overflow-y: auto;
  // overflow-x: hidden;
  :deep(.el-form-item__label) {
    color: var(--auxiliary-font-color);
  }
  :deep(.el-input--small) {
    font-size: 14px;
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    color: #000 !important;
    font-size: 14px;
    margin-left: 10px;
  }
}
.noSetting {
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.contentText {
  font-weight: bold;
  font-size: 16px;
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
