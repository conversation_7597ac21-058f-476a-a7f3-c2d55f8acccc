<template>
  <div class="bug-tab-placeholder">
    <el-alert
      title="Bug Tab 组件暂时不可用"
      type="warning"
      description="该组件在Vue3升级过程中遇到兼容性问题，已暂时禁用。请联系开发人员进行修复。"
      show-icon
      :closable="false"
    />
    <p class="mt-20">
      <strong>问题描述：</strong
      >该组件使用了Vue2特有的语法，在Vue3中无法正常编译。
    </p>
    <p><strong>解决方案：</strong>需要将组件重构为Vue3兼容的语法。</p>
  </div>
</template>

<script>
export default {
  name: 'BugTab',
  props: {
    issueId: {
      type: [String, Number],
      default: null,
    },
  },
}
</script>

<style scoped>
.bug-tab-placeholder {
  padding: 20px;
}
.mt-20 {
  margin-top: 20px;
}
</style>
