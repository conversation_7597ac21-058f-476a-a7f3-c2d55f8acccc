<template>
  <!-- 风险关联- -->
  <vone-div-wrapper :title="tabName" class="boxContent">
    <div slot="actions">
      <el-button
        type="text"
        class="con-idea"
        :icon="el-icon-setting"
        @click="addRelation = true"
        >关联</el-button
      >
    </div>
    <div v-loading="pageLoading">
      <div v-if="addRelation" class="shadow_bottom">
        <div class="row_host">
          <el-select
            v-model.trim="issueTab.relationIssue"
            :placeholder="placeholder"
            clearable
            filterable
            remote
            :remote-method="getIdeaList"
            :loading="requireLoading"
            class="requireSelect"
            multiple
            style="width: 100%"
            @focus="setOptionWidth"
          >
            <el-option
              v-for="ele in ideaList"
              :key="ele.id"
              :value="ele.id"
              :label="ele.name"
              :disabled="ele.disabled"
              :style="{ width: selectOptionWidth }"
              :title="ele.name"
            >
              {{ `${ele.code}   ${ele.name}` }}
            </el-option>
          </el-select>

          <div style="text-align: right">
            <el-button class="miniBtn" @click="addRelation = !addRelation"
              >取消</el-button
            >
            <el-button type="primary" class="miniBtn" @click="addRelationShip"
              >确定</el-button
            >
          </div>
        </div>
      </div>
      <section v-if="tableData.length != 0">
        <el-table
          v-show="tableData.length != 0"
          ref="riskRelation"
          class="vone-table voneNobg-table"
          :data="tableData"
        >
          <template>
            <el-table-column
              prop="code"
              label="标题"
              show-overflow-tooltip
              min-width="250"
              fixed
              class-name="name_col"
            >
              <template slot-scope="scope">
                <a @click="toRouter(scope.row)">
                  <span
                    v-if="
                      scope.row.bizIdInfoMap && scope.row.bizIdInfoMap.typeCode
                    "
                  >
                    <i
                      :class="`iconfont ${scope.row.bizIdInfoMap.typeCode.icon}`"
                      :style="{
                        color: `${
                          scope.row.bizIdInfoMap.typeCode.color || '#ccc'
                        }`,
                      }"
                    />
                  </span>
                  <span v-if="scope.row.bizIdInfo">
                    {{ scope.row.bizIdInfo.code }}

                    {{ scope.row.bizIdInfo.name }}
                  </span>
                </a>
              </template>
            </el-table-column>

            <el-table-column
              prop="handleBy"
              label="处理人"
              show-overflow-tooltip
              width="110"
            >
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.bizIdInfoMap && scope.row.bizIdInfoMap.handleBy
                  "
                >
                  <vone-user-avatar
                    :avatar-path="scope.row.bizIdInfoMap.handleBy.avatarPath"
                    :name="scope.row.bizIdInfoMap.handleBy.name"
                  />
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="stateCode"
              label="状态"
              show-overflow-tooltip
              width="90"
            >
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.bizIdInfoMap && scope.row.bizIdInfoMap.stateCode
                  "
                  :style="{
                    border: `1px solid ${scope.row.bizIdInfoMap.stateCode.color}`,
                    color: `${scope.row.bizIdInfoMap.stateCode.color}`,
                  }"
                  class="tagState"
                >
                  {{ scope.row.bizIdInfoMap.stateCode.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="expectedTime"
              label="计划完成时间"
              show-overflow-tooltip
              width="165"
            >
              <template slot-scope="scope">
                <span
                  v-if="scope.row.bizIdInfo && scope.row.bizIdInfo.expectedTime"
                >
                  {{ scope.row.bizIdInfo.expectedTime }}
                </span>
                <span v-else class="noTime"> 未设置 </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="$route.name != 'reqm-center-require-list'"
              fixed="right"
              label="操作"
              width="50"
            >
              <template slot-scope="scope">
                <a @click="deleteRelation(scope.row)">
                  <el-icon class="iconfont"
                    ><el-icon-icon-line-jiechuguanlian
                  /></el-icon>
                </a>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <vone-empty v-show="tableData.length == 0" />
      </section>
      <div v-else class="empty-container">
        <svg-icon icon-class="zanwu" class-name="empty-svg" />
        暂无关联内容
      </div>
    </div>
  </vone-div-wrapper>
</template>

<script>
import {
  createRiskItem,
  apiAlmRiskAssociable,
  getRiskItemQuery,
  delRiskItemRelation,
} from '@/api/vone/project/risk'

import { debounce } from 'lodash'

export default {
  name: 'RiskCommon',
  components: {},

  props: {
    issueId: {
      type: String,
      default: undefined,
    },
    tabName: {
      type: String,
      default: null,
    },

    issueInfo: {
      // 需求详情,看是否有关联的意向
      type: Object,
      default: () => {},
    },
    placeholder: {
      type: String,
      default: null,
    },
    classify: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      pageLoading: false,
      tableData: [],
      issueTab: {
        relationIssue: [],
      },
      addRelation: false,
      loading: false,
      selectOptionWidth: '',
      requireLoading: false,
      ideaList: [],
    }
  },
  watch: {
    issueId() {
      this.getInitTableData()
    },
    immediate: true,
  },

  mounted() {
    this.getInitTableData()
  },
  methods: {
    toRouter(val) {
      this.$emit('toRouter', val)
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },

    // 查数据源
    getIdeaList: debounce(async function (query, projectId) {
      try {
        const queryStr = query.trim()
        this.requireLoading = true
        const res = await apiAlmRiskAssociable(this.issueId, {
          keyword: queryStr,
          typeClassify: this.classify,
        })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        res.data.forEach((element) => {
          element.disabled = element.id == this.issueId
        })

        this.ideaList = res.data[0]
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 查询关系
    async getInitTableData() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true
      const res = await getRiskItemQuery({
        riskId: this.issueId,
        typeClassify: this.classify,
      })
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach((element) => {
        element.bizIdInfo =
          element.bizId && element.echoMap && element.echoMap.bizId
            ? element.echoMap.bizId
            : null
        element.bizIdInfoMap =
          element.bizId &&
          element.echoMap &&
          element.echoMap.bizId &&
          element.echoMap.bizId.echoMap
            ? element.echoMap.bizId.echoMap
            : null
      })

      this.tableData = res.data
    },
    // 添加关系
    async addRelationShip() {
      if (!this.issueTab.relationIssue.length) {
        this.$message.warning('请选择')
        return
      }

      const { isSuccess, msg } = await createRiskItem({
        bizIds: this.issueTab.relationIssue,
        riskId: this.issueId,
        typeClassify: this.classify,
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getInitTableData()
      this.$set(this.issueTab, 'relationIssue', [])
      this.ideaList = []
      this.addRelation = false
    },

    // 需求解除与用户需求的关系
    async deleteRelation(row) {
      // this.$confirm(`你确定要取消关联 【${row.bizIdInfo.name}】的关联关系吗?`, '取消关联', {
      //   confirmButtonText: '确认',
      //   type: 'warning',
      //   closeOnClickModal: false
      // })
      //   .then(async() => {
      const { isSuccess, msg } = await delRiskItemRelation([row.id])
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success(msg)
      this.getInitTableData()
      // })
      // .catch(() => { })
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-button + .el-button) {
  margin: 8px;
}
.con-idea {
  font-weight: 400;
  font-size: 14px;
}
.boxContent {
  height: 100%;
}

.shadow_bottom {
  margin: 0 -38px 16px;

  box-shadow: 0px 15px 10px -15px #ccc;
  .row_host {
    padding: 0px 32px 0px 32px;
    .el-row {
      .el-col {
        padding-right: 12px !important;
      }
    }
  }
}
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px;
  // height: 100%;
  height: calc(60vh - 158px);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.el-button) {
  line-height: 22px;
  height: 22px;
}
.tagState {
  padding: 0 5px;
  border-radius: 2px;
}
:deep(.el-table th.el-table__cell.is-leaf) {
  background: none;
}
.noTime {
  color: var(--placeholder-color);
}
</style>
