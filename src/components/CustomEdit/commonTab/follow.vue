<template>
  <div class="pollow-wrapper">
    <header class="header">关注者</header>
    <section>
      <a class="user_wrap">
        <span
          v-for="(item, index) in baseUsers"
          :key="index"
          class="item"
          :class="viewType"
        >
          <el-tooltip effect="dark" :content="item.name" placement="top">
            <vone-user-avatar
              :avatar-path="item.avatarPath"
              :avatar-type="true"
              :show-name="false"
            />
          </el-tooltip>
          <el-icon class="clearicon iconfont"><el-icon-icon-guanbi /></el-icon>
        </span>
        <template v-if="moreUsers.length > 0">
          <el-popover ref="addPopover" placement="top" trigger="hover">
            <ul class="el-select-dropdown__list">
              <li
                v-for="item in moreUsers"
                :key="item.id"
                class="user_item text-over"
              >
                <vone-user-avatar
                  :avatar-path="item.avatarPath"
                  :avatar-type="true"
                  height="22px"
                  width="22px"
                  :name="item.name"
                  :show-name="true"
                />
                <el-icon class="iconfont remove"
                  ><el-icon-icon-guanbi
                /></el-icon>
              </li>
            </ul>

            <span slot="reference" class="persionL"
              >+{{ moreUsers.length || 0 }}</span
            >
          </el-popover>
        </template>

        <el-popover
          v-if="viewType !== 'detail'"
          ref="popoverRef"
          placement="top"
          width="200"
          trigger="click"
          @show="showCommitSelector('userSelector')"
        >
          <selector
            ref="userSelector"
            datatype="user"
            :project-id="projectId"
            :selected="selectedIds"
            placeholder="搜索成员"
            @selectedChang="selectedChang"
          />

          <span slot="reference" type="text"
            ><el-icon class="iconfont"><el-icon-tips-plus-circle /></el-icon
          ></span>
        </el-popover>
      </a>
    </section>
    <div
      v-if="viewType == 'detail' && list && list.length == 0"
      class="type-empty"
    >
      <span class="text">暂无关注者</span>
    </div>
  </div>
</template>

<script>
import {
  IconGuanbi as ElIconIconGuanbi,
  TipsPlusCircle as ElIconTipsPlusCircle,
} from '@element-plus/icons-vue'
import selector from '../../Comment/selector.vue'
import { followList, followClear, followPost } from '@/api/vone/project/comment'
export default {
  components: {
    selector,
    ElIconIconGuanbi,
    ElIconTipsPlusCircle,
  },
  props: {
    projectId: {
      type: String,
      default: undefined,
    },
    sourceType: {
      type: String,
      default: '',
    },
    sourceId: {
      type: String,
      default: '',
    },
    viewType: {
      // 判断是详情页面
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      data: {},
      list: [],
      selectedIds: [],
    }
  },
  computed: {
    baseUsers() {
      return this.list?.slice(0, 5) || []
    },
    moreUsers() {
      return this.list?.slice(5) || []
    },
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 查询评论人员或工作项
    showCommitSelector(ref) {
      this.$refs[ref]?.getInitData()
    },
    selectedChang(e) {
      const has = this.selectedIds.indexOf(e.userId) > -1
      if (has) {
        const obj = { id: e.userId }
        this.delFollow(obj)
        return
      }

      if (this.projectId || this.$route.params.id) {
        this.$set(this.data, 'user', [e.userId])
      } else {
        this.$set(this.data, 'user', [e.id])
      }

      this.saveFollow()
    },
    close() {
      this.data = {}
      this.visible = false
    },
    async saveFollow() {
      const params = {
        classify: this.sourceType,
        itemId: this.sourceId,
        userId: this.data.user,
      }
      const res = await followPost(params)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.close()
      this.getList()
    },
    async delFollow(item) {
      const params = {
        itemId: this.sourceId,
        userId: item.id,
      }
      const res = await followClear(params)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.getList()
    },
    async getList() {
      const params = {
        classify: this.sourceType,
        itemId: this.sourceId,
      }
      const res = await followList(params)
      if (!res.isSuccess) {
        return
      }
      this.list = res.data
      this.selectedIds = this.list?.map((v) => v.id) || []
    },
  },
}
</script>

<style lang="scss" scoped>
.user_wrap {
  display: flex;
  align-items: center;
  gap: 6px;
}
.pollow-wrapper {
  height: 86px;
  padding: 16px;
  border-bottom: 1px solid #f2f3f5;
  .header {
    font-weight: 500;
    line-height: 22px;
    color: var(--font-main-color);
  }
  section {
    margin-top: 10px;
    i {
      color: #3e7bfa;
      font-size: 24px;
      &:hover {
        cursor: pointer;
      }
    }
    .item {
      position: relative;
      cursor: pointer;
      :deep(.avatar) {
        width: 26px;
        height: 26px;
        border: 2px solid #fff;
        border-radius: 50%;
        img {
          width: 24px;
          height: 24px;
        }
      }
      .clearicon {
        opacity: 0;
        position: absolute;
        font-size: 12px;
      }
      &:not(.detail):hover {
        :deep(.avatar) {
          border-color: #3e7bfa;
        }
        .clearicon {
          opacity: 1;
          top: -4px;
          right: -4px;
        }
      }
    }
  }
  .type-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    height: 70px;

    .text {
      font-weight: 400;
      color: #838a99;
    }
  }
}
.user_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  padding: 4px;
  color: var(--main-font-color);
  i {
    color: var(--auxiliary-font-color);
    cursor: pointer;
  }
  &:hover {
    background-color: var(--hover-bg-color);
  }
}
.persionL {
  display: inline-block;
  background: var(--col-hover-bg);
  height: 24px;
  width: 24px;
  text-align: center;
  border-radius: 50%;
  line-height: 24px;
  color: var(--main-font-color);
  margin: 0 7px 0 2px;
}
</style>
