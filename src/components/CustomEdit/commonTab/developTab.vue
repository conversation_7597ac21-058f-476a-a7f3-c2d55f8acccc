<template>
  <vone-div-wrapper title="关联代码库">
    <div slot="actions">
      <el-button
        type="text"
        class="action-btn"
        :icon="ElIconPlus"
        @click="connectLibrary('create')"
        >新建并关联</el-button
      >
      <el-button
        type="text"
        class="action-btn"
        :icon="ElIconIconfont elIconEditRelate"
        @click="connectLibrary('connect')"
        >关联</el-button
      >
    </div>
    <!-- 新增关联代码库 -->
    <div>
      <div class="row_host">
        <el-form
          v-show="addVisible"
          ref="addForm"
          :model="addForm"
          :rules="rules"
        >
          <el-row :gutter="12">
            <el-col :span="addType === 'create' ? 7 : 12">
              <el-form-item prop="library">
                <el-select
                  v-model="addForm.library"
                  placeholder="选择代码库"
                  style="width: 100%"
                  @change="getBranch"
                >
                  <el-option
                    v-for="item in adressList"
                    :key="item.id"
                    :label="item.path"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="addType === 'create' ? 5 : 12">
              <el-form-item prop="branch">
                <el-select
                  v-model="addForm.branch"
                  placeholder="源分支"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in branchList"
                    :key="item.id"
                    :label="item.branchName"
                    :value="item.branchName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="addType === 'create'" :span="12">
              <el-form-item prop="newBranch">
                <el-input v-model="addForm.newBranch" placeholder="分支名称" />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="5">
                <el-form-item> -->

            <!-- </el-form-item>
              </el-col> -->
          </el-row>
          <div class="minbtns">
            <el-button class="miniBtn" @click="cancelConnect">取消</el-button>
            <el-button
              class="miniBtn"
              style="margin-left: 8px"
              :loading="saveLoading"
              type="primary"
              @click="saveConnect"
              >确定</el-button
            >
          </div>
        </el-form>
      </div>
    </div>
    <!-- end -->
    <el-collapse
      v-if="libraryList.length > 0"
      accordion
      @change="changeCollapse"
    >
      <el-collapse-item
        v-for="item in libraryList"
        :key="item.id"
        :name="item.repositoryName + '&' + item.repositoryBranch"
      >
        <template slot="title">
          {{ item.repositoryName
          }}<el-tag effect="plain" class="codeTag" style="margin-left: 8px">{{
            item.repositoryBranch
          }}</el-tag>
          <el-tooltip content="取消关联" placement="top">
            <el-button
              type="text"
              :icon="ElIconIconfont elIconIconLineJiechuguanlian"
              class="rightbtn"
              @click.stop="confirmDisconnect(item)"
            />
          </el-tooltip>
        </template>
        <div v-loading="commitLoading">
          <vone-empty v-if="!commitLogs.length" />
          <el-table
            v-else
            ref="commitLogs"
            class="vone-table voneNobg-table"
            :data="commitLogs"
          >
            <el-table-column
              prop="title"
              label="commit log"
              show-overflow-tooltip
              min-width="250"
            >
              <template slot-scope="scope">
                {{ scope.row.title }} - {{ scope.row.shortId }}
              </template>
            </el-table-column>
            <el-table-column prop="authorName" label="提交人">
              <template slot-scope="scope">
                {{ scope.row.authorName }}
              </template>
            </el-table-column>
            <el-table-column prop="committedDate" label="提交时间">
              <template slot-scope="scope">
                提交于{{ dayjs(scope.row.committedDate).fromNow(true) }}前
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- <div class="commit-more">查看更多</div> -->
      </el-collapse-item>
    </el-collapse>
    <div v-else class="empty-container">
      <svg-icon icon-class="zanwu" class-name="empty-svg" />
      暂无关联内容
    </div>
  </vone-div-wrapper>
</template>

<script>
import {
  getRelateRepository,
  getReleasedLibByQuery,
} from '@/api/vone/code/library'
import { getRepoBranchList } from '@/api/vone/code/branch'
import {
  disconnectLibraryBranch,
  queryIssuerelatedCode,
  releaseExistedBranch,
  releaseNewBranchByRequireId,
} from '@/api/vone/code/index'

import { getDayCommitLogs } from '@/api/vone/code'

import storage from 'store'
import dayjs from 'dayjs'
export default {
  name: 'DevelopTab',
  props: {
    issueId: {
      type: String,
      default: undefined,
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入名称'))
      } else if (value == this.addForm.branch) {
        callback(new Error('源分支和新分支的名称一致,请重新输入'))
      } else {
        callback()
      }
    }
    return {
      ruleForm: {
        repositoryType: 'GitLab',
        repositoryName: '',
        repositoryBranch: '',
      },
      rules: {
        library: [
          { required: true, message: '请选择代码库', trigger: 'change' },
        ],
        branch: [
          { required: true, message: '请选择源分支', trigger: 'change' },
        ],
        newBranch: [
          { required: true, message: '请输入新建分支名称', trigger: 'blur' },
          {
            validator: validatePass,
          },
        ],
      },
      branchList: [], // 分支
      adressList: [], // 代码库
      commitLogs: [], // 提交历史
      saveLoading: false,
      infoLoading: false,
      commitLoading: false,
      addType: 'create',
      addVisible: false,
      addForm: {
        library: '',
        branch: '',
        newBranch: '',
      },
      libraryList: [],
    }
  },
  computed: {
    // 当前登录人
    loginUser() {
      return storage.get('user')
    },
  },
  watch: {
    issueId() {
      this.getReleasedLibsByRequireId()
    },
    immediate: true,
  },
  async mounted() {
    this.$nextTick(() => {
      this.getReleasedLibsByRequireId()
    })

    this.getAdressList()
    // this.getInfo()
  },
  methods: {
    // 查询当前需求已关联的代码库
    async getReleasedLibsByRequireId() {
      if (!this.issueId) {
        return
      }
      try {
        const res = await getReleasedLibByQuery({
          requirementId: this.issueId,
        })
        if (res.isSuccess) {
          this.libraryList = res.data
        }
      } catch (error) {
        return
      }
    },
    changeCollapse(val) {
      const libraryName = val.split('&')[0]
      const branchName = val.split('&')[1]
      const currentLib = this.adressList.find(
        (ele) => ele.repositoryName === libraryName
      )
      this.getHistory(currentLib, branchName)
    },

    // 根据需求号回显代码库信息
    async getInfo() {
      this.infoLoading = true

      const res = await queryIssuerelatedCode(this.issueId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.ruleForm = res.data
      if (!res.data.repositoryType) {
        this.$set(this.ruleForm, 'repositoryType', 'GitLab')
      }

      this.infoLoading = false
      this.getHistory()
    },
    // 查询所有代码库
    async getAdressList() {
      const params = {
        repositoryType: this.ruleForm.repositoryType,
        userId: this.loginUser.id,
      }
      const res = await getRelateRepository(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.adressList = res.data
    },
    // change事件查询分支
    async getBranch(val) {
      const currentLib = this.adressList.find((r) => r.id == val)
      const res = await getRepoBranchList({
        codeRepositoryId: val,
        repositoryType: currentLib.repositoryType || 'GitLab',
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.branchList = res.data
    },
    async getHistory(library, branchName) {
      this.commitLoading = true
      try {
        const {
          privateToken,
          account,
          password,
          codeEngineId,
          repositoryType,
          url,
          id,
        } = library

        const params = {
          branchName,
          codeRepositoryType: repositoryType,
          codeRepositoryId: id,
          url,
          privateToken,
          account,
          password,
          engineId: codeEngineId,
          updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        }
        const res = await getDayCommitLogs(params)
        this.commitLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.commitLogs = res.data
      } catch (e) {
        this.commitLoading = false
      }
    },

    // 复制标题到剪贴板
    titleCopy(item) {
      const _this = this
      this.$copyText(`${item.shortId} ${item.title}`).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    connectLibrary(type) {
      this.addVisible = true
      this.addType = type
      this.addForm = {
        library: '',
        branch: '',
        newBranch: '',
      }
      this.$refs.addForm.clearValidate()
    },
    cancelConnect() {
      this.addVisible = false
    },
    async saveConnect() {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }

      const currentLib = this.adressList.find(
        (r) => r.id == this.addForm.library
      )
      this.addType === 'create'
        ? this.createReleasedBranch(currentLib)
        : this.confirmReleasExitedBranch(currentLib)
    },
    // 新建关联分支
    async createReleasedBranch(library) {
      this.saveLoading = true
      try {
        const branch = this.branchList.find(
          (v) => v.branchName === this.addForm.branch
        )
        const params = {
          account: library.account,
          attributeOpen: branch.attributeOpen,
          branchName: this.addForm.newBranch,
          branchType: '',
          buildAfterCommit: branch.buildAfterCommit,
          codeCheckStatus: branch.codeCheckStatus,
          codeRepositoryId: this.addForm.library,
          codeRepositoryType: library.repositoryType,
          commitSha: '',
          createdBy: library.createdBy,
          description: library.description,
          engineId: library.codeEngineId,
          lcoalPath: '',
          maintenanceStaffId: library.maintenanceStaffId,
          mergeId: '',
          orignBranchName: this.addForm.branch,
          orignBranchVersion: branch.orignBranchVersion,
          password: library.password,
          path: library.path,
          privateToken: library.privateToken,
          rateProgress: branch.rateProgress,
          serviceApplicationId: branch.serviceApplicationId,
          status: branch.serviceApplicationId,
          systemId: library.systemId,
          title: '',
          url: library.url,
          requirementId: this.issueId,
        }
        const res = await releaseNewBranchByRequireId(this.issueId, params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning('创建失败')
          return
        }
        this.$message.success('新建成功')
        this.addVisible = false
        this.getReleasedLibsByRequireId()
      } catch (error) {
        this.$message.warning('创建失败')
        this.saveLoading = false
        return
      }
    },
    // 关联已存在代码库分支
    async confirmReleasExitedBranch(library) {
      this.saveLoading = true
      const branch = this.branchList.find(
        (v) => v.branchName === this.addForm.branch
      )
      const params = {
        branchId: branch?.id,
        repositoryBranch: this.addForm.branch,
        repositoryId: this.addForm.library,
        repositoryName: library.repositoryName,
        repositoryType: library.repositoryType || 'GitLab',
        requirementId: this.issueId,
      }
      const res = await releaseExistedBranch(params)
      this.saveLoading = false
      if (res.isSuccess) {
        this.$message.success('关联成功')
        this.addVisible = false
        this.getReleasedLibsByRequireId()
      } else {
        this.$message.error(res.msg)
      }
    },
    async confirmDisconnect(item) {
      try {
        await this.$confirm('确认取消关联吗？', '提示', {
          type: 'warning',
        })
      } catch (error) {
        return
      }
      const params = [item.id]
      const res = await disconnectLibraryBranch(params)
      if (res.isSuccess) {
        this.$message.success('取消成功')
        this.getReleasedLibsByRequireId()
      } else {
        this.$message.error(res.msg)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.mt-3 {
  margin-top: 10px;
}
:deep(.el-form-item__label) {
  float: left !important;
}
.action-btn {
  padding: 0 12px;
  min-width: auto;
  font-weight: 400;
  font-size: 14px;
}
:deep(.el-button + .el-button) {
  margin: 0;
}
:deep() {
  .el-collapse-item {
    position: relative;
  }
  .el-collapse-item__header.is-active {
    border-bottom-color: var(--disabled-bg-color);
    box-shadow: 0px 4px 4px rgba(32, 33, 36, 0.06);
  }
  .el-collapse-item__arrow {
    position: absolute;
    left: 0;
  }
}

.rightbtn {
  position: absolute;
  right: 6px;
}
.c {
  width: 312px;
  color: var(--main-theme-color);
}
.commit-item {
  display: flex;
  justify-content: space-between;
  height: 34px;
  line-height: 34px;
  border-bottom: 1px solid var(--disabled-bg-color);
}
.commit-date {
  max-width: 100px;
}
.commit-more {
  height: 34px;
  line-height: 34px;
  color: var(--main-theme-color);
  text-align: center;
  cursor: pointer;
}
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px;
  height: calc(60vh - 108px);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
.shadow_bottom {
  margin: 0 -38px 16px;
  // padding: 0 16px 0px;
  box-shadow: 0px 15px 10px -15px #ccc;
  .row_host {
    padding: 0px 38px 0px 32px;
  }
  .btnCty {
    height: 32px;
    display: flex;
    align-items: center;
    text-align: right;
    justify-content: flex-end;
  }
}
:deep(.el-button) {
  line-height: 24px;
  height: 24px;
}
.minbtns {
  text-align: right;
  :deep(.el-button + .el-button) {
    margin: -16px 0px 12px 12px;
  }
}
:deep(.el-collapse-item__arrow) {
  left: 10px;
}
:deep(.el-collapse-item__header) {
  height: 40px;

  padding: 0px 10px 0px 34px;
}
:deep(.el-collapse-item__content) {
  padding: 0px;
}
:deep(.el-collapse-item) {
  margin-bottom: 10px;
  border: 1rem solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  .el-button {
    min-width: unset;
    padding: 0px;
  }
}
.codeTag {
  border: 1px solid var(--font-second-color);
  color: var(--font-second-color);
}
</style>
