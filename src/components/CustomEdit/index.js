// CustomEdit 组件的逻辑文件
import { apiAlmGetInfo, apiAlmEdit } from '@/api/vone/project/issue'
import { getUser } from '@/utils/auth'
import storage from 'store'
import dayjs from 'dayjs'

export default {
  name: 'CustomEdit',
  props: {
    // 组件属性定义
    issueId: {
      type: [String, Number],
      default: null
    },
    issueType: {
      type: String,
      default: ''
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    // 编辑模式
    editMode: {
      type: Boolean,
      default: false
    },
    // 只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 显示的字段
    visibleFields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 组件数据
      issueInfo: {},
      formData: {},
      loading: false,
      saving: false,
      user: getUser(),
      dayjs,
      // 表单验证规则
      rules: {},
      // 自定义字段配置
      customFields: [],
      // 编辑状态
      isEditing: false
    }
  },
  computed: {
    // 计算属性
    currentProject() {
      return storage.get('currentProject') || {}
    },
    
    // 是否可以编辑
    canEdit() {
      return !this.readonly && (this.editMode || this.isEditing)
    }
  },
  watch: {
    // 监听器
    issueId: {
      handler(newVal) {
        if (newVal) {
          this.loadIssueInfo()
        }
      },
      immediate: true
    },
    
    editMode: {
      handler(newVal) {
        this.isEditing = newVal
      },
      immediate: true
    }
  },
  mounted() {
    // 组件挂载后的逻辑
    this.init()
  },
  methods: {
    // 组件方法
    init() {
      if (this.issueId) {
        this.loadIssueInfo()
      }
      this.loadCustomFields()
    },
    
    async loadIssueInfo() {
      if (!this.issueId) return
      
      this.loading = true
      try {
        const response = await apiAlmGetInfo(this.issueId)
        if (response.code === 200) {
          this.issueInfo = response.data || {}
          this.formData = { ...this.issueInfo }
        }
      } catch (error) {
        console.error('加载问题信息失败:', error)
        this.$message.error('加载问题信息失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadCustomFields() {
      // 加载自定义字段配置
      try {
        // 这里应该调用获取自定义字段的API
        this.customFields = []
      } catch (error) {
        console.error('加载自定义字段失败:', error)
      }
    },
    
    // 开始编辑
    startEdit() {
      this.isEditing = true
      this.formData = { ...this.issueInfo }
    },
    
    // 取消编辑
    cancelEdit() {
      this.isEditing = false
      this.formData = { ...this.issueInfo }
    },
    
    // 保存编辑
    async saveEdit() {
      if (!this.issueId) return
      
      this.saving = true
      try {
        const response = await apiAlmEdit(this.issueId, this.formData)
        if (response.code === 200) {
          this.issueInfo = { ...this.formData }
          this.isEditing = false
          this.$message.success('保存成功')
          this.$emit('saved', this.issueInfo)
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    
    // 字段值变更处理
    onFieldChange(field, value) {
      this.formData[field] = value
      this.$emit('field-change', { field, value })
    },
    
    // 刷新数据
    refresh() {
      this.loadIssueInfo()
    }
  }
}
