<template>
  <vone-customsimple-add
    ref="simpleAdd"
    v-model.trim="form.name"
    :rules="rules"
    :model="form"
    label="标题"
    :file-list="fileList"
    @update:file-list="fileList = $event"
    :input-width="318"
    :loading="saveLoading"
    @submit="saveSubmit"
    @open="openCreateDetail"
    @cancel="$emit('cancel')"
  >
    <!-- 类型下拉列表 -->
    <el-col :span="9">
      <el-dropdown
        v-model="form.typeCode"
        class="dropList"
        trigger="click"
        @command="changeClass"
      >
        <el-row type="flex" align="middle" justify="space-between">
          <span class="el-dropdown-link textTitle">
            <span v-if="defectMap && defectMap[form.typeCode]">
              <el-row
                type="flex"
                style="display: flex; align-items: center; cursor: pointer"
              >
                <i
                  :class="`iconfont ${defectMap[form.typeCode].icon}`"
                  :style="{
                    color: `${
                      defectMap[form.typeCode].color
                        ? defectMap[form.typeCode].color
                        : '#ccc'
                    }`,
                  }"
                  class="iconStyle"
                />
                <span> {{ defectMap[form.typeCode].name }} </span>
                <el-icon class="iconfont el-icon--right"
                  ><el-icon-direction-down
                /></el-icon>
              </el-row>
            </span>
            <span v-else>
              <span> 未设置分类 </span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </span>
          </span>
        </el-row>
        <el-dropdown-menu slot="dropdown">
          <template v-if="typeList && typeList.length > 0">
            <el-dropdown-item
              v-for="item in typeList"
              :key="item.code"
              :command="item.code"
            >
              <span v-if="item.icon">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{ color: `${item.color ? item.color : '#ccc'}` }"
                />
              </span>
              {{ item.name }}
            </el-dropdown-item>
          </template>
          <vone-empty v-else desc="无可选分类" />
        </el-dropdown-menu>
      </el-dropdown>
    </el-col>
    <!-- 人员下拉列表 -->
    <el-col :span="9">
      <vone-remote-user v-model="form.userId" :project-id="projectId" />
    </el-col>
  </vone-customsimple-add>
</template>

<script>
import { DirectionDown as ElIconDirectionDown } from '@element-plus/icons'

import { apiAlmTaskAdd } from '@/api/vone/project/task'
// import { apiAlmBugAdd } from '@/api/vone/project/defect'
// import { apiAlmRiskAdd } from '@/api/vone/project/risk'

import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { apiProjectUserNoPage } from '@/api/vone/project/index'

// import { apiAlmBugAdd } from '@/api/vone/project/defect'
import dayjs from 'dayjs'

import { mapGetters } from 'vuex'
export default {
  components: {
    ElIconDirectionDown,
  },
  props: {
    issueId: {
      // 需求id,用于需求关联任务时,保存时传需求id
      type: String,
      default: undefined,
    },
    parentId: {
      // 拆分子任务
      type: String,
      default: undefined,
    },
    noFile: Boolean,

    typeCode: {
      // 分类枚举值,只作为查询分类的入参,ISSUE,BUG,TASK,RISK,及区分保存时调哪个接口
      type: String,
      default: undefined,
    },
    noEpic: Boolean, // 史诗拆分用户故事,需求新增时,过滤掉史诗
  },
  data() {
    return {
      form: {
        name: '',
        typeCode: '',
        userId: '',
        envCode: '',
      },
      typeList: [], // 分类下拉框
      envList: [], // 环境
      users: [], // 用户
      fileList: [],
      fileAni: false,
      defectMap: {}, // 缺陷分类
      userMap: {},
      envMap: {},
      saveLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入标题', trigger: 'change' },
          {
            pattern: '^.{1,250}$',
            message: '请输入不超过250个字符组成的标题',
            trigger: 'change',
          },
        ],
      },
      projectId: this.$route.params.id,
    }
  },
  computed: {
    projectKey() {
      return this.$route.params.projectKey
    },
    ...mapGetters(['user']),
  },
  mounted() {
    this.getTypeList()
    // this.getUser()
    this.$set(this.form, 'userId', this.user?.id)
  },
  methods: {
    // 打开新建详细数据弹窗
    openCreateDetail() {
      this.$emit('createDetail')
      this.$emit('cancel')
    },
    // 切换类型
    changeClass(value) {
      this.$set(this.form, 'typeCode', value)
    },

    // 查询类型
    async getTypeList() {
      const res = await apiAlmGetTypeNoPage(
        this.$route.params.id,
        this.typeCode
      )
      if (!res.isSuccess) {
        this.$message.success(res.msg)
        return
      }

      this.typeList = res.data

      this.defectMap = this.typeList.reduce((r, v) => (r[v.code] = v) && r, {})

      this.$set(this.form, 'typeCode', this.typeList[0].code)
    },
    // 查询项目下人员
    async getUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      // res.data.forEach(element => {
      //   element.name = element.id && element.echoMap && element.echoMap.userId ? element.echoMap.userId.name : '未查询到该用户'
      // })
      this.users = res.data

      // this.$set(this.form, 'userId', this.users[0].userId || this.users[0].id)
    },
    async saveSubmit() {
      const { id } = this.$route.params
      const params = {
        name: this.form.name,
        typeCode: this.form.typeCode,
        estimateHour: 1,
        priorityCode: 'HIGH',
        createdBy: this.form.userId,
        putBy: this.form.userId,
        handleBy: this.form.userId,
        leadingBy: this.form.userId,
        projectId: id,
        planEtime: dayjs().add(240, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        planStime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        description: '',
        sourceCode: this.typeCode === 'BUG' ? 'BASIC_SCENES' : null,
        files: [],
      }
      if (this.issueId) params.requirementId = this.issueId
      if (this.parentId) params.parentId = this.parentId
      this.saveTask(params)
    },

    // 保存任务
    async saveTask(params) {
      this.saveLoading = true
      const res = await apiAlmTaskAdd(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('创建成功')
      this.$emit('success')
      this.form = {
        name: '',
        typeCode: this.typeList[0].code,
        userId: this.user?.id,
      }
      // this.fileList = []
      // this.$refs['uloadFile'].resetData()
    },
  },
}
</script>

<style lang="scss" scoped>
.textTitle {
  white-space: nowrap;
}
.iconStyle {
  margin-left: 5px;
  margin-right: 5px;
}
.dropList {
  width: 100%;
  border: 1rem solid var(--input-border-color);
  color: var(--font-main-color);
  height: 32px;
  line-height: 32px;
  padding: 0px 10px;
  .textTitle {
    width: 100%;
  }
  .el-row--flex span {
    flex: 1;
  }
  .el-icon--right {
    float: right;
    color: var(--font-second-color);
    line-height: 32px;
  }
}
</style>
