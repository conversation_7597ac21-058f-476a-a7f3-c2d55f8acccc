<template>
  <el-dialog
    :model-value="visible"
    width="40%"
    title="变更工作项类型"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="dialog"
    append-to-body
    @close="close"
  >
    <div class="model-title">目标工作项变更</div>
    <el-table :data="tableData" class="vone-table" style="width: 100%">
      <el-table-column prop="project" label="所属项目" />
      <el-table-column prop="oldtypecode" label="原工作项类型" />
      <el-table-column prop="newtypecode" label="目标工作项类型">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.newtypecode"
            style="width: 100%"
            filterable
            @change="typecodeChange"
          >
            <el-option
              v-for="item in typeCodeList"
              :key="item.key"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="formList.length > 0" class="model-title">补充工作项属性</div>
    <vone-empty
      v-else
      style="height: 200px"
      height="60%"
      desc="无需要补充的字段"
    />
    <!-- 表单部分 -->
    <el-form
      ref="form"
      v-loading="formLoading"
      :rules="rules"
      :model="form"
      label-position="top"
    >
      <el-row :gutter="12" class="row-box">
        <el-col
          v-for="item in formList"
          :key="item.id"
          :span="item.type == 'EDITOR' || item.type == 'TEXTAREA' ? 24 : 12"
        >
          <el-form-item :label="item.name" :prop="item.key">
            <!-- 平台人员组件 -->
            <vone-remote-user
              v-if="item.type == 'USER'"
              v-model="form[item.key]"
              :multiple="item.multiple"
            />
            <!-- 项目人员组件 -->
            <projectRemoteUser
              v-if="item.type == 'PROJECTUSER'"
              v-model="form[item.key]"
              :multiple="item.multiple"
            />
            <!-- 整数 -->
            <el-input-number
              v-else-if="item.type == 'INT'"
              v-model="form[item.key]"
              :min="0.1"
              :max="1000"
              :precision="2"
              controls-position="right"
              style="width: 100%"
            />

            <!-- 文件 -->
            <vone-upload
              v-else-if="item.type == 'FILE'"
              ref="formUploadFile"
              :files-data="
                form.echoMap && form.echoMap[item.key]
                  ? form.echoMap[item.key]
                  : form[item.key]
              "
              :biz-type="fileMap[typeCode]"
              @onSuccess="(files) => fileChange(files, item.key)"
              @remove="(file) => fileRemove(file, item.key)"
            />

            <!-- 文本编辑器 -->
            <span v-else-if="item.type == 'EDITOR'">
              <vone-editor
                ref="editor"
                v-model="form[item.key]"
                @input.native="eventDisposalRangeChange(form[item.key])"
              />
            </span>

            <!-- 日期组件 -->
            <el-date-picker
              :default-time="
                `${item.defaultTime}:00`.map((d) =>
                  dayjs(d, 'hh:mm:ss').toDate()
                )
              "
              v-else-if="item.type == 'DATE'"
              v-model="form[item.key]"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="item.placeholder"
              style="width: 100%"
            ></el-date-picker>
            <!-- 输入框 -->
            <el-input
              v-if="item.type == 'INPUT'"
              v-model="form[item.key]"
              :placeholder="item.placeholder"
              maxlength="200"
              show-word-limit
            />
            <!-- 组织机构 -->
            <vone-tree-select
              v-else-if="item.type == 'ORG'"
              v-model="form[item.key]"
              search-nested
              :tree-data="orgData"
              placeholder="请选择机构"
              :multiple="item.multiple"
            />
            <!-- 输入文本框 -->
            <el-input
              v-else-if="item.type == 'TEXTAREA'"
              v-model="form[item.key]"
              type="textarea"
              autosize
              :placeholder="item.placeholder"
            />
            <!-- 标签 -->
            <tagSelect
              v-else-if="item.type == 'SELECT' && item.key == 'tagId'"
              v-model="form[item.key]"
              multiple
              collapse-tags
            />
            <!-- 下拉单选框 -->
            <template
              v-else-if="
                item.type == 'SELECT' && item.key != 'productModuleFunctionId'
              "
            >
              <!-- 远程搜索下拉 -->
              <el-select
                v-if="
                  item.key == 'requirementId' ||
                  item.key == 'planId' ||
                  item.key == 'projectId' ||
                  item.key == 'bugId'
                "
                v-model="form[item.key]"
                remote
                :placeholder="item.placeholder"
                :multiple="item.multiple"
                clearable
                filterable
                :remote-method="(e) => remoteMethod(e, item.key)"
                @focus="setOptionWidth"
              >
                <el-option
                  v-for="i in item.options"
                  :key="i.id"
                  :label="i.name"
                  :value="i.id"
                  :style="{ width: selectOptionWidth }"
                />
              </el-select>
              <!-- 下拉单选框 -->
              <el-select
                v-else
                v-model="form[item.key]"
                :placeholder="item.placeholder"
                clearable
                filterable
                :multiple="item.multiple"
                @focus="setOptionWidth"
                @change="changeSelect"
              >
                <el-option
                  v-for="i in item.options"
                  :key="i.id"
                  :label="i.name"
                  :value="
                    item.key == 'sourceCode' ||
                    item.key == 'typeCode' ||
                    item.key == 'priorityCode'
                      ? i.code
                      : i.id
                  "
                  :style="{ width: selectOptionWidth }"
                >
                  <span v-if="item.key == 'ideaId'">{{
                    `${i.code}  ${i.name}`
                  }}</span>

                  <span v-if="item.key == 'productId'">
                    {{ i.name }}
                    <span v-if="i.echoMap" style="float: right">
                      <el-tag v-if="i.echoMap.isHost" type="success">
                        主
                      </el-tag>
                      <el-tag v-if="i.echoMap.isHost == false" type="warning">
                        辅
                      </el-tag>
                    </span>
                  </span>
                </el-option>
              </el-select>
              <!-- 下拉多选框 -->
              <!-- <el-select v-else-if="item.type == 'SELECT' && item.multiple&&item.key!='productModuleFunctionId'" v-model="form[item.key]" :placeholder="item.placeholder" multiple clearable filterable :disabled="!item.isUpdate" @focus="setOptionWidth">
                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.name" :style="{ width: selectOptionWidth }" />
                  </el-select> -->
            </template>

            <!-- 功能模块字段 -->
            <vone-tree-select
              v-else-if="
                item.type == 'SELECT' && item.key == 'productModuleFunctionId'
              "
              v-model="form[item.key]"
              search-nested
              :tree-data="item.options"
              placeholder="请选择"
            />
            <!-- 关联类型 -->
            <dataSelect
              v-else-if="item.type == 'LINKED'"
              text-info="edit"
              :model="form[item.key]"
              :config="item"
              :placeholder="item.placeholder"
              @change="dataSelectChange($event, item.key)"
            />
            <!-- 引用类型 -->
            <div v-else-if="item.type == 'QUOTE'">
              <vone-remote-user
                v-if="item.quoteType === 'user'"
                v-model="form[item.key]"
                disabled
              />
              <el-input
                v-else
                v-model="form[item.key]"
                disabled
                placeholder=""
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { orgList } from '@/api/vone/base/org'
import { planListByCondition } from '@/api/vone/project/iteration'

import { apiAlmGetInfo, apiAlmSourceNoPage } from '@/api/vone/project/issue'
import {
  apiAlmPriorityNoPage,
  apiAlmGetTypeNoPage,
  apiAlmTypeNoPage,
  getAllProductInfoList,
} from '@/api/vone/alm/index'

import {
  productListByCondition,
  queryListByCondition,
} from '@/api/vone/project/index'
import { ideaListByCondition } from '@/api/vone/reqmcenter/idea'

import { requirementListByCondition } from '@/api/vone/project/issue'
import { bugListByCondition } from '@/api/vone/project/defect'
import { getProductVersionList, getModule } from '@/api/vone/product/index'
import { getSourceById } from '@/api/vone/base/source'

import { gainTreeList } from '@/utils'

import projectRemoteUser from '@/components/CustomEdit/components/project-user-remote.vue'
import tagSelect from '@/components/CustomEdit/components/tag-select.vue'
import dataSelect from '@/components/CustomEdit/components/data-select.vue'
import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject,
} from '@/api/vone/base/customForm'
import { getTargetTypeCode } from '@/api/vone/project/index'
import _ from 'lodash'
import dayjs from 'dayjs'

const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD',
}

export default {
  data() {
    return {
      fileMap,
      saveLoading: false,
      formLoading: false,
      rules: {},
      form: {},
      orgData: [],
      formList: [],
      selectOptionWidth: '',
      typeCode: '',
      isChange: false,
      tableData: [],
      typeCodeList: [],
      dayjs,
    }
  },
  components: {
    projectRemoteUser,
    tagSelect,
    dataSelect,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    fields: {
      type: Array,
      default: () => [],
    },
    typeClassfiy: {
      type: String,
      default: undefined,
    },
    dataId: {
      type: String,
      default: undefined,
    },
  },
  watch: {
    'form.projectId': {
      handler(value) {
        if (!value) {
          this.$set(this.form, 'planId', '')
          this.setData(this.formList, 'planId', [])
          return
        }
        this.getPlanList(value)
      },
      immediate: true,
    },
    // 'form.productId': {
    //   handler(value) {
    //     if (!value) {
    //       this.$set(this.form, 'productVersionId', '')
    //       this.$set(this.form, 'productRepairVersionId', '')
    //       this.$set(this.form, 'productModuleFunctionId', null)
    //       this.setData(this.formList, 'productVersionId', [])
    //       this.setData(this.formList, 'productRepairVersionId', [])
    //       this.setData(this.formList, 'productModuleFunctionId', [])
    //       return
    //     }
    //     this.getVersion(value)
    //     this.getModel(value)
    //   },
    //   immediate: true
    // }
  },
  async mounted() {
    await this.getInfo()
    // await this.getTemplete()
    if (this.$route.params.projectKey) {
      this.getAllTypeCode()
    }
  },
  methods: {
    typecodeChange(e) {
      this.form.typeCode = e
      this.getTemplete(e)
    },
    remoteMethod: _.debounce(function (e, t) {
      if (t == 'requirementId') {
        this.getRequirementList(e)
      } else if (t == 'planId') {
        this.getPlanList(this.form.projectId, e)
      } else if (t == 'projectId') {
        this.getProjectList(e)
      } else if (t == 'bugId') {
        this.getBugList(e)
      }
    }, 1000),
    fileChange(fileList, key) {
      if (key === 'files') {
        this.form[key] = fileList.map((v) => v?.response?.data || v) || []
      } else {
        this.form[key] = fileList?.map((v) => v.id)?.join(',')
      }
      this.$refs.form.clearValidate(key)
    },
    fileRemove(file, key) {
      if (key === 'files') {
        this.form[key] = this.form[key].filter((v) => v.id != file.id) || []
      } else {
        this.form[key] = this.form[key].replace(key, '').replace(',,', ',')
      }

      // this.$refs.form.validateField(key)
    },
    // 查询详情信息
    async getInfo() {
      this.formLoading = true
      const urlMap = {
        ISSUE: `/api/alm/alm/requirement/${this.dataId}`,
        BUG: `/api/alm/alm/bug/${this.dataId}`,
        TASK: `/api/alm/alm/task/${this.dataId}`,
        IDEA: `/api/alm/alm/idea/${this.dataId}`,
        RISK: `/api/alm/alm/risk/${this.dataId}`,
      }
      const res = await apiAlmGetInfo(urlMap[this.typeClassfiy])
      this.formLoading = false

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data?.tagId?.length && res.data?.echoMap?.tagId) {
        this.$set(
          res.data,
          'tagId',
          res.data.echoMap.tagId.map((r) => r.name) || []
        )
      }
      this.form = res.data
      // if (this.form && this.form.projectId) {
      //   this.isChange = false
      //   this.getPlanList(this.form.projectId)
      // }

      if (this.form && this.form.productId) {
        this.isChange = false

        // this.getVersion(this.form.productId)
        // this.getModel(this.form.productId)
      }
      this.formList.forEach((e) => {
        if (
          e.key == 'requirementId' ||
          e.key == 'planId' ||
          e.key == 'projectId' ||
          e.key == 'bugId'
        ) {
          this.$set(
            e,
            'options',
            res.data.echoMap[e.key] ? [res.data.echoMap[e.key]] : []
          )
        }
      })
      if (this.form.echoMap) {
        this.tableData.push({
          project: this.form.echoMap?.projectId?.name || '',
          oldtypecode: this.form.echoMap?.typeCode?.name || '',
          newtypecode: '',
        })
      }
      // this.$nextTick(() => {
      //   this.$refs['form'].clearValidate()
      // })
    },
    // 查询引用字段
    async getQuoteType() {
      this.formList.forEach((e, index) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          this.getTableConfig(config.relationShipsheet, e, (item) => {
            e.quoteType = item?.type
            this.$set(this.formList, index, e)
          })
        }
      })
    },
    async getTableConfig(id, e, callback) {
      const config = JSON.parse(e.config)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        const list = res.data.fields.map((v) => {
          v.prop = v.id
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj,
          }
        })
        const objs = list.find(
          (e) => e.id == config.relationField && !e.primary
        )
        callback(objs)
      }
    },
    async getTemplete(val) {
      const activeApp = this.$route.meta.activeApp
      const params = {
        typeCode: val,
        stateCode: '',
        projectId: activeApp == 'project' ? this.$route.params.id : null,
      }
      const res =
        activeApp == 'projectm'
          ? await apiVaBaseCustomFormFieldProgram(
              this.$route.params.id,
              this.typeClassfiy,
              params
            )
          : activeApp == 'project'
          ? await apiVaBaseCustomFormFieldProject(
              this.$route.params.id,
              this.typeClassfiy,
              params
            )
          : await apiVaBaseCustomFormField(this.typeClassfiy, params)

      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach((element) => {
        element.placeholder = JSON.parse(element.config)?.placeholder
        element.multiple = JSON.parse(element.config)?.multiple
        element.message = JSON.parse(element.config)?.message
        element.validator = JSON.parse(element.config)?.validator
        element.defaultTime = JSON.parse(element.config)?.defaultTime
        element.options = !element.isBuilt
          ? JSON.parse(element.config)?.options
          : []
        element.disabled = !!(
          element.key == 'planId' &&
          this.sprintId &&
          this.sprintId != -1
        )
        element.type = element.type.code
        element.precision = JSON.parse(element.config)?.precision

        element.defaultValue = JSON.parse(element.config)?.defaultValue
        element.relationShipsheet = JSON.parse(
          element.config
        )?.relationShipsheet

        // if (element.type == 'SELECT' || element.type == 'LINKED' || element.type == 'INT') {
        //   this.$set(this.form, element.key, JSON.parse(element.config)?.defaultValue)
        // }
        if (element.type == 'DATE' && !this.form[element.key]) {
          this.$set(
            this.form,
            element.key,
            JSON.parse(element.config)?.defaultValue == 'now'
              ? dayjs().format('YYYY-MM-DD HH:mm:ss')
              : ''
          )
        } else if (
          (element.type == 'SELECT' ||
            element.type == 'LINKED' ||
            element.type == 'INT') &&
          !this.form[element.key]
        ) {
          this.$set(
            this.form,
            element.key,
            JSON.parse(element.config)?.defaultValue
          )
        } else if (element.key == 'tagId') {
          element.multiple = true
        }
        const rulesObj = {
          required: element.isRequired,
          message: element.message ? element.message : '该字段为必填项',
          max: element.max,
          pattern: element.validator,
          key: element.key,
          type: element.multiple
            ? 'array'
            : element.type == 'INT'
            ? 'number'
            : 'string',
        }
        if (element.type == 'LINKED') {
          this.rules[element.key] = [
            { required: true, message: element.message },
          ]
        } else if (element.type == 'FILE') {
          this.rules[element.key] = [
            {
              required: true,
              message: element.message ? element.message : '该字段为必填项',
            },
          ]
        } else {
          this.rules[element.key] = [rulesObj]
        }
      })
      this.formList = res.data.filter((e) => {
        return e.isRequired && e.key != 'typeCode' && !this.form[e.key]
      })
      this.getOption()
    },
    getOption() {
      this.getOrgList()
      this.getSourceList()
      this.getPrioritList()
      this.getProjectList()

      // 项目下查询指定接口，查询开启状态的类型
      if (this.$route.params.projectKey) {
        // this.getAllTypeCode()
        this.getRequirementList()
        this.getBugList()
        this.getPlanList(this.$route.params.id)
        this.getProjectProductList()
      } else {
        this.getIssueType()
        this.productList()
      }

      this.typeClassfiy == 'ISSUE' ? this.getIdeaList() : null
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
      })
      this.$forceUpdate()
    },
    // 保存
    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (error) {
        return error
      }

      try {
        this.saveLoading = true
        const params = {
          ...this.form,
        }

        // 判断key里面有没有C1到C30的，有的话，他的值如果是[],就是用逗号分隔成字符串传给后端
        var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
        const multipleList = this.formList
          .filter((r) => r.multiple)
          .map((r) => r.key)
        for (var item in params) {
          if (item.key === 'echoMap') {
            delete params[item.key]
          }
          if (arr.includes(item)) {
            if (multipleList.includes(item) && Array.isArray(params[item])) {
              params[item] = params[item].map((r) => r).join(',') || null
            }
          }
        }
        const formData = {
          itemId: this.dataId,
          targetTypeCode: this.form.typeCode,
          typeClassify: this.typeClassfiy,
          ...params,
        }
        const res = await getTargetTypeCode(
          this.typeClassfiy,
          this.dataId,
          this.form.typeCode,
          formData
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('变更成功')
        this.$emit('success')
        this.close()
      } catch (e) {
        this.saveLoading = false
      }
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeSelect() {
      this.isChange = true
    },

    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    eventDisposalRangeChange(value) {
      const textLength = this.$refs.editor[0].$el.innerText.replace(
        /[|]*\n/,
        ''
      ).length

      if (textLength >= 1000) {
        this.$refs.form.validateField(['description'])
      } else {
        this.$refs.form.clearValidate(['description'])
      }
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }
      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    // 查询项目下开启了的类型
    async getAllTypeCode() {
      const res = await apiAlmGetTypeNoPage(
        this.$route.params.id,
        this.typeClassfiy
      )
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      this.setData(this.formList, 'typeCode', res.data)
    },

    // 查询所有类型
    async getIssueType() {
      const res = await apiAlmTypeNoPage({
        classify: this.typeClassfiy,
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'typeCode', res.data)
    },
    // 查项目下需求
    async getRequirementList(e) {
      const res = await requirementListByCondition({
        projectId: this.$route.params.id,
        name: e,
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.formList, 'requirementId', res.data)
    },
    // 查项目下缺陷
    async getBugList(e) {
      const res = await bugListByCondition({
        projectId: this.$route.params.id,
        name: e,
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'bugId', res.data)
    },

    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeClassfiy,
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'sourceCode', res.data)
    },
    // 查询项目关联的产品，用于缺陷和任务表单关联产品【主办/辅办】
    async getProjectProductList() {
      const res = await getAllProductInfoList(this.$route.params.id)
      if (!res.isSuccess) {
        return
      }
      this.setData(this.formList, 'productId', res.data)
    },
    // 关联产品版本
    async getVersion(val) {
      if (this.isChange) {
        this.$set(this.form, 'productVersionId', '')
        this.$set(this.form, 'productRepairVersionId', '')
      }

      const res = await getProductVersionList({ productId: val })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.formList, 'productVersionId', res.data)
      this.setData(this.formList, 'productRepairVersionId', res.data)
    },
    // 关联产品功能
    async getModel(val) {
      if (this.isChange) {
        this.$set(this.form, 'productModuleFunctionId', null)
      }

      const res = await getModule({ productId: val })
      if (!res.isSuccess) {
        return
      }
      this.setData(
        this.formList,
        'productModuleFunctionId',
        this.getTreeList(res.data)
      )
    },
    getTreeList(data, obj = []) {
      data.map((item) => {
        const type =
          item.nodeType == 1 ? ' (模块)' : item.nodeType == 2 ? ' (功能)' : ''
        item.label = item.name + type
        if (item.children && item.children.length > 0) {
          this.getTreeList(item.children)
        }
      })
      return data
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'priorityCode', res.data)
    },

    // 迭代计划
    async getPlanList(val, e) {
      if (this.isChange) {
        this.$set(this.form, 'planId', '')
      }
      const res = await planListByCondition({
        projectId: val,
        name: e,
      })
      if (!res.isSuccess) {
        return
      }

      this.planIdList = this.sprintId
        ? res.data.filter((r) => r.stateCode != 4)
        : res.data
      this.setData(this.formList, 'planId', this.planIdList)
    },

    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'productId', res.data)
    },
    // 关联用户需求
    async getIdeaList() {
      const res = await ideaListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'ideaId', res.data)
    },

    // 归属项目
    async getProjectList(e) {
      const res = await queryListByCondition({
        name: e,
      })

      if (!res.isSuccess) {
        return
      }

      this.setData(this.formList, 'projectId', res.data)
    },
    dataSelectChange({ item, list, user }, key) {
      this.formList.forEach((e) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          if (key == config.queryCriteriaC) {
            this.form[e.key] = item[config.relationField]
            const obj = list.find(
              (e) => e.id == config.relationField && !e.primary
            )
            e.quoteType = obj.type
            if (e.quoteType == 'user') {
              e.user = user[item[config.relationField]]
            }
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.model-title {
  font-weight: 500;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  color: #2c2e36;
  border-left: 4px solid #3e7bfa;
  padding-left: 10px;
  margin: 10px 0;
}
.dialog {
  :deep(.el-dialog__body) {
    height: 400px;
    overflow-y: scroll;
    padding: 16px !important;
  }
  :deep(.el-form-item) {
    margin-bottom: 16px !important;
    // max-height: 70px;
  }
  :deep(.el-form-item__label) {
    margin-top: 6px !important;
    color: #202124 !important;
    // padding: 0 0 10px !important;
    // margin-bottom: 6px !important;
  }
  :deep(.el-input--small .el-input__inner) {
    border: 1px solid #c1c8d6 !important;
    height: 32px !important;
    line-height: 32px !important;
    border-radius: 2px !important;
  }
  :deep(.el-form-item--small .el-form-item__error) {
    padding-top: 8px !important;
  }

  .row-box {
    display: flex;
    flex-wrap: wrap;
  }
}
</style>
