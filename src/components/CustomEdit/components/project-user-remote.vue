<template>
  <!-- 项目成员 -->
  <el-select
    filterable
    remote
    clearable=""
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    loading-text="正在查询..."
    no-match-text="暂未查询到匹配数据,请重新输入"
    :loading="loading"
    v-bind="$attrs"
    popper-class="remote-user"
    :multiple="multiple"
    @change="(val) => watchInput(val)"
  >
    <!-- noName 回显时不显示头像，用于工作项编辑时 -->
    <div v-if="noName && !multiple" slot="prefix" class="select-header">
      <vone-user-avatar
        v-if="$attrs.value && selectIconVal"
        :avatar-path="selectIconVal.avatarPath"
        :avatar-type="selectIconVal.avatarType"
        :show-name="false"
        height="20px"
        width="20px"
      />
      <el-icon class="iconfont" style="font-size: 20px"
        ><ElIconUser
      /></el-icon>
    </div>
    <el-option
      v-for="item in selectData"
      :key="item.id"
      :label="item.name"
      :value="item.id || item.userId"
    >
      <vone-user-avatar
        :avatar-path="item.avatarPath"
        :avatar-type="item.avatarType"
        :show-name="false"
        height="20px"
        width="20px"
      />
      {{ item.name }}
    </el-option>
  </el-select>
</template>

<script>
// import { IconLightAvatar as ElIconIconLightAvatar } from '@element-plus/icons-vue'
import { debounce } from 'lodash'
import { getUser } from '@/utils/auth'
import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { getUserDetail } from '@/api/vone/base/user'

export default {
  components: {
    // ElIconIconLightAvatar,
  },
  name: 'RemoteUser',
  props: {
    placeholder: {
      type: String,
      default: '输入用户名称查询',
    },
    defaultData: {
      type: Array,
      default: () => [],
    },
    default: {
      type: String,
      default: '',
    },
    noName: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectData: [],
      loading: false,
      selectIconVal: null,
      defaultUser: getUser(),
      defaultoption: '',
    }
  },
  watch: {
    '$attrs.value': {
      handler: function (val, old) {
        if (val != old) {
          const defData = [...this.defaultData, this.defaultUser]
          this.selectData = this.removeSomeData(defData)
        }
        this.defaultoption = val
      },
      deep: true,
    },
  },
  mounted() {
    const defData = [...this.defaultData, this.defaultUser]
    this.selectData = this.removeSomeData(defData)
    // 初始化设置选中数据
    this.watchInput(this.$attrs.value)
    const unwatch = this.$watch('defaultoption', (val, old) => {
      if (val) {
        if (Array.isArray(val) && val.length > 0) {
          val.forEach((r) => {
            this.getOptionlist(r)
          })
          unwatch()
        }
        if (typeof val == 'string' && val) {
          this.getOptionlist(val)
          unwatch()
        }
      }
    })
  },
  methods: {
    getOptionlist(e) {
      getUserDetail(e).then((res) => {
        if (res.isSuccess) {
          const remoteData = [this.defaultUser, ...[res.data]]
          this.selectData = this.removeSomeData(remoteData)
          this.watchInput(e)
        }
      })
    },
    remoteMethod: debounce(function (query) {
      if (!this.$route.params.id) {
        return
      }

      this.loading = true
      if (query != '') {
        apiProjectUserNoPage({
          keyword: query,
          projectId: this.$route.params.id,
        }).then((res) => {
          if (res.isSuccess) {
            const remoteData = [this.defaultUser, ...res.data]
            const defData = [
              this.defaultUser,
              ...this.defaultData,
              ...this.removeSomeData(remoteData),
            ]
            this.selectData = this.removeSomeData(defData)
            this.$emit('getUser', res.data)
          } else {
            this.$message.warning(res.msg)
          }
          this.loading = false
        })
      }
    }, 1000),
    watchInput(val) {
      this.$emit('inputChange', val)
      // 返回选中的人员数据列表
      this.$emit(
        'updateSelectedRow',
        this.selectData.filter((v) => v.id === val) || []
      )
      if (val && this.selectData.length > 0) {
        this.selectIconVal = this.selectData.find((item) => item.id == val)
      } else {
        this.selectIconVal = null
      }
    },
    // 用户去重
    removeSomeData(data) {
      let resultData = []
      var obj = {}
      resultData = data.reduce((item, next) => {
        obj[next.id] ? '' : (obj[next.id] = true && item.push(next))
        return item
      }, [])
      return resultData
    },
  },
}
</script>

<style lang="scss" scoped>
.remote-user {
  padding-right: 10px;
}
:deep(.select-header) {
  min-width: 28px;
  height: 100%;
  display: flex;
  align-items: center;
  text-align: center;
  font-size: 14px;
  color: #c1c8d6;
}
.el-select {
  :deep(.el-select__caret:first-child::before) {
    content: '\e6e1';
  }
  :deep(.is-focus) {
    .el-select__caret:first-child {
      transform: rotateZ(0deg);
    }
  }
}
</style>

<style lang="scss">
.remote-user {
  .el-select-dropdown__wrap {
    .el-select-dropdown__item {
      display: flex;
      align-items: center;
      .avatar {
        padding-right: 6px;
      }
    }
  }
}
</style>
