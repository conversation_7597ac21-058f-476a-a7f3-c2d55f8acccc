import { registerNode } from '@antv/g6'
// 设置各模块节点显示的标题
const titleFn = function (e) {
  const map = {
    repositery: e.path,
    branch: e.branch?.branchName ?? '',
    pipeline: e.pipeline?.name ?? '',
    other: e.name ?? '',
  }
  return map[e.types] || map.other
}
// 设置各模块节点icon图标和颜色
const iconReflect = (e) => {
  const map = {
    other: {
      iconText: '\ue697',
      iconColor: '#8a8f99',
    },
    // 需求
    requirement: {
      iconText: '\ue697',
      iconColor: '#8791FA',
    },
    // 制品
    package: {
      iconText: '\ue679',
      iconColor: '#8791FA',
    },
    // 代码库
    repositery: {
      iconText: '\ue602',
      iconColor: '#E24329',
    },
    // 分支
    branch: {
      iconText: '\ue65d',
      iconColor: '#3E7BFA',
    },
    // 迭代
    iteration: {
      iconText: '\ue623',
      iconColor: '#3E7BFA',
    },
    // 缺陷
    bug: {
      iconText: '\ue693',
      iconColor: '#FA6B57',
    },
    // 测试计划
    testPlan: {
      iconText: '\ue621',
      iconColor: '#3E7BFA',
    },
    // 流水线
    pipeline: {
      iconText: e.pipeline?.mode?.code == 'CUSTOM' ? '\ue6c4' : '\ue6c5',
      iconColor: e.pipeline?.mode?.code == 'CUSTOM' ? '#64BEFA' : '#BD7FFA',
    },
  }
  return map[e.types] || map.other
}
function initNode() {
  const itemHeight = 100
  const avatar = require('@/assets/avatar/avatar1.png')
  registerNode('dice-er-box', {
    draw(cfg, group) {
      const width = 248
      const height = 600
      const itemCount = 7
      const boxStyle = {
        stroke: '#000000',
        // fill: '#EBEEF5',
        lineWidth: 0,
        radius: 4,
      }

      const { attrs = [], startIndex = 0 } = cfg
      // 节点显示区域数据
      const afterList = attrs.slice(
        Math.floor(startIndex),
        Math.floor(startIndex + itemCount - 1)
      )
      const offsetY = (0.5 - (startIndex % 1)) * itemHeight + 30

      // 设置最外层容器
      group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          height: height + 31,
          width,
          fill: '#EBEEF5',
          cursor: 'pointer',
        },
      })

      group.addShape('text', {
        attrs: {
          x: -5,
          y: 40,
          height: 31,
          width: width + 5,
          fontFamily: 'iconfont', // 对应css里面的font-family: "iconfont";
          text: '\ue62b',
          // ariaHidden: true,
          fontSize: 31,
          fill:
            cfg.id == 'requirement'
              ? '#8790FA'
              : cfg.id == 'task'
              ? '#FFBF47'
              : cfg.id == 'development'
              ? '#37CDDE'
              : cfg.id == 'testing'
              ? '#FC9772'
              : '#4BCCBB', // 这里去掉会变成透明
        },
      })

      group.addShape('text', {
        attrs: {
          y: 28,
          x: 110,
          fill: '#fff',
          text: cfg.label,
          fontSize: 12,
          fontWeight: 500,
        },
      })

      // 节点容器
      const keyshape = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          width,
          height,
          ...boxStyle,
        },
        draggable: true,
      })

      const listContainer = group.addGroup({})
      // 设置节点容器区域
      listContainer.setClip({
        type: 'rect',
        attrs: {
          x: -8,
          y: 30 + 15,
          width: width + 16,
          height: height - 25,
        },
      })
      listContainer.addShape({
        type: 'rect',
        attrs: {
          x: 1,
          y: 30 + 15,
          width: width - 2,
          height: height - 50 - 15,
          // fill: '#fff'
        },
        draggable: true,
      })
      // 滚动条样式
      const barStyle = {
        width: 4,
        padding: 0,
        boxStyle: {
          stroke: '#00000022',
        },
        innerStyle: {
          fill: '#00000022',
        },
      }
      // 添加滚动条区域
      listContainer.addShape('rect', {
        // 滚动条后面的滚动区域高度
        attrs: {
          y: 30 + 15,
          x: width - barStyle.padding - barStyle.width,
          width: barStyle.width,
          height: height - 30 - 15 + 30,
          // ...barStyle.boxStyle
        },
      })

      const indexHeight =
        afterList.length > itemCount
          ? (afterList.length / attrs.length) * height
          : 100

      listContainer.addShape('rect', {
        // 滚动条
        attrs: {
          y:
            30 + barStyle.padding + (startIndex / attrs.length) * (height - 30),
          x: width - barStyle.padding - barStyle.width,
          width: barStyle.width,
          height: Math.min(height, indexHeight),
          // fill: '#fff', // config.bgColor, F7F9FC
          // ...barStyle.innerStyle
        },
      })
      if (afterList?.length > 0) {
        afterList.forEach((e, i) => {
          listContainer.addShape('rect', {
            // 小卡片
            attrs: {
              x: 5,
              y: i * itemHeight - itemHeight / 2 + offsetY + 15,
              width: width - 10,
              height: itemHeight - 10,
              radius: 2,
              lineWidth: 1,
              cursor: 'pointer',
              fill: '#fff',
              ...boxStyle,
            },
            visible: e.types != 'placeholder',
            name: `item-${Math.floor(startIndex) + i}-content`,
            draggable: false,
          })
          /* 左边的粗线 */
          if (e.types == 'requirement') {
            listContainer.addShape('rect', {
              attrs: {
                x: 5,
                y: i * itemHeight - itemHeight / 2 + offsetY + 15,
                width: 3,
                height: itemHeight - 10,
                fill: '#5B8FF9',
                radius: 1.5,
              },
              name: 'left-border-shape',
            })
          }
          /* icon*/
          const { iconText, iconColor } = iconReflect(e)

          // icon图标
          listContainer.addShape('text', {
            attrs: {
              x: 15,
              y: i * itemHeight + offsetY - 6,
              height: 20,
              width: 20,
              fontFamily: 'iconfont', // 对应css里面的font-family: "iconfont";
              text: iconText,
              // ariaHidden: true,
              fontSize: 20,
              fill: iconColor, // 这里去掉会变成透明
            },
            visible: e.types != 'placeholder',
            name: 'icon-shape',
          })
          const stageTitle = titleFn(e)
          /* 标题 */
          listContainer.addShape('text', {
            attrs: {
              x: 40,
              y: i * itemHeight + offsetY - 10,
              text:
                stageTitle.length > 18
                  ? stageTitle.slice(0, 17) + '...'
                  : stageTitle,
              fontSize: 12,
              fill: '#000',
              cursor: 'pointer',
            },
            name: 'name-text-shape',
          })
          // 设置需求节点显示内容
          if (e.types == 'requirement') {
            const user =
              e.echoMap?.handleBy ??
              e.echoMap?.putBy ??
              e.echoMap?.leadingBy ??
              {}
            // 替换标签
            const desc = e.description?.replace(/<\/?.+?\/?>/g, '')
            // 描述文字
            listContainer.addShape('text', {
              attrs: {
                text:
                  desc?.length > 22 ? desc.slice(0, 21) + '...' : desc ?? '',
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-desc-shape',
            })
            // 头像
            listContainer.addShape('image', {
              attrs: {
                x: 15,
                y: i * itemHeight + offsetY + 26,
                width: 20,
                height: 20,
                img: user.avatarPath
                  ? require(`@/assets/avatar/${user.avatarPath}.png`)
                  : avatar,
              },
              // visible: e.types == 'requirement',
              name: 'icon',
            })
            // 人员name
            listContainer.addShape('text', {
              attrs: {
                text: user.name ?? e.handleBy ?? '',
                x: 45,
                y: i * itemHeight + offsetY + 42,
                fontSize: 10,
                textAlign: 'left',
                fill: '#8A8F99',
              },
              name: 'username',
            })
            // 需求时间
            listContainer.addShape('text', {
              attrs: {
                text: (e.updateTime ?? e.createTime).split(' ')[0],
                x: 180,
                y: i * itemHeight + offsetY + 42,
                fontSize: 10,
                textAlign: 'left',
                fill: '#8A8F99',
              },
              name: 'username',
            })
          }
          // 设置分支显示内容
          if (e.types == 'branch') {
            // 版本描述
            listContainer.addShape('text', {
              attrs: {
                text: e.branch?.description,
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-version-shape',
            })
            // 分支数量
            listContainer.addShape('text', {
              attrs: {
                text: '提交记录' + (e.commitLog?.length ?? 0) + '条',
                x: 15,
                y: i * itemHeight + offsetY + 26,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-text-shape',
            })
          }
          // 设置迭代显示内容
          if (e.types == 'iteration') {
            const text =
              (e.planStime
                ? e.planStime.split(' ')[0].replace(/-/g, '.') + '-'
                : '') +
              (e.planEtime ? e.planEtime.split(' ')[0].replace(/-/g, '.') : '')
            // 迭代的时间
            listContainer.addShape('text', {
              attrs: {
                text: text,
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
              },
              name: 'bottom-text-shape',
            })
          }
          // 设置缺陷显示内容
          if (e.types == 'bug') {
            // 描述
            listContainer.addShape('text', {
              attrs: {
                text: e.description,
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'desc-shape',
            })
            listContainer.addShape('text', {
              attrs: {
                text: '创建时间: ' + e.createTime,
                x: 15,
                y: i * itemHeight + offsetY + 26,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
              },
              name: 'text-shape',
            })
          }
          if (e.types === 'testPlan') {
            const text =
              (e.planStime
                ? e.planStime.split(' ')[0].replace(/-/g, '.') + '-'
                : '') +
              (e.planEtime ? e.planEtime.split(' ')[0].replace(/-/g, '.') : '')
            // 迭代的时间
            listContainer.addShape('text', {
              attrs: {
                text: text,
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
              },
              name: 'bottom-text-shape',
            })
          }
          // 设置流水线显示内容
          if (e.types == 'pipeline') {
            // 流水线的时间
            listContainer.addShape('text', {
              attrs: {
                text: '最后执行: ' + (e.pipeline.updateTime ?? ''),
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-text-shape',
            })
          }
          // 开发节点代码库显示内容
          if (e.types == 'repositery') {
            listContainer.addShape('text', {
              attrs: {
                text: e.url,
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-text-shape',
            })
          }
          // 设置版本显示内容
          if (e.types == 'package') {
            // 版本号
            listContainer.addShape('text', {
              attrs: {
                text: '版本：' + (e.version ?? ''),
                x: 15,
                y: i * itemHeight + offsetY + 6,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-text-shape',
            })
            // 版本更新时间
            listContainer.addShape('text', {
              attrs: {
                text: '更新时间' + (e.updateTime ?? ''),
                x: 15,
                y: i * itemHeight + offsetY + 26,
                fontSize: 10,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#8A8F99',
                cursor: 'pointer',
              },
              name: 'bottom-text-shape',
            })
          }
        })
      }

      return keyshape
    },
    getAnchorPoints() {
      return [
        [0, 0],
        [1, 0],
      ]
    },
  })
}

export default initNode
