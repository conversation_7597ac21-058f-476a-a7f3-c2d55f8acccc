<template>
  <div id="graph" ref="graph" class="graph" />
</template>

<script>
import G6 from '@antv/g6'
import { rowData } from './data1'
import initBehavior from './initBehavior'
import initNode from './initNode'
import initEdge from './initEdge'

export default {
  name: 'GraphGroup',
  props: {
    graphData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      data: rowData,
      timer: null,
      graph: null,
      container: null,
    }
  },
  async mounted() {
    // 注册自定义行为
    initBehavior()
    // 注册自定义边
    initEdge()
    // 注册自定义节点
    initNode()
    await this.initGraph(rowData)
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.graph = null
    this.container = null
  },
  methods: {
    dataTransform(data) {
      const nodes = []
      const edges = []

      data.map((node) => {
        nodes.push({
          ...node,
        })
        if (node.attrs) {
          node.attrs.forEach((attr) => {
            if (attr.relation) {
              attr.relation.forEach((relation) => {
                edges.push({
                  lineType: relation.lineType,
                  source: node.id, // 起始节点
                  target: relation.nodeId, // 结束节点
                  sourceKey: attr.key || attr.id,
                  targetKey: relation.key || relation.id,
                })
              })
            }
          })
        }
      })
      return {
        nodes,
        edges,
      }
    },
    initGraph(data) {
      const container = document.querySelector('#graph')
      this.container = container
      const width = container.clientWidth
      const height = container.clientHeight

      const graph = new G6.Graph({
        container: container,
        width,
        height,
        fitView: true,
        defaultNode: {
          size: [248, 600],
          type: 'dice-er-box',
          color: '#5B8FF9',
          style: {
            fill: '#9EC9FF',
            lineWidth: 3,
          },
          labelCfg: {
            style: {
              fill: 'black',
              fontSize: 20,
            },
          },
        },
        defaultEdge: {
          type: 'dice-er-edge',
          style: {
            stroke: '#e2e2e2',
            lineWidth: 4,
            endArrow: true,
          },
        },
        modes: {
          default: ['dice-er-scroll', 'drag-node', 'drag-canvas'],
        },
        layout: {
          type: 'dagre',
          rankdir: 'LR',
          align: 'UL',
          preventOverlap: true,
          controlPoints: true,
          nodesepFunc: () => 20,
          ranksepFunc: () => 0.5,
        },
        animate: true,
      })
      this.graph = graph

      // graph.data(this.dataTransform(this.graphData))
      // graph.render()
      // graph.fitView()
      // graph.zoom(0.88)
      // graph.translate(-width / 3 - 114, -height / 3)
    },
    refreshGrap() {
      this.graph.data(this.dataTransform(this.graphData))
      this.graph.render()
      this.graph.fitView()
      this.graph.zoom(0.88)
      this.graph.translate(
        -this.container.clientWidth / 3 - 114,
        -this.container.clientHeight / 3
      )
    },
    handleWindowResize() {
      window.onresize = () => {
        if (!this.graph || this.graph.get('destroyed')) return
        if (
          !this.container ||
          !this.container.scrollWidth ||
          !this.container.scrollHeight
        )
          return
        if (this.timer) clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.graph.changeSize(
            this.container.scrollWidth,
            this.container.scrollHeight
          )
          this.graph.fitView()
        }, 200)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.graph {
  overflow: overlay;
  min-height: calc(100vh - 92px);
  background: var(--main-bg-color, #fff);
}
</style>
