<template>
  <span v-if="userAvatar" class="avatar">
    <img :src="userAvatar" alt="" :width="width" :height="height" />
    <span v-if="showName">{{ name }}</span>
  </span>
  <span v-else class="avatar">
    <el-icon class="iconfont" style="font-size: 20px"
      ><el-icon-icon-light-avatar
    /></el-icon>
    <span v-if="noData" class="noData">未设置</span>
  </span>
</template>

<script>
import { IconLightAvatar as ElIconIconLightAvatar } from '@element-plus/icons-vue'

import { avatarMap } from '@/assets/avatar/avatar'

export default {
  components: {
    ElIconIconLightAvatar,
  },
  name: 'UserAvatar',
  props: {
    avatarPath: {
      type: String,
      default: null,
    },
    avatarType: {
      type: Boolean,
      default: true,
    },
    name: {
      type: String,
      default: null,
    },
    width: {
      type: String,
      default: '24px',
    },
    height: {
      type: String,
      default: '24px',
    },
    showName: {
      type: Boolean,
      default: true,
    },
    noData: {
      // 为了处理工作项里筛选条件里默认要加上一个没有处理人的数据
      type: Boolean,
      default: true,
    },
  },
  data: function () {
    return {
      userAvatar: null,
      avatarMap,
    }
  },
  watch: {
    avatarPath: {
      handler: function (val) {
        this.userAvatar = this.avatarType
          ? this.avatarMap[val] && this.avatarMap[val].src
          : `http://${window.location.host}/api/base/webapps/${val}`
      },
      deep: true,
    },
  },
  mounted() {
    // this.userAvatar = this.avatarPath ? `http://${window.location.host}/api/base/webapps/${this.avatarPath}` : this.avatarMap[this.avatarPath].src
    this.userAvatar = this.avatarType
      ? this.avatarMap[this.avatarPath] && this.avatarMap[this.avatarPath].src
      : `http://${window.location.host}/api/base/webapps/${this.avatarPath}`
  },
}
</script>

<style lang="scss" scoped>
.avatar {
  display: flex;
  align-items: center;
  color: var(--col-no-setting);
  .noData {
    color: var(--col-no-setting);
    font-size: 14px;
  }
  span {
    margin-left: 8px;
    color: var(--main-font-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
