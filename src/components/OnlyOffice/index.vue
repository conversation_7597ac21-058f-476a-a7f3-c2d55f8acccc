<template>
  <el-dialog
    class="only-office-dialog"
    :show-close="false"
    fullscreen
    :model-value="visible"
    :before-close="back"
    append-to-body
  >
    <div slot="title">
      <vone-back :title="pagetitle" class="toolbar" @back="back">
        {{ pagetitle }}
        <span slot="toolbar">
          <el-button
            type="text"
            :icon="ElIconIconfont elIconEditDownload"
            @click="downLoad"
            >下载</el-button
          >
          <el-button
            v-if="showDel"
            type="text"
            :icon="ElIconIconfont elIconApplicationDelete"
            @click="delFile"
            >删除</el-button
          >
          <el-button
            type="text"
            style="width: 30px"
            :icon="ElIconIconfont elIconTipsClose"
            @click="back"
          />
        </span>
      </vone-back>

      <!-- <div class="dialog-title">
          <span style="flex: 1">{{ pagetitle }}</span>
          <span>
            <el-button type="primary" @click="back">返回</el-button>
          </span>
        </div> -->
    </div>
    <div id="VOnlyOffice" class="only-office" />
  </el-dialog>
</template>

<script>
import { onlyofficeConfig } from '@/api/common'
import loadScript from '@/utils/load-script'
import { download } from '@/utils'
import { apiBaseFileLoadById } from '@/api/vone/base/file'
export default {
  name: 'VOnlyOffice',
  props: {
    option: {
      type: Object,
      default: () => {
        return {}
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    showDel: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      pagetitle: '',
      doctype: '',
      docEditor: null,
      isEdit: false,
      serverUrl:
        process.env.NODE_ENV === 'development'
          ? '*************:58124'
          : window.location.host,
    }
  },
  watch: {
    option: {
      handler: function (n) {
        if (n.id) {
          const userId = this.$store.state.user.user.id
          n.url = `http://${this.serverUrl}/api/base/base/file/noToken/download/${n.id}`
          n.callbackUrl = `http://${this.serverUrl}/api/base/base/file/noToken/save/${n.id}/${userId}`
          this.setEditor(n)
          this.doctype = this.getFileType(n.fileType)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  beforeDestroy() {
    if (this.docEditor !== null) {
      this.docEditor.destroyEditor()
      this.docEditor = null
    }
  },
  async created() {
    await onlyofficeConfig().then((res) => {
      if (res.isSuccess) {
        loadScript(
          `${res.data.url}/web-apps/apps/api/documents/api.js`,
          'onlyoffice-script'
        )
        this.isEdit = res.edit
      }
    })
  },
  methods: {
    // backRouter() {

    // },
    back() {
      if (this.docEditor !== null) {
        this.docEditor.destroyEditor()
        this.docEditor = null
      }
      this.$emit('update:visible', false)
    },
    async setEditor(option) {
      if (this.docEditor !== null) {
        this.docEditor.destroyEditor()
        this.docEditor = null
      }
      this.doctype = this.getFileType(option.fileType)

      this.pagetitle = option.title
      const config = {
        document: {
          // 后缀
          fileType: option.fileType,
          key: option.key || '',
          title: option.title,
          permissions: {
            edit: this.isEdit, // 是否可以编辑: 只能查看，传false
            print: false,
            download: false,
            // "fillForms": true,//是否可以填写表格，如果将mode参数设置为edit，则填写表单仅对文档编辑器可用。 默认值与edit或review参数的值一致。
            // "review": true //跟踪变化
          },
          url: option.url,
        },
        documentType: this.doctype,
        editorConfig: {
          // callbackUrl: option.editUrl, // "编辑word后保存时回调的地址，这个api需要自己写了，将编辑后的文件通过这个api保存到自己想要的位置
          callbackUrl: option.callbackUrl || '',
          lang: 'zh-CN', // 语言设置
          // 定制
          customization: {
            // autosave: false, // 是否自动保存
            forcesave: true,
            chat: false,
            comments: false,
            help: false,
            // "hideRightMenu": false,//定义在第一次加载时是显示还是隐藏右侧菜单。 默认值为false
            // 是否显示插件
            plugins: false,
          },
          user: {
            id: option?.user?.id,
            name: option?.user?.name,
          },
          mode: option.model ? option.model : 'edit',
        },
        width: '100%',
        height: '100%',
      }
      // eslint-disable-next-line no-undef,no-unused-vars
      this.docEditor = new DocsAPI.DocEditor('VOnlyOffice', config)
    },
    getFileType(fileType) {
      let docType = ''
      const fileTypesDoc = [
        'doc',
        'docm',
        'docx',
        'dot',
        'dotm',
        'dotx',
        'epub',
        'fodt',
        'htm',
        'html',
        'mht',
        'odt',
        'ott',
        'pdf',
        'rtf',
        'txt',
        'djvu',
        'xps',
      ]
      const fileTypesCsv = [
        'csv',
        'fods',
        'ods',
        'ots',
        'xls',
        'xlsm',
        'xlsx',
        'xlt',
        'xltm',
        'xltx',
      ]
      const fileTypesPPt = [
        'fodp',
        'odp',
        'otp',
        'pot',
        'potm',
        'potx',
        'pps',
        'ppsm',
        'ppsx',
        'ppt',
        'pptm',
        'pptx',
      ]
      if (fileTypesDoc.includes(fileType)) {
        docType = 'text'
      }
      if (fileTypesCsv.includes(fileType)) {
        docType = 'spreadsheet'
      }
      if (fileTypesPPt.includes(fileType)) {
        docType = 'presentation'
      }
      return docType
    },
    // 单个下载
    async downLoad() {
      download(
        this.option.originalFileName,
        await apiBaseFileLoadById([this.option.id])
      )
    },
    delFile() {
      this.$emit('delFile', this.option)
    },
  },
}
</script>

<style lang="scss" scoped>
.only-office-dialog {
  .dialog-title {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.el-dialog) {
    min-height: 100vh;
    border-radius: 0;
  }
  :deep(.el-dialog__body) {
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
  }
}
// .only-office {
// 	height: calc(100vh - 100px);
// }
:deep(.el-dialog .el-dialog__header) {
  border-bottom: none;
  padding: 12px 20px 0 20px;

  .el-button {
    color: #777f8e;
    &:hover {
      color: var(--main-theme-color);
    }
  }
}

:deep(.el-button + .el-button) {
  margin-left: 0px !important;
}
.header {
  margin-left: -20px;
  margin-right: -20px;
  padding: 8px 0px 8px 16px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
}
.toolbar {
  :deep(.el-button) {
    padding: 0 !important;
  }
}
</style>
