<template>
  <div @click="clickTag('true')">
    <el-tag v-if="showValue" type="danger">
      {{ optionMap[selectVal] }}
    </el-tag>

    <el-select
      v-if="!showValue"
      ref="vone-select-tag"
      v-model="selectVal"
      class="vone-select-tag"
      v-bind="$attrs"
      automatic-dropdown
      @blur.native.capture="blur(selectVal)"
    >
      <el-option
        v-for="item in optionList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'VoneSelectTag',
  props: {
    label: {
      type: String,
      default: () => '',
    },
    optionList: {
      type: Array,
      default: () => [],
    },
    // eslint-disable-next-line vue/require-default-prop
    formValue: [String, Array, Number],
  },
  data() {
    return {
      autoDropdown: true,
      showValue: true,
      optionMap: {},
      selectVal: this.formValue,
    }
  },
  mounted() {
    if (this.formValue && this.optionList.length) {
      this.showValue = true
      this.optionMap = this.optionList.reduce(
        (r, v) => (r[v.id] = v.name) && r,
        {}
      )
    } else {
      this.showValue = false
    }
  },
  methods: {
    clickTag(val) {
      // this.autoDropdown = true
      if (val === 'true') {
        this.showValue = false
      } else {
        this.showValue = true
      }

      this.$nextTick(() => {
        this.$refs['vone-select-tag'].focus()
      })

      // this.$refs['vone-select-tag'].focus()
    },
    changSelect(val) {
      // this.$emit('update:formValue', val)
      // this.selectVal = val
      this.showValue = true
    },
    blur(val) {
      if (this.$refs['vone-select-tag'].hoverOption.value !== 'undefined') {
        this.selectVal = this.$refs['vone-select-tag'].hoverOption.value
      } else {
        this.selectVal = this.$refs['vone-select-tag'].value
      }

      this.showValue = true
    },
  },
}
</script>

<style lang="scss" scoped>
.vone-select-tag {
  padding-right: 10px;
}
</style>
