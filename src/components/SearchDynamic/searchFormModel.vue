<template>
  <div class="padding16">
    <section
      v-for="(item, index) in fieldFormList"
      :key="index"
      class="flexRow"
    >
      <span class="flexLabel">
        <el-select
          v-model="item.key"
          placeholder="请选择"
          filterable
          @change="keyChange($event, item, index)"
        >
          <el-option-group
            v-for="group in fieldOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="(e, index) in group.options"
              :key="index"
              :label="e.name"
              :value="e.key"
              :disabled="e.disabled"
            />
          </el-option-group>
        </el-select>
      </span>
      <span class="flexValue">
        <!-- 人员 -->
        <vone-remote-user
          v-if="item.type.code == 'USER'"
          v-model="item.value"
          :collapse-tags="true"
          :no-name="false"
          :multiple="item.multiple"
        />

        <!-- 标签 -->
        <tagSelect
          v-else-if="
            (item.type.code == 'SELECT' && item.key == 'tagId') ||
            item.key == 'tabName'
          "
          v-model="item.value"
          multiple
          is-id
        />
        <!-- 下拉框 -->
        <el-select
          v-else-if="item.type.code == 'SELECT' || item.type.code == 'ICON'"
          v-model="item.value"
          collapse-tags
          :placeholder="item.placeholder || '请选择'"
          filterable
          :multiple="item.multiple"
          :multiple-limit="item.key == 'productId' ? 1 : null"
          @change="onTypeChange($event, item)"
        >
          <el-option
            v-for="o in item.optionList"
            :key="o.id"
            :label="o.name"
            :value="item.valueType == 'id' ? o.id : o.code"
          />
        </el-select>
        <!-- 数字框 -->
        <input-number-range
          v-else-if="item.type.code == 'INT'"
          v-model="item.value"
          :precision="1"
        />
        <!-- 输入框 -->
        <el-input
          v-else-if="item.type.code == 'INPUT' || item.type.code == 'EDITOR'"
          v-model.trim="item.value"
          :placeholder="item.placeholder || '请输入'"
          style="width: 100%"
          :disabled="!item.key ? true : false"
        />
        <!-- 日期 -->
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => dayjs(d, 'hh:mm:ss').toDate())
          "
          v-else-if="item.type.code == 'DATE'"
          v-model="item.value"
          style="width: 100%"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
        <!-- treeSelect -->
        <vone-tree-select
          v-else-if="item.type.code == 'TREE'"
          v-model="item.value"
          search-nested
          :tree-data="item.optionList"
          :placeholder="item.placeholder || '请选择'"
        />
      </span>
      <span>
        <a @click="changeFixed(item, index)">
          <svg v-if="item.isFixed" class="icon" aria-hidden="true">
            <use xlink:href="#el-icon-pin-selected" />
          </svg>
          <svg v-else class="icon" aria-hidden="true">
            <use xlink:href="#el-icon-pin-normal" />
          </svg>
        </a>
        <a @click="deleteRow(index, item)">
          <el-icon class="iconfont"><el-icon-application-delete /></el-icon>
        </a>
      </span>
    </section>

    <a class="addRow" @click="addRow">
      <el-icon><el-icon-plus /></el-icon> 添加条件
    </a>
  </div>
</template>

<script>
import { ApplicationDelete as ElIconApplicationDelete,Plus as ElIconPlus } from '@element-plus/icons'import * as dayjs from 'dayjs';
import tagSelect from '@/components/CustomEdit/components/tag-select.vue'
import { cloneDeep } from 'lodash'

export default {
  components: {
    tagSelect,
    ElIconApplicationDelete,ElIconPlus,
  },
  data() {

return {
  fieldFormList: this.basicFieldFormList,
fieldOptions: [],
  dayjs,
}
},
name: 'SearchFormModel',
props: {
defaultFileds: { // 默认可以选的字段
type: Array,
default: () => []
},
basicFieldFormList: { // 回显的数据
type: Array,
default: () => []
}

},
watch: {
basicFieldFormList: {
handler: function(val) {
  this.$nextTick(() => {
    if (!val) return
    val.forEach(element => {
      element.disabled = !!element.value
    })
    this.fieldFormList = val
  })
},
immediate: true
},
fieldFormList: {
handler: function(val, oldVal) {
  this.$nextTick(() => {
    if (!val) return
    const hasKey = this.fieldFormList.map(r => r.key)
    this.fieldOptions.forEach(element => {
      element.options.forEach(i => {
        if (i.key == 'typeCodes') {
          i.disabled = !!hasKey.includes(i.key) || !hasKey.includes('classify')
        } else {
          i.disabled = !!hasKey.includes(i.key)
        }
      })
    })
    this.$emit('update:basicFieldFormList', val)
  })
},
immediate: true,
deep: true
},
defaultFileds: {
handler: function(val) {
  this.$nextTick(() => {
    if (!val) return
    this.fieldOptions = [
      {
        label: '基础',
        options: val.filter(r => !r.isBasicFilter)
      },
      {
        label: '自定义',
        options: val.filter(r => r.isBasicFilter)
      }
    ]
    const hasModuleField = this.fieldFormList.filter(item => item.key == 'productModuleFunctionId')
    if (hasModuleField?.length) {
      hasModuleField[0].optionList = val.filter(item => item.key == 'productModuleFunctionId')[0]?.optionList
    }
    const hasTypeCodes = this.fieldFormList.filter(item => item.key == 'typeCodes')
    if (hasTypeCodes?.length) {
      hasTypeCodes[0].optionList = val.filter(item => item.key == 'typeCodes')[0]?.optionList
    }
  })
},
immediate: true,
deep: true
}
},
mounted() {
},
methods: {
addRow() {
const hasKey = this.fieldFormList.map(r => r.key)
this.fieldOptions.forEach(element => {
  element.options.forEach(i => {
    if (i.key == 'typeCodes') {
      i.disabled = !!hasKey.includes(i.key) || !hasKey.includes('classify')
    } else {
      i.disabled = !!hasKey.includes(i.key)
    }
  })
})
const obj = {
  isFixed: true,
  type: {
    code: 'INPUT'
  },
  placeholder: '请选择筛选条件'
}
this.fieldFormList.push(obj)
},

keyChange(key, item, index) {
const allData = this.defaultFileds
const obj = allData.find(r => r.key == key)
this.$set(item, 'type', obj.type)
this.$set(item, 'isFixed', true)
this.$set(obj, 'isFixed', true)
// this.$set(obj, 'type', true)
this.$set(item, 'placeholder', obj.placeholder)
this.$set(item, 'optionList', obj.optionList)
this.$set(item, 'value', null)
this.$set(item, 'valueType', obj.valueType)
this.$set(item, 'multiple', obj.multiple)
this.$emit('update:basicFieldFormList', this.fieldFormList)
this.$emit('updateDefaultFileds', this.defaultFileds)
},
deleteRow(index, item) {
this.fieldFormList.splice(this.fieldFormList.findIndex(i => i.key === item.key), 1)
if (item.key == 'classify') {
  const keyIndex = this.fieldFormList.findIndex(i => i.key === 'typeCodes')
  if (keyIndex != -1) {
    this.fieldFormList.splice(keyIndex, 1)
  }
}
this.fieldOptions.forEach(element => {
  element.options.forEach(i => {
    i.disabled = i.key == item.key ? false : i.disabled
  })
})
this.$emit('update:basicFieldFormList', this.fieldFormList)
},
changeFixed(item) {
item.isFixed = !item.isFixed
const defaultFileds = cloneDeep(this.defaultFileds)
const selectFiled = defaultFileds.find(r => r.key == item.key)
if (selectFiled) {
  selectFiled.isFixed = item.isFixed
  this.$emit('updateDefaultFileds', defaultFileds)
}
},
onTypeChange(val, item, callback) {
if (item.key == 'classify' || item.key == 'typeCode' || item.key == 'releasePlanId') {
  this.$emit('onTypeChange', val, item)
}
if (item.key == 'productId') {
  this.$emit('onTypeChange', item)
}
if (item.key == 'classify') {
  const indexdata = this.fieldFormList.findIndex(e => e.key == 'typeCodes')
  if (indexdata == -1) return
  const obj = this.fieldFormList[indexdata]
  obj.value = null
  this.$set(this.fieldFormList, indexdata, obj)
}
}

}
}
</script>

<style lang="scss" scoped>
.custom-search-dropdown {
  padding: 16px;
  .dropdown-box {
    max-height: 200px;
    overflow-y: auto;
  }
  .el-dropdown-menu__item {
    padding: 0;
  }
  .dropdown-item-grouping {
    display: inline-block;
    line-height: 32px;
    color: #838a99;
    // margin-bottom: 12px;
  }
}
.custom-search-form {
  // padding: 16px 0;
  // border-top: 1px solid #f2f3f5;
  .el-dropdown {
    color: #3e7bfa;
  }
}

.flexRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .flexLabel {
    flex: 1;
  }
  .flexValue {
    flex: 2;
    margin: 0 10px;
  }
  i,
  svg {
    cursor: pointer;
    margin-left: 10px;
    &:hover {
      color: #3e7bfa;
    }
  }
}
.flexRow + .flexRow {
  margin-top: 10px;
}
.el-dropdown-link {
  color: #3e7bfa;
  // margin: 10px;
}
.addRow {
  color: #3e7bfa;
}
.padding16 {
  padding-bottom: 16px;
}
:deep(.el-select-group__title) {
  color: #777f8e;
  font-weight: 600;
}
</style>
