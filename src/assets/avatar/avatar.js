// const { reduce } = require("core-js/fn/array");

const avatarList = [
  {
    name: 'avatar1',
    src: require('@/assets/avatar/avatar1.png'),
  },
  {
    name: 'avatar2',
    src: require('@/assets/avatar/avatar2.png'),
  },
  {
    name: 'avatar3',
    src: require('@/assets/avatar/avatar3.png'),
  },
  {
    name: 'avatar4',
    src: require('@/assets/avatar/avatar4.png'),
  },
  {
    name: 'avatar5',
    src: require('@/assets/avatar/avatar5.png'),
  },
  {
    name: 'avatar6',
    src: require('@/assets/avatar/avatar6.png'),
  },
  {
    name: 'avatar7',
    src: require('@/assets/avatar/avatar7.png'),
  },
  {
    name: 'avatar8',
    src: require('@/assets/avatar/avatar8.png'),
  },
  {
    name: 'avatar9',
    src: require('@/assets/avatar/avatar9.png'),
  },
  {
    name: 'avatar10',
    src: require('@/assets/avatar/avatar10.png'),
  },
  {
    name: 'avatar11',
    src: require('@/assets/avatar/avatar11.png'),
  },
  {
    name: 'avatar12',
    src: require('@/assets/avatar/avatar12.png'),
  },
  {
    name: 'avatar13',
    src: require('@/assets/avatar/avatar13.png'),
  },
  {
    name: 'avatar14',
    src: require('@/assets/avatar/avatar14.png'),
  },
  {
    name: 'avatar15',
    src: require('@/assets/avatar/avatar15.png'),
  },
  {
    name: 'avatar16',
    src: require('@/assets/avatar/avatar16.png'),
  },
  {
    name: 'avatar17',
    src: require('@/assets/avatar/avatar17.png'),
  },
  {
    name: 'avatar18',
    src: require('@/assets/avatar/avatar18.png'),
  },
  {
    name: 'avatar19',
    src: require('@/assets/avatar/avatar19.png'),
  },
  {
    name: 'avatar20',
    src: require('@/assets/avatar/avatar20.png'),
  },
  // {
  //   name: "avatar21",
  //   src: require("@/assets/avatar/avatar21.png")
  // },
  // {
  //   name: "avatar22",
  //   src: require("@/assets/avatar/avatar22.png")
  // },
  // {
  //   name: "avatar23",
  //   src: require("@/assets/avatar/avatar23.png")
  // },
  // {
  //   name: "avatar24",
  //   src: require("@/assets/avatar/avatar24.png")
  // },
  // {
  //   name: "avatar25",
  //   src: require("@/assets/avatar/avatar25.png")
  // },
  // {
  //   name: "avatar26",
  //   src: require("@/assets/avatar/avatar26.png")
  // },
  // {
  //   name: "avatar27",
  //   src: require("@/assets/avatar/avatar27.png")
  // },
  // {
  //   name: "avatar28",
  //   src: require("@/assets/avatar/avatar28.png")
  // },
  // {
  //   name: "avatar29",
  //   src: require("@/assets/avatar/avatar29.png")
  // },
  // {
  //   name: "avatar30",
  //   src: require("@/assets/avatar/avatar30.png")
  // },
  // {
  //   name: "avatar31",
  //   src: require("@/assets/avatar/avatar31.png")
  // },
  // {
  //   name: "avatar32",
  //   src: require("@/assets/avatar/avatar32.png")
  // },
  // {
  //   name: "avatar33",
  //   src: require("@/assets/avatar/avatar33.png")
  // },
  // {
  //   name: "avatar34",
  //   src: require("@/assets/avatar/avatar34.png")
  // },
  // {
  //   name: "avatar35",
  //   src: require("@/assets/avatar/avatar35.png")
  // },
  // {
  //   name: "avatar36",
  //   src: require("@/assets/avatar/avatar36.png")
  // },
]

const avatarMap = {}
avatarList.map((item) => {
  avatarMap[item.name] = item
})
export { avatarList, avatarMap }

export default avatarList
