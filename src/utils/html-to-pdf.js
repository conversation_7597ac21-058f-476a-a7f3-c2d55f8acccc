// 导出页面为PDF格式
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
/**
 * @desc 页面导出为pdf
 * @param {string} title 标题
 * @param {string} el 导出页面元素的class类名或id
 */
const htmlToPdf = function (title, el = '#pdfDom') {
  const dom = document.querySelector(el)
  // 需要把滚动条置顶
  document.body.scrollTop = 0
  html2Canvas(dom, {
    allowTaint: true,
    scale: 2,
    // useCORS: true
  }).then(function (canvas) {
    // 得到canvas画布的单位是px 像素单位
    const contentWidth = canvas.width
    const contentHeight = canvas.height
    // 一页pdf显示html页面生成的canvas高度;
    const pageHeight = (contentWidth / 592.28) * 841.89
    // 未生成pdf的html页面高度
    let leftHeight = contentHeight
    // 页面偏移
    let position = 0
    // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
    const imgWidth = 595.28
    const imgHeight = (592.28 / contentWidth) * contentHeight
    // 将canvas转为base64图片
    const pageData = canvas.toDataURL('image/jpeg', 1.0)
    const PDF = new JsPDF('', 'pt', 'a4')
    // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
    // 当内容未超过pdf一页显示的范围，无需分页
    if (leftHeight < pageHeight) {
      PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
    } else {
      while (leftHeight > 0) {
        PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
        leftHeight -= pageHeight
        position -= 841.89
        // 避免添加空白页
        if (leftHeight > 0) {
          PDF.addPage()
        }
      }
    }
    // 下载
    PDF.save(title + '.pdf')
  })
}

export default htmlToPdf
