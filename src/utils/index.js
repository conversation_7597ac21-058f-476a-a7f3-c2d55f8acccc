import * as dayjs from 'dayjs'
import SnowflakeId from 'snowflake-id'
import store from '@/store'
import axios from 'axios'
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {Array} data
 * @param { Array } 需要添加哪些字段及接口数据对应的字段 [{key: name, value: label}] key为接口字段，value 为转换或新增字段
 * @returns {Array}
 */

export function gainTreeList(data, obj = []) {
  data.map((item) => {
    item.label = item.name
    if (item.children && item.children.length > 0) {
      gainTreeList(item.children)
    }
  })
  return data
}

/**
 * 下载方法
 * @param response
 */
export const downloadFile = (response) => {
  const res = response.data
  const type = res.type
  if (type.includes('application/json')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target.readyState === 2) {
        const data = JSON.parse(e.target.result)
        this.$message({
          message: data.msg,
          type: 'warning',
        })
      }
    }
    reader.readAsText(res)
  } else {
    const disposition = response.headers['content-disposition']
    let fileName = '下载文件.zip'
    if (disposition) {
      const respcds = disposition.split(';')
      for (let i = 0; i < respcds.length; i++) {
        const header = respcds[i]
        if (header !== null && header !== '') {
          const headerValue = header.split('=')
          if (headerValue !== null && headerValue.length > 0) {
            if (headerValue[0].trim().toLowerCase() === 'filename') {
              fileName = decodeURI(headerValue[1])
              break
            }
          }
        }
      }
    }
    // 处理引号
    if (
      (fileName.startsWith("'") || fileName.startsWith('"')) &&
      (fileName.endsWith("'") || fileName.endsWith('"'))
    ) {
      fileName = fileName.substring(1, fileName.length - 1)
    }

    const blob = new Blob([res])
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(link.href)
  }
}
// ZIP文件下载------------------------------
export const downloadZipFile = (fileName, blob) => {
  const binaryData = []
  binaryData.push(blob)
  const blobURL = URL.createObjectURL(
    new Blob(binaryData, { type: 'application/zip' })
  )
  // 创建a标签，用于跳转至下载链接
  const tempLink = document.createElement('a')
  tempLink.style.display = 'none'
  tempLink.href = blobURL
  tempLink.setAttribute('download', decodeURI(fileName))
  // 兼容：某些浏览器不支持HTML5的download属性
  if (typeof tempLink.download === 'undefined') {
    tempLink.setAttribute('target', '_blank')
    // tempLink.setAttribute('herf', url)
  }
  // 挂载a标签
  document.body.appendChild(tempLink)
  tempLink.click()
  document.body.removeChild(tempLink)
}
// 单个文件文件下载 下载方式type: blob、link
export const download = (fileName, blob, type) => {
  const binaryData = []
  binaryData.push(blob)
  const blobURL = URL.createObjectURL(new Blob(binaryData))
  // 创建a标签，用于跳转至下载链接
  const tempLink = document.createElement('a')
  tempLink.style.display = 'none'

  tempLink.href = type == 'link' ? blob : blobURL
  tempLink.setAttribute('download', decodeURI(fileName))
  // 兼容：某些浏览器不支持HTML5的download属性
  if (typeof tempLink.download === 'undefined') {
    tempLink.setAttribute('target', '_blank')
  }
  // 挂载a标签
  document.body.appendChild(tempLink)
  tempLink.click()
  document.body.removeChild(tempLink)
}

// 换肤加class函数
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  element.className = className
}
// 获取从今天起过去7天的时间列表 2021-11-09 00:00:00
const list = []
export function getWeekTimes() {
  if (list.length > 6) return list
  const date = dayjs()
  list.push(date.format('YYYY-MM-DD') + ' 00:00:00')
  for (let i = 1; i < 7; i++) {
    const c = date.subtract(i, 'day')
    list.unshift(c.format('YYYY-MM-DD') + ' 00:00:00')
  }
  return list
}

// 雪花算法
export function snowGuid(num) {
  const snowflake = new SnowflakeId()
  return snowflake.generate()
}

/**
 * 格式化文件单位
 * @param {String || Number} size  文件大小(kb)
 */
export const fileFormatSize = (size) => {
  var i
  var unit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  for (i = 0; i < unit.length && size >= 1024; i++) {
    size /= 1024
  }
  return (Math.round(size * 100) / 100 || 0) + unit[i]
}
/**
 * @description 捕获async await错误
 * @param {function} promise 请求函数
 * @returns {Array} 返回结果和错误对象
 */
export function catchErr(promise) {
  return promise
    .then((res) => {
      return [res, null]
    })
    .catch((err) => {
      return [{}, err]
    })
}

// 文件过大下载方法
export function downloadAll(url, params, filename) {
  let downProgress = {}
  const uniSign = new Date().getTime() + '' // 可能会连续点击下载多个文件，这里用时间戳来区分每一次下载的文件
  return axios({
    url,
    method: 'get',
    headers: { 'Content-Type': 'application/json; application/octet-stream' },
    auth: {
      username: params.username,
      password: params.password,
    },
    responseType: 'blob',
    onDownloadProgress: function (progress) {
      // progress对象中的loaded表示已经下载的数量，total表示总数量，这里计算出百分比
      downProgress = Math.round((100 * progress.loaded) / progress.total)
      // 将此次下载的文件名和下载进度组成对象再用vuex状态管理
      store.commit('downLoadProgress/SET_PROGRESS', {
        path: uniSign,
        progress: downProgress,
        filename: filename + '.zip',
      })
    },
  })
    .then((data) => {
      // 文件流传输完成后，开启文件下载
      const blob = new Blob([data.data])
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(blob)
      var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      var contentDisposition = decodeURI(data.headers['content-disposition'])
      var result = patt.exec(contentDisposition)
      var fileName = result[1]
      fileName = fileName.replace(/\"/g, '')
      // 后台再header中传文件名
      const name = fileName
      downloadElement.href = href
      downloadElement.download = name
      document.body.appendChild(downloadElement)
      downloadElement.click()
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href)
    })
    .catch((r) => {
      this.$message.error('该文件无法下载')
    })
}

// el dom元素
export function textRange(el) {
  const textContent = el
  const targetW = textContent.getBoundingClientRect().width
  const range = document.createRange()
  range.setStart(textContent, 0)
  range.setEnd(textContent, textContent.childNodes.length)
  const rangeWidth = range.getBoundingClientRect().width
  return rangeWidth > targetW
}
