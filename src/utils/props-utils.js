function getType(fn) {
  const match = fn && fn.toString().match(/^\s*function (\w+)/)
  return match ? match[1] : ''
}
const filterProps = (props, propsData = {}) => {
  const res = {}
  Object.keys(props).forEach((k) => {
    if (k in propsData || props[k] !== undefined) {
      res[k] = props[k]
    }
  })
  return res
}

const getScopedSlots = (ele) => {
  return (ele.data && ele.data.scopedSlots) || {}
}

const getSlots = (ele) => {
  let componentOptions = ele.componentOptions || {}
  if (ele.$vnode) {
    componentOptions = ele.$vnode.componentOptions || {}
  }
  const children = ele.children || componentOptions.children || []
  const slots = {}
  children.forEach((child) => {
    if (!isEmptyElement(child)) {
      const name = (child.data && child.data.slot) || 'default'
      slots[name] = slots[name] || []
      slots[name].push(child)
    }
  })
  return { ...slots, ...getScopedSlots(ele) }
}

const getSlot = (self, name = 'default', options = {}) => {
  return (
    (self.$scopedSlots &&
      self.$scopedSlots[name] &&
      self.$scopedSlots[name](options)) ||
    self.$slots[name] ||
    undefined
  )
}

const getOptionProps = (instance) => {
  if (instance.componentOptions) {
    const componentOptions = instance.componentOptions
    const { propsData = {}, Ctor = {} } = componentOptions
    const props = (Ctor.options || {}).props || {}
    const res = {}
    for (const [k, v] of Object.entries(props)) {
      const def = v.default
      if (def !== undefined) {
        res[k] =
          typeof def === 'function' && getType(v.type) !== 'Function'
            ? def.call(instance)
            : def
      }
    }
    return { ...res, ...propsData }
  }
  const { $options = {}, $props = {} } = instance
  return filterProps($props, $options.propsData)
}

export function isEmptyElement(c) {
  return !(c.tag || (c.text && c.text.trim() !== ''))
}

export function filterEmpty(children = []) {
  return children.filter((c) => !isEmptyElement(c))
}

export { getSlot, getSlots, getOptionProps }
