import qs from 'qs'
// import _qs from 'querystring'

export default {
  qs,
  /**
   * Content-Type: multipart/form-data; boundary=
   * 上传文件formdata
   * @param {*} data
   */
  fileFormData(data) {
    const formData = new FormData()
    for (const i in data) {
      formData.append(i, data[i])
    }
    return formData
  },
  download:
    ({ fileName }) =>
    (blob) => {
      const blobURL = URL.createObjectURL(blob)
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a')
      tempLink.style.display = 'none'
      tempLink.href = blobURL
      tempLink.setAttribute('download', decodeURI(fileName))
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank')
      }
      // 挂载a标签
      document.body.appendChild(tempLink)
      tempLink.click()
      document.body.removeChild(tempLink)
      // 释放blob URL地址
      // URL.revokeObjectURL(blobURL);
    },
}
