import { every, isNull, isArray } from 'lodash'
function getTableHeight(val, ts) {
  const result = every(
    val,
    (v) => v == '' || isNull(v) || (isArray(v) && v?.length == 0)
  )
  if (result) {
    ts.tableHeight = `calc(100vh - 185px)`
  } else {
    ts.$nextTick(() => {
      const height =
        ts.$refs?.searchFilter?.$refs?.filterContent?.offsetHeight || 20
      const heightPx = height + 'px'
      ts.tableHeight = `calc(100vh - 185px - ${heightPx})`
    })
  }
}

export default getTableHeight
