import Cookies from 'js-cookie'

// 简单的storage封装，兼容原有的store API
const storage = {
  get(key) {
    const value = localStorage.getItem(key)
    try {
      return value ? JSON.parse(value) : null
    } catch {
      return value
    }
  },
  set(key, value) {
    localStorage.setItem(
      key,
      typeof value === 'string' ? value : JSON.stringify(value)
    )
  },
  remove(key) {
    localStorage.removeItem(key)
  },
}
const clearData = [
  'token',
  'user',
  'permission',
  'tenant',
  'userRouter',
  'expiration',
  'logo',
]
// 缓存token
export function getToken() {
  return storage.get('token')
}
export function setToken(token) {
  return storage.set('token', token)
}
// 缓存用户信息
export function getUser() {
  return storage.get('user')
}
export function setUser(user) {
  return storage.set('user', user)
}
// 缓存权限
export function getPermission() {
  return storage.get('permission')
}
export function setPermission(permission) {
  return storage.set('permission', permission)
}
// 缓存租户
export function setTenant(tenant) {
  return storage.set('tenant', tenant)
}
export function getTenant() {
  return storage.get('tenant')
}
// 缓存router 动态路由
export function setRouter(router) {
  return storage.set('userRouter', router)
}
export function getRouter() {
  return storage.get('userRouter')
}
// 缓存过期时间
export function setExpiration(expiration) {
  return storage.set('expiration', expiration)
}
export function getExpiration() {
  return storage.get('expiration')
}
// 清空浏览器存储的数据
export function removeToken() {
  clearData.map((key) => {
    storage.remove(key)
  })
  // return storage.clearAll()
}

// const TokenKey = 'vue_admin_template_token'
const LOCAL_NAME = 'chatStorage'
export function defaultState() {
  const uuid = 1002
  return {
    active: uuid,
    usingContext: true,
    history: [{ uuid, title: 'New Chat', isEdit: false }],
    chat: [{ uuid, data: [] }],
  }
}
export function getLocalState() {
  const localState = storage.get(LOCAL_NAME)
  return { ...defaultState(), ...localState }
}

export function setLocalState(state) {
  storage.set(LOCAL_NAME, state)
}
