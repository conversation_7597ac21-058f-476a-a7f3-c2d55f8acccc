import axios from 'axios'
import { ElMessage as Message } from 'element-plus'
import { getToken } from '@/utils/auth'

axios.interceptors.request.use(
  (config) => {
    const isToken =
      config.headers['X-isToken'] === false ? config.headers['X-isToken'] : true
    const token = getToken()
    if (token && isToken) {
      config.headers.token = 'Bearer ' + token
    }
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 接口返回处理
axios.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    return Promise.reject(error)
  }
)

function handleError(error, reject, opts) {
  // debugger
  if (error.code === 'ECONNABORTED') {
    Message({
      showClose: true,
      message: '请求超时',
    })
  } else if (error.response && error.response.data) {
    // const resData = error.response.data
    if (error.response.status === 403 || error.response.status === 401) {
      console.log('error.response.status', error.response.status)
    } else if (error.response.status === 500 || error.response.status == 503) {
      console.log('error.response.status', error.response.status)
    }
  }
  reject(error)
}

function handleSuccess(res, resolve, opts) {
  const resData = res.data
  resolve(resData)
}

// http请求
const httpServer = (opts) => {
  // http默认配置
  const method = opts.method.toUpperCase()
  const httpDefaultOpts = {
    method,
    // baseURL: '' + process.env.VUE_APP_BASE_API,
    url: opts.url,
    responseType: opts.responseType || '',
    // transformResponse: [function(data) { // 抓换long
    //   return JSON.parse(data)
    // }],
    timeout: opts.timeout ? opts.timeout : 300000000,
  }
  if (opts['meta']) {
    httpDefaultOpts.headers = opts['meta']
  }
  // 按需加上时间戳，已注释
  const dataRequest = ['PUT', 'POST', 'DELETE', 'PATCH']
  if (dataRequest.includes(method)) {
    httpDefaultOpts.data = opts.data || {}
    // httpDefaultOpts.params = publicParams
  } else {
    httpDefaultOpts.params = {
      // ...publicParams,
      ...(opts.data || {}),
    }
  }

  // formData转换
  if (opts.formData) {
    httpDefaultOpts.transformRequest = [
      (data) => {
        const formData = new FormData()
        if (data) {
          Object.entries(data).forEach((item) => {
            formData.append(item[0], item[1])
          })
        }
        return formData
      },
    ]
  }

  const promise = new Promise((resolve, reject) => {
    axios(httpDefaultOpts)
      .then((response) => {
        handleSuccess(response, resolve, opts)
      })
      .catch((error) => {
        handleError(error, reject, opts)
      })
  })
  return promise
}

export default httpServer
