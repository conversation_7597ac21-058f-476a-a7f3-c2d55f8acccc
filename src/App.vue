<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {}
  },
  async created() {
    var link =
      document.querySelector("link[rel*='icon']") ||
      document.createElement('link')
    link.type = 'image/x-icon'
    link.rel = 'shortcut icon'
    // link.href = '../public/favicon.ico'
    document.getElementsByTagName('head')[0].appendChild(link)
  },
  methods: {},
}
</script>
