import Layout from '@/layout/index.vue'
/**
 * hidden: true                   选填 隐藏当前菜单
 * redirect: noRedirect           选填 路由重定向
 * name:'router-name'             必须填 路由名称
 * meta : {
    * title: 'title'              必须填 页面title
    icon: 'svg-name'/'el-icon-x'  选填 应用或菜单icon
    breadcrumb: false             选填 面包屑是否展示
    activeMenu: '/example/list'   必须填 设置默认选中的菜单
    * activeApp: 'dashbord',      必须填 设置默认选中的应用
    key: 'dashbord'               选填 如activeApp无效，则取key
    * keepAlive: false            必须填 页面是否缓存
    * refreshType: "add"     必须填 新增 回到第一页（重置条件） 编辑回到当前页（保留查询条件）
  }
 */
export default [
  // 登录
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    hidden: true,
    meta: { title: 'login' },
  },
  // 版本更新说明
  {
    path: '/releaseNote',
    component: () => import('@/views/release-note/index.vue'),
    hidden: true,
  },
  // 404
  {
    path: '/exception',
    component: Layout,
    hidden: true,
    // redirect: '/exception/404',
    children: [
      {
        path: '404',
        component: () => import('@/views/404.vue'),
        hidden: true,
        meta: { activeApp: 'notFound' },
      },
      {
        path: '401',
        component: () => import('@/views/404.vue'),
        hidden: true,
        name: 401,
        meta: { activeApp: 'notFound' },
      },
      {
        path: '500',
        component: () => import('@/views/404.vue'),
        hidden: true,
        name: 500,
        meta: { activeApp: 'notFound' },
      },
      // 工作台研发度量
      // {
      //   path: '/dashboard/measure',
      //   component: () => import('@/views/dashboard/dashboard/measure/index'),
      //   hidden: true
      // }
    ],
  },
]
