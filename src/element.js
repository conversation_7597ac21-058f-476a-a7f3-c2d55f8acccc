import ElementPlus from 'element-plus'
import { ElMessage } from 'element-plus'

export default function setupElementPlus(app) {
  app.use(ElementPlus, {
    size: 'small',
  })

  // 配置全局消息方法
  app.config.globalProperties.$message = function (msg) {
    const options =
      typeof msg === 'string' || msg === null
        ? { message: msg, showClose: true }
        : { showClose: true, ...msg }
    return ElMessage({
      duration: 2000,
      ...options,
    })
  }

  app.config.globalProperties.$message.success = function (msg) {
    return ElMessage.success({
      showClose: true,
      message: msg,
      duration: 2000,
    })
  }

  app.config.globalProperties.$message.error = function (msg) {
    return ElMessage.error({
      showClose: true,
      message: msg,
      duration: 2000,
    })
  }

  app.config.globalProperties.$message.warning = function (msg) {
    return ElMessage.warning({
      showClose: true,
      message: msg,
      duration: 2000,
    })
  }
}
