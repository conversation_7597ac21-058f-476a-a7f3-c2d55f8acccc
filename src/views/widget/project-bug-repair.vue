<template>
  <!-- 项目----bug修复和缺陷趋势图 -->
  <el-card ref="bug-repair-card" :header="formInfo.name" class="cardPage">
    <vone-echarts :options="options" />
    <section>
      <strong>最近{{ formInfo.days }}天的问题</strong> ( 已分组
      {{ typeMap[formInfo.timeCycle] }})
      <ul>
        <li>
          <el-icon class="iconfont iconCreated"
            ><el-icon-application-circleFill
          /></el-icon>
          创建的问题 ( {{ valueCretedSum }} )
        </li>
        <li>
          <el-icon class="iconfont iconDone"
            ><el-icon-application-circleFill
          /></el-icon>
          解决的问题 ( {{ valueDoneSum }} )
        </li>
        <li v-if="formInfo.isShowTodo == 0">
          <el-icon class="iconfont iconTodo"
            ><el-icon-application-circleFill
          /></el-icon>
          未修复的问题 ( {{ valueTodoSum }} )
        </li>
      </ul>
    </section>
  </el-card>
</template>

<script>
import { ApplicationCircleFill as ElIconApplicationCircleFill } from '@element-plus/icons-vue'
import { apiInsightBugTrend } from '@/api/vone/weidget/index'
import ResizeListener from 'element-resize-detector'
import _ from 'lodash'
export default {
  components: {
    ElIconApplicationCircleFill,
  },
  props: {
    formInfo: {
      type: Object,
      default: null,
    },
    keys: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      options: {},
      xAxis: [],
      yAxis: [],
      series: [],
      grid: [],
      valueTodoSum: null,
      valueCretedSum: null,
      valueDoneSum: null,
      typeMap: {
        DAY: '每日',
        WEEK: '每周',
        MOUTH: '每月',
        QUARTER: '每季',
        YEAR: '每年',
      },
    }
  },
  watch: {},
  mounted() {
    this.getOptions()
    this.addChartResizeListener()
  },
  beforeDestroy() {
    this.instance.uninstall(this.$el)
  },
  methods: {
    async getOptions() {
      const { data, isSuccess, msg } = await apiInsightBugTrend({
        timeCycle: this.formInfo.timeCycle,
        days: this.formInfo.days,
        y: this.formInfo.y,
        tableViewId: this.formInfo.tableViewId,
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }

      const date = Array.from(new Set(data.outList.map((r) => r.key)))

      const todo = data.outList.filter((r) => r.group == 'todo')
      const creted = data.outList.filter((r) => r.group == 'created')
      const done = data.outList.filter((r) => r.group == 'done')

      const valueTodo = todo.map((j) => j.value)
      const valueCreted = creted.map((j) => j.value)
      const valueDone = done.map((j) => j.value)

      const todoLast = todo[todo.length - 1].value
      const cretedLast = creted[creted.length - 1].value
      const doneLast = done[done.length - 1].value

      this.valueTodoSum =
        this.formInfo.y == 'sum' ? todoLast : _.sum(valueTodo.map(Number))
      this.valueCretedSum =
        this.formInfo.y == 'sum' ? cretedLast : _.sum(valueCreted.map(Number))
      this.valueDoneSum =
        this.formInfo.y == 'sum' ? doneLast : _.sum(valueDone.map(Number))

      const xAxis = [
        {
          data: date,
        },
        {
          data: date,
          gridIndex: 1,
        },
      ]

      const yAxis = [
        {},
        {
          gridIndex: 1,
        },
      ]

      const series = [
        {
          name: '创建的问题',
          type: 'line',
          showSymbol: false,
          data: valueCreted,
          areaStyle: {},
        },
        {
          name: '解决的问题',
          type: 'line',
          showSymbol: false,
          data: valueDone,
          areaStyle: {},
        },
        {
          name: '未修复的',
          type: 'line',
          showSymbol: false,
          data: valueTodo,
          xAxisIndex: 1,
          yAxisIndex: 1,
        },
      ]

      // const grid = [

      // ]

      if (this.formInfo.isShowTodo == 0) {
        this.xAxis = xAxis
        this.yAxis = yAxis
        this.series = series
        this.grid = [
          {
            top: '10%',
            bottom: '60%',
          },
          {
            top: '60%',
            bottom: '15%',
          },
        ]
      } else {
        this.xAxis = xAxis.filter((r) => !r.gridIndex)
        this.yAxis = yAxis.filter((r) => !r.gridIndex)
        this.series = series.filter((r) => !r.xAxisIndex)
        this.grid = [
          {
            top: '10%',
            bottom: '15%',
          },
        ]
      }

      this.options = {
        color: ['#f68483', '#6ad2a8', '#7486eb'],
        // Make gradient line here

        tooltip: {
          trigger: 'axis',
        },
        xAxis: this.xAxis,
        yAxis: this.yAxis,
        grid: this.grid,
        series: this.series,
      }
    },
    /**
     * 对chart元素尺寸进行监听，当发生变化时同步更新echart视图
     */
    addChartResizeListener() {
      this.instance = ResizeListener({
        strategy: 'scroll',
        callOnAdd: true,
      })
      this.instance.listenTo(this.$el, () => {
        this.$nextTick(() => {
          if (this.$refs['bug-repair-card']?.$children.length > 0) {
            this.$refs['bug-repair-card'].$children[0].$el.style.height =
              this.$el.offsetHeight - 178 + 'px'
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.cardPage {
  background-color: var(--main-bg-color);
  border: 1px solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  border-radius: 4px;
  overflow: hidden;
  height: 100%;
}
section {
  background-color: var(--content-bg-hover-color);
  border-radius: 4px;
  padding: 10px;
  margin: 0 6%;
}
ul {
  margin-top: 5px;
  li {
    height: 25px;
    line-height: 25px;
    font-size: 13px;
    i {
      font-size: 10px;
    }
  }
}
.iconCreated {
  color: #f68483;
}
.iconDone {
  color: #6ad2a8;
}
.iconTodo {
  color: #7486eb;
}
:deep(.el-card__header) {
  height: 26px;
  line-height: 26px;
  border: none;
  padding-left: 16px;
  font-weight: 700;
  font-size: 14px;
}
</style>
