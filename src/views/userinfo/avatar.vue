<template>
  <div>
    <img
      v-if="avatarSrc"
      class="j-user-avatar"
      :style="style"
      :src="avatarSrc"
    />
    <span
      v-else
      class="j-user-avatar j-user-avatar__empty iconfont el-icon-application-member"
      :style="style"
    />
  </div>
</template>

<script>
import { avatarMap } from '@/assets/avatar/avatar'

export default {
  props: {
    name: {
      type: String,
      default: '',
    },
    size: {
      type: [String, Number],
      default: 'small',
    },
  },
  data() {
    return {
      avatarMap,
    }
  },
  computed: {
    style() {
      if (typeof this.size === 'number') {
        return {
          height: `${this.size}px`,
          width: `${this.size}px`,
          lineHeight: `${this.size}px`,
        }
      }
      return {}
    },
    avatarSrc() {
      if (!this.name || !this.avatarMap[this.name]) return
      return this.avatarMap[this.name].src
    },
  },
}
</script>

<style lang="scss" scoped>
.j-user-avatar {
  display: inline-block;
  height: 28px;
  width: 28px;
  line-height: 28px;
  border-radius: 50%;
  background-color: var(--border-color-base);
}
.j-user-avatar__empty {
  text-align: center;
  background-color: var(--border-color-base);
}
</style>
