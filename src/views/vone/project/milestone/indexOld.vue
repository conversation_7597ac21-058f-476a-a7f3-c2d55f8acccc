<template>
  <div>
    <el-row :gutter="12">
      <el-col :span="6" class="left-aside">
        <el-card shadow="false">
          <div slot="header">
            <div class="testTitle">
              <span style="font-weight: 600">里程碑</span>
              <el-tooltip effect="dark" content="新增里程碑" placement="top">
                <el-button
                  plain
                  :icon="el-icon-setting"
                  class="addIcon"
                  size="mini"
                  :disabled="!$permission('project_milestone_add')"
                  @click="addPlan"
                />
              </el-tooltip>
            </div>
          </div>
          <div v-loading="listLoading" class="tree-container">
            <el-tree
              ref="tree"
              :data="planList"
              :props="defaultProps"
              default-expand-all
              :expand-on-click-node="false"
              node-key="id"
              highlight-current
              @node-click="sendId"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <div
                  v-if="data.id != -1"
                  class="tree-border"
                  :style="{
                    backgroundColor: `${stateColorMap[data.stateCode]}`,
                  }"
                />

                <div class="left-Box">
                  <el-tooltip :content="node.label" placement="top">
                    <span>
                      {{
                        node.label.length > 12
                          ? node.label.substring(0, 11) + '...'
                          : node.label
                      }}
                    </span>
                  </el-tooltip>

                  <!-- <span>{{ node.label }}
                    </span> -->
                  <span
                    v-if="data.id != -1 && data.planStime"
                    class="left-time it_item_time"
                  >
                    <small>
                      {{ data.planStime | format }} -
                      {{ data.planEtime | format }}
                    </small>
                  </span>
                </div>

                <div v-if="data.id != -1">
                  <el-dropdown trigger="click" placement="top-end">
                    <a
                      class="dropItem"
                      :class="{
                        nostart: data.stateCode == 1,
                        progress: data.stateCode == 2,
                        finish: data.stateCode == 3,
                      }"
                    >
                      <!-- <div
                          :style="{ textAlign:'center',
                                    borderRadius: '5px' , border:`1px solid ${ data.stateColor}`,width:'60px',color:`${data.stateColor}`}"
                        > -->

                      {{
                        data.stateCode == 1
                          ? '未开始'
                          : data.stateCode == 2
                          ? '进行中'
                          : '已完成'
                      }}<el-icon class="iconfont" style="margin-left: 4px"
                        ><ElIconDirectionDown
                      /></el-icon>
                      <!-- </div> -->
                    </a>

                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        :disabled="
                          data.stateCode == 1 ||
                          !$permission('project_milestone_edit')
                        "
                        @click.native="changeStatus(data, 1)"
                        >未开始</el-dropdown-item
                      >
                      <el-dropdown-item
                        :disabled="
                          data.stateCode == 2 ||
                          !$permission('project_milestone_edit')
                        "
                        @click.native="changeStatus(data, 2)"
                        >进行中</el-dropdown-item
                      >
                      <el-dropdown-item
                        :disabled="
                          data.stateCode == 3 ||
                          !$permission('project_milestone_edit')
                        "
                        @click.native="changeStatus(data, 3)"
                        >已完成</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>

                  <span class="right-drop">
                    <el-dropdown trigger="click" placement="bottom-end">
                      <el-icon><ElIconSetting /></el-icon>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          :icon="el-icon-setting"
                          :disabled="!$permission('project_milestone_add')"
                          @click.native="addChildrenPlan(data)"
                          >新增子里程碑</el-dropdown-item
                        >
                        <el-dropdown-item
                          :icon="el-icon-setting"
                          :disabled="!$permission('project_milestone_edit')"
                          @click.native="editPlan(data)"
                          >编辑</el-dropdown-item
                        >
                        <el-dropdown-item
                          :icon="el-icon-setting"
                          :disabled="!$permission('project_milestone_del')"
                          @click.native="removeDel(data)"
                          >删除</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                  </span>
                </div>
              </span>
            </el-tree>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="right-aside">
          <div slot="header">
            <el-row type="flex" justify="space-between">
              <div class="iteration_title">
                <div v-if="iterationDetail.id == -1" class="planName">
                  {{ pageName }}
                </div>

                <div v-else>
                  <!-- <a> -->
                  <!-- <div class="planName"> -->
                  <!-- <svg-icon icon-class="icon-plan" /> -->
                  <!-- 里程碑概览 -->
                  <!-- </div> -->

                  <!-- </a> -->
                </div>
              </div>
              <div class="iteration_btn">
                <div class="btn-group-style">
                  <template v-if="iterationDetail.id != -1">
                    <el-button
                      plain
                      :icon="el-icon-setting"
                      @click="cancleConnect"
                      >取消关联里程碑</el-button
                    >
                  </template>
                  <el-button
                    plain
                    :icon="el-icon-setting"
                    @click="connectPlan"
                    >关联里程碑</el-button
                  >
                  <el-dropdown
                    trigger="click"
                    style="margin-left: 10px"
                    @command="showAdd"
                  >
                    <el-button-group>
                      <el-button
                        type="primary"
                        :icon="el-icon-setting"
                        :disabled="!$permission('project_milestone_add_all')"
                        >新增</el-button
                      >
                      <el-button
                        class="rightBotton"
                        :icon="el-icon-setting"
                        type="primary"
                        :disabled="!$permission('project_milestone_add_all')"
                      />
                    </el-button-group>

                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="1">需求</el-dropdown-item>
                      <el-dropdown-item command="2">缺陷</el-dropdown-item>
                      <el-dropdown-item command="3">任务</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </el-row>
          </div>

          <!-- 表格 -->
          <planTable
            ref="planTable"
            :current-nodekey="currentNodekey"
            @showInfo="showInfo"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增编辑里程碑对话框 -->
    <add-plan-dialog
      v-if="addPlanParam.visible"
      v-model="addPlanParam.visible"
      v-bind="addPlanParam"
      @success="getTestPlanList"
    />

    <!-- 新增完整需求 -->
    <vone-custom-add
      v-if="issueAddParam.visible"
      :key="issueAddParam.key"
      v-model="issueAddParam.visible"
      v-bind="issueAddParam"
      :type-code="'ISSUE'"
      :title="'新增需求'"
      @success="getTableData"
      @initList="getTableData"
    />

    <!-- 编辑完整需求 -->

    <vone-custom-edit
      v-if="issueParam.visible"
      :key="issueParam.key"
      v-model="issueParam.visible"
      v-bind="issueParam"
      :type-code="'ISSUE'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getTableData"
    />

    <!-- 新增完整任务 -->
    <vone-custom-add
      v-if="taskParamAdd.visible"
      :key="taskParamAdd.key"
      v-model="taskParamAdd.visible"
      v-bind="taskParamAdd"
      :type-code="'TASK'"
      :title="'新增任务'"
      @success="getTableData"
    />

    <!-- 编辑完整任务 -->
    <vone-custom-edit
      v-if="taskParam.visible"
      :key="taskParam.key"
      v-model="taskParam.visible"
      v-bind="taskParam"
      :type-code="'TASK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getTableData"
    />

    <!-- 新建完整缺陷 -->
    <vone-custom-add
      v-if="defectParamAdd.visible"
      :key="defectParamAdd.key"
      v-model="defectParamAdd.visible"
      v-bind="defectParamAdd"
      :type-code="'BUG'"
      :title="'新增缺陷'"
      @success="getTableData"
    />

    <!-- 编辑完整缺陷 -->
    <vone-custom-edit
      v-if="defectParam.visible"
      :key="defectParam.key"
      v-model="defectParam.visible"
      v-bind="defectParam"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getTableData"
    />

    <!-- 关联里程碑 -->
    <el-dialog
      title="关联里程碑"
      width="30%"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
      :before-close="onClose"
    >
      <el-form ref="planForm" :model="planForm" label-position="left">
        <el-form-item label="里程碑" prop="id">
          <vone-tree-select
            v-model="planForm.id"
            search-nested
            :tree-data="newPlanList"
            placeholder="请选择里程碑"
            style="width: 90%"
          />
        </el-form-item>
        <div class="info">
          <el-icon class="iconfont"
            ><el-icon-tips-exclamation-circle
          /></el-icon>
          任务，缺陷不能创建子任务
        </div>

        <el-form-item prop="createType">
          <el-checkbox-group v-model="planForm.createType">
            <el-row>
              <el-col :span="24">
                <el-checkbox label="自动创建前端任务" />
              </el-col>
              <el-col :span="24">
                <el-checkbox label="自动创建后端任务" />
              </el-col>
              <!-- <el-col :span="24">
                  <el-checkbox label="自动创建测试任务" />
                </el-col> -->
            </el-row>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="sureAdd"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
const stateColorMap = {
  1: '#ADB0B8', // 未开始
  2: '#63BDF9', // 进行中
  3: '#3CB540', // 已完成
}
import * as dayjs from 'dayjs'

import {
  apiAlmProjectPlanNoPage,
  apiAlmProjectPlanDel,
  apiAlmPlanLinkedSprint,
  apiAlmPlanCancleSprint,
  apiAlmProjectPlanAdd,
} from '@/api/vone/project/iteration'

import { list2Tree } from '@/utils/list2Tree'
import { gainTreeList } from '@/utils'

import planTable from './components/planTable.vue'

import addPlanDialog from './components/add-plan-dialog.vue'

export default {
  components: {
    planTable,
    addPlanDialog,
  },
  filters: {
    format(val) {
      if (!val) return ''
      return dayjs(val).format('MM月DD日')
    },
  },
  data() {
    return {
      stateColorMap,
      planForm: {
        createType: [],
      },
      saveLoading: false,
      dialogFormVisible: false,
      tableData: {},
      addPlanParam: { visible: false },
      issueAddParam: { visible: false }, // 新增需求
      issueParam: { visible: false }, // 编辑需求
      taskParamAdd: { visible: false },
      taskParam: { visible: false },
      defectParamAdd: { visible: false }, // 缺陷新增
      defectParam: { visible: false }, // 缺陷编辑
      pageName: '未规划事项',
      originalTree: [],
      treeData: [],
      listLoading: false,
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      defaultList: [
        {
          id: '-1',
          name: '未规划事项',
          parentId: '0',
        },
      ],
      planList: [], // 计划列表
      newPlanList: [],
      currentNodekey: '',
      isLarge: false,
      iterationDetail: {
        id: '-1',
      },
      rightTabs: [
        {
          label: '活动',
          name: 'active',
        },
      ],
      leftTabs: [],
    }
  },
  provide() {
    return {
      treeData: this.treeData,
    }
  },
  created() {
    this.getTestPlanList()
  },
  methods: {
    onClose() {
      this.dialogFormVisible = false
      this.$refs.planForm.resetFields()
    },
    // 新增页判断
    showAdd(command) {
      if (command == 1) {
        // 需求新增
        this.issueAddParam = {
          visible: true,
          sprintId: this.currentIteraId,
          key: Date.now(),
        }
      } else if (command == 2) {
        // 缺陷新增
        this.defectParamAdd = {
          visible: true,
          sprintId: this.currentIteraId,
          key: Date.now(),
        }
      } else if (command == 3) {
        // 任务新增
        this.taskParamAdd = {
          visible: true,
          sprintId: this.currentIteraId,
          key: Date.now(),
        }
      }
    },
    showInfo(row) {
      // 获取分类,判断当前数据属于需求/任务/缺陷
      const type = row.echoMap.typeCode.classify.code
      if (type == 'ISSUE') {
        this.issueParam = {
          visible: true,
          title: '编辑需求',
          id: row.bizId,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: row.typeCode,
          stateCode: row.stateCode,
        }
      } else if (type == 'BUG') {
        this.defectParam = {
          visible: true,
          title: '编辑缺陷',
          id: row.bizId,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: row.typeCode,
          stateCode: row.stateCode,
        }
      } else if (type == 'TASK') {
        this.taskParam = {
          visible: true,
          title: '编辑任务',
          id: row.bizId,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: row.typeCode,
          stateCode: row.stateCode,
        }
      }
    },
    // 查询里程碑计划
    async getTestPlanList() {
      this.listLoading = true
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        type: 'MILESTONE',
      })
      this.listLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      const originList = this.defaultList.concat(res.data) // 平级数据

      const tree = this.defaultList.concat(res.data)
      this.planList = list2Tree(tree, { parentKey: 'parentId' })

      const stoneTree = gainTreeList(this.planList)
      this.newPlanList = stoneTree.filter((r) => r.id != -1)

      this.currentNodekey = this.planList[0].id

      // 数据处理,如果当前迭代有进行中的,默认选中第一个进行中的数据,没有则选中未规划事项

      const hasIng = originList.filter((r) => r.stateCode == 2).length
        ? originList.filter((r) => r.stateCode == 2)[0]
        : null

      if (hasIng) {
        this.currentNodekey = hasIng.id
        this.pageName = hasIng.name
        this.iterationDetail = hasIng
        this.currentIteraId = hasIng.id
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.currentNodekey) // treeBox 元素的ref   value 绑定的
        })

        this.$refs.planTable.getPlanCaseList(this.currentNodekey)
      } else {
        this.currentNodekey = originList[0].id
        this.pageName = '未规划事项'
        this.iterationDetail = originList[0]
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.currentNodekey) // treeBox 元素的ref   value 绑定的
        })

        this.$refs.planTable.getPlanCaseList(this.currentNodekey)
      }
    },
    // 新增里程碑
    addPlan() {
      this.addPlanParam = { visible: true, hasParent: false }
    },
    // 新增子里程碑
    addChildrenPlan(node) {
      this.addPlanParam = {
        visible: true,
        hasParent: true,
        parentId: node.id,
        node: node,
      }
    },
    // 编辑里程碑
    editPlan(row) {
      this.addPlanParam = { visible: true, id: row.id }
    },
    // 删除里程碑
    async removeDel(item) {
      await this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
      })
        .then(async (actions) => {
          this.loading = true
          const { isSuccess, msg } = await apiAlmProjectPlanDel([item.id])
          if (!isSuccess) {
            this.loading = false
            this.$message.error(msg)
            return
          }
          this.$message.success('删除成功')
          this.getTestPlanList()
        })
        .catch(() => {})
    },
    // 关联里程碑
    connectPlan() {
      const tableData = this.$refs.planTable.selecteTableData
      if (!tableData.length) {
        this.$message.warning('请选择要关联里程碑的需求/任务/缺陷')
        return
      }
      this.dialogFormVisible = true
    },

    // 查询计划下需求/任务/缺陷
    sendId(val) {
      if (val.id == this.currentNodekey) {
        return
      }
      this.pageName = val.name
      this.currentNodekey = val.id
      this.iterationDetail = val
      this.$refs.planTable.getPlanCaseList(val.id)
    },
    // 修改计划状态
    async changeStatus(item, key) {
      this.$set(item, 'stateCode', key)
      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...item,
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        this.saveLoading = false
        return
      }
      this.saveLoading = false
      this.$message.success('修改里程碑状态成功')
    },
    // 刷新列表
    getTableData() {
      this.$refs.planTable.getPlanCaseList(this.currentNodekey)
    },
    // 关联里程碑--保存
    async sureAdd() {
      const tableData = this.$refs.planTable.selecteTableData

      const objMap = {
        自动创建前端任务: 1,
        自动创建后端任务: 2,
      }

      const params = tableData.map((r) => ({
        createTypes: this.planForm.createType.length
          ? this.planForm.createType.map((r) => objMap[r])
          : [],
        issueId: r.bizId,
        planId: this.planForm.id,
        typeClassify:
          r.typeCode && r.echoMap && r.echoMap.typeCode
            ? r.echoMap.typeCode.classify.code
            : '',
      }))

      try {
        this.saveLoading = true
        const res = await apiAlmPlanLinkedSprint(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.onClose()
        this.$message.success('关联里程碑成功')
        this.getTableData()
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 取消关联里程碑
    async cancleConnect() {
      const tableData = this.$refs.planTable.selecteTableData
      if (!tableData.length) {
        this.$message.warning('请选择要取消关联里程碑的需求/任务/缺陷')
        return
      }

      await this.$confirm(`确定取消关联的里程碑吗?`, '提示', {
        type: 'warning',
        closeOnClickModal: false,
      })

      const params = tableData.map((r) => ({
        issueId: r.bizId,
        planId: this.currentNodekey,
        typeClassify:
          r.typeCode && r.echoMap && r.echoMap.typeCode
            ? r.echoMap.typeCode.classify.code
            : '',
      }))

      const res = await apiAlmPlanCancleSprint(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('取消关联里程碑成功')
      this.getTestPlanList()
    },
  },
}
</script>

<style lang="scss" scoped>
.mt-8 {
  margin-top: 8px;
}
.mb-8 {
  margin-bottom: 8px;
}
.left-aside {
  :deep(.el-card__body) {
    height: calc(100vh - 158px);
    overflow-y: auto;
  }
  // height: calc(100vh - 158px);
}

.tree-container {
  height: calc(100vh - 198px);
  overflow-y: overlay;
  div {
    border: none;
  }
}
.dropItem {
  font-size: 12px;
}
.nostart {
  color: #adb0b8;
}
.progress {
  color: #63bdf9;
}
.finish {
  color: #3cb540;
}

.testTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 50px;
  .addIcon {
    min-width: 50px;
  }
}
.btn-group-style {
  float: right;
  // text-align: right;
}
.iteration_btn {
  float: right;
}
:deep(.el-tree .el-tree-node__content) {
  height: 70px;
  line-height: 70px;
  border: 1px solid #ccc;
  margin-bottom: 10px;
  border-radius: 5px;
  position: relative;
  padding-bottom: 10px;

  .tree-border {
    width: 4px;
    height: 70px;
    position: absolute;
    left: 0;
    top: -0.2px;
    border-radius: 5px 0 0 5px;
  }
  &.selected {
    background-color: rgb(230, 243, 255, 0.6);
  }

  .left-time {
    position: absolute;
    top: 20px;
    left: 30px;
  }
  .right-drop {
    position: absolute;
    top: 20px;
    right: 40px;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    margin-right: 30px;

    .it_item_time {
      color: #8c8c8c;
    }
    &.it_stateCode_1 {
      border-left: 4px solid var(--main-theme-color, #3e7bfa);
    }
    &.it_stateCode_2 {
      border-left: 4px solid #f7cd55;
    }
    &.it_stateCode_3 {
      border-left: 4px solid #6fc38a;
    }
    &.it_stateCode_-1 {
      border-left: 4px solid #cccccc;
      margin-top: 0px;
    }
  }
}
.rightBotton {
  min-width: 8px;
}
.planName {
  padding: 0 10px;
  height: 32px;
  line-height: 32px;

  background: rgb(245, 246, 250, 0.3);
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
  text-align: center;
}

.info {
  line-height: 30px;
  margin: 10px 5px;
  font-size: 12px;

  color: #b5b8bd;
}

:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #e6f3ff !important;
}
</style>
