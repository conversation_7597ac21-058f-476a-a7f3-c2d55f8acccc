<template>
  <!-- vone-custom-table -->
  <el-table
    ref="plan-table"
    :loading="tableLoading"
    table-key="plan-table"
    :table-options="tableOptions"
    row-key="bizId"
    node-key="bizId"
    default-expand-all
    :tree-props="{
      children: 'children',
      hasChildren: 'hasChildren',
    }"
    :table-data="tableData"
    height="calc(100vh - 462px)"
    :show-column="false"
    :search="false"
    @getTableData="getPlanCaseList"
    @selection-change="(value) => (selecteTableData = value)"
  >
    <!-- 查询条件 -->
    <vone-search-dynamic
      slot="search"
      :model="formData"
      label-position="top"
      @getTableData="getPlanCaseList"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入名称"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="负责人" prop="leadingBy">
            <vone-icon-select
              v-model="formData.leadingBy"
              select-type="user"
              clearable
              filterable
              :data="pUserList"
              style="width: 100%"
            >
              <el-option
                v-for="item in pUserList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                style="width: 100%; display: flex"
              >
                <vone-user-avatar
                  v-if="item.id"
                  :avatar-path="item.avatarPath"
                  :avatar-type="item.avatarType"
                  :show-name="false"
                  height="22px"
                  width="22px"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理人" prop="handleBy">
            <vone-icon-select
              v-model="formData.handleBy"
              select-type="user"
              clearable
              filterable
              :data="pUserList"
              style="width: 100%"
            >
              <el-option
                v-for="item in pUserList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                style="width: 100%; display: flex"
              >
                <vone-user-avatar
                  v-if="item.id"
                  :avatar-path="item.avatarPath"
                  :avatar-type="item.avatarType"
                  :show-name="false"
                  height="22px"
                  width="22px"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态" prop="stateCode">
            <el-select
              v-model="formData.stateCode"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in stateCodeList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="计划完成时间" prop="planEtime">
            <el-date-picker
              v-model="formData.planEtime"
              type="date"
              placeholder="选择计划完成时间"
              style="width: 100%"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否延期" prop="delay">
            <el-select v-model="formData.delay" style="width: 100%" clearable>
              <el-option label="是" value="true" />
              <el-option label="否" value="false" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priorityCode">
            <vone-icon-select
              v-model="formData.priorityCode"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </el-form-item>
        </el-col>
      </el-row>
    </vone-search-dynamic>

    <template>
      <el-table-column type="selection" width="55" />

      <el-table-column
        prop="name"
        label="标题"
        min-width="300"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <a
            :class="
              !$permission('project_milestone_update_all')
                ? 'noPermissionBtn'
                : ''
            "
            @click="showInfo(scope.row)"
          >
            <span
              v-if="!scope.row.children"
              style="width: 22px; opacity: 0; display: inline-block"
            />
            <span
              v-if="
                scope.row.typeCode &&
                scope.row.echoMap &&
                scope.row.echoMap.typeCode
              "
            >
              <i
                :class="`iconfont ${scope.row.echoMap.typeCode.icon}`"
                :style="{
                  color: `${
                    scope.row.echoMap.typeCode
                      ? scope.row.echoMap.typeCode.color
                      : '#ccc'
                  }`,
                }"
              />
            </span>
            <span style="color: #409eff">
              {{ scope.row.code }} {{ scope.row.name }}
            </span>
          </a>
          <!-- 是否延期 -->
          <span v-if="scope.row.delay">
            <el-icon class="color-danger ml-2"
              ><el-icon-warning-outline
            /></el-icon>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="stateCode"
        label="状态"
        width="120"
        sortable
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row && scope.row.echoMap && scope.row.echoMap.typeCode"
          >
            <issueStatus
              v-if="scope.row.echoMap.typeCode.classify.code == 'ISSUE'"
              :key="Date.now()"
              :no-permission="!$permission('project_milestone_flow')"
              :workitem="scope.row"
              @changeFlow="getPlanCaseList"
            />

            <taskStatus
              v-else-if="scope.row.echoMap.typeCode.classify.code == 'TASK'"
              :key="Date.now()"
              :no-permission="!$permission('project_milestone_flow')"
              :workitem="scope.row"
              @changeFlow="getPlanCaseList"
            />

            <defectStatus
              v-else-if="scope.row.echoMap.typeCode.classify.code == 'BUG'"
              :key="Date.now()"
              :no-permission="!$permission('project_milestone_flow')"
              :workitem="scope.row"
              @changeFlow="getPlanCaseList"
            />
          </span>
          <span v-else>{{ scope.row.typeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="priorityCode" label="优先级" width="100" sortable>
        <template slot-scope="scope">
          <div
            v-if="
              scope.row.priorityCode &&
              scope.row.echoMap &&
              scope.row.echoMap.priorityCode
            "
          >
            <i
              :class="`iconfont ${scope.row.echoMap.priorityCode.icon}`"
              :style="{
                color: `${
                  scope.row.priorityCode &&
                  scope.row.echoMap &&
                  scope.row.echoMap.priorityCode
                    ? scope.row.echoMap.priorityCode.color
                    : '#ccc'
                }`,
              }"
            />
            {{ scope.row.echoMap.priorityCode.name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="planEtime"
        label="计划完成时间"
        width="135"
        sortable
      >
        <template slot-scope="scope">
          <span v-if="scope.row.planEtime">
            {{ dayjs(scope.row.planEtime).format('YYYY-MM-DD') }}
          </span>
          <span v-else>
            {{ scope.row.planEtime }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="handleBy"
        label="处理人"
        width="90"
      >
        <template slot-scope="scope">
          <span
            v-if="
              scope.row.handleBy &&
              scope.row.echoMap &&
              scope.row.echoMap.handleBy
            "
          >
            <vone-user-avatar
              :avatar-path="scope.row.echoMap.handleBy.avatarPath"
              :name="scope.row.echoMap.handleBy.name"
            />
          </span>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="leadingBy"
        label="负责人"
        width="90"
      >
        <template slot-scope="scope">
          <span
            v-if="
              scope.row.leadingBy &&
              scope.row.echoMap &&
              scope.row.echoMap.leadingBy
            "
          >
            <vone-user-avatar
              :avatar-path="scope.row.echoMap.leadingBy.avatarPath"
              :name="scope.row.echoMap.leadingBy.name"
            />
          </span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script>
import { WarningOutline as ElIconWarningOutline } from '@element-plus/icons-vue'
import { apiAlmFindItemNoPage } from '@/api/vone/project/iteration'

import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiProjectUserNoPage } from '@/api/vone/project/index'

import { list2Tree } from '@/utils/list2Tree'

import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import taskStatus from '@/views/vone/project/common/change-status/index.vue'
import defectStatus from '@/views/vone/project/common/change-status/index.vue'

export default {
  components: {
    issueStatus,
    taskStatus,
    defectStatus,
    ElIconWarningOutline,
  },
  props: {
    currentNodekey: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      tableData: {
        records: [],
      },
      tableLoading: false,

      selecteTableData: [],

      tableOptions: {},

      formData: {},
      pUserList: [],
      stateCodeList: [], // 状态
      prioritList: [],
    }
  },
  mounted() {
    this.getProjectUser()
    this.getAllStatus()
    this.getPrioritList()
  },
  methods: {
    showInfo(row) {
      if (!this.$permission('project_milestone_update_all')) {
        this.$message.warning('暂无操作权限,请联系管理员分配权限')
        return
      }
      this.$emit('showInfo', row)
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateCodeList = res.data
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    // 查询项目下人员
    async getProjectUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })

      if (!res.isSuccess) {
        return
      }

      this.pUserList = res.data
    },
    // 查询计划下用例
    async getPlanCaseList(val) {
      this.tableLoading = true
      const res = await apiAlmFindItemNoPage({
        planId: val || this.currentNodekey,
        projectId: this.$route.params.id,
        leadingBy: this.formData.leadingBy,
        handleBy: this.formData.handleBy,
        delay: this.formData.delay && this.formData.delay == 'true',
        stateCode: this.formData.stateCode,
        planEtime: this.formData.planEtime,
        priorityCode: this.formData.priorityCode,
        name: this.formData.name,
      })
      this.tableLoading = false

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData.records = list2Tree(res.data, {
        key: 'bizId',
        parentKey: 'parentId',
      })
    },
  },
}
</script>

<style></style>
