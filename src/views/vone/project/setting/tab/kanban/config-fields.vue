<template>
  <div class="card_config-item">
    <div class="card_config-pic">
      <!-- 卡片展示 -->
      <configCard v-bind="cardData" :customs="customNames" />
    </div>
    <div v-if="configType === 'custom'" class="card_config-field">
      <div class="card_config-default">
        <div class="fieldTitle">固定字段</div>
        <ul class="list">
          <li
            v-for="item in defaultFields"
            :key="item"
            class="item"
            @click="changeDefault(item)"
          >
            <span class="text">{{ item }}</span>
            <el-icon class="iconfont" style="color: var(--main-theme-color)"
              ><el-icon-tips-done
            /></el-icon>
          </li>
        </ul>
      </div>
      <div class="card_config-custom">
        <div class="fieldTitle">
          <div class="custom-field">
            <span>自定义字段</span>
            <el-tooltip content="最多添加两个字段" placement="top">
              <el-icon class="iconfont" style="margin-left: 4px"
                ><el-icon-tips-exclamation-circle
              /></el-icon>
            </el-tooltip>
          </div>

          <ul v-if="customNames && customNames.length > 0" class="list">
            <li v-for="(item, i) in customNames" :key="item.key" class="item">
              <el-tooltip
                v-if="item.name.length > 8"
                :content="item.name"
                placement="top"
              >
                <span class="text text-over">{{ item.name }}</span>
              </el-tooltip>
              <span v-else class="text text-over">{{ item.name }}</span>
              <el-icon
                class="iconfont"
                style="color: var(--main-theme-color); cursor: pointer"
                ><el-icon-application-delete
              /></el-icon>
            </li>
          </ul>
          <div v-if="customList.length < 2" class="body">
            <el-select
              v-model="customSelect"
              placeholder="请选择"
              @change="changeSelect"
            >
              <el-option
                v-for="item in customOps"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
            <el-icon style="color: var(--main-theme-color); cursor: pointer"
              ><el-icon-plus
            /></el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  TipsDone as ElIconTipsDone,
  TipsExclamationCircle as ElIconTipsExclamationCircle,
  ApplicationDelete as ElIconApplicationDelete,
  Plus as ElIconPlus,
} from '@element-plus/icons-vue'
import configCard from './config-card.vue'
export default {
  components: {
    configCard,
    ElIconTipsDone,
    ElIconTipsExclamationCircle,
    ElIconApplicationDelete,
    ElIconPlus,
  },
  props: {
    configType: {
      type: String,
      default: '',
    },
    cardData: {
      type: Object,
      default: () => ({}),
    },
    customs: {
      type: Array,
      default: () => [],
    },
    customFields: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      customSelect: '',
      customList: [],
      customNames: [],
    }
  },
  computed: {
    defaultFields() {
      return this.cardData.type === 'TASK'
        ? ['编号', '状态', '工时', '优先级', '计划完成时间', '处理人']
        : ['编号', '状态', '规模', '优先级', '计划完成时间', '处理人']
    },
    customOps() {
      return this.customFields[this.cardData.type] || []
    },
    defaultSelected() {
      return this.cardData.defaultFields || []
    },
  },
  watch: {
    configType(val) {
      if (val === 'init') {
        this.$emit('updateDefault', [...this.defaultFields])
        this.$emit('updateCustoms', [])
      }
    },
    customs(val) {
      this.customList = val || []
      // 查询对应类型字段列表
      const list = this.customFields[this.cardData.type] || []
      this.customNames = val?.map((v) => list.find((o) => o.key === v)) || []
    },
  },
  methods: {
    isChecked(val) {
      return this.defaultSelected.indexOf(val) > -1 || false
    },
    changeDefault(val) {
      const list = this.cardData.defaultFields
      const index = list.indexOf(val)
      index > -1 ? list.splice(index, 1) : list.push(val)
      this.$emit('updateDefault', list)
    },
    visibleInput() {
      if (!this.customSelect) return
      if (this.customList.indexOf(this.customSelect) > -1) {
        this.$message.warning('已选择此字段，请重新选择')
      } else {
        this.customList.push(this.customSelect)
        this.customNames.push(this.activeFiled)
        this.$emit('updateCustoms', this.customList)
      }
      this.customSelect = ''
    },
    changeSelect(val) {
      this.activeFiled = this.customs.find((val) => val.key === val)
    },
    deleteCustom(i) {
      this.$confirm('确认删除当前项吗?', '删除', {
        type: 'warning',
        customClass: 'delConfirm',
      }).then(() => {
        this.customList.splice(i, 1)
        this.customNames.splice(i, 1)
        this.$emit('updateCustoms', this.customList)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.card_config {
  &-item {
    display: flex;
    max-width: 636px;
    height: 220px;
    border: 1px solid #eaecf0;
    border-radius: 4px;
  }
  &-pic {
    width: 234px;
    padding: 12px;
    background-color: #f2f3f5;
  }
  &-field {
    display: flex;
    flex: 1;
    width: 354px;
    padding: 12px;
    gap: 0 24px;
    .fieldTitle {
      color: #838a99;
      .custom-field {
        margin-bottom: 4px;
      }
    }
    .list {
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 12px 0;
      padding: 12px 0;
      overflow-y: auto;
    }
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 22px;
      cursor: pointer;
    }
    .text {
      max-width: 100px;
      color: #1d2129;
      font-weight: 400;
    }
  }
  &-default {
    flex: 1;
    padding-bottom: 5px;
    overflow: hidden;
  }
  &-custom {
    flex: 1;
    .body {
      display: flex;
      flex-direction: column;
      gap: 12px 0;
    }
  }
}
</style>
