<template>
  <page-wrapper class="kanban_wrap">
    <header class="kanban_header">
      <el-icon class="iconfont back"><el-icon-direction-back /></el-icon>
      <span class="title">看板配置</span>
    </header>
    <main class="kanban_body">
      <el-form
        ref="form"
        :model="configForm"
        :rules="rules"
        label-position="top"
      >
        <el-row class="kanban_item">
          <div class="title">基本信息</div>
          <el-form-item label="看板名称" prop="name">
            <el-input
              v-model="configForm.name"
              placeholder="请输入"
              style="width: 320px"
            />
          </el-form-item>
        </el-row>
        <el-row class="kanban_item">
          <div class="title">泳道设置</div>
          <el-form-item label="泳道划分方式" prop="channel">
            <el-tooltip
              content="同一分类下采用相同表单的工作项类型可配置自定义分组"
              placement="right"
              class="channel_tip"
            >
              <el-icon><el-icon-warning-outline /></el-icon>
            </el-tooltip>
            <el-radio-group v-model="configForm.channel" class="channel_group">
              <el-radio label="default">无泳道</el-radio>
              <el-radio label="handleBy">按处理人划分</el-radio>
              <el-radio label="requirementId">按需求划分</el-radio>
              <el-radio label="parentId">按父级工作项划分</el-radio>
              <el-radio
                v-for="v in customChannels"
                :key="v.key"
                :label="v.key"
                >{{ v.name }}</el-radio
              >
              <!-- <el-radio label="release">按关联工作项划分</el-radio> -->
            </el-radio-group>
          </el-form-item>
        </el-row>
        <el-row class="kanban_item">
          <div class="title">看板设置</div>
          <el-form-item>
            <div style="margin: 12px 0">
              <i style="color: red; margin-right: 4px">*</i>
              <span>工作项类型(多选)</span>
            </div>

            <!-- 工作项类型 -->
            <workPopper
              :work-selected-type="workSelectedType"
              :type-map="typeMap"
              @checkType="triggerWorkItem"
            />
            <div v-if="workSelectedType.length === 0" class="type-empty">
              <svg-icon icon-class="empty-file" />
              <span class="text">暂无看板</span>
            </div>
            <div v-show="workSelectedType.length > 0">
              <el-tag
                v-for="item in configForm.workType"
                :key="item.id"
                class="workTag"
                closable
                @close="triggerWorkItem(item)"
                >{{ item.classify.desc + '/' + item.name }}</el-tag
              >

              <!-- 看板列设置 -->
              <kanbanColumns
                ref="columns"
                :work-type="configForm.workType"
                :status-map="statusFlowMap"
                :saved-columns="savedColumns"
                @updateStatusCols="updateStatusCols"
                @updateCols="getColumns"
              />
            </div>
          </el-form-item>
        </el-row>
        <el-row class="kanban_item">
          <div class="title">高级设置</div>
          <el-form-item label="排序设置" prop="sort">
            <el-radio-group v-model="configForm.sort">
              <el-radio label="default">默认排序</el-radio>
              <el-radio label="priority_up"
                >按优先级<el-icon><el-icon-bottom /></el-icon
              ></el-radio>
              <el-radio label="priority_down"
                >按优先级<el-icon><el-icon-top /></el-icon
              ></el-radio>
              <el-radio label="planEtime_down"
                >按计划完成时间<el-icon><el-icon-bottom /></el-icon
              ></el-radio>
              <el-radio label="planEtime_up"
                >按计划完成时间<el-icon><el-icon-top /></el-icon
              ></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="卡片设置" prop="cardConfig">
            <el-radio-group v-model="configForm.cardConfig">
              <el-radio label="init">统一设置</el-radio>
              <el-radio label="custom">按工作项类型设置</el-radio>
            </el-radio-group>
            <div class="card_config">
              <!-- 字段定义 -->
              <configFields
                v-for="item in cardConfigList"
                :key="item.type"
                :config-type="configForm.cardConfig"
                :card-data="item"
                :customs="item.customs"
                :custom-fields="customFields"
                @updateCustoms="(data) => updateCardCustoms(item, data)"
                @updateDefault="(data) => updateDefault(item, data)"
              />
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </main>
    <footer class="kanban_footer">
      <el-button @click="backPage">取消</el-button>
      <el-button type="primary" @click="saveConfig">确定</el-button>
    </footer>
  </page-wrapper>
</template>

<script>
import {
  DirectionBack as ElIconDirectionBack,
  WarningOutline as ElIconWarningOutline,
  Bottom as ElIconBottom,
  Top as ElIconTop,
} from '@element-plus/icons-vue'
import {
  addBoard,
  editBoard,
  getBoardInfoByKanbanId,
  getProjectWorkStatusFlow,
} from '@/api/vone/project/board'

import configFields from './config-fields.vue'
import kanbanColumns from './kanban-columns.vue'
import workPopper from './work-popper.vue'
import { catchErr } from '@/utils'
import { apiAlmTypeNoPage } from '@/api/vone/alm'
import { cloneDeep } from 'lodash'
import { apiVaBaseCustomProjectFormCheck } from '@/api/vone/base/customForm'
import { apiAlmTypePage } from '@/api/vone/base/work-flow'

export default {
  components: {
    configFields,
    kanbanColumns,
    workPopper,
    ElIconDirectionBack,
    ElIconWarningOutline,
    ElIconBottom,
    ElIconTop,
  },
  data() {
    return {
      configForm: {
        name: '',
        channel: 'default',
        workType: [],
        sort: 'default',
        cardConfig: 'init',
      },
      boardData: {},
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            min: 1,
            max: 100,
            message: '名称不能超过100个字符',
            trigger: 'blur',
          },
        ],
        channel: [
          { required: true, message: '请选择泳道划分方式', trigger: 'blur' },
        ],
        workType: [
          { required: true, message: '请配置工作项类型', trigger: 'change' },
        ],
      },
      typeMap: {
        ISSUE: [],
        TASK: [],
        BUG: [],
      },
      customFields: {
        ISSUE: [],
        TASK: [],
        BUG: [],
      },
      savedColumns: [],
      columns: [],
      statusFlowMap: {},
      workSelectedType: [], // 选中工作项类型id
      workStatusList: [], // 选中工作项类型列表
      customChannels: [],
      cardConfigList: [
        {
          title: '这是一个需求',
          head: {
            color: '#8791fa',
            icon: 'el-icon-icon-xuqiu',
          },
          date: '2022-01-02',
          code: 'STORY-32',
          type: 'ISSUE',
          priority: {
            color: '#4bccbb',
            icon: 'el-icon-icon-dengji-zuidi2',
          },
          status: {
            color: '#2cc750',
            text: '已完成',
          },
          defaultFields: ['编号', '规模', '优先级', '计划完成时间', '处理人'],
          customs: [],
        },
        {
          title: '这是一个任务',
          head: {
            color: '#3e7bfa',
            icon: 'el-icon-icon-renwu',
          },
          date: '2022-01-02',
          code: 'TASK-20',
          type: 'TASK',
          priority: {
            color: '#3e7bfa',
            icon: 'el-icon-icon-dengji-putong2',
          },
          status: {
            color: '#8c8c8c',
            text: '未开始',
          },
          defaultFields: ['编号', '工时', '优先级', '计划完成时间', '处理人'],
          customs: [],
        },
        {
          title: '这是一个缺陷',
          head: {
            color: '#fa6b57',
            icon: 'el-icon-icon-quexian',
          },
          date: '2022-01-02',
          code: 'BUG-39',
          type: 'BUG',
          priority: {
            color: '#FA6A69',
            icon: 'el-icon-icon-dengji-zuigao2',
          },
          status: {
            color: '#42aaff',
            text: '进行中',
          },
          defaultFields: ['编号', '规模', '优先级', '计划完成时间', '处理人'],
          customs: [],
        },
      ],
    }
  },
  mounted() {
    Promise.allSettled([
      this.getWorkItemTypes('ISSUE'),
      this.getWorkItemTypes('TASK'),
      this.getWorkItemTypes('BUG'),
    ]).then(() => {
      this.getKanbanInfo()
    })
    // 查询自定义字段
    this.getCustomFields('ISSUE')
    this.getCustomFields('TASK')
    this.getCustomFields('BUG')
  },
  methods: {
    backPage() {
      const { projectKey, projectTypeCode, id } = this.$route.params

      this.$router.push({
        path: `/project/setting/${projectKey}/${projectTypeCode}/${id}`,
        query: {
          active: 'kanban_config',
        },
      })
    },
    // 查询不同类型自定义字段
    async getCustomFields(type) {
      const res = await apiVaBaseCustomProjectFormCheck(
        this.$route.params.id,
        type
      )
      if (res.isSuccess) {
        this.customFields[type] = res.data.filter((v) => !v.isBasic && v.isShow)
      }
    },
    // 查询工作项类型
    async getWorkItemTypes(type) {
      const res = await apiAlmTypeNoPage({ classify: type })
      if (res.isSuccess) {
        this.typeMap[type] = res.data
      }
    },
    // 查询工作项类似状态工作流
    async getWorkStatusList(item, multiple) {
      const data = multiple
        ? item
        : {
            [item.classify.code]: [item.code],
          }
      const res = await getProjectWorkStatusFlow(this.$route.params.id, data)
      if (res.isSuccess) {
        for (const key in res.data) {
          const states = res.data[key].map((v) => v.echoMap.stateCode)
          this.$set(this.statusFlowMap, key, states || [])
        }
      }
    },
    async getKanbanInfo() {
      const { kanbanId } = this.$route.params
      if (kanbanId === 'add') return
      const res = await getBoardInfoByKanbanId(kanbanId)
      if (res.isSuccess) {
        const { channel, sort, cardConfig, customs, defaultFields, workType } =
          JSON.parse(res.data.config)
        this.boardData = res.data
        this.workSelectedType = []
        this.configForm = {
          name: res.data.name,
          sort,
          channel,
          cardConfig,
          workType: [],
        }
        // 设置卡片自定义字段名
        for (const type in customs) {
          const card = this.cardConfigList.find((val) => val.type === type)
          card.customs = customs[type]
          card.defaultFields = defaultFields[type]
        }
        const classifyMap = {}
        for (const id in workType) {
          const type = workType[id]
          const work = this.typeMap[type].find((v) => v.id === id)
          if (!work) continue
          this.configForm.workType.push(work)
          this.workSelectedType.push(id)
          if (classifyMap[work.classify.code]) {
            classifyMap[work.classify.code].push(work.code)
          } else {
            classifyMap[work.classify.code] = [work.code]
          }
        }
        // 校验是否显示自定义泳道字段
        this.checkCustomGroup()
        // 查询保存的工作项类似状态
        await this.getWorkStatusList(classifyMap, true)
        const cols = res.data.columns
          .map((item) => {
            item.exsited = {}
            item.statuses = item.statuses.map((ele) => {
              const workType =
                this.configForm.workType.find(
                  (val) => val.code === ele.typeCode
                ) || {}
              const obj = { ...workType }
              // 当前列已存在id
              item.exsited[obj.id + '_' + ele.stateCode] = true
              obj.typeCode = ele.typeCode
              const index = this.statusFlowMap[ele.typeCode]?.findIndex(
                (v) => v.code === ele.stateCode
              )
              const status = this.statusFlowMap[ele.typeCode]?.splice(
                index,
                1
              )[0]
              obj.status = status || {}
              return obj
            })
            item.statuses = item.statuses.filter(
              (e) => Object.keys(e.status).length > 0
            )
            return item
          })
          .sort((a, b) => a.sort - b.sort)

        this.savedColumns = cols
        this.getColumns(cols)
      }
    },
    triggerWorkItem(item) {
      if (!item) return
      const exited = this.workSelectedType.indexOf(item.id) > -1
      exited ? this.deleteWorkItem(item) : this.addWorkItem(item)
      this.checkCustomGroup()
    },
    async addWorkItem(item) {
      this.workSelectedType = [...this.workSelectedType, item.id]
      this.configForm.workType = [...this.configForm.workType, item]
      // 查询工作项状态后设置列显示
      await this.getWorkStatusList(item)
      this.$nextTick(() => {
        this.$refs.columns.refreshColumns()
      })
    },
    deleteWorkItem(item) {
      this.workSelectedType = this.workSelectedType.filter(
        (id) => id != item.id
      )
      this.configForm.workType = this.configForm.workType.filter(
        (val) => val.id != item.id
      )
      if (this.workSelectedType.length == 0) {
        this.savedColumns = []
      }
    },
    updateStatusCols(data) {
      for (const key in data) {
        this.statusFlowMap[key] =
          data[key].length > 0 ? [...this.statusFlowMap[key], ...data[key]] : []
      }
    },
    // 设置看板列
    getColumns(columns) {
      const { kanbanId } = this.$route.params
      const clones = cloneDeep(columns)
      this.columns = clones.map((col, i) => {
        col.sort = i
        col.id = null
        col.kanbanId = kanbanId !== 'add' ? kanbanId : null
        col.statuses = col.statuses.map((val) => {
          const obj = {
            classifyCode: val.classify?.code,
            color: val.status.color,
            kanbanColumnId: kanbanId !== 'add' ? col.id : null,
            kanbanId: kanbanId !== 'add' ? kanbanId : null,
            stateCode: val.status.code,
            typeCode: val.code,
          }
          return obj
        })
        return col
      })
    },
    // 是否显示自定义分组
    checkCustomGroup() {
      this.configForm.channel = 'default'
      this.customChannels = []
      if (this.configForm.workType.length == 0) return

      const codeList = []
      for (const ele of this.configForm.workType) {
        codeList.push(ele.code)
      }
      this.getIssueType(codeList)
    },
    // 查询类型下所有分类
    async getIssueType(codeList) {
      const res = await apiAlmTypePage({
        current: 1,
        extra: {},
        model: { classify: '' },
        order: 'descending',
        size: 99999,
        sort: 'createTime',
      })
      if (res.isSuccess) {
        const typeCodes =
          res.data.records?.filter((ele) => codeList.indexOf(ele.code) > -1) ||
          []
        let formId = null
        let formFields = []

        for (const val of typeCodes) {
          if (!formId) {
            formId = val.echoMap?.customFormId?.id
            formFields = val.echoMap?.customFormId?.customFormFields
          } else if (formId != val.echoMap.customFormId.id) {
            formId = null
            formFields = []
            this.configForm.channel = 'default'
            this.customChannels = []
            return
          }
        }
        const exclude = ['planId', 'tagId', 'projectId', 'typeCode']
        this.customChannels =
          formFields?.filter(
            (v) =>
              exclude.indexOf(v.key) === -1 &&
              (v.type.code === 'SELECT' || v.type.code === 'LINKED')
          ) || []
      }
    },
    updateDefault(item, fields) {
      item.defaultFields = fields
    },
    // 更新自定义字段
    updateCardCustoms(item, customs) {
      item.customs = customs
    },
    async saveConfig() {
      if (this.workSelectedType.length == 0) {
        this.$message.error('请配置工作项类型')
        return
      }
      try {
        await this.$refs.form.validate((valid) => {
          if (!valid) throw Error('valid false')
        })
      } catch (err) {
        return
      }

      const { id, kanbanId } = this.$route.params
      const workType = this.configForm.workType.reduce(
        (acc, cur) => (acc[cur.id] = cur.classify.code) && acc,
        {}
      )
      const customs = {}
      const defaultFields = {}
      for (const value of this.cardConfigList) {
        customs[value.type] = value.customs
        defaultFields[value.type] = value.defaultFields
      }
      const config = {
        channel: this.configForm.channel,
        sort: this.configForm.sort,
        cardConfig: this.configForm.cardConfig,
        defaultFields,
        customs,
        workType,
      }
      const params = {
        id: kanbanId !== 'add' ? kanbanId : null,
        projectId: id,
        name: this.configForm.name,
        state: true,
        sort: this.boardData.sort || 0,
        columns: this.columns,
        config: JSON.stringify(config),
      }
      kanbanId === 'add'
        ? this.addKanbanBoard(params)
        : this.editKanbanBoard(params)
    },
    async addKanbanBoard(params) {
      const res = await addBoard(params)
      if (res.isSuccess) {
        this.$message.success('新建成功')
        this.backPage()
      } else {
        this.$message.error('新建失败')
      }
    },
    async editKanbanBoard(params) {
      const [res, err] = await catchErr(editBoard(params))
      if (err) return
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.backPage()
      } else {
        this.$message.error('修改失败')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__label {
    margin: 12px 0;
    height: 22px;
    line-height: 22px;
    font-weight: 400;
  }
  .el-form-item__content {
    line-height: normal;
  }
  .el-radio__label {
    font-weight: 400;
    color: #1d2129;
  }
}
.kanban_ {
  &wrap {
    height: 100%;
    padding: 0px;
  }
  &header {
    display: flex;
    align-items: center;
    height: 56px;
    padding-left: 16px;
    border-bottom: 1px solid #ebeef5;
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
    }
  }
  &body {
    height: calc(100% - 50px);
    margin-bottom: 60px;
    .title {
      height: 22px;
      line-height: 22px;
      font-weight: 500;
      color: #1d2129;
    }
  }
  &item {
    padding: 16px;
    &:not(:last-child) {
      border-bottom: 1px solid #ebeef5;
    }
  }
  &footer {
    position: fixed;
    bottom: 0px;
    height: 60px;
    width: calc(100% - 72px);
    margin-left: -16px;
    padding: 16px 20px;
    text-align: right;
    background: #ffffff;
    box-shadow: 6px -3px 12px rgba(29, 33, 41, 0.06);
  }
}
.back {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 30px;
  height: 30px;
  margin-right: 16px;
  color: var(--main-theme-color);
  box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
  border-radius: 16px;
}
.type-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  height: 70px;
  svg {
    width: 50px;
    height: 40px;
  }
  .text {
    font-weight: 400;
    color: #838a99;
  }
}
.channel_tip {
  position: absolute;
  top: -30px;
  left: 100px;
}
.channel_group {
  display: flex;
  gap: 12px 0;
  flex-wrap: wrap;
}

.workTag {
  font-weight: 500;
  color: #1d2129;
  background-color: #f2f3f5;
  border-radius: 2px;
  margin-right: 4px;

  :deep(.el-tag__close) {
    color: #6b7385;
    &:hover {
      color: var(--main-theme-color);
      background-color: #f2f3f5;
    }
  }
}

.card_config {
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;
  gap: 16px;
}
</style>
