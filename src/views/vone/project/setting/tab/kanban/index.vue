<template>
  <div class="pageBox pageContentNoH">
    <header class="header">
      <span class="title">看板管理</span>
      <el-button
        type="primary"
        :disabled="!$permission('project_setting_kanban_add')"
        :icon="el-icon-setting"
        class="addBtn"
        @click="createBoard"
        >新建</el-button
      >
    </header>
    <main style="height: calc(100vh - 202rem)">
      <vxe-table
        ref="kanbanTable"
        class="vone-vxe-table kanbanTable"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData"
        row-id="id"
        :column-config="{ minWidth: '120px' }"
      >
        <vxe-column title="看板名称" field="name">
          <template slot="header">
            <span style="margin-left: 22px">看板名称</span>
          </template>
          <template slot-scope="{ row, rowIndex }">
            <el-icon class="iconfont handle"
              ><el-icon-icon-line-tuozhuai
            /></el-icon>
            <span>{{ row.name }}</span>
            <span v-if="rowIndex === defaultIndex" class="default">默认</span>
          </template>
        </vxe-column>
        <vxe-column title="泳道" field="channel">
          <template slot-scope="{ row }">
            <span v-if="row.channel">{{ channelMap[row.channel] }}</span>
            <span v-else>无泳道</span>
          </template>
        </vxe-column>
        <vxe-column title="看板栏" field="tabs" />
        <vxe-column title="状态" width="120">
          <template slot-scope="{ row }">
            <el-switch
              v-model="row.state"
              :disabled="!$permission('project_setting_kanban_edit')"
              class="switchStyle"
              active-text="启用"
              inactive-text="禁用"
              @change="(val) => switchBoardEnable(row, val)"
            />
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="80">
          <template slot-scope="{ row }">
            <el-tooltip content="编辑" placement="top">
              <el-button
                :disabled="!$permission('project_setting_kanban_edit')"
                type="text"
                :icon="el-icon-setting"
                @click="editConfig(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip content="删除" placement="top">
              <el-button
                :disabled="!$permission('project_setting_kanban_del')"
                type="text"
                :icon="el-icon-setting"
                @click="deleteConfig(row)"
              />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
  </div>
</template>

<script>
import {
  deleteBoards,
  getProjectBoards,
  sortProjectBoard,
  toggleBoardEnable,
} from '@/api/vone/project/board'
import { catchErr } from '@/utils'
import Sortable from 'sortablejs'

export default {
  data() {
    return {
      tableData: [],
      channelMap: {
        default: '无泳道',
        handleBy: '按处理人划分',
        parent: '按父级工作项划分',
        release: '按关联工作项划分',
      },
    }
  },
  computed: {
    defaultIndex() {
      return this.tableData.findIndex((v) => v.state)
    },
  },
  mounted() {
    this.getBoardsInfo()
  },
  methods: {
    // 表格行拖拽
    rowDrop() {
      const tbody = document.querySelector(
        '.kanbanTable .vxe-table--body-wrapper tbody'
      )

      Sortable.create(tbody, {
        group: 'list',
        handle: '.handle',
        animation: 150,
        avoidImplicitDeselect: true, // 外部点击不能取消选中
        removeCloneOnHide: true,
        dragClass: 'draggingRow', // 拖动中的dom类名
        // 拖拽结束回调，处理添加到左侧分组业务逻辑
        onEnd: (evt) => {
          const { newIndex, oldIndex } = evt
          if (newIndex === oldIndex) return
          const moved = this.tableData.splice(oldIndex, 1)[0]

          this.tableData.splice(newIndex, 0, moved)
          const ids = this.tableData.map((v) => v.id)
          sortProjectBoard(ids).then((res) => {
            if (res.isSuccess) {
              this.$message.success('修改成功')
            } else {
              this.$message.error('修改失败')
            }
          })
        },
      })
    },
    async getBoardsInfo() {
      const res = await getProjectBoards(this.$route.params.id)
      if (res.isSuccess) {
        this.tableData = res.data.map((ele) => {
          const config = JSON.parse(ele.config)
          ele.channel = config.channel
          if (ele.columns.length) {
            ele.tabs = ele.columns.map((col) => col.name).join(',')
          }
          return ele
        })
        this.$nextTick(() => {
          this.rowDrop()
        })
      }
    },
    createBoard() {
      const { projectKey, projectTypeCode, id } = this.$route.params

      this.$router.push({
        path: `/project/settings/board/${projectKey}/${projectTypeCode}/${id}/add`,
      })
    },
    editConfig(row) {
      const { projectKey, projectTypeCode, id } = this.$route.params

      this.$router.push({
        path: `/project/settings/board/${projectKey}/${projectTypeCode}/${id}/${row.id}`,
      })
    },
    async deleteConfig(row) {
      const confirm = await catchErr(
        this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
          type: 'warning',
          closeOnClickModal: false,
          customClass: 'delConfirm',
          showClose: false,
        })
      )
      if (confirm[1]) return
      const [res, err] = await catchErr(deleteBoards([row.id]))
      if (err) return
      if (res.isSuccess) {
        this.$message.success('删除成功')
        this.getBoardsInfo()
      } else {
        this.$message.error('删除失败')
      }
    },
    async switchBoardEnable(row, enable) {
      const res = await toggleBoardEnable(row.id, enable)
      if (!res.isSuccess) {
        this.$message.error('切换失败')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.pageBox {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 16px;
  overflow-y: auto;
}
header.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }
}
.default {
  line-height: 18px;
  padding: 0 6px;
  margin-left: 6px;
  font-weight: 500;
  color: #838a99;
  border-radius: 2px;
  border: 1px solid #838a99;
}
.switchStyle :deep() {
  .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }
  .el-switch__label--left {
    z-index: 9;
    left: 18px;
  }
  .el-switch__label--right {
    z-index: 9;
    left: -5px;
  }
  .el-switch__label.is-active {
    display: block;
  }
  .el-switch__core {
    width: 54px !important;
  }
  .el-switch__label {
    width: 54px !important;
  }
}
.handle {
  display: inline-block;
  color: #6b7385;
  cursor: grab;
  margin-right: 4px;
  &:hover {
    color: var(--main-theme-color);
  }
}
</style>
