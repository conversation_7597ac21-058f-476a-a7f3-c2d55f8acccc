<template>
  <el-popover
    v-model="boardShow"
    placement="bottom-start"
    trigger="click"
    popper-class="status_columns-popper"
  >
    <el-input v-model="filterboard" placeholder="请输入关键字" />
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.name"
        :label="tab.label"
        :name="tab.name"
      >
        <div v-if="typeMap[activeName].length == 0" class="nothing">
          暂无数据
        </div>
        <div v-else>
          <ul class="board-list" @click="boardTypeClick">
            <li
              v-for="item in tabList"
              :key="item.id"
              class="board-item"
              :data-id="item.id"
              :data-name="item.name"
            >
              <span class="text" :data-id="item.id">{{ item.name }}</span>
              <el-icon class="iconfont" style="color: var(--main-theme-color)"
                ><el-icon-tips-done
              /></el-icon>
            </li>
          </ul>
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="用户需求" name="idea">
          <div v-if="ideaTypeList.length==0" class="nothing">暂无数据</div>
          <div v-else>
            <ul class="board-list" @click="boardTypeClick">
              <li v-for="item in ideaTypeList" :key="item.id" class="board-item" :data-id="item.id" :data-name="item.name">
                <span class="text">{{ item.name }}</span>
                <i v-if="workSelectedType.includes(item.id)" class="iconfont el-icon-tips-done" style="color:var(--main-theme-color)" />
              </li>
            </ul>
          </div>
        </el-tab-pane> -->
    </el-tabs>

    <template slot="reference">
      <el-button type="text" :icon="ElIconPlus" class="searchBtn"
        >添加</el-button
      >
    </template>
  </el-popover>
</template>

<script>
import {
  TipsDone as ElIconTipsDone,
  Plus as ElIconPlus,
} from '@element-plus/icons-vue'
export default {
  data() {
    return {
      boardShow: false,
      filterboard: '',
      activeName: 'ISSUE',
      tabs: [
        {
          name: 'ISSUE',
          label: '需求',
        },
        {
          name: 'TASK',
          label: '任务',
        },
        {
          name: 'BUG',
          label: '缺陷',
        },
      ],
      ElIconPlus,
    }
  },
  components: {
    ElIconTipsDone,
  },
  props: {
    workSelectedType: {
      type: Array,
      default: () => [],
    },
    typeMap: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    tabList() {
      const list = this.typeMap[this.activeName]
      return this.filterboard
        ? list.filter((val) => val.name.indexOf(this.filterboard) > -1)
        : list
    },
  },
  methods: {
    boardTypeClick(e) {
      const { id } = e.target.dataset
      const item = this.typeMap[this.activeName].find((val) => val.id === id)
      this.$emit('checkType', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.searchBtn {
  font-weight: 400;
  font-size: 14px;
  position: absolute;
  top: 6px;
  left: 110px;
}
:deep(.el-tabs__content) {
  margin: 0 -16px;
}
</style>

<style lang="scss">
.status_columns-popper {
  padding: 16px 16px 0;
  width: 325px;
  .nothing {
    line-height: 42px;
    height: 42px;
    text-align: center;
    color: #999;
  }
  .board-list {
    padding: 4px 0;
    height: 220px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .board-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 16px;
    line-height: 22px;
    cursor: pointer;
    &:hover {
      background-color: #f2f3f5;
    }
  }
  .el-tabs__nav-wrap::after {
    height: 0;
  }
  .el-tabs__header {
    margin: 0;
  }
  .el-tabs__item {
    color: #838a99;
  }
  .el-tabs__item.is-active {
    color: #3e7bfa;
  }
  .el-tabs__active-bar {
    background-color: #3e7bfa;
  }
}
</style>
