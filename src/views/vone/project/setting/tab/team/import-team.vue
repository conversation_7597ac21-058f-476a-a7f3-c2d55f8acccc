<template>
  <div>
    <el-dialog
      title="导入团队"
      width="800px"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="teamDialog">
        <div class="leftPart">
          <el-input
            v-model="searchText"
            clearable
            placeholder="搜索团队"
            @input="searchOrg"
          />
          <el-tree
            ref="orgTree"
            class="treeList"
            :data="organList"
            node-key="id"
            default-expand-all
            check-strictly
            show-checkbox
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :default-checked-keys="defaultKey"
            @check="changeOrg"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <el-tooltip
                v-showTooltips
                :content="data.label"
                placement="top"
                class="node-label"
              >
                <div>
                  <svg
                    v-if="data.major === true"
                    class="icon svg-icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-peizhi-tuandui-chanpin" />
                  </svg>
                  <svg
                    v-if="data.major === false"
                    class="icon svg-icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-jigou-zhuanye" />
                  </svg>
                  <span class="label" :data-node="node.level">{{
                    data.label
                  }}</span>
                </div>
              </el-tooltip>
            </span>
          </el-tree>
        </div>
        <div class="rightPart">
          <div style="margin: 8px 0px">已选：{{ chosePersion.length }}个</div>
          <div class="select-team">
            <div
              v-for="(item, index) in chosePersion"
              :key="index"
              class="team-main"
            >
              <span class="title">{{ item.name }}</span>
              <span style="cursor: pointer" @click="removePersion(item)">
                <el-icon><el-icon-close /></el-icon>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveTeam"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Close as ElIconClose } from '@element-plus/icons-vue'
import { importTeam } from '@/api/vone/project/team'
import { teamList } from '@/api/vone/base/team'
import { gainTreeList } from '@/utils'
function disabledFun(data, selectData) {
  data.map((item) => {
    if (item?.children?.length > 0) {
      disabledFun(item.children, selectData)
    }
    if (selectData && selectData.includes(item.id)) {
      item.disabled = true
    }
  })
}
export default {
  components: {
    ElIconClose,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    checkList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      organList: [],
      searchText: '',
      checkedList: [],
      chosePersion: [],
      saveLoading: false,
      defaultKey: [],
    }
  },
  mounted() {
    this.getPersionList()
  },
  methods: {
    searchOrg() {
      this.$refs.orgTree.filter(this.searchText)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name != null && data.name.indexOf(value) !== -1
    },
    // 获取左侧列表
    getPersionList(item) {
      this.treeLoading = true
      teamList({ type: 'OrgTeam' }).then((res) => {
        this.treeLoading = false
        const orgTree = gainTreeList(res.data || [])
        this.organList = orgTree
        this.disabledData(orgTree)
        this.defaultKey = this.checkList
      })
    },
    // 禁用已选的数据
    disabledData(data) {
      disabledFun(data, this.checkList)
    },
    changeOrg(val) {
      const checkData = this.$refs.orgTree
        .getCheckedNodes()
        .filter((v) => !v.disabled)
      this.chosePersion = checkData.filter(
        (item) => !this.checkList.includes(item.id)
      )
      this.checkedList = checkData.map((item) => {
        return item.id
      })
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    // 弹窗移除选中人
    removePersion(row) {
      this.chosePersion.map((item, index) => {
        if (item.id == row.id) {
          this.chosePersion.splice(index, 1)
          this.checkedList.splice(index, 1)
        }
      })
      this.$refs.orgTree.setChecked(row.id, false, true)
    },
    // 保存机构
    saveTeam() {
      if (this.chosePersion.length < 1) {
        this.$message.warning('请选择团队')
        return
      }
      const data = this.chosePersion.map((item) => item.id)
      this.saveLoading = true
      const params = {
        projectId: this.$route.params.id,
        teamIds: data,
      }
      importTeam(params)
        .then((res) => {
          this.saveLoading = false
          if (res.isSuccess) {
            this.$emit('update:visible', false)
            this.$emit('success')
            this.$message.success('添加成功')
          } else {
            this.$message.warning(res.msg)
          }
        })
        .catch(() => {
          this.saveLoading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-cascader__search-input) {
  margin: 2px 0 2px 13px;
}
:deep(.el-dialog .el-dialog__body) {
  padding: 0px;
}
.teamDialog {
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  .leftPart {
    width: 400px;
    padding: 16px;
    border-right: 1px solid var(--disabled-bg-color);
    height: calc(100vh - 10vh - 115rem);
  }
  .rightPart {
    width: 400px;
    padding: 16px;
    height: calc(100vh - 10vh - 115rem);
  }
}
.treeList,
.select-team {
  height: calc(100vh - 10vh - 165rem);
  overflow-y: auto;
  .team-main {
    display: flex;
    align-items: center;
    height: 30px;
    .title {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.custom-tree-node {
  flex: 1;
  width: calc(100% - 90px);
  height: 100%;
  display: flex;
  align-items: center;
}
.el-tree-node__content {
  height: 36px;
  color: var(--font-main-color);
  display: inline-block;
  .node-label {
    display: inline-block;
    width: calc(100% - 90px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .svg-icon {
      width: 16px;
      height: 16px;
    }
  }
  .svg-icon {
    margin-right: 4px;
    vertical-align: -0.25em;
  }
}
</style>
