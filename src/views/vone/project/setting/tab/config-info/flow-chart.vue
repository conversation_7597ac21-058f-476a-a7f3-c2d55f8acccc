<template>
  <div v-loading="cardLoading" class="pageBox pageContentNoH">
    <el-row type="flex" class="rowLine">
      <el-col :span="12" class="header">
        <span class="back" @click="goBack">
          <el-icon class="iconfont"><ElIconArrowLeft /></el-icon
        ></span>
        <strong>预览{{ `【${typeName}】` }}流程图</strong>
      </el-col>
    </el-row>

    <vone-work-flow
      ref="vone-g"
      :xml="xml"
      hide-text-annotation
      single
      :node-style="nodeStyle"
      :properties-props="{ width: 250 }"
    >
      <div slot="properties" slot-scope="{ element }">
        <el-form-item label="进度">
          <el-input-number
            :model-value="element.data.progress"
            :step="5"
            :min="0"
            :max="100"
            controls-position="right"
            @input="(v) => progressChange(element, v)"
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            key="nodeType"
            v-model="element.data.nodeType"
            style="width: 180px"
            clearable
            placeholder="请选择类型"
            @change="(v) => typeChange(element, v)"
          >
            <el-option label="开始节点" value="START_NODE" />
            <el-option label="中间节点" value="INTERMEDIATE_NODE" />
            <el-option label="结束节点" value="END_NODE" />
          </el-select>
        </el-form-item>
      </div>
      <div slot="connect" slot-scope="{ element }">
        <el-form-item label="指定人员">
          <el-select
            key="permission"
            v-model="element.data.permission"
            style="width: 180px"
            multiple
            placeholder="请选择权限"
            @change="(v) => userChange(element, v)"
          >
            <el-option label="处理人" value="HANDLER" />
            <el-option label="负责人" value="RESPONSIBLE" />
            <el-option label="创建人/提出人" value="CREATOR" />
          </el-select>
        </el-form-item>
        <!-- {{ element .data.role }} -->
        <el-form-item label="指定角色">
          <el-select
            key="role"
            v-model="element.data.role"
            style="width: 180px"
            multiple
            placeholder="请选择角色"
            @change="(v) => roleChange(element, v)"
          >
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="指定用户">
          <el-select
            key="user"
            v-model="element.data.user"
            style="width: 180px"
            multiple
            placeholder="请选择用户"
            @change="(v) => personChange(element, v)"
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </div>
    </vone-work-flow>
  </div>
</template>

<script>
// import { DirectionBack as ElIconDirectionBack } from '@element-plus/icons-vue'
import { apiProjectUserNoPage, getFlowDetail } from '@/api/vone/project/index'
import { queryProjectRoleById } from '@/api/vone/project/team'

import _ from 'lodash'
import { jsonToXml } from './xmlUtils'
export default {
  components: {
    // ElIconDirectionBack,
  },
  props: {
    typeName: {
      type: String,
      default: undefined,
    },
    workFlowId: {
      type: String,
      default: undefined,
    },
    typeClassify: {
      type: String,
      default: null,
    },
    typeCode: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      cardLoading: false,
      nodeStyle: {
        width: 100,
        height: 36,
      },
      xml: undefined,
      roleList: [], // 项目下角色,
      userList: [], // 项目下人员
    }
  },
  mounted() {
    this.getFlow()
    this.getRole()
    this.getUser()
  },
  methods: {
    progressChange(element, v) {
      element.el.businessObject.set('progress', v)
    },
    typeChange(element, v) {
      element.el.businessObject.set('nodeType', v)
    },
    userChange(element, v) {
      element.el.businessObject.set('permission', v)
    },
    roleChange(element, v) {
      element.el.businessObject.set('role', v)
    },
    personChange(element, v) {
      element.el.businessObject.set('user', v)
    },
    // 查询人员
    async getUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.userList = res.data
    },

    // 查询角色
    async getRole() {
      const res = await queryProjectRoleById({
        projectId: this.$route.params.id,
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.roleList = res.data
    },
    async getFlow() {
      this.cardLoading = true
      let nodes = []
      let edges = []
      await getFlowDetail(
        this.$route.params.id,
        this.typeClassify,
        this.typeCode
      ).then((res) => {
        this.cardLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
        } else {
          if (
            res.data?.workflowInfo?.workflowNodes.length > 0 &&
            res.data?.workflowInfo?.workflowTransitions.length > 0
          ) {
            this.workflowId =
              res.data?.workflowInfo?.workflowNodes[0].workflowId
            nodes = _.cloneDeep(res.data?.workflowInfo?.workflowNodes)
            edges = _.cloneDeep(res.data?.workflowInfo?.workflowTransitions)
            nodes.map((item) => {
              item.nodeType = item.nodeType.code
              item.color = item.echoMap.stateCode.color
              item.onlyId = item.id
              item.id = item.code
              delete item.shape
              delete item.size
              delete item.echoMap
            })
            edges.map((item) => {
              item.onlyId = item.id
              item.id = item.code
              item.source = item.sourceAnchor
              item.target = item.targetAnchor
              const permissions = []
              const roles = []
              const users = []
              res.data?.workflowInfo?.workflowAuthorities.map((itm) => {
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'RESPONSIBLE'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'HANDLER'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'CREATOR'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'ROLE'
                ) {
                  roles.push(itm.userOrRole)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'USER'
                ) {
                  users.push(itm.userOrRole)
                }
              })

              item.permission = _.compact(permissions)
              item.role = _.compact(roles)
              item.user = _.compact(users)
            })

            this.xml = jsonToXml({ nodes, edges })
          }
        }
      })
    },

    goBack() {
      this.$emit('back')
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.bpmn) {
  height: calc(100vh - 180px);
}

.pageBox {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  :deep(.el-card__body) {
    padding: 16px;
  }
  :deep(.el-form--label-top .el-form-item__label) {
    font-weight: bold;
  }
  .rowLine {
    border-bottom: 1px solid var(--el-divider);
    min-height: 32px;
    line-height: 32px;
    padding: 0 16px 16px;
  }
  .header {
    display: flex;
    align-items: center;
    .back {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      width: 30px;
      height: 30px;
      color: var(--main-theme-color);
      box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
      border-radius: 16px;
    }
  }
}
</style>
