<template>
  <div v-loading="treeLoading" class="contentbox">
    <div class="left pageContentNoH">
      <el-tree
        ref="tree"
        :data="treeData"
        :expand-on-click-node="false"
        default-expand-all
        :highlight-current="true"
        :props="defaultProps"
        node-key="id"
        @node-click="currentChange"
      >
        <span slot-scope="{ node }" class="custom-tree-node">
          <span>{{ node.label }}</span>
          <span
            v-if="$permission(`project_setting_${typeTransform[type]}_form`)"
            class="operation-icon"
          >
            <!-- <el-tooltip class="item" effect="dark" content="配置表单" placement="top-start">
                <el-button type="text" size="mini" icon="el-icon-setting" @click.stop="() => configForm(data)" />
              </el-tooltip> -->
          </span>
        </span>
      </el-tree>
    </div>
    <!-- 配置工作流节点显示字段 -->
    <configFlowForm
      v-if="showFlow"
      :custom-form-id="customFormId"
      :type-classify="typeClassify"
      :type-code="typeCode"
      :allcustom="allcustom"
      :type-name="typeName"
      @back="back"
    />

    <!-- 预览流程图 -->
    <previewFlow
      v-else-if="showPreviewFlow"
      :work-flow-id="workFlowId"
      :custom-form-id="customFormId"
      :type-classify="typeClassify"
      :type-code="typeCode"
      :allcustom="allcustom"
      :type-name="typeName"
      @back="back"
    />

    <div v-else v-loading="tableLoading" class="card_content pageContentNoH">
      <el-row class="toolbars">
        <div style="flex: 1" class="title">
          {{ typeInfo.name }}
        </div>
        <div style="display: flex; align-items: center">
          <el-button
            type="primary"
            :disabled="
              !$permission(`project_setting_${typeTransform[type]}_switchFlow`)
            "
            :icon="el-icon-setting"
            @click="changeWork"
            >切换工作流</el-button
          >
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"
              ><el-icon class="iconfont"><ElIconSetting /></el-icon
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="showCheckBox"
                :disabled="
                  !$permission(
                    `project_setting_${typeTransform[type]}_circulation`
                  )
                "
              >
                批量修改节点流转权限
              </el-dropdown-item>
              <el-dropdown-item
                v-if="showChecked"
                :command="showConfigs"
                :disabled="
                  !$permission(
                    `project_setting_${typeTransform[type]}_circulation`
                  )
                "
              >
                批量流转
              </el-dropdown-item>
              <el-dropdown-item
                :disabled="
                  !$permission(
                    `project_setting_${typeTransform[type]}_previewFlow`
                  )
                "
                :icon="el-icon-setting"
                :command="nodeClick"
              >
                预览流程图
              </el-dropdown-item>
              <el-dropdown-item
                :icon="el-icon-setting"
                :command="(e) => configForm(typeInfo)"
              >
                配置表单
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-row>
      <main style="height: calc(100vh - 265rem)">
        <vxe-table
          class="vone-vxe-table"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :show-header="false"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData"
          row-id="id"
          :column-config="{ minWidth: '120px' }"
        >
          <vxe-column field="label" min-width="220">
            <template #default="{ row }"> 从【{{ row.name }}】流转到 </template>
          </vxe-column>
          <template v-for="(item, index) in tableData">
            <vxe-column :key="item.id" :title="item.name" align="center">
              <template #default="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="item.name"
                  placement="top"
                >
                  <div v-if="settedArr.includes(scope.row.id + '-' + item.id)">
                    <el-checkbox
                      v-if="showChecked"
                      ref="check"
                      :source-node="scope.row"
                      :target-node="item"
                      class="mr-2"
                      @change="selectTarget(index, scope.row)"
                    />
                    <a
                      v-if="
                        $permission(
                          `project_setting_${typeTransform[type]}_circulation`
                        )
                      "
                      @click="showConfig(index, scope.row, item)"
                    >
                      <el-tag>{{ item.name }}</el-tag>
                    </a>
                    <el-tag v-else>{{ item.name }}</el-tag>
                  </div>
                </el-tooltip>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </main>
    </div>

    <!-- 流转缺陷配置 -->
    <flowConfDialog
      v-if="flowConfParams.visible"
      v-bind="flowConfParams"
      v-model="flowConfParams.visible"
      :type="type"
      :type-code="typeCode"
      v-model:select-more-data="selectMoreData"
      :work-flow-id="workFlowId"
      :tree-data="treeData"
      @noCheck="noCheck"
      @success="getTable"
    />

    <!-- 切换工作流对话框 -->
    <changeWorkDialog
      v-if="workFlowParams.visible"
      v-model="workFlowParams.visible"
      :tf-id="workFlowId"
      :type="type"
      :tree-data="treeData"
      :type-code="typeCode"
      @success="getTable"
    />
  </div>
</template>

<script>
import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { apiProjectmFindWorkflow } from '@/api/vone/project/index'
import { getFlow } from '@/api/vone/base/work-flow'

import flowConfDialog from './flowConfDialog.vue'
import changeWorkDialog from './changeWorkDialog'
import configFlowForm from './form-flow/index.vue'
import previewFlow from './flow-chart.vue'
const typeTransform = {
  ISSUE: 'issue',
  BUG: 'defect',
  TASK: 'task',
  RISK: 'risk',
}
export default {
  components: {
    flowConfDialog,
    changeWorkDialog,
    configFlowForm,
    previewFlow,
  },
  props: {
    type: {
      default: undefined,
      type: String,
    },
  },
  data() {
    return {
      treeLoading: false,
      tableLoading: false,
      showFlow: false, // 配置
      showPreviewFlow: false, // 预览
      showChecked: false,
      treeData: null,
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      tableData: [],
      settedArr: [],
      workflowFunction: [],
      flowConfParams: { visible: false },
      sourceTargetData: [], //,
      typeCode: undefined,
      workFlowId: undefined,
      selectMoreData: [],
      workFlowParams: {
        // 切换任务流
        visible: false,
      },
      typeInfo: {},
      allcustom: {},
      typeName: '',
      customFormId: '',
      typeClassify: '',
      typeTransform,
    }
  },
  mounted() {
    this.getClassfiyList()
  },
  methods: {
    back() {
      this.showFlow = false
      this.showPreviewFlow = false
    },
    async getClassfiyList() {
      this.treeLoading = true
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, this.type)
      this.treeLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.treeData = res.data
      // this.currentNodeKey = res.data[0].id
      // 设置默认选中

      this.$nextTick(() => {
        this.$refs.tree?.setCurrentKey(res.data[0].id)
        this.currentChange(res.data[0])
      })
    },
    async currentChange(val) {
      this.showFlow = false
      this.showPreviewFlow = false
      this.showChecked = false
      this.typeCode = val.code
      this.typeInfo = val
      this.getTable()
    },
    async getTable() {
      this.tableLoading = true
      const res = await apiProjectmFindWorkflow(
        this.$route.params.id,
        this.type,
        this.typeCode
      )
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.workFlowId = res.data.workflowId // 获取当前分类的任务流ID

      const res1 = await getFlow(res.data.workflowId)
      if (!res1.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableLoading = false
      this.getFlowInfo(res1.data.workflowTransitions)
      this.tableData = res1.data.workflowNodes
    },
    getFlowInfo(edges) {
      if (edges && edges.length > 0) {
        this.workflowFunction = edges
        const arr = []
        this.workflowFunction.forEach((item) => {
          arr.push(item.source + '-' + item.target)
        })
        this.settedArr = arr
      }
    },
    selectTarget() {},
    // 配置
    configForm(data) {
      this.showFlow = true
      this.typeCode = data.code
      this.typeName = data.name
      this.typeClassify = data.classify.code
    },
    // 预览
    nodeClick() {
      this.showPreviewFlow = true
      this.typeCode = this.typeInfo.code
      this.typeName = this.typeInfo.name
      this.typeClassify = this.typeInfo.classify.code
    },
    showConfig(index, row, item) {
      this.selectMoreData = []

      this.flowConfParams = { visible: true, sourceNode: row, targetNode: item }
    },
    noCheck() {
      this.showChecked = !this.showChecked
    },
    // 切换勾选时清空选择项
    showCheckBox() {
      this.showChecked = !this.showChecked
      if (this.showChecked) {
        // this.getFlowTfId(true)
      }
      this.sourceTargetData = []
      this.$nextTick(() => {
        if (this.showChecked) {
          const checkList = this.$refs.check
          checkList.forEach((item) => {
            item.model = true
          })
        }
      })
    },
    showConfigs() {
      const checkList = this.$refs.check
      checkList.forEach((item) => {
        if (item.model) {
          this.selectMoreData.push({
            sourceId: item.$attrs['source-node'].id,
            sourceName: item.$attrs['source-node'].name,
            targetId: item.$attrs['target-node'].id,
            targetName: item.$attrs['target-node'].name,
          })
        }
      })
      this.flowConfParams = { visible: true, workFlowId: this.workFlowId }
    },
    changeWork() {
      this.workFlowParams = { visible: true }
    },
  },
}
</script>

<style lang="scss" scoped>
.contentbox {
  display: flex;
}
.left {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 10px 0px;
  overflow-y: auto;
  width: 240px;
  margin-right: 10px;
}
.card_content {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}
.toolbars {
  display: flex;
  // margin-bottom: 10px;
  margin: -16px -16px 10px;
  padding: 8px 16px;
  border: 1px solid var(--solid-border-color);
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .operation-icon {
    i {
      padding-left: 6px;
      font-size: 16px;
      color: #409eff;
    }
  }
}
:deep(.el-tree-node__content) {
  height: 40px;
}
.title {
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
  color: var(--font-main-color);
}
:deep(.vxe-body--row) {
  .vxe-body--column:first-child {
    background-color: var(--content-bg-hover-color);
  }
}
</style>
