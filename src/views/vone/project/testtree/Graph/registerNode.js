import G6 from '@antv/g6'

import folder from '@/assets/graph/icon-folder.svg'
import document from '@/assets/graph/icon-document.svg'
import step from '@/assets/graph/icon-step.svg'
import highest from '@/assets/graph/icon-highest.svg'
import higher from '@/assets/graph/icon-higher.svg'
import normal from '@/assets/graph/icon-normal.svg'
import lower from '@/assets/graph/icon-lower.svg'
import lowest from '@/assets/graph/icon-lowest.svg'
const priorityMap = new Map([
  [5, {
    name: '最高',
    code: 5,
    svg: highest,
    color: '#FA6A69'
  }],
  [4, {
    name: '较高',
    code: 4,
    svg: higher,
    color: '#FA8669'
  }],
  [3, {
    name: '普通',
    code: 3,
    svg: normal,
    color: '#3e7bfa'
  }],
  [2, {
    name: '较低',
    code: 2,
    svg: lower,
    color: '#5ACC5E'
  }],
  [1, {
    name: '最低',
    code: 1,
    svg: lowest,
    color: '#4ECF95'
  }]
])
const customNode = {
  init() {
    G6.registerNode('treeNode', {
      draw: (cfg, group) => {
        const BaseConfig = {
          nameFontSize: 12,
          childCountWidth: 22,
          countMarginLeft: 0,
          itemPadding: 16,
          nameMarginLeft: 4,
          rootPadding: 12,
        }
        const { name, selected, children, depth, caseKey, priority } = cfg
        const rootNode = depth === 0
        const hasChildren = children && children.length !== 0

        const {
          childCountWidth,
          countMarginLeft,
          itemPadding,
          nameMarginLeft,
          rootPadding,
        } = BaseConfig

        let width = 0
        const height = 28
        const x = 0
        const y = -height / 2

        // 名称文本
        const text = group.addShape('text', {
          attrs: {
            text: cfg.stepType == 'testStep' ? cfg.testStep : name,
            x: x * 2,
            y,
            textAlign: 'left',
            textBaseline: 'top',
            fontFamily: 'PingFangSC-Regular',
          },
          cursor: 'pointer',
          name: 'name-text-shape',
        })
        const textWidth = text.getBBox().width
        width = textWidth + itemPadding + nameMarginLeft + 10

        // width = width < minWidth ? minWidth : width

        if (!rootNode && hasChildren) {
          width += countMarginLeft
          width += childCountWidth
        }

        const keyShapeAttrs = {
          x,
          y,
          width,
          height,
          radius: 4,
        }

        // keyShape根节点选中样式
        if (rootNode && selected) {
          keyShapeAttrs.fill = '#e8f7ff'
          keyShapeAttrs.stroke = '#e8f7ff'
        }
        const keyShape = group.addShape('rect', {
          attrs: keyShapeAttrs,
          name: 'root-key-shape-rect-shape',
        })

        // if (!rootNode) {
        //   // 底部横线
        //   group.addShape('path', {
        //     attrs: {
        //       path: [
        //         ['M', x - 1, 0],
        //         ['L', width, 0]
        //       ],
        //       stroke: '#AAB7C4',
        //       lineWidth: 1
        //     },
        //     name: 'node-path-shape'
        //   })
        // }
        const mainX = x
        const mainY = -height + 15
        if (rootNode) {
          group.addShape('rect', {
            attrs: {
              x: mainX,
              y: mainY,
              width: width,
              height,
              radius: 2,
              fill: '#fff',
              stroke: '#ebeef5',
              cursor: 'pointer',
              shadowColor: 'rgba(41, 82, 166, 0.16)',
              shadowBlur: 16,
              lineWidth: 0,
            },
            name: 'main-shape',
          })
        } else {
          group.addShape('rect', {
            attrs: {
              x: mainX,
              y: mainY,
              fill: caseKey ? '#f5f6fa' : '#fff', // 如果是用例
              width: width,
              height,
              radius: 2,
              stroke: '#ebeef5',
              cursor: 'pointer',
              shadowColor: 'rgba(41, 82, 166, 0.16)',
              shadowBlur: 16,
              lineWidth: 0,
            },
            name: 'main-shape',
          })
        }

        const nameColor = selected ? '#40A8FF' : '#202124'

        // 名称
        if (rootNode) {
          // 图标
          group.addShape('image', {
            attrs: {
              x: mainX + 5,
              y: -6,
              width: 15,
              height: 15,
              img: folder,
            },
            name: 'image-shape',
          })
          group.addShape('text', {
            attrs: {
              text: name,
              x: mainX + rootPadding + 15,
              y: 1,
              textAlign: 'left',
              textBaseline: 'middle',
              fill: nameColor,
              fontSize: 12,
              fontFamily: 'PingFangSC-Regular',
              cursor: 'pointer',
            },
            name: 'root-text-shape',
          })
        } else if (caseKey) {
          if (priority) {
            const prioInfo = priorityMap.get(priority) || priority.get(3)

            group.addShape('image', {
              attrs: {
                x: mainX + 5,
                y: mainY + 8.5,
                width: 13,
                height: 13,
                img: prioInfo.svg,
              },
              name: 'image-shape',
            })

            group.addShape('image', {
              attrs: {
                x: mainX + 22,
                y: mainY + 8.5,
                width: 13,
                height: 13,
                img: cfg.stepType == 'subclause' ? step : document,
              },
              name: 'image-shape',
            })

            group.addShape('text', {
              attrs: {
                text: name,
                x: mainX + 40,
                y: mainY + 15,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: nameColor,
                fontSize: 12,
                fontFamily: 'PingFangSC-Regular',
                cursor: 'pointer',
              },
              name: 'not-root-text-shape',
            })
          } else {
            group.addShape('text', {
              attrs: {
                text: cfg.testStep,
                x: mainX + 10,
                y: mainY + 15,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: nameColor,
                fontSize: 12,
                fontFamily: 'PingFangSC-Regular',
                cursor: 'pointer',
              },
              name: 'not-root-text-shape',
            })
          }
        } else if (cfg.type !== 'text') {
          // 图标
          group.addShape('image', {
            attrs: {
              x: mainX + 5,
              y: mainY + 7,
              width: 15,
              height: 15,
              img: folder,
            },
            name: 'image-shape',
          })
          group.addShape('text', {
            attrs: {
              text: name,
              x: mainX + 25,
              y: mainY + 15,
              textAlign: 'left',
              textBaseline: 'middle',
              fill: nameColor,
              fontSize: 12,
              fontFamily: 'PingFangSC-Regular',
              cursor: 'pointer',
            },
            name: 'not-root-text-shape',
          })
        }

        return keyShape
      },
    })
  },
}
export default customNode
