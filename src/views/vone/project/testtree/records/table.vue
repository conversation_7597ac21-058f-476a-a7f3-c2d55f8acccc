<template>
  <div>
    <div style="height: calc(100vh - 48px - 10px - 10px - 48px - 38px - 26px)">
      <vxe-table
        ref="product-case-records"
        class="vone-vxe-table"
        border
        height="auto"
        :loading="tableLoading"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        row-id="id"
      >
        <vxe-column title="状态" field="status" show-overflow-tooltip>
          <template v-slot="{ row }">
            <el-tag v-if="row.status === 'system'" class="systembox"
              >成功</el-tag
            >
            <el-tag v-else-if="row.status === 'smoke'" class="smokebox"
              >失败</el-tag
            >
            <el-tag v-else-if="row.status === 'block'" class="blockbox"
              >阻塞</el-tag
            >
            <el-tag v-else-if="row.status === 'skip'" class="skipbox"
              >跳过</el-tag
            >
            <el-tag v-else class="unbeginbox">未测</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="版本" field="version" show-overflow-tooltip>
          <template v-slot="{ row }">
            <span> {{ row.version }}</span>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priority" show-overflow-tooltip>
          <template v-slot="{ row }">
            <span v-if="row.priority === 1">
              <el-icon class="iconfont" style="color: #4ecf95"
                ><el-icon-icon-dengji-zuidi2
              /></el-icon>
              最低
            </span>
            <span v-else-if="row.priority === 2">
              <el-icon class="iconfont" style="color: #5acc5e"
                ><el-icon-icon-dengji-jiaodi2
              /></el-icon>
              较低
            </span>
            <span v-else-if="row.priority === 5">
              <el-icon class="iconfont" style="color: #fa6a69"
                ><el-icon-icon-dengji-zuigao2
              /></el-icon>
              最高
            </span>
            <span v-else-if="row.priority === 4">
              <el-icon class="iconfont" style="color: #fa8669"
                ><el-icon-icon-dengji-jiaogao2
              /></el-icon>
              较高
            </span>
            <span v-else>
              <el-icon
                class="iconfont"
                style="color: var(--main-theme-color, #3e7bfa)"
                ><el-icon-icon-dengji-putong2
              /></el-icon>
              普通
            </span>
          </template>
        </vxe-column>
        <vxe-column title="执行人" field="execBy" show-overflow-tooltip>
          <template v-slot="{ row }">
            <span v-if="row.execBy && row.echoMap && row.echoMap.execBy">
              <vone-user-avatar
                :avatar-path="row.echoMap.execBy.avatarPath"
                :name="row.echoMap.execBy.name"
              />
            </span>
            <span v-else> {{ row.execBy }}</span>
          </template>
        </vxe-column>
        <vxe-column
          title="上次执行时间"
          field="createTime"
          show-overflow-tooltip
        >
          <template v-slot="{ row }">
            {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm') }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTableData"
    />
  </div>
</template>

<script>
import {
  IconDengjiZuidi2 as ElIconIconDengjiZuidi2,
  IconDengjiJiaodi2 as ElIconIconDengjiJiaodi2,
  IconDengjiZuigao2 as ElIconIconDengjiZuigao2,
  IconDengjiJiaogao2 as ElIconIconDengjiJiaogao2,
  IconDengjiPutong2 as ElIconIconDengjiPutong2,
} from '@element-plus/icons-vue'
import { findRecordByCaseIdAndPlanId } from '@/api/vone/testmanage/index'
export default {
  components: {
    ElIconIconDengjiZuidi2,
    ElIconIconDengjiJiaodi2,
    ElIconIconDengjiZuigao2,
    ElIconIconDengjiJiaogao2,
    ElIconIconDengjiPutong2,
  },
  props: {
    planId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableLoading: false,
      tableData: {},
      tableOptions: {},
      formData: {},
      statusAllList: [
        // 下拉框状态
        {
          value: 'system',
          label: '成功',
          class: 'iconfont el-icon-success',
          color: '#3CB540',
        },
        {
          value: 'smoke',
          label: '失败',
          class: 'iconfont el-icon-error',
          color: '#FA6B57',
        },
        {
          value: 'block',
          label: '阻塞',
          class: 'iconfont el-icon-tips-minus-circle-fill',
          color: '#FFBF47',
        },
        {
          value: 'skip',
          label: '跳过',
          class: 'iconfont el-icon-shijian1',
          color: '#BD7FFA',
        },
      ],
      statusAllMap: {},
    }
  },
  watch: {
    async planId(v) {
      if (!v) return
      this.getTableData()
    },
  },
  mounted() {},
  methods: {
    async getTableData() {
      this.tableLoading = true
      this.statusAllMap = this.statusAllList.reduce(
        (r, v) => (r[v.value] = v) && r,
        {}
      )

      this.$set(this.formData, 'testcaseId', this.$route.params.caseId)
      this.$set(this.formData, 'planId', this.planId)
      let params = {}
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData },
      }
      const res = await findRecordByCaseIdAndPlanId(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      res.data.records.forEach((element) => {
        element.caseKey =
          element.echoMap && element.echoMap.testCase
            ? element.echoMap.testCase.caseKey
            : ' '
        element.name =
          element.echoMap && element.echoMap.testCase
            ? element.echoMap.testCase.name
            : ' '
        element.version =
          element.echoMap && element.echoMap.testCase
            ? element.echoMap.testCase.version
            : ' '
        element.priority =
          element.echoMap && element.echoMap.testCase
            ? element.echoMap.testCase.priority
            : null
      })

      this.tableData = res.data
    },
  },
}
</script>

<style></style>
