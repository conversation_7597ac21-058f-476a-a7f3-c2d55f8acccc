<template>
  <!-- 执行记录 -->
  <page-wrapper v-loading="pageLoading">
    <!-- <vone-back title="执行记录">
        <div slot="custom">
          <el-divider style="margin: 0px 8px" direction="vertical" />
          <el-tag effect="dark" type="info" size="mini">{{ record.caseKey }} </el-tag>
          <h1 style="margin-left: 6px">{{ record.name }}</h1>
        </div>
      </vone-back> -->
    <div class="header">
      <div style="display: flex; flex: 1; align-items: center">
        <div class="back" @click="back">
          <el-icon class="iconfont"><el-icon-direction-back /></el-icon>
        </div>
        <div class="title">执行记录</div>
        <el-divider style="margin: 0px 8px" direction="vertical" />
        <el-tag effect="dark" type="info" size="mini"
          >{{ record.caseKey }}
        </el-tag>
        <h1 style="margin-left: 6px">{{ record.name }}</h1>
      </div>
    </div>
    <div class="contentbox">
      <div class="leftbox">
        <div class="twoTitle">测试计划</div>
        <div class="menubox">
          <div
            v-for="item in planList"
            :key="item.planId"
            :class="[item.planId == planId ? 'active' : '']"
            @click="(e) => handleChange(item.planId)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="rightbox">
        <recordTable ref="recordTable" :plan-id="planId" />
      </div>
    </div>
    <!-- <div class="titleBox">

        <span class="title">【{{ caseName }}】用例执行历史记录</span>

        <span class="back" @click="back">
          <i class="iconfont el-icon-tips-close" />
        </span>

      </div>
      <el-collapse v-if="planList&&planList.length" v-model="activeNames" accordion @change="handleChange">
        <el-collapse-item v-for="item in planList" :key="item.planId" :name="item.planId">
          <template slot="title">
            <span :class="{isDisabled:item.echoMap.testPlan}">{{ item.name }}</span>
          </template>
          <recordTable v-if="item.id" ref="recordTable" :key="item.id" :plan-id="planId" />
        </el-collapse-item>
      </el-collapse>
      <vone-empty v-else style="height:calc(100vh - 200px)" /> -->
  </page-wrapper>
</template>

<script>
// import { DirectionBack as ElIconDirectionBack } from '@element-plus/icons-vue'

import { findPlanByCaseId } from '@/api/vone/testmanage/index'
import recordTable from './records/table.vue'

export default {
  components: {
    recordTable,
    // ElIconDirectionBack,
  },
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      planId: '',
      activeNames: '',
      pageLoading: false,
      caseName: '',
      planList: [],
    }
  },
  mounted() {
    this.getplanList()

    this.caseName = this.record.caseKey
      ? `${this.record.caseKey} - ${this.record.name}`
      : ''
  },
  methods: {
    async getplanList() {
      const res = await findPlanByCaseId(this.record.id)
      if (!res.isSuccess) {
        return
      }
      this.planList = res.data
    },
    handleChange(val) {
      this.planId = val
    },
    back() {
      this.$emit('triggerRecord', { visible: false })
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  height: 48px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  margin-top: -16px;
  margin-left: -16px;
  margin-right: -16px;
  padding: 8px 16px;
  border-radius: 4px 4px 0 0;
  .back {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 10px;
    width: 32px;
    height: 32px;
    background-color: #ffffff;
    box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
    border-radius: 100px;
    margin-right: 16px;
    cursor: pointer;
    i {
      color: var(--main-theme-color);
    }
  }
  .title {
    font-weight: 500;
    font-size: 16px;
    color: var(--font-main-color);
    user-select: none;
  }
}
h1 {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  display: inline-block;
}
.contentbox {
  margin: 0px -16px -16px -16px;
  display: flex;
}
.leftbox {
  height: calc(100vh - 48px - 10px - 10px - 48px);
  border-right: 1px solid var(--solid-border-color);
  width: 240px;
  .twoTitle {
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid var(--solid-border-color);
    font-weight: 500;
    font-size: 16px;
    padding: 0px 16px;
  }
  .menubox {
    margin-top: 8px;
    height: calc(100vh - 48px - 10px - 10px - 48px - 8px - 48px);
    overflow: auto;
    div {
      padding: 0px 16px;
      height: 36px;
      line-height: 36px;
      color: var(--font-main-color);
      cursor: pointer;
    }
    .active {
      color: var(--main-theme-color);
      background: var(--main--50);
    }
  }
}
.rightbox {
  flex: 1;
  padding: 16px;
}
</style>
