<template>
  <div v-loading="pageLoading">
    <div class="box">
      <div class="header">
        <div class="title">
          {{ dateType === 'week' ? '每周' : '每天' }}工时日志填报
        </div>
        <div class="data-switching">
          <el-button
            :icon="ElIconArrowLeft"
            :disabled="preDisabled"
            size="mini"
            @click="previous"
          />
          <span v-if="dateType === 'week'" class="data-title"
            >{{ startDate }} 到 {{ endDate }}</span
          >
          <span v-else class="data-title">{{ dayDate }}</span>
          <el-button
            :icon="ElIconArrowRight"
            size="mini"
            :disabled="nextDisabled"
            @click="next"
          />
        </div>
        <span class="rightContent">
          <div class="date-tabs">
            <span :active="dateType === 'week'" @click="changeTab('week')"
              >周</span
            >
            <span :active="dateType === 'day'" @click="changeTab('day')"
              >天</span
            >
          </div>
          <el-divider direction="vertical" />

          <el-button
            type="primary"
            size="mini"
            :disabled="!$permission('project_man_hour_submit')"
            @click="addTime"
            >提交</el-button
          >
        </span>
      </div>

      <el-form ref="timeForm" :model="timeForm" :inline-message="false">
        <main style="height: calc(100vh - 250rem)">
          <!-- 周 -->
          <vxe-table
            v-if="dateType === 'week'"
            ref="timeTable"
            class="vone-vxe-table"
            border
            resizable
            height="auto"
            show-overflow="tooltip"
            :empty-render="{ name: 'empty' }"
            :data="timeForm.timelist"
            :column-config="{ minWidth: '120px' }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
          >
            <vxe-column key="1" title="用户" width="120">
              <template #default="{ row, rowIndex }">
                <el-form-item
                  v-if="!row.echoMap"
                  :key="'timelist.' + rowIndex + '.filledBy'"
                  label-width="0"
                  :prop="'timelist.' + rowIndex + '.filledBy'"
                  :rules="{
                    required: true,
                    message: '请选择用户',
                    trigger: 'change',
                  }"
                >
                  <vone-remote-user
                    v-model="row.filledBy"
                    disabled
                    placeholder="请选择"
                  />
                </el-form-item>

                <span v-else>
                  <vone-user-avatar
                    :avatar-path="row.echoMap.filledBy.avatarPath"
                    :avatar-type="row.echoMap.filledBy.avatarType"
                    :name="row.echoMap.filledBy.name"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column key="2" title="工作项类型" width="100">
              <template #default="{ row, rowIndex }">
                <el-row v-if="!row.echoMap" class="typeselect">
                  <el-form-item
                    label-width="0"
                    :prop="'timelist.' + rowIndex + '.type'"
                    :rules="{
                      required: true,
                      message: '请选择类型',
                      trigger: 'change',
                    }"
                  >
                    <el-select
                      v-model="row.type"
                      class="select-tyle"
                      placeholder="请选择"
                      @change="(e) => workTypeChange(e, row)"
                    >
                      <el-option
                        v-for="op in typeCodeList"
                        :key="op.value"
                        :label="op.label"
                        :value="op.value"
                      >
                        <i :class="op.icon" :style="{ color: op.color }" />
                        {{ op.label }}
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-row>
                <span v-else>
                  <span v-if="row.type && typeCodeMap[row.type]">
                    <i
                      :class="typeCodeMap[row.type].icon"
                      :style="{ color: typeCodeMap[row.type].color }"
                    />
                    {{ typeCodeMap[row.type].label }}
                  </span>
                </span>
              </template>
            </vxe-column>
            <vxe-column key="5" title="工作项" min-width="240">
              <template #default="{ row, rowIndex }">
                <el-row v-if="!row.echoMap" class="typeselect">
                  <el-form-item
                    label-width="0"
                    :prop="'timelist.' + rowIndex + '.bizId'"
                    :rules="{
                      required: true,
                      message: '请选择工作项',
                      trigger: 'change',
                    }"
                  >
                    <el-select
                      v-model="row.bizId"
                      filterable
                      remote
                      class="select-option"
                      :remote-method="(query) => remoteMethod(query, row)"
                      @change="getAllStatus(row)"
                      :loading="loading"
                      no-match-text="暂未查询到匹配数据,请重新输入"
                      placeholder="请输入编号或标题搜索"
                      clearable
                    >
                      <virtual-list
                        :style="{ 'max-height': '258px', 'overflow-y': 'auto' }"
                        data-key="id"
                        :extra-props="{
                          label: 'name',
                          value: 'id',
                        }"
                        :data-sources="row.workItem || []"
                        :data-component="itemConponent"
                      />
                      <!-- <el-option v-for="i in row.workItem" :key="i.id" :label="i.name" :value="i.id" :disabled="i.disabled">
                          <i :class="`iconfont ${i.echoMap.typeCode.icon}`" :style="{ color:`${i.echoMap.typeCode ? i.echoMap.typeCode.color : '#ccc'}`}" />
                          <span class="fontstyle">{{ i.code }} {{ i.name }}</span>
                        </el-option> -->
                    </el-select>
                  </el-form-item>
                </el-row>
                <span v-else>
                  <span v-if="row.echoMap && row.echoMap.biz">
                    {{ row.echoMap.biz.name }}
                  </span>
                </span>
              </template>
            </vxe-column>
            <vxe-column title="状态" min-width="240">
              <template #default="{ row, rowIndex }">
                <el-row v-if="!row.echoMap" class="stateCode">
                  <el-form-item
                    label-width="0"
                    :prop="'timelist.' + rowIndex + '.stateCode'"
                    :rules="{
                      required: true,
                      message: '请选择工作项状态',
                      trigger: 'change',
                    }"
                  >
                    <el-select
                      v-model="row.stateCode"
                      filterable
                      placeholder="请选择工作项状态"
                      clearable
                      collapse-tags
                    >
                      <el-option
                        v-for="item in stateCodeListMap[row.bizId] || []"
                        :key="item.id"
                        :label="item.name"
                        :value="item.stateCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-row>
                <span v-else>
                  <span
                    v-if="
                      row.echoMap &&
                      row.echoMap.stateCode &&
                      row.echoMap.stateCode.name
                    "
                  >
                    {{ row.echoMap.stateCode.name }}
                  </span>
                </span>
              </template>
            </vxe-column>
            <div v-for="(item, index) in dateList" :key="item.date + '_'">
              <vxe-column
                :key="item.date + '3_' + index"
                width="110"
                :title="item.date"
                :sum="true"
                :field="item.date"
              >
                <template v-slot:header="{ column }">
                  <div>
                    <div>
                      {{ item.name }}
                      <span>
                        {{ item.previewDate }}
                      </span>
                    </div>
                    <div>
                      <span
                        :style="{
                          color: getColor(
                            Number(getSum(column.property) + item.duration)
                          ),
                        }"
                      >
                        <span>{{
                          Number(getSum(column.property) + item.duration)
                        }}</span>
                        /
                        <span>{{
                          configInfo['NORM_HOURS_OF_EVERY_DAY'].value
                        }}</span>
                      </span>
                    </div>
                  </div>
                </template>
                <template #default="{ row, rowIndex }">
                  <el-form-item
                    :key="'timelist.' + rowIndex + '_' + item.date"
                    label-width="0"
                    :prop="'timelist.' + rowIndex + '.' + item.date"
                    :rules="[
                      {
                        required: false,
                        message: '请输入工时',
                        trigger: 'blur',
                      },
                      { validator: checkTime },
                    ]"
                  >
                    <el-input
                      v-model.number="row[item.date]"
                      type="number"
                      :disabled="isdisabled(item, row)"
                      :class="[
                        'number-input',
                        isValid(item, row) ? 'is-valid' : '',
                      ]"
                      placeholder="请输入"
                      @change="numberChange($event, row, item.date)"
                    />
                  </el-form-item>
                </template>
              </vxe-column>
            </div>

            <template>
              <vxe-column
                :key="Math.random()"
                width="80"
                field="total"
                :sum="true"
              >
                <template v-slot:header="{ column }">
                  <span style="font-size: 14px"> 全部工时 </span>
                  <!-- <div style="color:#00BF80">
                      {{ Number(getSum(column.property)) }} h
                    </div> -->
                </template>
                <template #default="{ row }"> {{ getTotal(row) }} h </template>
              </vxe-column>
            </template>
            <vxe-column title="操作" fixed="right" align="left" width="50">
              <template #default="{ row, rowIndex }">
                <el-button
                  :disabled="
                    row.verified || !$permission('project_man_hour_del')
                      ? true
                      : false
                  "
                  :icon="el-icon-setting"
                  type="text"
                  size="small"
                  @click.native.prevent="deleteRow(rowIndex, timeForm.timelist)"
                />
              </template>
            </vxe-column>
          </vxe-table>
          <!-- 天 -->
          <DayTable
            v-if="dateType === 'day'"
            ref="dateTypeDay"
            v-bind="dayParams"
            :config-info="configInfo"
            :type-code-list="typeCodeList"
            :day-date="dayDate"
          />
        </main>
      </el-form>
    </div>
    <footer v-if="dateType == 'week'">
      <el-button :icon="ElIconPlus" type="text" @click="addChild"
        >添加行</el-button
      >
    </footer>
    <footer v-else>
      <el-button :icon="ElIconPlus" type="text" @click="addDayRow"
        >添加行</el-button
      >
    </footer>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  workItemSearch,
  addWorkingHoursInfoBatch,
  getWorkingHoursConfig,
  getWorkingHoursDay,
  deleteWorkingHoursInfo,
} from '@/api/vone/manhour/index'
import storage from 'store'
import DayTable from './day-table.vue'
import VirtualList from 'vue-virtual-scroll-list'
import itemConponent from './item-conponent.vue'
import { apiAlmFindNextNode } from '@/api/vone/project/issue'
import { getFlow } from '@/api/vone/base/work-flow'

import _ from 'lodash'

const calendar = require('dayjs/plugin/calendar')
dayjs.extend(calendar)
const typeCodeList = [
  {
    label: '需求',
    icon: 'iconfont el-icon-icon-xuqiu',
    value: 'ISSUE',
    color: 'rgb(135, 145, 250)',
  },
  {
    label: '缺陷',
    icon: 'iconfont el-icon-icon-quexian',
    value: 'BUG',
    color: 'rgb(250, 107, 87)',
  },
  {
    label: '任务',
    icon: 'iconfont el-icon-icon-renwu',
    value: 'TASK',
    color: 'rgb(62, 123, 250)',
  },
]

export default {
  name: 'Submit',
  components: {
    DayTable,
    VirtualList,
    itemConponent,
  },
  data() {
    return {
      stateCodeListMap: {},
      itemConponent: itemConponent,
      dayParams: {},
      pageLoading: false,
      typeCodeList,
      typeCodeMap: {},
      configInfo: {},
      dateList: [],
      workItem: [],
      startDate: '',
      endDate: '',
      dayDate: '',
      weekday: [
        '星期日',
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六',
      ],
      currentDay: new Date(),
      currentToday: new Date(),
      timeForm: {
        timelist: [],
      },
      loading: false,
      params: {},
      dateType: 'week',
      config: {},
      pickerType: {},
      column: [
        {
          label: 'filledBy',
          width: '150px',
          value: 'filledBy',
        },
      ],
      preDisabled: false,
      nextDisabled: false,
    }
  },
  computed: {
    getColor() {
      return function (count) {
        if (count < this.configInfo['NORM_HOURS_OF_EVERY_DAY'].value) {
          return '#DB2C3A' // √
        } else if (count == this.configInfo['NORM_HOURS_OF_EVERY_DAY'].value) {
          return '#F7A500' // 当数量等于8时显示黄色
        } else {
          return '#00BF80' // 当数量大于8时显示绿色
        }
      }
    },
  },
  mounted() {
    this.pickerType = {
      week: dayjs().add(-6, 'day').startOf('day').format('YYYY-MM-DD'),
      month: dayjs().add(-30, 'day').startOf('day').format('YYYY-MM-DD'),
      quarter: dayjs().add(-90, 'day').startOf('day').format('YYYY-MM-DD'),
    }
    this.addChild()

    this.typeCodeMap = this.typeCodeList.reduce(
      (r, v) => (r[v.value] = v) && r,
      {}
    )
  },
  created() {
    this.getWorkingHoursConfFn()
  },
  methods: {
    async getAllStatus(row) {
      let typeCode = row.workItem.find((r) => {
        return r.id === row.bizId
      }).typeCode
      let url = ''
      url = `/api/alm/alm/projectWorkflow/findProjectByProjectIdAndTypeClassifyAndTypeCode/${this.$route.params.id}/${row.type}/${typeCode}`

      if (url) {
        const r = await apiAlmFindNextNode(url)
        const flowId = r.data.workflowId
        const res = await getFlow(flowId)
        if (!res.isSuccess) {
          return
        }
        this.$set(this.stateCodeListMap, row.bizId, res.data.workflowNodes)
        this.$forceUpdate()
      }
    },
    remoteMethod: _.debounce(function (query, row, date) {
      this.loading = true
      if (query != '') {
        this.params.search = query
        this.getworkItemSearch(row, date)
      }
    }, 1000),
    getSum(field) {
      return (
        (this.$refs.timeTable &&
          this.$refs.timeTable.tableData.reduce(
            (total, row) => total + (row[field] ? row[field] : 0),
            0
          )) ||
        0
      )
    },
    isValid(item, row) {
      const data = row.filledInfos?.find((r) => r.fillingTime == item.date)
      if (data && data.valid == false) {
        return true
      } else {
        return false
      }
    },
    isdisabled(item, row) {
      // 可填报周期
      const setting = this.config['FILLABLE_PERIOD']
      // today
      const currentDay = dayjs().format('YYYY-MM-DD')
      // today -> 30天
      const monthDay = dayjs(currentDay)
        .subtract(30, 'day')
        .format('YYYY-MM-DD')
      // today -> 90天
      const quarterDay = dayjs(currentDay)
        .subtract(90, 'day')
        .format('YYYY-MM-DD')

      const isVerified = row.filledInfos?.find(
        (r) => r.fillingTime == item.date
      )?.verified
      if (setting == 'month') {
        return !!(
          dayjs(item.date).isBefore(dayjs(monthDay)) ||
          (isVerified && !!row[item.date])
        )
      } else if (setting == 'quarter') {
        return !!(
          dayjs(item.date).isBefore(dayjs(quarterDay)) ||
          (isVerified && !!row[item.date])
        )
      } else {
        return isVerified && !!row[item.date]
      }
    },
    async getWorkingHoursConfFn() {
      try {
        const res = await getWorkingHoursConfig()
        res.data.forEach((e) => {
          this.config[e.key] = e.value
        })

        this.configInfo = res.data.reduce((r, v) => (r[v.key] = v) && r, {})
        this.dayDate = dayjs(new Date()).format('YYYY-MM-DD')

        this.getDateList()
      } catch (e) {
        return
      }
    },
    async getDateList() {
      this.pageLoading = true
      var d = this.getMonDate()
      var arry = []
      if (this.dateType === 'week') {
        for (var i = 0; i < 7; i++) {
          var obj = {
            name: this.weekday[dayjs(d).day()],
            week: dayjs(d).day(),
            date: dayjs(d).format('YYYY-MM-DD'),
            previewDate: dayjs(d).format('DD'),
          }

          arry.push(obj)
          d.setDate(d.getDate() + 1)
        }
        this.startDate = arry[0].date
        this.endDate = arry[6].date
      } else {
        var objs = {
          name: this.weekday[dayjs(this.dayDate).day()],
          week: dayjs(this.dayDate).day(),
          date: this.dayDate,
        }
        arry.push(objs)
      }
      this.getBtnDIsabled()

      const userInfo = storage.get('user')

      // const params =

      const res = await getWorkingHoursDay(
        this.dateType == 'week' ? this.startDate : this.dayDate,
        this.dateType == 'week' ? this.endDate : this.dayDate,
        [userInfo.id],
        this.$route.params.id
      )
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.dateList = arry

      const dateMap = res.data.hoursDays.reduce(
        (r, v) => (r[v.day] = v) && r,
        {}
      )

      // return

      const times = []

      res.data.userHistory.forEach((element) => {
        times.push(element.filledInfos)
      })

      this.dateList.forEach((element) => {
        element.duration = dateMap[element.date]
          ? dateMap[element.date].otherDuration
          : 0
      })

      const result = []

      res.data.userHistory.forEach((element, index) => {
        element.dateContainer = []
        if (element.filledInfos && element.filledInfos.length) {
          element.filledInfos.forEach((j) => {
            element.dateContainer.push(j.fillingTime)
          })
        }

        element.type = element.type?.code
        const obj = element.filledInfos.reduce(
          (r, v) => (r[v.fillingTime] = v.duration) && r,
          {}
        )

        element.verified = element.filledInfos.length
          ? element.filledInfos[0].verified
          : false

        stateCode: element.stateCode
        result.push(Object.assign({}, element, obj))
      })

      this.timeForm.timelist = result

      this.pageLoading = false

      this.dateCalculation()

      // this.getgetWorkingHoursDay()
    },
    getBtnDIsabled() {
      // 可填报周期
      const setting = this.config['FILLABLE_PERIOD']
      // today
      const currentDay = dayjs().format('YYYY-MM-DD')
      // today -> 一周
      const weekDay = dayjs(currentDay).subtract(6, 'day').format('YYYY-MM-DD')
      // today -> 30天
      const monthDay = dayjs(currentDay)
        .subtract(30, 'day')
        .format('YYYY-MM-DD')
      // today -> 90天
      const quarterDay = dayjs(currentDay)
        .subtract(90, 'day')
        .format('YYYY-MM-DD')

      if (this.dateType == 'week') {
        // rifht按钮
        this.nextDisabled = this.endDate == currentDay
        // left 按钮
        if (setting == 'week') {
          this.preDisabled = true
        } else if (setting == 'month') {
          this.preDisabled = dayjs(this.startDate).isBefore(dayjs(monthDay)) // 默认毫秒
        } else if (setting == 'quarter') {
          this.preDisabled = dayjs(this.startDate).isBefore(dayjs(quarterDay)) // 默认毫秒
        } else {
          this.preDisabled = false
        }
      } else {
        // rifht按钮
        this.nextDisabled = this.dayDate == currentDay
        // left 按钮
        if (setting == 'week') {
          this.preDisabled = dayjs(this.dayDate).isSame(dayjs(weekDay)) // 默认毫秒
        } else if (setting == 'month') {
          this.preDisabled = dayjs(this.dayDate).isSame(dayjs(monthDay)) // 默认毫秒
        } else if (setting == 'quarter') {
          this.preDisabled = dayjs(this.dayDate).isSame(dayjs(quarterDay)) // 默认毫秒
        } else {
          this.preDisabled = false
        }
      }
    },
    changeTab(e) {
      this.dateType = e
      this.dayDate = dayjs(new Date()).format('YYYY-MM-DD')
      this.timeForm.timelist.forEach((e, index) => {
        if (e.dateContainer && e.dateContainer.length > 0) {
          e.dateContainer.forEach((item) => {
            e[item] = null
          })
        }
        this.$set(this.timeForm.timelist, index, e)
      })
      this.currentDay = new Date()
      this.getDateList()
      this.getWorkingHoursConfFn()
    },

    getTotal(row) {
      var total = 0
      row.datelist.forEach((item) => {
        if (row[item.date] && row[item.date] > 0) {
          total += row[item.date]
        }
      })
      row.total = total
      return total
    },
    checkTime(rule, value, callback) {
      const filed = rule.field.split('.')
      const key = filed[filed.length - 1]
      const duration = this.dateList.find((r) => r.date == key).duration

      if (value == null || value === '' || typeof value == 'undefined') {
        if (this.dateType === 'week') {
          return callback()
        } else {
          return callback(new Error('请输入工时'))
        }
      }
      if (typeof value !== 'number') {
        callback(new Error('工时为数字'))
      } else if (value <= 0) {
        callback(new Error('工时应大于0'))
      } else if (
        Number(value + duration) > Number(this.config['MAX_HOURS_OF_EVERY_DAY'])
      ) {
        callback(
          new Error(`工时不大于${this.config['MAX_HOURS_OF_EVERY_DAY']}`)
        )
      } else {
        callback()
      }
    },

    resetForm(formName) {
      this.timeForm.timelist.forEach((e) => {
        e.total = 0
      })
      this.$refs[formName].resetFields()
    },
    numberChange(e, row, item) {
      if (e == '') {
        this.$set(row, item, null)
        if (row.dateContainer.includes(item)) {
          row.dateContainer.splice(
            row.dateContainer.findIndex((e) => e === item),
            1
          )
        }
      } else {
        if (!row.dateContainer.includes(item)) {
          row.dateContainer.push(item)
        }
      }
    },
    workTypeChange(e, row, date) {
      this.params.search = null
      this.params.sourceTypes = [e]
      this.params.rateProgress = this.configInfo?.BIZ_STATUS?.value
      this.params.projectId = this.$route.params.id
      this.getworkItemSearch(row, date)
    },

    async getworkItemSearch(row, date) {
      this.loading = true
      const res = await workItemSearch({ ...this.params, limit: '-1' })

      if (res.isSuccess) {
        const week = this.$refs.timeTable.tableData
          .filter((r) => r.bizId)
          .map((j) => j.bizId)

        const day = this.$refs.timeTable.tableData
          .filter((r) => r[date + '_bizId'] || r.bizId)
          .map((j) => j[date + '_bizId'] || j.bizId)

        const DATA = this.dateType == 'week' ? week : day

        // res.data.forEach(element => {
        //   element.disabled = !!DATA.includes(element.id)
        // })
        row[date + '_workItem'] = res.data

        row.workItem = res.data
      }
      this.loading = false
    },
    previous() {
      const date = dayjs(this.currentDay).subtract(7, 'day').toDate()
      this.currentDay = date
      if (this.dateType === 'day') {
        const todate = dayjs(this.currentToday).subtract(1, 'day').toDate()
        this.currentToday = todate
        this.dayDate = dayjs(this.currentToday).format('YYYY-MM-DD')
      }
      this.getDateList()
    },
    next() {
      const date = dayjs(this.currentDay).add(7, 'day').toDate()
      this.currentDay = date
      if (this.dateType === 'day') {
        const todate = dayjs(this.currentToday).add(1, 'day').toDate()
        this.currentToday = todate
        this.dayDate = dayjs(this.currentToday).format('YYYY-MM-DD')
      }
      this.getDateList()
    },
    async addTime() {
      if (this.dateType == 'day') {
        this.addDay()
        return
      }

      try {
        await this.$refs.timeForm.validate()
        var arry = []

        this.timeForm.timelist.forEach((e) => {
          e.dateContainer.forEach((item) => {
            if (e[item]) {
              const historyId =
                e.filledInfos?.find((r) => r.fillingTime == item)?.id || null
              var obj = {
                filledBy:
                  this.dateType === 'week' || e.echoMap
                    ? e.filledBy
                    : e[item + '_filledBy'],
                type:
                  this.dateType === 'week' || e.echoMap
                    ? e.type
                    : e[item + '_type'],
                bizId:
                  this.dateType === 'week' || e.echoMap
                    ? e.bizId
                    : e[item + '_bizId'],
                fillingTime: item,
                duration: e[item],
                description: e[item + '_description'],
                projectId: this.$route.params.id,
                id: historyId,
                stateCode: e.stateCode,
              }
              arry.push(obj)
            }
          })
        })
        if (arry.length > 0) {
          this.saveLoading = true
          const res = await addWorkingHoursInfoBatch(arry)
          this.saveLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.$message.success('操作成功')
          this.getWorkingHoursConfFn()
          // this.resetForm('timeForm')
        } else {
          this.$message.warning('请填写工时')
        }
      } catch (e) {
        this.saveLoading = false
      }
    },
    async addDay() {
      // console.log(this.$refs.dateTypeDay.$children[0].validate(), 'this.$refs.dateTypeDay.dayForm')
      await this.$refs.dateTypeDay.$children[0].validate()
      // console.log(this.$refs.dateTypeDay, '9909')

      const table = this.$refs.dateTypeDay.dayForm.tableData

      const params = table.map((r) => ({
        filledBy: r.filledBy,
        type: r.type,
        bizId: r.bizId,
        duration: r.duration,
        description: r.description,
        projectId: this.$route.params.id,
        id: r.echoMap ? r.id : null,
        fillingTime: this.dayDate,
      }))

      const res = await addWorkingHoursInfoBatch(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('操作成功')
      this.$refs.dateTypeDay.getTableData()
    },
    addChild() {
      const userInfo = storage.get('user')
      const parameter = {
        filledBy: userInfo.id,
        type: '',
        name: '',
        stateCode: '',
        datelist: this.dateList,
        total: 0,
        dateContainer: [],
        description: '',
        duration: null,
        verified: false,
      }
      this.dateList.forEach((e) => {
        parameter[e.date] = null
      })

      this.params = {}
      this.timeForm.timelist.push(parameter)
    },

    // deleteRow(e) {
    //   this.timeForm.timelist.splice(e, 1)
    // },
    getMonDate() {
      var d = _.cloneDeep(this.currentDay)
      const dd = dayjs(d).subtract(6, 'day').$d
      return dd
    },

    dateCalculation() {
      const userInfo = storage.get('user')

      this.timeForm.timelist.forEach((e, index) => {
        e.datelist = this.dateList
        e.filledBy = userInfo.id
        this.$set(this.timeForm.timelist, index, e)
      })

      this.$nextTick(() => {
        // this.$refs['timeTable'].doLayout()
      })
    },
    async deleteRow(e, data) {
      if (!data[e].filledInfos || !data[e].filledInfos.length) {
        this.timeForm.timelist.splice(e, 1)
        return
      }

      try {
        await this.$confirm(`确定删除此条工时记录吗?`, '删除', {
          type: 'warning',
          closeOnClickModal: false,
          customClass: 'delConfirm',
        })

        const res = await deleteWorkingHoursInfo([data[e].filledInfos[0].id])
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        } else {
          this.$message.success('删除成功')
          this.getWorkingHoursConfFn()
        }
      } catch (e) {
        return
      }
    },
    addDayRow() {
      this.$refs.dateTypeDay.addChild()
    },
  },
}
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  height: calc(100vh - 170px);
  padding: 16px;
}
.header {
  padding: 0 16px;
  margin: -16px -16px 16px;
  border-bottom: 1px solid var(--disabled-bg-color);
  height: 56px;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-weight: 500;
    color: var(--main-font-color);
  }
  .data-switching {
    width: 400px;
    text-align: center;
    .data-title {
      font-weight: 600;
      margin: 0 24px;
      color: var(--main-font-color);
    }
    .el-button {
      width: 32px;
    }
  }
  .rightContent {
    display: flex;
    align-items: center;
  }
  .date-tabs {
    background: #f2f3f5;
    border-radius: 2px;
    padding: 4px;
    height: 32px;
    > span {
      color: var(--auxiliary-font-color);
      font-size: 14px;
      cursor: pointer;
      height: 24px;
      width: 32px;
      text-align: center;
      line-height: 24px;
      display: inline-block;
      border-radius: 2px;
      &[active] {
        background: #fff;
        color: #3e7bfa;
        font-weight: 500;
      }
    }
  }
}
:deep(.el-button) {
  min-width: 32px;
  &--default {
    background-color: var(--main-bg-color);
    border-color: #ced1d9;
    color: #6b7385;
    &:focus {
      background-color: var(--main-bg-color);
      border-color: #ced1d9;
      color: #6b7385;
    }
    &:hover {
      background-color: #f2f3f5;
      border-color: #ced1d9;
      color: #6b7385;
    }
  }
}
:deep(th.el-table__cell.is-leaf) {
  height: 48px;
}
:deep(.el-table__cell:nth-child(-n + 2)) {
  font-size: 14px;
}
:deep(.el-table__cell:nth-child(n + 2)) {
  font-size: 14px;
}
:deep(.vxe-table .el-form-item) {
  // margin-bottom: 16px;
  margin: 16px 0;
  .el-form-item {
    margin: 0;
  }
}
.select-tyle {
  min-width: 90px;
  height: 30px;
  :deep(.el-input__inner) {
    border: none;
  }
  &::after {
    content: '';
    width: 1px;
    height: 20px;
    background: #eaecf0;
    position: absolute;
    right: 0px;
    top: calc(50% - 10px);
  }
}
.select-option {
  height: 30px;
  :deep(.el-input__inner) {
    border: none;
  }
}
.is-valid {
  :deep(.el-input__inner) {
    color: red !important;
  }
}
.foot {
  width: calc(100% - 72px);
  height: 64px;
  line-height: 64px;
  text-align: right;
  position: absolute;
  bottom: 0;
  right: 0;
  padding-right: 16px;
  background-color: var(--main-bg-color);
  box-shadow: 0px -3px 12px rgba(29, 33, 41, 0.06);
  z-index: 10;
  // background-color: red;
}
.pushbut {
  width: 100%;
  margin-bottom: 30px;
}
.typeselect {
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  border: 1px solid var(--input-border-color);
  border-radius: 2px;
  :deep(.el-form-item) {
    margin: 0;
  }
  &:focus {
    border-color: #3e7bfa;
  }
  :deep(.el-input__inner + .typeselect) {
    &:focus {
      border-color: #3e7bfa;
      color: red;
    }
  }
}
.iconfont {
  line-height: 22px;
  margin-right: 9px;
}
.fontstyle {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.vone-vxe-table .vxe-body--column) {
  height: 64px;
  line-height: 64px;
  & > .vxe-cell {
    max-height: 64px;
  }
}
:deep(.el-button--text) {
  padding: 0;
  min-width: auto;
}
:deep(.vxe-table::before) {
  display: none;
}
:deep(.el-table__fixed-right::before),
.el-table__fixed::before {
  display: none;
}
.number-input {
  :deep(input::-webkit-inner-spin-button) {
    appearance: none !important;
  }

  :deep(input::-webkit-outer-spin-button) {
    appearance: none !important;
  }

  input[type='number'] {
    appearance: textfield;
  }
}
footer {
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
}

:deep(.vxe-table .el-form-item) {
  margin: 0;
}

:deep(.vone-vxe-table .vxe-body--column) {
  height: 40px;
  line-height: 40px;
}
</style>
