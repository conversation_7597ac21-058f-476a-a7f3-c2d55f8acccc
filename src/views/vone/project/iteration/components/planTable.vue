<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          table-search-key="projectIterationTable"
          :table-ref="$refs['projectIterationTable']"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :model="formData"
          @getTableData="getPlanCaseList(currentNodekey)"
        >
          <!-- <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="名称" prop="name">
                  <el-input v-model="formData.name" placeholder="输入名称" />
                </el-form-item>
              </el-col>
            </el-row> -->
        </vone-search-dynamic>
      </template>
      <template slot="actions">
        <span v-if="currentNodekey !== '-1'" class="iteration_title">
          <span
            v-if="!$permission('project_iteration_count_view')"
            class="noPermissionBtn planName"
          >
            <svg-icon icon-class="icon-plan" />
            迭代概览
          </span>

          <span v-else-if="iterationDetail.id !== -1">
            <a @click="toPlanOverView(iterationDetail)">
              <span class="planName">
                <svg-icon icon-class="icon-plan" />
                迭代概览
              </span>
            </a>
          </span>
        </span>

        <el-dropdown
          v-if="type !== 'fileTree'"
          trigger="click"
          style="margin-left: 10px"
          :disabled="iterationDetail.enable"
          @command="showAdd"
        >
          <el-button-group>
            <el-button
              type="primary"
              :disabled="
                iterationDetail.enable ||
                !$permission('project_iteration_add_all')
              "
              :icon="el-icon-setting"
              >新增<el-icon class="iconfont el-icon--right"
                ><ElIconSetting /></el-icon
            ></el-button>
            <!-- <el-button class="subBtton" :disabled="iterationDetail.enable || !$permission('project_iteration_add_all')" icon="iconfont el-icon-direction-double-down" type="primary" /> -->
          </el-button-group>

          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1">需求</el-dropdown-item>
            <el-dropdown-item command="2">缺陷</el-dropdown-item>
            <el-dropdown-item command="3">任务</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
          v-if="type !== 'fileTree'"
          trigger="click"
          :disabled="iterationDetail.enable"
          @command="showConnect"
        >
          <el-button-group>
            <el-button
              class="btnMore"
              :disabled="
                iterationDetail.enable ||
                !$permission('project_iteration_add_all')
              "
              ><el-icon class="iconfont"><ElIconSetting /></el-icon
            ></el-button>
          </el-button-group>

          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="2">关联迭代</el-dropdown-item>
            <el-dropdown-item command="1">取消关联迭代</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getPlanCaseList(currentNodekey)"
        />
      </template>
    </vone-search-wrapper>
    <main style="height: calc(100vh - 155px)">
      <vxe-table
        ref="projectIterationTable"
        class="vone-vxe-table"
        height="auto"
        border
        resizable
        :loading="tableLoading"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :tree-config="{
          transform: true,
          rowField: 'bizId',
          parentField: 'parentId',
        }"
        :checkbox-config="{ reserve: true }"
        :cell-class-name="cellShow"
        row-id="bizId"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="标题"
          field="name"
          min-width="300"
          fixed="left"
          class-name="name_col"
          tree-node
        >
          <template #default="scope">
            <div class="name_icon">
              <span v-if="!scope.row.isGrouping" style="display: flex">
                <a
                  :class="
                    !$permission('project_iteration_update_all')
                      ? 'noPermissionBtn'
                      : ''
                  "
                  @click="showInfo(scope.row)"
                >
                  <span
                    v-if="
                      scope.row.typeCode &&
                      scope.row.echoMap &&
                      scope.row.echoMap.typeCode
                    "
                  >
                    <i
                      :class="`iconfont ${scope.row.echoMap.typeCode.icon}`"
                      :style="{
                        color: `${
                          scope.row.echoMap.typeCode
                            ? scope.row.echoMap.typeCode.color
                            : '#ccc'
                        }`,
                      }"
                    />
                  </span>
                  <span style="color: #3e7bfa">
                    {{ scope.row.code }} {{ scope.row.name }}
                  </span>
                </a>
                <!-- 是否延期 -->
                <span
                  v-if="scope.row.delay"
                  style="position: absolute; left: -13px"
                >
                  <el-tooltip
                    :show-after="500"
                    content="当前工作项已延期"
                    placement="top-start"
                  >
                    <el-icon class="color-danger ml-2"
                      ><el-icon-warning-outline
                    /></el-icon>
                  </el-tooltip>
                </span>
              </span>
              <span v-else> {{ scope.row.name }}</span>
            </div>
          </template>
        </vxe-column>
        <!-- <vxe-column field="classify" title="分类" width="80">
            <template #default="{ row }">
              <span v-if="row.classify">
                {{ row.classify.desc }}
              </span>

            </template>
          </vxe-column>
          <vxe-column field="typeCode" title="类型" width="90">
            <template #default="{ row }">
              <span v-if="row.echoMap && row.echoMap.typeCode">
                {{ row.echoMap.typeCode.name }}
              </span>

            </template>
          </vxe-column> -->
        <vxe-column field="stateCode" title="状态" width="100" sortable>
          <template #default="scope">
            <span
              v-if="
                scope.row &&
                scope.row.echoMap &&
                scope.row.echoMap.typeCode &&
                !scope.row.isGrouping
              "
            >
              <issueStatus
                v-if="scope.row.echoMap.typeCode.classify.code == 'ISSUE'"
                :no-permission="!$permission('project_iteration_flow')"
                :workitem="scope.row"
                @changeFlow="getPlanCaseList(currentNodekey)"
              />

              <taskStatus
                v-else-if="scope.row.echoMap.typeCode.classify.code == 'TASK'"
                :no-permission="!$permission('project_iteration_flow')"
                :workitem="scope.row"
                @changeFlow="getPlanCaseList(currentNodekey)"
              />

              <defectStatus
                v-else-if="scope.row.echoMap.typeCode.classify.code == 'BUG'"
                :no-permission="!$permission('project_iteration_flow')"
                :workitem="scope.row"
                @changeFlow="getPlanCaseList(currentNodekey)"
              />
            </span>
            <span v-else>{{ scope.row.typeCode }}</span>
          </template>
        </vxe-column>
        <vxe-column field="priorityCode" title="优先级" width="80" sortable>
          <template #default="scope">
            <div
              v-if="
                scope.row.priorityCode &&
                scope.row.echoMap &&
                scope.row.echoMap.priorityCode &&
                !scope.row.isGrouping
              "
            >
              <i
                :class="`iconfont ${scope.row.echoMap.priorityCode.icon}`"
                :style="{
                  color: `${
                    scope.row.priorityCode &&
                    scope.row.echoMap &&
                    scope.row.echoMap.priorityCode
                      ? scope.row.echoMap.priorityCode.color
                      : '#ccc'
                  }`,
                }"
              />
              {{ scope.row.echoMap.priorityCode.name }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="planEtime" title="计划完成时间" width="120" sortable>
          <template #default="scope">
            <span v-if="scope.row.planEtime">
              {{ dayjs(scope.row.planEtime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>
              {{ scope.row.planEtime }}
            </span>
          </template>
        </vxe-column>
        <vxe-column field="handleBy" title="处理人" width="100">
          <template #default="scope">
            <template v-if="!scope.row.isGrouping">
              <span
                v-if="
                  scope.row.handleBy &&
                  scope.row.echoMap &&
                  scope.row.echoMap.handleBy
                "
              >
                <vone-user-avatar
                  :avatar-path="scope.row.echoMap.handleBy.avatarPath"
                  :name="scope.row.echoMap.handleBy.name"
                />
              </span>
            </template>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="100">
          <template #default="scope">
            <template v-if="!scope.row.isGrouping">
              <span
                v-if="
                  scope.row.leadingBy &&
                  scope.row.echoMap &&
                  scope.row.echoMap.leadingBy
                "
              >
                <vone-user-avatar
                  :avatar-path="scope.row.echoMap.leadingBy.avatarPath"
                  :name="scope.row.echoMap.leadingBy.name"
                />
              </span>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
  </div>
</template>

<script>
import { apiAlmFindItemNoPage } from '@/api/vone/project/iteration'

import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'

import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import taskStatus from '@/views/vone/project/common/change-status/index.vue'
import defectStatus from '@/views/vone/project/common/change-status/index.vue'
import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'
import setDataMixin from '@/mixin/set-data'
import { valid } from 'mockjs'
export default {
  components: {
    issueStatus,
    taskStatus,
    defectStatus,
  },
  mixins: [setDataMixin],
  props: {
    type: {
      type: String,
      default: undefined,
    },
    iterationDetail: {
      type: Object,
      default: () => {},
    },
    currentNodekey: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      // label="名称" prop="name"
      //     label="负责人" prop="leadingBys"  multiple
      //     label="处理人" prop="handleBys" multiple
      //     label="状态" prop="stateCodes" multiple  stateCodeList
      //     label="计划完成时间" prop="planEtime"
      //     label="是否延期" prop="delay"   true / false
      //     label="优先级" prop="priorityCodes"   multiple  prioritList
      defaultFileds: [
        {
          key: 'ISSUE',
          name: '需求',
          type: {
            code: 'SELECT',
          },
          multiple: true,
          placeholder: '请选择需求',
        },
        {
          key: 'TASK',
          name: '任务',
          type: {
            code: 'SELECT',
          },
          multiple: true,
          placeholder: '请选择任务',
        },
        {
          key: 'BUG',
          name: '缺陷',
          type: {
            code: 'SELECT',
          },
          multiple: true,
          placeholder: '请选择缺陷',
        },
        // {
        //   key: 'classify',
        //   name: '分类',
        //   type: {
        //     code: 'SELECT'
        //   },
        //   placeholder: '请选择分类',
        //   association: 'typeCodes',
        //   optionList: [
        //     {
        //       code: 'ISSUE',
        //       name: '需求',
        //       id: '1'
        //     },
        //     {
        //       code: 'BUG',
        //       name: '缺陷',
        //       id: '2'
        //     },
        //     {
        //       code: 'TASK',
        //       name: '任务',
        //       id: '3'
        //     }
        //   ]
        // },
        // {
        //   key: 'typeCodes',
        //   name: '类型',
        //   isDefault: true,
        //   type: {
        //     code: 'SELECT'
        //   },
        //   multiple: true,
        //   placeholder: '请选择类型'
        // },
        {
          key: 'name',
          name: '标题',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入标题',
        },
        {
          key: 'leadingBys',
          name: '负责人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择负责人',
          multiple: true,
        },
        {
          key: 'handleBys',
          name: '处理人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择处理人',
          multiple: true,
        },
        {
          key: 'stateCodes',
          name: '状态',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择状态',
          multiple: true,
        },
        {
          key: 'priorityCodes',
          name: '优先级',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择优先级',
          multiple: true,
        },
        {
          key: 'delay',
          name: '是否延期',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择是否延期',
          optionList: [
            {
              code: true,
              name: '是',
              id: '1',
            },
            {
              code: false,
              name: '否',
              id: '2',
            },
          ],
        },
        {
          key: 'planEtime',
          name: '计划完成时间',
          type: {
            code: 'DATE',
          },
          placeholder: '请选择计划完成时间',
        },
      ],
      extraData: {},
      tableLoading: false,
      tableOptions: {
        isGrouping: true,
        groupingOptions: [
          { name: '按处理人', value: 'handleBy', key: 'echoMap.handleBy.name' },
          { name: '按产品', value: 'productId', key: 'echoMap.productId.name' },
          {
            name: '按计划完成时间',
            value: 'planEtime',
            key: 'planEtime',
            type: 'time',
          },
        ],
        isOperation: false,
      },
      tableData: {
        records: [],
      },
      formData: {
        priorityCodes: [],
        stateCodes: [],
        handleBys: [],
        leadingBys: [],
      },

      stateCodeList: [], // 状态
      prioritList: [],
      selecteTableData: [],
    }
  },
  watch: {
    'formData.classify': {
      handler: function (val) {
        if (!val) return
        this.getIssueType(val)
      },
      immediate: true,
    },
  },
  mounted() {
    this.getAllStatus()
    this.getPrioritList()
    this.getIssueType('ISSUE')
    this.getIssueType('TASK')
    this.getIssueType('BUG')
  },
  methods: {
    async getIssueType(e) {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, e)
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, e, res.data)
    },
    cellShow(row) {
      if (row.row.isGrouping && row.columnIndex === 0) {
        return 'hide-checkbox'
      }
    },
    showConnect(val) {
      // 取消关联
      if (val == '1') {
        this.$emit('cancleConnect', this.selecteTableData)
      } else {
        this.$emit('connectPlan', this.selecteTableData)
      }
    },
    showAdd(val) {
      this.$emit('showAdd', val)
    },
    // 迭代概览
    toPlanOverView(val) {
      this.$router.push({
        name: 'project_iteration_count_view',
        params: { planIs: val.id },
        // query: {
        //   iterationName: val.name
        // }
      })
    },
    showInfo(row) {
      if (!this.$permission('project_iteration_update_all')) {
        this.$message.warning('暂无操作权限,请联系管理员分配权限')
        return
      }
      this.$emit('showInfo', row)
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateCodeList = res.data
      this.setData(this.defaultFileds, 'stateCodes', res.data)
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCodes', res.data)
    },
    selectAllEvent({ checked }) {
      this.selecteTableData =
        this.$refs['projectIterationTable'].getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData =
        this.$refs['projectIterationTable'].getCheckboxRecords()
    },
    // 查询计划下用例
    async getPlanCaseList(val) {
      if (!val) {
        return
      }
      this.tableLoading = true
      const res = await apiAlmFindItemNoPage({
        planId: val || this.currentNodekey,
        projectId: this.$route.params.id,
        leadingBys: this.formData.leadingBys,
        handleBys: this.formData.handleBys,
        delay: this.formData.delay && this.formData.delay == 'true',
        stateCodes: this.formData.stateCodes,
        planEtime: this.formData.planEtime,
        priorityCodes: this.formData.priorityCodes,
        name: this.formData.name,
        classify: this.formData.classify,
        typeCodes: [
          ...(this.formData.ISSUE || []),
          ...(this.formData.TASK || []),
          ...(this.formData.BUG || []),
        ],
      })
      this.tableLoading = false

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData.records = res.data
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.name_col) {
  padding-left: 10px;
  .cell {
    display: flex;
    div.name_icon {
      width: 85%;
      display: flex;
      align-items: center;
    }
  }
  .vxe-table--render-default .vxe-tree--btn-wrapper {
    left: 5px;
  }
}
.planName {
  display: inline-block;
  padding: 0 10px;
  height: 32px;
  line-height: 32px;
  background: #f5f6fa;
  border-radius: 2px;
  text-align: center;
  color: var(--main-theme-color, #3e7bfa);
  font-weight: 600;
  svg {
    font-size: 16px;
  }
}

.planTable :deep(th.gutter) {
  display: initial;
}
// :deep(.vxe-cell--tree-node) {
//   padding-left: 8px !important;
// }
</style>
