<template>
  <vone-simple-add
    ref="simpleAdd"
    v-model.trim="form.name"
    :rules="rules"
    :model="form"
    label="标题"
    v-model:file-list="fileList"
    :input-width="318"
    :loading="saveLoading"
    @submit="saveSubmit"
    @open="openCreateDetail"
    @cancel="$emit('cancel')"
  >
    <!-- 需求类型下拉列表 -->
    <el-dropdown
      v-model="form.typeCode"
      class="dropList"
      trigger="click"
      @command="changeClass"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="defectMap && defectMap[form.typeCode]">
            <el-row
              type="flex"
              style="display: flex; align-items: center; cursor: pointer"
            >
              <i
                :class="`iconfont ${defectMap[form.typeCode].icon}`"
                :style="{
                  color: `${
                    defectMap[form.typeCode].color
                      ? defectMap[form.typeCode].color
                      : '#ccc'
                  }`,
                }"
                class="iconStyle"
              />
              <span> {{ defectMap[form.typeCode].name }} </span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置分类 </span>
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="typeList && typeList.length > 0">
          <el-dropdown-item
            v-for="item in typeList"
            :key="item.code"
            :command="item.code"
          >
            <span v-if="item.icon">
              <i
                :class="`iconfont ${item.icon}`"
                :style="{ color: `${item.color ? item.color : '#ccc'}` }"
              />
            </span>
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选分类" />
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 人员下拉列表 -->
    <vone-remote-user v-model="form.userId" style="width: 140px" />

    <el-popover v-if="!noFile" width="517" trigger="hover">
      <vone-upload ref="uloadFile" :biz-type="bizType" @change="onChange" />
      <div slot="upload" />
      <vone-empty v-if="fileList.length === 0" desc="暂无附件" />
      <el-icon
        class="iconfont"
        style="cursor: pointer; color: var(--main-theme-color); margin: 0 12px"
        ><el-icon-application-attachment
      /></el-icon>
    </el-popover>
  </vone-simple-add>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  ApplicationAttachment as ElIconApplicationAttachment,
} from '@element-plus/icons-vue'

import { apiAlmSaveIssue, apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { apiAlmTaskAdd } from '@/api/vone/project/task'
import { apiAlmBugAdd } from '@/api/vone/project/defect'
import { apiAlmRiskAdd } from '@/api/vone/project/risk'

import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { apiProjectUserNoPage } from '@/api/vone/project/index'

import dayjs from 'dayjs'

import { mapGetters } from 'vuex'
export default {
  components: {
    ElIconDirectionDown,
    ElIconApplicationAttachment,
  },
  props: {
    testCaseId: {
      type: Number,
      default: null,
    },
    planId: {
      type: String,
      default: '',
    },
    issueId: {
      // 需求id,用于需求关联任务时,保存时传需求id
      type: String,
      default: undefined,
    },
    noFile: Boolean,

    typeCode: {
      // 分类枚举值,只作为查询分类的入参,ISSUE,BUG,TASK,RISK,及区分保存时调哪个接口
      type: String,
      default: undefined,
    },
    noEpic: Boolean, // 史诗拆分用户故事,需求新增时,过滤掉史诗

    bizType: {
      // 附件上传时传的类型参数
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      form: {
        name: '',
        typeCode: '',
        userId: '',
        envCode: '',
      },
      sourceList: [], // 来源
      typeList: [], // 分类下拉框
      envList: [], // 环境
      users: [], // 用户
      fileList: [],
      defectMap: {}, // 缺陷分类
      userMap: {},
      envMap: {},
      saveLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          {
            pattern: '^.{1,250}$',
            message: '请输入不超过250个字符组成的标题',
            trigger: 'change',
          },
        ],
      },
    }
  },
  computed: {
    projectKey() {
      return this.$route.params.projectKey
    },
    ...mapGetters(['user']),
  },
  mounted() {
    this.getTypeList()
    // this.getUser()
    this.getSourceList()
    this.$set(this.form, 'userId', this.user?.id)
  },
  methods: {
    // 打开新建详细数据弹窗
    openCreateDetail() {
      this.$emit('createDetail')
      this.$emit('cancel')
    },
    // 切换类型
    changeClass(value) {
      this.$set(this.form, 'typeCode', value)
    },
    changeUser(v) {
      this.form.userId = v
    },
    onChange(val) {
      this.fileList = val
    },
    // 查询类型
    async getTypeList() {
      const res = await apiAlmGetTypeNoPage(
        this.$route.params.id,
        this.typeCode
      )
      if (!res.isSuccess) {
        this.$message.success(res.msg)
        return
      }

      if (this.noEpic) {
        // 史诗拆分用户故事,需求新增时,过滤掉史诗
        this.typeList = res.data.filter((r) => r.code != 'EPIC')
      } else {
        this.typeList = res.data
      }
      this.defectMap = this.typeList.reduce((r, v) => (r[v.code] = v) && r, {})
      this.$set(this.form, 'typeCode', this.typeList[0]?.code)
    },
    // 查询项目下人员
    async getUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.users = res.data
    },
    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode,
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
    },
    async saveSubmit() {
      const { id } = this.$route.params
      const params = {
        name: this.form.name,
        typeCode: this.form.typeCode,
        estimateHour:
          this.typeCode === 'TASK' || this.typeCode === 'BUG' ? 8 : null,
        priorityCode: 'HIGH',
        createdBy: this.form.userId,
        putBy: this.form.userId,
        handleBy: this.form.userId,
        leadingBy: this.form.userId,
        projectId: id,
        planEtime: dayjs().add(240, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        planStime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        description: '',
        sourceCode: this.sourceList[0].code,
        requirementId: this.issueId ? this.issueId : null,
        parentId: this.noEpic ? this.issueId : null,
        files: this.fileList.length ? this.fileList : [],
      }

      if (this.typeCode == 'ISSUE') {
        // 保存需求
        this.saveIssue(params)
      } else if (this.typeCode == 'TASK') {
        this.saveTask(params)
      } else if (this.typeCode == 'BUG') {
        this.saveBug(params)
      } else {
        this.saveRisk(params)
      }
    },
    async saveIssue(params) {
      try {
        this.saveLoading = true
        const res = await apiAlmSaveIssue(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('创建成功')
        this.$emit('success')
        this.form = {
          name: '',
          typeCode: this.typeList[0]?.code,
          userId: this.user?.id,
        }
        this.fileList = []
        this.$refs['uloadFile'].resetData()
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 保存任务
    async saveTask(params) {
      try {
        this.saveLoading = true
        const res = await apiAlmTaskAdd(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('创建成功')
        this.$emit('success')
        this.form = {
          name: '',
          typeCode: this.typeList[0]?.code,
          userId: this.user?.id,
        }
        this.fileList = []

        this.$refs['uloadFile'].resetData()
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 保存缺陷
    async saveBug(params) {
      try {
        this.saveLoading = true
        const res = await apiAlmBugAdd(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('创建成功')
        this.$emit('success')
        this.form = {
          name: '',
          typeCode: this.typeList[0]?.code,
          userId: this.user?.id,
        }
        this.fileList = []

        this.$refs['uloadFile'].resetData()
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 保存风险
    async saveRisk(params) {
      try {
        this.saveLoading = true
        const res = await apiAlmRiskAdd(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('创建成功')
        this.$emit('success')
        this.form = {
          name: '',
          typeCode: this.typeList[0]?.code,
          userId: this.user?.id,
        }
        this.fileList = []
        this.$refs['uloadFile'].resetData()
      } catch (e) {
        this.saveLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dropList {
  min-width: 80px;
  margin: 0 6px;
  max-width: 140px;
}
.textTitle {
  white-space: nowrap;
}
// .el-icon--right {
//   margin-top: 9px;
// }
.iconStyle {
  // margin-top: 10%;
  margin-left: 5px;
  margin-right: 5px;
}
.dropdown {
  max-height: 350px;
  overflow: auto;
}
</style>
