<template>
  <el-dialog
    width="50%"
    title="产品需求拆分"
    :model-value="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="产品需求" name="first">
        <div>
          <el-row v-if="addTask == null">
            <el-button
              type="text"
              :icon="el-icon-setting"
              @click="addTask = 0"
              >新增</el-button
            >
            <el-button type="text" :icon="ElIconLink" @click="addTask = 1"
              >关联</el-button
            >
          </el-row>
          <el-form ref="taskFormRef" :model="taskForm" :rules="rules">
            <el-row
              type="flex"
              justify="space-between"
              style="margin-bottom: 10px"
            >
              <simpleAddIssue
                v-if="addTask == 0"
                no-file
                :type-code="'ISSUE'"
                :issue-id="rowId"
                no-epic
                style="margin-bottom: 20px"
                @success="init"
                @cancel="addTask = null"
              />

              <!-- <div> -->
              <el-col v-if="addTask == 1" style="margin-right: 6px">
                <el-form-item prop="taskId">
                  <el-select
                    v-model="taskForm.taskId"
                    placeholder="请选择需求"
                    multiple
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in taskList"
                      :key="item.id"
                      :label="`${item.code}  ${item.name}`"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-button
                v-if="addTask == 1"
                class="miniBtn"
                @click="addTask = null"
                >取消</el-button
              >
              <el-button
                v-if="addTask == 1"
                class="miniBtn"
                type="primary"
                :loading="taskLoading"
                @click="saveTask"
                >确定</el-button
              >

              <!-- </div> -->
            </el-row>
          </el-form>

          <!-- 关联需求列表 -->
          <el-table
            ref="issue-task"
            v-loading="loading"
            :table-options="tableOptions"
            row-key="id"
            table-key="issue-task"
            :data="tableData.records"
            @getTableData="getTableData"
          >
            <template>
              <el-table-column prop="name" label="名称" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ `${scope.row.code} ${scope.row.name}` }}
                </template>
              </el-table-column>
              <el-table-column
                prop="planEtime"
                width="180"
                show-overflow-tooltip
                label="计划完成时间"
              />
              <el-table-column
                prop="handleBy"
                label="处理人"
                width="180"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <vone-user-avatar
                    :avatar-path="
                      getUserInfo(scope.row)
                        ? getUserInfo(scope.row).avatarPath
                        : ''
                    "
                    :name="
                      getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                    "
                  />
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="依赖关系" name="second">
        <!-- 依赖关系-------------------------------------------------------------- -->
        <div>
          <el-form ref="issueTab" :model="issueTab" :rules="relateRules">
            <el-row
              type="flex"
              justify="space-between"
              style="margin-bottom: 10px"
            >
              <el-button
                v-if="!addRelation"
                type="text"
                :icon="ElIconPlus"
                @click="addRelation = true"
                >添加依赖</el-button
              >

              <el-col v-if="addRelation" style="margin-right: 6px">
                <el-form-item prop="relation">
                  <el-select
                    v-model="issueTab.relation"
                    placeholder="请选择依赖关系"
                  >
                    <el-option label="依赖" value="DEPEND" />
                    <el-option label="影响" value="AFFECT" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="addRelation" style="margin-right: 6px">
                <el-form-item prop="projectId">
                  <el-select
                    v-model="issueTab.projectId"
                    filterable
                    style="width: 100%"
                    disabled
                    @change="changeProject"
                    @focus="setOptionWidth"
                  >
                    <el-option
                      v-for="item in projectIdList"
                      :key="item.key"
                      :label="item.name"
                      :value="item.id"
                      :style="{ width: selectOptionWidth }"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="addRelation" style="margin-right: 6px">
                <el-form-item prop="relationIssue">
                  <el-select
                    v-model="issueTab.relationIssue"
                    placeholder="请输入需求名称"
                    clearable
                    filterable
                    remote
                    :remote-method="getRequirementList"
                    :loading="requireLoading"
                    class="requireSelect"
                    @focus="setOptionWidth"
                  >
                    <el-option
                      v-for="ele in issueList"
                      :key="ele.id"
                      :value="ele.id"
                      :label="ele.name"
                      :disabled="ele.disabled"
                      :style="{ width: selectOptionWidth }"
                      :title="ele.name"
                    >
                      {{ `${ele.code}   ${ele.name}` }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-button
                v-if="addRelation"
                class="miniBtn"
                @click="addRelation = false"
                >取消</el-button
              >
              <el-button
                v-if="addRelation"
                class="miniBtn"
                type="primary"
                :loading="relationLoading"
                @click="addRelationShip"
                >确定</el-button
              >
            </el-row>
          </el-form>

          <el-table
            ref="relationTable"
            v-loading="tableLoading"
            class="vone-table"
            :data="relationTable"
          >
            <template>
              <el-table-column prop="relation" label="关系" width="150">
                <template slot-scope="scope">
                  <el-radio-group
                    v-model="scope.row.relationType"
                    size="mini"
                    @change="EditState(scope.row)"
                  >
                    <el-radio-button label="DEPEND" size="mini"
                      >依赖</el-radio-button
                    >
                    <el-radio-button label="AFFECT" size="mini"
                      >影响</el-radio-button
                    >
                  </el-radio-group>
                </template>
              </el-table-column>
              <el-table-column
                prop="projectId"
                label="项目"
                show-overflow-tooltip
                width="200"
              >
                <template slot-scope="scope">
                  <span
                    v-if="
                      scope.row.projectId &&
                      scope.row.echoMap &&
                      scope.row.echoMap.projectId
                    "
                  >
                    {{ scope.row.echoMap.projectId.name }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column prop="name" label="需求" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ `${scope.row.code}   ${scope.row.name}` }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    :icon="el-icon-setting"
                    @click="deleteRelation(scope.row)"
                  />
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="close">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  apiAlmIssuePage,
  apiAlmFindTodoByProjectIde,
  apiAlmCorrelatedRequirement,
  apiAlmProjectRelation,
  apiAlmRequirementNoPage,
  apiAlmAddDepend,
  apiAlmPutDepend,
  apiAlmDeleteDepend,
} from '@/api/vone/project/issue'
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
import _, { debounce } from 'lodash'

import simpleAddIssue from '@/views/vone/project/issue/function/simple-add-issue.vue'
export default {
  components: {
    simpleAddIssue,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    rowId: {
      type: String,
      default: undefined,
    },

    projectKey: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      taskLoading: false,
      activeName: 'first',
      relationLoading: false,
      tableData: {
        records: [],
      },
      projectIdList: [], // 项目
      issueTab: {
        relation: 'DEPEND',
      },
      addRelation: false,
      tableLoading: false,
      createSimple: false,
      typeId: '',
      formData: {},
      tableOptions: {
        isOperation: true,
        isSelection: true,
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '80', // 根据实际情况给宽度
          data: [
            {
              type: 'icon', // 为icon则是图标
              label: '取消关联', // 功能名称
              icon: 'iconfont el-icon-edit-unrelate', // icon class
              handler: this.tableDelete, // 操作事件
            },
          ],
          // 更多操作按钮
          moreData: [],
        },
      },
      taskList: [],
      addTask: null,
      taskForm: {
        taskId: [],
      },
      rules: {
        taskId: [{ required: true, message: '请选择任务', trigger: 'change' }],
      },
      relateRules: {
        relation: [
          { required: true, message: '请选择依赖关系', trigger: 'change' },
        ],
        projectId: [
          { required: true, message: '请选择项目', trigger: 'change' },
        ],
        relationIssue: [
          { required: true, message: '请选择需求', trigger: 'change' },
        ],
      },
      loading: true,
      relationTable: [],
      issueList: [],
      selectOptionWidth: '',
      requireLoading: false,
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    },
  },

  mounted() {
    this.getIssueList()
    this.getProjectList()
    this.getTableData()
  },
  methods: {
    handleClick(val) {
      if (val.name == 'second') {
        this.addRelation = false
        this.getRelationTable()
      } else {
        this.addTask = null
        this.getTableData()
      }
    },
    initList() {
      this.$emit('success')
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeProject(val) {
      this.$set(this.issueTab, 'relationIssue', '')
      this.issueList = []
    },
    // 查需求列表
    getRequirementList: debounce(async function (query, projectId) {
      try {
        this.requireLoading = true
        const res = await apiAlmRequirementNoPage({
          name: query,
          projectId: this.issueTab.projectId,
        })
        this.requireLoading = false
        if (!res.isSuccess) {
          return
        }

        res.data.forEach((element) => {
          element.disabled = element.id == this.rowId
        })

        this.issueList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    close() {
      this.$emit('update:visible', false)
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data

      this.$set(this.issueTab, 'projectId', this.$route.params.id)
    },

    async getTableData() {
      this.loading = true
      let params = {}

      this.$set(this.formData, 'parentId', [this.rowId])
      // const tableAttr = this.$refs['issue-task']?.exportTableQueryData()
      params = {
        // ...tableAttr,
        extra: {},
        model: { ...this.formData },
      }
      const res = await apiAlmIssuePage(params)
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    // 查询依赖关系
    async getRelationTable() {
      this.tableLoading = true

      const res = await apiAlmProjectRelation('ISSUE', this.rowId)
      this.tableLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.AFFECT.forEach((element) => {
        element.relationType = 'AFFECT'
      })

      res.data.DEPEND.forEach((element) => {
        element.relationType = 'DEPEND'
      })

      const allData = _.concat(res.data.AFFECT, res.data.DEPEND)

      this.relationTable = allData
    },
    async tableDelete(val) {
      this.$confirm(`你确定要取消【 ${val.name} 】和需求的关联吗?`, '删除', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false,
      })
        .then(async () => {
          const { isSuccess, msg } = await apiAlmCorrelatedRequirement({
            correlatedType: 'DELETE',
            requirementId: this.rowId,
            requirementIds: [val.id],
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success('操作成功')
          this.getTableData()
          this.getIssueList()
          this.$emit('initList')
        })
        .catch(() => {})
    },
    // 查询需求列表
    async getIssueList() {
      const { data, isSuccess, msg } = await apiAlmFindTodoByProjectIde(
        this.$route.params.id
      )
      if (!isSuccess) {
        return
      }

      this.taskList = data.length
        ? data.filter((r) => r.typeCode != 'EPIC')
        : []
    },
    async saveTask() {
      try {
        await this.$refs.taskFormRef.validate()
      } catch (error) {
        return
      }

      try {
        this.taskLoading = true
        const { isSuccess, msg } = await apiAlmCorrelatedRequirement({
          correlatedType: 'ADD',
          requirementId: this.rowId,
          requirementIds: this.taskForm.taskId,
        })
        this.taskLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success('保存成功')

        this.addTask = null
        this.$set(this.taskForm, 'taskId', [])
        this.getTableData()
        this.getIssueList()
      } catch (e) {
        this.taskLoading = false
      }
    },
    init() {
      this.addTask = null
      this.getTableData()
      this.$emit('initList')
    },
    // 添加依赖关系
    async addRelationShip() {
      try {
        await this.$refs.issueTab.validate()
      } catch (error) {
        return
      }

      try {
        this.relationLoading = true
        const { isSuccess, msg } = await apiAlmAddDepend({
          fromId: this.rowId,
          fromType: 'ISSUE',
          relationType: this.issueTab.relation,
          toId: this.issueTab.relationIssue,
          toType: 'ISSUE',
        })
        this.relationLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success(msg)
        this.getRelationTable()
        this.addRelation = false
        this.$set(this.issueTab, 'relation', 'DEPEND')
        this.$set(this.issueTab, 'relationIssue', '')
      } catch (e) {
        this.relationLoading = false
      }
    },
    // 修改依赖关系
    async EditState(row) {
      const { isSuccess, msg } = await apiAlmPutDepend(
        row.relationId,
        row.relationType
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
    },
    // 删除依赖关系
    async deleteRelation(row) {
      this.$confirm(`你确定要删除 [${row.name}]的依赖关系吗?`, '删除', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false,
      })
        .then(async () => {
          const { isSuccess, msg } = await apiAlmDeleteDepend(row.relationId)
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)
          this.getRelationTable()
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  // max-height: 600px;
  overflow-y: scroll;
}
:deep(.el-tabs__content) {
  min-height: 400px;
}
</style>
