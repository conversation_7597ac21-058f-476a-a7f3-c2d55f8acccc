<template>
  <div id="ganttGraph">
    <div class="toolbox">
      <span class="toolGround today" @click="scrolltoday">今天</span>
      <el-select v-model="dateValue" style="width: 58px" @change="settingScale">
        <el-option value="day" label="天" />
        <el-option value="week" label="周" />
        <el-option value="month" label="月" />
        <el-option value="quarter" label="季度" />
        <el-option value="half" label="半年" />
        <el-option value="year" label="年" />
      </el-select>
      <span class="toolGround triggleBox">
        <el-icon><ElIconSetting /></el-icon>
        <el-icon><ElIconSetting /></el-icon>
      </span>
      <el-icon class="toolGround iconfont"
        ><el-icon-direction-fullscreen
      /></el-icon>
    </div>

    <div ref="gantt" v-loading="loading" class="gantt-container" />

    <!-- 新增计划对话框 -->
    <createDialog
      v-if="createParam.visible"
      v-model="createParam.visible"
      v-bind="createParam"
      @success="getPlanTree"
    />
    <!-- 编辑弹窗 -->
    <editDialog
      v-if="taskParam.visible"
      v-bind="taskParam"
      v-model="taskParam.visible"
      @success="getPlanTree"
    />
    <!-- 关联计划对话框 -->
    <connectDialog
      v-if="connectParam.visible"
      v-bind="connectParam"
      v-model="connectParam.visible"
      @success="getPlanTree"
    />
  </div>
</template>

<script>
import {
  Plus as ElIconPlus,
  Minus as ElIconMinus,
  DirectionFullscreen as ElIconDirectionFullscreen,
} from '@element-plus/icons-vue'
import { avatarMap } from '@/assets/avatar/avatar'

import {
  apiAlmFindItemNoPage,
  apiAlmPlanCancleSprint,
  apiAlmProjectPlanAdd,
  apiAlmProjectPlanNoPage,
  apiAlmProjectPlanDel,
  deleteIssueLinkedPlan,
  updateIssueLinkedPlan,
} from '@/api/vone/project/iteration'

import createDialog from './createDialog'
import editDialog from './editDialog.vue'
import connectDialog from './connectDialog.vue'

import pick from 'lodash/pick'
import dayjs from 'dayjs'
import gantt from 'dhtmlx-gantt' // 引入模块
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css'
import { catchErr } from '@/utils'

const quarterOfYear = require('dayjs/plugin/quarterOfYear')
dayjs.extend(quarterOfYear)

const scaleMap = {
  day: [
    { unit: 'day', step: 1, format: '%d' },
    { unit: 'month', step: 1, format: '%Y-%n' },
  ],
  week: [
    {
      unit: 'week',
      step: 1,
      format(date) {
        return '第' + gantt.date.getWeek(date) + '周'
      },
    },
    { unit: 'month', step: 1, format: '%Y-%n' },
  ],
  month: [{ unit: 'month', step: 1, format: '%Y-%n' }],
  quarter: [
    {
      unit: 'month',
      step: 3,
      format(date) {
        const map = {
          1: '一',
          2: '二',
          3: '三',
          4: '四',
        }
        const month = date.getMonth()
        const year = date.getFullYear()
        const quarter = Math.floor((month + 2) / 3)
        return year + ' 第' + map[quarter] + '季度'
      },
    },
  ],
  half: [
    {
      unit: 'month',
      step: 6,
      format(date) {
        return date.getMonth() < 6 ? '上半年' : '下半年'
      },
    },
    { unit: 'year', step: 1, format: '%Y' },
  ],
  year: [{ unit: 'year', step: 1, format: '%Y' }],
}

export default {
  components: {
    connectDialog,
    createDialog,
    editDialog,
    ElIconPlus,
    ElIconMinus,
    ElIconDirectionFullscreen,
  },
  name: 'PlanGantt',
  data() {
    return {
      eventList: [],
      tasks: {
        data: [
          // { type: gantt.config.types.milestone, id: 4, name: '里程碑3', start_date: '2022-08-18 00:00:00', duration: 1, color: '#3CB540' },
          // { id: 'add', name: 'add' }
        ],
        links: [
          // { id: 1, source: 1, target: 2, type: '0' }
        ],
      },
      loading: false,
      dialogVisible: false,
      dateValue: 'day',
      menuTop: '',
      menuLeft: '',
      markerId: null,
      createParam: {
        visible: false,
      },
      taskParam: { visible: false },
      connectParam: { visible: false },
      allPlans: [],
      itemId: null,
      prevLeft: null, // 上一次滚动距离
    }
  },
  created() {
    this.getPlanTree()
  },
  mounted() {
    const self = this
    // 本地化
    gantt.i18n.setLocale('cn')
    // 初始化的时候就展开树结构
    gantt.config.open_tree_initially = true
    gantt.config.show_links = false

    gantt.config.show_progress = true

    // gantt.config.placeholder_task = true;
    // gantt.config.show_task_cells = true

    // 从后端过来的左侧展示列时间数据格式化
    gantt.config.xml_date = '%Y-%m-%d'

    gantt.config.scale_height = 48 // 表头高度
    gantt.config.row_height = 48 // 表格行高
    gantt.config.task_height = 5 // 设置任务条高度
    gantt.config.scroll_size = 6 // 4.3 设置滚动条宽/高度

    gantt.config.fit_tasks = true // 自动延长时间刻度以适应所有显示的任务
    // 禁用双击事件
    gantt.config.details_on_dblclick = false
    // 关闭所有错误提示信息：gantt有自己的异常消息，如果不关闭可能页面会弹出异常消息
    gantt.config.show_errors = false

    // 时间轴图表中，如果不设置，只有行边框，区分上下的任务，设置之后带有列的边框，整个时间轴变成格子状。
    gantt.config.show_task_cells = true

    const statusMap = ['', 'start', 'process', 'finish']
    // 隐藏新建设置列
    gantt.templates.task_class = function (start, end, task) {
      const status = statusMap[task.stateCode] || 'start'
      return task.text === 'add'
        ? 'gantt_task_class_add'
        : task.type === 'milestone'
        ? 'gantt_task_class_bg ' + status
        : 'gantt_task_class_bg'
    }
    // 计算任务的左侧和右侧的距离
    function getRange(start, end, type) {
      if (type === 'half') {
        return dayjs(end).diff(start, 'month') > 6 ? 2 : 1
      }
      return dayjs(end).diff(start, type)
    }
    // 自定义右侧图上任务展示的文字
    gantt.templates.task_text = function (start, end, task) {
      const range = getRange(start, end, self.dateValue)
      const bgColor = typeof task.color == 'undefined' ? '#64BEFA' : task.color
      return range >= 2
        ? `<div class="processBox inline" style="background-color:${bgColor}"><span>${task.text}</span><span>${task.duration}天</span></div>`
        : `<div class="processBox" style="background-color:${bgColor}"></div>`
      // return task.text + '(' + task.duration + '天' + ')'
    }
    gantt.templates.rightside_text = function (start, end, task) {
      if (task.typeCode === 'MILESTONE') return ''
      const range = getRange(start, end, self.dateValue)
      return range >= 2
        ? ''
        : `<div class="processBox rightside"><span>${task.text}</span><span>${task.duration}天</span></div>`
    }
    // 自定义图标
    gantt.templates.grid_folder = function (item) {
      return `<div style='display: inline-flex;align-items: center;margin-right: 2px;font-size: 14px;'><svg class="icon" aria-hidden="true" ><use xlink:href="#el-icon-jihua"></use></svg>
</div>`
    }
    // 子项图标
    gantt.templates.grid_file = function (obj) {
      if (obj.text === 'add') return ''
      return `<div style='display: inline-flex;align-items: center;margin-right: 2px;font-size: 14px;'><svg class="icon" aria-hidden="true" ><use xlink:href="#${
        obj.typeCode === 'MILESTONE'
          ? 'el-icon-lichengbei'
          : obj.typeCode === 'PLAN'
          ? 'el-icon-jihua'
          : obj.echoMap.typeCode.icon
      }"></use></svg></div>`
    }
    // 打开关闭图标
    gantt.templates.grid_open = function (item) {
      return (
        "<div class='gantt_tree_icon " +
        (item.$open
          ? 'iconfont el-icon-direction-down'
          : 'el-icon-arrow-right') +
        "'></div>"
      )
    }
    // 自定义区分周末列,列头样式
    gantt.templates.scale_cell_class = function (date) {
      const today = dayjs().format('YYYY-MM-DD')
      const dateFormat = dayjs(date).format('YYYY-MM-DD')
      if (dateFormat === today) return 'today'
      // 只设置天和周时的休息日样式
      if (self.dateValue != 'day' || self.dateValue != 'week') return
      if (date.getDay() == 0 || date.getDay() == 6) {
        return 'weekend'
      }
    }
    gantt.templates.timeline_cell_class = function (task, date) {
      if (self.dateValue != 'day' || self.dateValue != 'week') return
      if (date.getDay() == 0 || date.getDay() == 6) return 'weekend'
    }

    // gantt.templates.grid_row_class = function(start, end, task, e) {
    // }

    // gantt.config.autofit = false;

    // 自适应甘特图的尺寸大小, 使得在不出现滚动条的情况下, 显示全部任务
    gantt.config.autosize = false

    // 开启提示：鼠标悬浮在gantt行上tooltip显示
    gantt.plugins({
      // tooltip: true
      fullscreen: true,
      marker: true,
    })
    // 添加今天的日期标记
    const dateToStr = gantt.date.date_to_str(gantt.config.task_date)
    this.markerId = gantt.addMarker({
      start_date: new Date(), // a Date object that sets the marker's date
      css: 'today', // a CSS class applied to the marker
      // text: '', // the marker title
      title: dateToStr(new Date()), // the marker's tooltip
    })
    gantt.getMarker(this.markerId)
    // 定时更新今天的日期标记
    setInterval(function () {
      var today = gantt.getMarker(this.markerId)
      if (!today) return
      today.start_date = new Date()
      today.title = gantt.date.date_to_str(today.start_date)
      gantt.updateMarker(this.markerId)
    }, 1000 * 60 * 15)
    // 自定义tooltip
    // gantt.templates.tooltip_text = function(start, end, task) {
    //   return '<b>任务名称:</b> ' + task.name + '<br/><b>时长:</b> ' + task.duration + '天' + '<br/><b>进度:</b>' + task.progress * 100 + '%'
    // }
    gantt.config.drag_progress = false // 禁止拖拽进度
    // 设置拖拽
    gantt.config.drag_move = true
    gantt.config.order_branch = 'marker'
    gantt.config.order_branch_free = true
    this.eventList.push(
      gantt.attachEvent('onRowDragEnd', function (id, target) {
        const task = gantt.getTask(id)
        const typeCode = task?.echoMap?.typeCode
        // 工作项拖入
        if (typeCode) {
          const params = [
            {
              issueId: task.bizId,
              planId: target == 0 ? null : target,
              typeClassify: typeCode.classify.code,
              typeCode: typeCode.code,
            },
          ]
          // 取消关联计划
          if (target == 0) {
            deleteIssueLinkedPlan(params).then((res) => {
              if (!res.isSuccess) {
                this.$message.error('取消关联计划失败')
              }
            })
          } else {
            updateIssueLinkedPlan(params).then((res) => {
              if (!res.isSuccess) {
                this.$message.error('关联计划失败')
              }
            })
          }
          return
        }

        const params = {
          ...pick(task, ['name', 'stateCode', 'leadingBy']),
          id: id,
          parentId: target,
          type: task.typeCode,
          projectId: self.$route.params.id,
        }
        apiAlmProjectPlanAdd(params).then((res) => {
          if (!res.isSuccess) {
            this.$message.error(res.message)
          }
        })
      })
    )
    this.eventList.push(
      gantt.attachEvent('onBeforeRowDragMove', function (id, parent, tindex) {
        const current = gantt.getTask(id)
        if (current.text == 'add') return false
        return true
      })
    )
    this.eventList.push(
      gantt.attachEvent('onBeforeRowDragEnd', function (id, parent, tindex) {
        const current = gantt.getTask(id)
        const parentTask = gantt.getTask(parent)

        if (current.text == 'add') return false
        // 根节点下拖拽
        if (!current.parent && !parentTask) return false
        // 当前节点下
        if (current.parent === parent) return false
        // 拖到根节点下
        if (!parentTask) return true
        if (parentTask?.text === 'add') {
          self.$message.error('不能拖动到新建下')
          return false
        }

        if (parentTask?.typeCode !== 'PLAN') {
          self.$message.error(`请拖动到计划类型下`)
          return false
        }

        return true
      })
    )
    // 当前日期
    let startDate = null
    let endDate = null
    // 动态计算日期范围，包括当前日期范围
    this.eventList.push(
      gantt.attachEvent('onBeforeGanttRender', function () {
        const range = gantt.getSubtaskDates()

        if (
          endDate &&
          range.end_date?.valueOf() <= endDate &&
          startDate &&
          range.start_date?.valueOf() >= startDate
        )
          return
        const scaleUnit = gantt.getState().scale_unit
        if (range.start_date && range.end_date) {
          endDate =
            new Date().valueOf() > range.end_date.valueOf()
              ? new Date()
              : range.end_date
          startDate = range.start_date.valueOf()
          gantt.config.start_date = gantt.calculateEndDate(
            range.start_date,
            -1,
            scaleUnit
          )
          gantt.config.end_date = gantt.calculateEndDate(endDate, 6, scaleUnit)
        }
      })
    )
    // 自定义右侧时间轴
    gantt.config.scales = [
      { unit: 'day', step: 1, format: '%d' },
      { unit: 'month', step: 1, format: '%Y-%n' },
    ]

    // 激活列表展开（折叠）功能
    gantt.config.open_split_tasks = true

    // const textEditor = { type: 'name', map_to: 'name' }
    // 表格列设置：我们在后台获取数据后，会解析到这个表格列中，这里面会含有很多隐藏列，作用是甘特图中不需要看隐藏列，但当我们获取甘特图的任务时，这些隐藏列会跟随任务方便使用
    gantt.config.columns = [
      // {
      //   // 最左侧新增符号列，甘特图内置可选使用列
      //   name: 'add',
      //   label: '',
      //   width: '40'
      // },
      {
        name: 'delay',
        label: ' ',
        width: '40',
        template: function (obj) {
          if (obj.text === 'add') return ''
          return `<i class="iconfont dragItem el-icon-icon-tuodong" style="color:#6B7385;margin-left:-2px;"></i>${
            obj.delay
              ? '<div class="iconfont el-icon-tips-exclamation-circle" style="color:#FA5F2B;"></div>'
              : ''
          }`
        },
      },
      {
        name: 'text',
        label: '名称',
        tree: true,
        width: '180',
        align: 'left',
        // editor: textEditor,
        template(obj) {
          if (obj.text === 'add') {
            return `<div class="createPlan el-icon-plus" style="color:#3E7BFA;margin-left: -15px;cursor:pointer;"> 新建</div>`
          }
          const hideLink =
            obj.typeCode !== 'MILESTONE' && obj.typeCode !== 'PLAN'
              ? 'none'
              : 'inline-block'
          const hideAddChild = obj.typeCode !== 'PLAN' ? 'none' : 'inline-block'
          // const unlinkedItem = obj.parent === 0 && obj.typeCode !== 'MILESTONE' && obj.typeCode !== 'PLAN' ? 'none' : 'inline-block'
          return `<div class="projectItem text-over" title="${obj.text}">${obj.text}
      <span class="tools">
      <i class="edit iconfont el-icon-application-rename" data-value="edit" data-id="${obj.id}" title="编辑" style="display:${hideLink}"></i>
      <i class="link iconfont el-icon-edit-relate" data-value="link" data-id="${obj.id}" title="关联工作项" style="display:${hideAddChild}"></i>
      <i class="add iconfont el-icon-tips-plus-circle" data-value="addchild" data-id="${obj.id}" title="新增子层级" style="display:${hideAddChild}"></i>
      <i class="detail iconfont el-icon-application-jump-content" data-value="detail" data-id="${obj.id}" title="跳转详情" style="display:${hideLink}"></i>
      <i class="delete iconfont el-icon-edit-unrelate" data-value="delete" data-id="${obj.id}" title="取消关联"></i>
      </span>
    </div>`
        },
      },
      {
        name: 'typeText',
        label: '类型',
        width: '80',
        align: 'left',
      },
      {
        name: 'leadingBy',
        label: '负责人',
        width: '100',
        align: 'left',
        template: function (obj) {
          if (obj.text === 'add') return ''
          const leadingBy = obj.echoMap?.leadingBy
          const avatar = leadingBy ? avatarMap[leadingBy.avatarPath] : null
          if (avatar && obj.leadingBy) {
            return `<span class="avatar" style="display:flex;justify-content: start;align-items:center;gap:0 4px;">
            <img src="${avatar.src}" alt="" width="24px" height="24px">
            <span>${leadingBy.name}</span>
          </span>`
          }
          return obj.leadingBy ? `<span>${obj.leadingBy}</span>` : ''
        },
      },
      {
        name: 'start_date',
        label: '开始日期',
        width: '100',
        align: 'left',
        template(obj) {
          if (obj.text === 'add') return ''
          return obj.start_date || '-'
        },
      },
      {
        name: 'end_date',
        label: '完成日期',
        width: '100',
        align: 'left',
        template(obj) {
          if (obj.text === 'add') return ''

          const day =
            obj.typeCode !== 'MILESTONE'
              ? dayjs(obj.end_date).subtract(1, 'day').format('YYYY-MM-DD')
              : obj.end_date
          return day || '-'
        },
      },
    ]

    // 重定义getFullscreenElement方法
    gantt.ext.fullscreen.getFullscreenElement = function () {
      return document.getElementById('ganttGraph')
    }

    const button = document.getElementById('toggle_fullscreen')
    const fixHeader = document.querySelector('.fixed-header')

    // 添加全屏事件
    button.addEventListener(
      'click',
      function () {
        const isFullscreen = gantt.getState().fullscreen

        isFullscreen ? gantt.collapse() : gantt.expand()
      },
      false
    )
    this.eventList.push(
      gantt.attachEvent('onCollapse', function () {
        const container = document.querySelector('.gantt-container')
        container.style.height = 'calc(100vh - 208px)'
        fixHeader.style.zIndex = '1500'
        button.classList.remove('el-icon-direction-fullscreen-exit')
        button.classList.add('el-icon-direction-fullscreen')
      })
    )
    this.eventList.push(
      gantt.attachEvent('onExpand', function () {
        const container = document.querySelector('.gantt-container')
        container.style.height = '100%'
        fixHeader.style.zIndex = '-1'
        button.classList.remove('el-icon-direction-fullscreen')
        button.classList.add('el-icon-direction-fullscreen-exit')
      })
    )
    this.eventList.push(
      // 设置点击展开和隐藏列表的事件
      gantt.attachEvent('onTaskClick', function (id, e) {
        // 展开隐藏任务列
        if (e.target.classList.contains('gantt_tree_icon')) {
          this.$open = this.$open === undefined ? false : !this.$open
          this.$open ? gantt.open(id) : gantt.close(id)
          return true
        }
        // 触发事件
        if (e.target.dataset.value) {
          self.triggerToolEvent(e)
          return true
        }
        if (id === 'add') {
          self.openCreate()
          return true
        }
        return true
      })
    )
    this.eventList.push(
      // 修改表头固定日期时间
      gantt.attachEvent('onGanttScroll', function (left, top) {
        if (self.prevLeft === null) self.prevLeft = left
        if (Math.abs(self.prevLeft - left) > 10) {
          self.initStickyDay()
          self.prevLeft = left
        }
      })
    )
    // gantt.attachEvent('onMouseMove', function(id, e) {
    //   if (id && e.target.id != 'taskCheckBoxId') {
    //     // self.display = 'none'
    //   }
    // })
    const { inlineEditors } = gantt.ext
    this.eventList.push(
      inlineEditors.attachEvent('onBeforeEditStart', function (state) {
        // -> {id: itemId, columnName: columnName};
        // 新增不显示
        return state.id !== 'add'
      })
    )
    // 行内编辑保存事件
    // inlineEditors.attachEvent('onSave', function(state) {

    // })
    // // 初始化
    // gantt.init(this.$refs.gantt)
    // // 数据解析
    // gantt.parse(this.tasks)
    // 甘特图外清除选中
    const ganttDom = document.querySelector('#ganttGraph')

    document.onclick = function (event) {
      const id = gantt.getSelectedId()
      if (!id) return
      const eventx = event.clientX
      const eventy = event.clientY
      const { x, y, width, height } = ganttDom.getBoundingClientRect()
      const isInside =
        eventx > x &&
        eventx < x + width &&
        eventy > y &&
        eventy < eventy + height
      if (!isInside) {
        gantt.unselectTask()
      }
    }
  },
  beforeDestroy() {
    this.initToolsEvent('remove')
    // 清除甘特图事件
    this.eventList.map((id) => {
      gantt.detachEvent(id)
    })
  },
  methods: {
    initToolsEvent(type) {
      // 注册表格内工具事件
      const toolsEls = document.querySelectorAll('.tools')
      const toolsClick = (e) => {
        this.triggerToolEvent(e)
      }
      toolsEls?.forEach((ele) => {
        type === 'remove'
          ? ele.removeEventListener('click', toolsClick)
          : ele.addEventListener('click', toolsClick)
      })
    },
    // 触发工具事件
    triggerToolEvent(e) {
      const { value, id } = e.target.dataset
      const data = gantt.$data.tasksStore._eachItemMainRangeCache.find(
        (item) => item.id == id
      )

      switch (value) {
        case 'edit':
          this.editTask(data)
          break
        case 'detail':
          this.viewTaskDetail(data)
          break
        case 'link':
          this.openConnectDialog(data)
          break
        case 'addchild':
          this.openCreateChild(data)
          break
        case 'delete':
          this.deleteItem(data)
          break
      }
    },
    // 查询项目计划数据
    async getPlanTree() {
      // 清空gantt数据
      gantt.clearAll()
      this.loading = true
      const [{ data, isSuccess, msg }, err] = await catchErr(
        apiAlmProjectPlanNoPage({
          isFiled: false,
          projectId: this.$route.params.id,
        })
      )
      if (err) return
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }

      const nodeIds = data.reduce((acc, item) => {
        acc[item.id] = item.id
        return acc
      }, {})

      data.forEach((ele) => {
        // 不存在的父节点，表格中不显示
        ele.text = ele.name
        ele.typeText = ele.type?.desc // 类型
        ele.typeCode = ele.type?.code // 类型code
        ele.start_date = ele.planStime // 开始日期
        // ele.end_date = ele.planEtime // 完成日期
        ele.progress = ele.rateProgress * 1 // 进度
        ele.parent =
          nodeIds[ele.parentId] && ele.parentId != ele.id
            ? ele.parentId || 0
            : 0 // 关联父节点
        ele.color =
          ele.stateCode == 1
            ? '#ADB0B8'
            : ele.stateCode == 2
            ? '#64BEFA'
            : '#3CB540' // 进度颜色

        ele.duration = dayjs(ele.planEtime).diff(ele.planStime, 'day') + 1 || 1 // 进度长度

        // 里程碑
        if (ele.type.code === 'MILESTONE') {
          ele.start_date = ele.planEtime
          ele.type = gantt.config.types.milestone
        }
        if (ele.typeCode === 'PLAN') {
          this.getTaskWork(ele)
        }
      })
      this.allPlans = data
      // 添加显示新增
      this.tasks.data = [...data, { id: 'add', text: 'add' }]
      // 请求数据后初始化
      gantt.init(this.$refs.gantt)
      // 数据解析
      gantt.parse(this.tasks)
      this.scrolltoday()
      this.loading = false
      setTimeout(() => {
        this.initStickyDay()
      }, 100)
    },
    // 查询当前计划下的事项
    async getTaskWork(task) {
      const res = await apiAlmFindItemNoPage({
        projectId: this.$route.params.id,
        planId: task?.id || '-1',
      })
      if (res.isSuccess) {
        if (res.data?.length === 0) return
        res.data.forEach((ele) => {
          ele.text = ele.code + '' + ele.name
          ele.typeText = ele.echoMap.typeCode.name
          ele.parent = task?.id || 0
          ele.start_date =
            ele.planStime ||
            (ele.planEtime ? dayjs(ele.planEtime) : dayjs())
              .subtract(1, 'day')
              .format('YYYY-MM-DD') // 开始日期
          ele.end_date = ele.planEtime
            ? ele.planEtime
            : dayjs().format('YYYY-MM-DD')
          ele.color = ele.rateProgress
            ? ele.rateProgress == '100'
              ? '#3CB540'
              : ele.rateProgress == '0'
              ? '#ADB0B8'
              : '#64BEFA'
            : '#ADB0B8' // 进度颜色
          ele.duration =
            Math.abs(dayjs(ele.planEtime).diff(ele.planStime, 'day')) + 1 || 1 // 进度长度
        })
        this.tasks.data = [...res.data, ...this.tasks.data]
      }
      gantt.parse(this.tasks)
      gantt.render()
      this.scrolltoday()
      setTimeout(() => {
        this.initStickyDay()
      }, 100)
    },
    // 打开新建弹窗
    openCreate(type) {
      this.createParam = {
        visible: true,
        title: type ? (type === 'PLAN' ? '新建计划' : '新建里程碑') : '',
        dialogType: type || '',
      }
    },
    // 新建子层级
    openCreateChild(data) {
      this.createParam = {
        visible: true,
        title: '新建子项',
        dialogType: '',
        parentId: data.id,
      }
    },
    // 编辑
    editTask(row) {
      this.taskParam = {
        visible: true,
        title:
          row.typeCode === 'MILESTONE'
            ? '编辑里程碑'
            : row.typeCode === 'PLAN'
            ? '编辑计划'
            : '编辑工作项',
        dialogType: row.typeCode,
        infoData: row,
      }
    },
    // 查看详情
    viewTaskDetail(row) {
      this.taskParam = {
        visible: true,
        title: '查看详情',
        dialogType: row.typeCode,
        infoData: row,
        view: true,
      }
    },
    // 打开关联工作项弹窗
    openConnectDialog(row) {
      this.connectParam = {
        visible: true,
        infoData: row,
      }
    },
    async deleteItem(row) {
      const title =
        row.typeCode === 'MILESTONE'
          ? '里程碑'
          : row.typeCode === 'PLAN'
          ? '计划'
          : '工作项'
      const confirm = await catchErr(
        this.$confirm(`确定取消关联该${title}吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      )
      if (confirm[1]) return
      let fetch
      if (row.typeCode != 'PLAN' && row.typeCode != 'MILESTONE') {
        fetch = await catchErr(
          apiAlmPlanCancleSprint([
            {
              issueId: row.bizId,
              planId: row.planId,
              typeClassify: row.classify?.code,
              typeCode: row.typeCode,
            },
          ])
        )
      } else {
        fetch = await catchErr(apiAlmProjectPlanDel([row.id]))
      }

      const [{ isSuccess, msg }, err] = fetch
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('删除成功')
      // 刷新
      this.getPlanTree()
    },
    // 滚动到今天位置
    scrolltoday() {
      gantt.showDate(new Date())
    },
    // 加减修改右侧表头日期
    plusDateValue() {
      if (this.dateValue === 'year') return
      const valueSet = ['day', 'week', 'month', 'quarter', 'half', 'year']
      const index = valueSet.indexOf(this.dateValue)
      this.dateValue = valueSet[index + 1]
      this.settingScale(this.dateValue)
    },
    minusDateValue() {
      if (this.dateValue === 'day') return
      const valueSet = ['day', 'week', 'month', 'quarter', 'half', 'year']
      const index = valueSet.indexOf(this.dateValue)
      this.dateValue = valueSet[index - 1]
      this.settingScale(this.dateValue)
    },
    // 设置右侧表头日期格式
    settingScale(val) {
      gantt.config.scales = scaleMap[val]
      // 是否显示标记
      gantt.config.show_markers = true
      gantt.render()
      setTimeout(() => {
        this.initStickyDay()
      }, 100)
    },
    // 设置表头固定时间提示
    initStickyDay() {
      let stickyDay = document.querySelector('.stickyday')
      if (!stickyDay) {
        const scaleLayout = document.querySelector(
          '.timeline_cell .gantt_layout_content'
        )
        stickyDay = document.createElement('div')
        stickyDay.className = 'stickyday'
        scaleLayout?.appendChild(stickyDay)
      }
      const dayDom = document.querySelector('.gantt_scale_cell')
      if (!dayDom) return
      stickyDay.style.height = dayDom.style.height
      stickyDay.style.lineHeight = dayDom.style.height
      stickyDay.innerText = dayDom.innerText
    },
  },
}
</script>

<style lang="scss" scoped>
#ganttGraph {
  width: 100%;
  min-height: calc(100vh - 208px);
  margin: 0;
  padding: 0;
  background-color: var(--main-bg-color);
  z-index: 3 !important;
}
:deep(.gantt-container) {
  height: calc(100vh - 208px);
  margin: 0;
  padding: 0;
  background-color: var(--main-bg-color) !important;
}
.toolbox {
  position: absolute;
  top: 70px;
  right: 16px;
  height: 24px;
  z-index: 100;
  display: inline-flex;
  gap: 0 8px;

  i {
    font-size: 14px;
    cursor: pointer;
  }

  :deep() {
    .el-input__inner {
      border: none;
      padding: 0 8px 0 4px;
      height: 24px;
      line-height: 24px;
      border-radius: 4px;
      box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
    }
    .el-input__icon {
      width: 14px;
      line-height: 24px;
    }
  }
}

.today {
  font-size: 13px;
}
.toolGround {
  padding: 5px;
  border-radius: 4px;
  cursor: pointer;
  background-color: var(--main-bg-color);
  box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
}
.triggleBox {
  padding: 0;
  i {
    padding: 5px;
  }
  .disabled {
    cursor: not-allowed;
    background-color: #f7f8fa;
  }
}

.gantt_layout_cell .scrollHor_cell {
  height: 10px !important;
}

/*滚动条整体样式*/
.gantt-wrapper ::-webkit-scrollbar {
  width: 14px; /*宽分别对应竖滚动条的尺寸*/
  height: 6px; /*高分别对应横滚动条的尺寸*/
}

/*滚动条里面轨道*/
.gantt-wrapper ::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.2);
}
/*滚动条里面拖动条*/
.gantt-wrapper ::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
}

.title {
  height: 30px;
  line-height: 30px;
  margin-bottom: 10px;
}
.milestone {
  padding-left: 10px;
  border-left: 5px solid var(--main-theme-color, #3e7bfa);
}

:deep() {
  .gantt_task_class_add {
    display: none;
  }
  .gantt_task_class_bg {
    background-color: #fff !important;
    // &.milestone {
    //   background-color: #3e7bfa !important;
    // }
    &.start {
      background-color: #adb0b8 !important;
    }
    &.process {
      background-color: #3e7bfa !important;
    }
    &.finish {
      background-color: #3cb540 !important;
    }
  }
  // 选中样式
  .gantt_task_row.gantt_selected {
    background-color: #f2f3f5 !important;
  }
  .gantt_grid_data .gantt_row.gantt_selected {
    background: #f2f3f5 !important;
  }
  .gantt_grid_data .gantt_row.odd.gantt_selected {
    background: #f2f3f5 !important;
  }
  .gantt_task_row.gantt_selected .gantt_task_cell {
    border-color: #f2f3f5;
  }
  .gantt_grid_data .gantt_row.odd:hover,
  .gantt_grid_data .gantt_row:hover {
    background-color: var(--main-bg-color);
  }
  // .grid_cell {
  //   box-shadow: 0px -9px 16px 0px #ddd;
  //   z-index: 100;
  // }
  // .gantt_grid {
  //   border-right: 1px solid #eaecf0;
  // }
  .gantt_grid_head_cell.gantt_grid_head_add {
    display: none;
  }
  .gantt_layout_cell {
    border: none;
  }
  .gantt_grid_scale {
    font-size: 14px;
    border-bottom: none;
  }
  .gantt_tree_icon {
    cursor: pointer;
    padding: 16px 0;
    width: 20px;
  }
  // 表格cell样式
  .gantt_tree_content {
    width: 100%;
  }
  // 表头样式
  .gantt_grid_scale .gantt_grid_head_cell {
    background-color: var(--bottom-bg-color) !important;
    color: var(--main-font-color) !important;
    text-align: left;
    padding-left: 6px;

    &[aria-label='名称'] {
      padding-left: 23px;
    }
  }
  .gantt_grid_data .gantt_cell {
    position: relative;
    font-size: 14px;
    // background-color: var(--main-bg-color);
    color: var(--main-font-color);
  }
  .dragItem {
    padding: 0 2px;
    opacity: 0;
    visibility: hidden;
  }
  .gantt_row {
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    &:hover {
      .dragItem {
        padding: 0 2px;
        opacity: 1;
        visibility: visible;
        cursor: grab;
      }
    }
  }
  // 行内编辑输入框样式
  .gantt_grid_editor_placeholder input {
    height: 32px;
    border: 1px solid #3e7bfa;
    border-radius: 2px;
    margin-top: 8px;
  }
  .gantt_task_scale {
    background-color: #fafafa;
    border-bottom-color: #eaecf0;
  }
  // 甘特头部样式
  .gantt_scale_line {
    border-top: none;
    &:first-child {
      .gantt_scale_cell {
        text-align: left;
        padding-left: 4px;
      }
    }
  }
  .gantt_task .gantt_task_scale .gantt_scale_cell {
    // background-color: var(--main-bg-color);
    color: #6b7385;
    font-weight: 700;
    border-right-color: #eaecf0;

    &.today {
      background-color: #fa962b;
      color: #fff;
    }

    &.weekend {
      color: #b2b6bf;
      font-weight: 400;
      // background-color: var(--disabled-bg-color);
    }
  }
  .gantt_task_row {
    border-bottom: none;

    &[data-task-id='add'] {
      border-bottom: 1px solid #eaecf0;
    }
  }
  .gantt_task_cell {
    // background-color: var(--main-bg-color);
    color: var(--main-font-color);

    &.today {
      &::before {
        content: '';
        display: block;
        height: 100%;
        border-left: 2px solid #fa962b;
        margin-left: 50%;
      }
    }
    &.weekend {
      color: #b2b6bf;
      font-weight: 400;
      background-color: #fafafa;
    }
  }
  .gantt_data_area {
    background-color: var(--main-bg-color) !important;
  }
  .gantt_grid_data {
    background-color: var(--main-bg-color) !important;
  }
  .gantt_bar_task {
    border: none;
  }
  .gantt_side_content.gantt_right {
    padding-left: 0;
  }
  .gantt_task_line.gantt_selected {
    box-shadow: 0 0 5px #f2f3f5;
  }
  // 里程碑样式
  .gantt_task_line.gantt_milestone {
    border-radius: 50%;
    visibility: initial;
    margin-top: 5px;
    margin-left: 5px;
    height: 19px !important;
    width: 19px !important;
    line-height: 19px !important;
    border: none !important;

    .gantt_task_content {
      width: 11px !important;
      height: 11px !important;
      background-color: #fff;
      top: 4px;
      right: 4px;
    }
  }
  .processBox {
    display: flex;
    justify-content: space-between;
    padding: 0 8px;
    gap: 0 8px;
    height: 32px;
    line-height: 32px;
    margin-top: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    border-radius: 4px;
    &.rightside {
      color: #1d2129;
    }
  }
  // 任务进度拖拽样式
  .gantt_task_progress_drag {
    z-index: 10;
  }
  // 任务项拖拽样式
  .gantt_task_drag {
    background: url('@/assets/project/gantt-drag.png') no-repeat;
    background-size: contain;
    background-position: 50%;
    background-color: #fff;
    width: 16px;
    top: 0px;
    &.task_start_date {
      left: -16px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    &.task_end_date {
      right: -16px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
  // 表格工作项操作样式
  .projectItem {
    .tools {
      position: absolute;
      right: 0;
      opacity: 0;
      padding: 0 0 0 4px;
      cursor: pointer;
      color: #6b7385;
      background-color: #fff;
      z-index: 10;
    }
    &:hover {
      .tools {
        opacity: 1;
      }
    }
    .iconfont:hover {
      color: var(--main-theme-color);
    }
  }
  .gantt_selected {
    .projectItem {
      .tools {
        background-color: #f2f3f5;
      }
    }
  }
}
</style>

<style lang="scss">
.stickyday {
  position: absolute;
  top: 0;
  left: 0;
  height: 29px;
  line-height: 29px;
  padding: 0 5px;
  z-index: 10;
  color: #6b7385;
  font-weight: 700;
  font-size: 12px;
  background-color: #fafafa;
}
</style>
