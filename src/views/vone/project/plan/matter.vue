<template>
  <!-- vone-custom-table -->
  <div class="wrapper-box">
    <el-row>
      <el-col :span="4">
        <div class="tree-style">
          <div class="title">
            <span class="title-text">计划</span>
            <el-button
              :icon="ElIconTipsPlusCircleFill iconfont"
              type="text"
              size="mini"
              style="float: right; min-width: 0"
              @click="addPlan"
            />
          </div>
          <div
            :class="['not-planned', { 'not-planned-select': nodekey == '-1' }]"
            @click="notPlanned"
          >
            <div class="not-planned-btn">
              <span>
                <svg class="icon" aria-hidden="true" style="font-size: 16px">
                  <use xlink:href="#el-icon-application-filejia-weifenzu" />
                </svg>
              </span>
              <span>未规划事项</span>
            </div>
          </div>
          <el-tree
            ref="treeRef"
            :data="planlist"
            :props="defaultProps"
            node-key="id"
            :indent="22"
            highlight-current
            check-on-click-node
            check-strictly
            @node-click="nodeClick"
            @node-expand="nodeExpand"
            @node-collapse="nodeCollapse"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node father">
              <span
                style="display: flex; flex: 1; width: 0"
                :class="node.childNodes.length == 0 ? 'group-style' : ''"
              >
                <span>
                  <svg class="icon" aria-hidden="true" style="font-size: 16px">
                    <use
                      v-if="data.type.code === 'PLAN'"
                      xlink:href="#el-icon-jihua"
                    />
                    <use
                      v-if="data.type.code === 'MILESTONE'"
                      xlink:href="#el-icon-lichengbei"
                    />
                  </svg>
                </span>
                <span style="margin-left: 6px" :title="data.name">
                  {{ data.name }}
                </span>
              </span>
              <span class="operation-icon">
                <el-button
                  type="text"
                  size="mini"
                  :icon="el-icon-setting"
                  @click.stop="() => editNode(data, node)"
                />
                <el-button
                  type="text"
                  size="mini"
                  :icon="el-icon-setting"
                  @click.stop="() => deleteItem(data, node)"
                />
                <el-button
                  type="text"
                  size="mini"
                  :icon="el-icon-setting"
                  @click.stop="fileDel(data)"
                />
              </span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="20">
        <el-form
          v-if="nodekey != '-1'"
          ref="iterationForm"
          disabled
          label-position="top"
          :model="iterationForm"
        >
          <el-row class="basicHeader" :gutter="24">
            <el-col :span="24">
              <el-input v-model="iterationForm.name" placeholder="请填写" />
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <span
                  v-if="
                    iterationForm.echoMap && iterationForm.echoMap.leadingBy
                  "
                >
                  <vone-user-avatar
                    :avatar-path="iterationForm.echoMap.leadingBy.avatarPath"
                    :avatar-type="true"
                    :show-name="false"
                    height="42px"
                    width="42px"
                  />
                </span>
                <span v-else>
                  <el-icon class="iconfont" style="font-size: 42px"
                    ><el-icon-icon-light-avatar
                  /></el-icon>
                </span>
                <el-form-item label="处理人" prop="leadingBy">
                  <vone-remote-user
                    v-model="iterationForm.leadingBy"
                    :no-name="false"
                  />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <el-icon class="iconfont"
                  ><el-icon-icon-fill-zhuangtai
                /></el-icon>
                <el-form-item label="状态" prop="stateCode">
                  <el-select
                    v-model="iterationForm.stateCode"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="i in stateOption"
                      :key="i.id"
                      :label="i.name"
                      :value="i.code"
                    >
                      <i
                        style="
                          display: inline-block;
                          width: 10px;
                          height: 10px;
                          border-radius: 50%;
                        "
                        :style="{
                          color: i.color,
                          border: `2px solid ${i.color}`,
                        }"
                      />
                      {{ i.name }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <svg-icon icon-class="vone-date" />
                <el-form-item label="开始日期" prop="planEtime">
                  <el-date-picker
                    v-model="iterationForm.planEtime"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择日期"
                  />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <svg-icon icon-class="vone-date" />
                <el-form-item label="完成日期" prop="planEtime">
                  <el-date-picker
                    v-model="iterationForm.planEtime"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择日期"
                  />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
        <div class="table-box">
          <vone-search-wrapper>
            <template slot="search">
              <vone-search-dynamic
                slot="search"
                :project-id="$route.params.id"
                :model="formData"
                label-position="top"
                @getTableData="getConnetedItems"
              >
                <el-row :gutter="20">
                  <el-col v-for="item in filterList" :key="item.key" :span="12">
                    <el-form-item :label="item.name" :prop="item.key">
                      <!-- 人员组件 -->
                      <vone-remote-user
                        v-if="item.type == 'user'"
                        v-model="formData[item.key]"
                        multiple
                      />

                      <!-- 输入框 -->
                      <el-input
                        v-else-if="item.type == 'input'"
                        v-model="formData[item.key]"
                        :placeholder="item.placeholder"
                      />

                      <!-- 下拉多选框 -->
                      <el-select
                        v-else-if="item.type == 'select'"
                        v-model="formData[item.key]"
                        :placeholder="item.placeholder"
                        multiple
                      >
                        <el-option
                          v-for="i in item.options"
                          :key="i.id"
                          :label="i.name"
                          :value="item.key == 'planIds' ? i.id : i.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </vone-search-dynamic>
            </template>
            <template slot="actions">
              <el-row>
                <span class="btnContainer">
                  <el-button
                    v-if="nodekey != '-1'"
                    :icon="el-icon-setting"
                    @click.stop="disassociate()"
                    >取消关联</el-button
                  >
                  <el-button
                    :icon="el-icon-setting"
                    @click.stop="relation()"
                    >关联</el-button
                  >
                  <el-dropdown
                    style="margin-left: 10px"
                    trigger="click"
                    @command="showAdd"
                  >
                    <el-button-group>
                      <el-button
                        type="primary"
                        :icon="el-icon-setting"
                        >新增</el-button
                      >
                      <el-button
                        class="subBtton"
                        :icon="el-icon-setting"
                        type="primary"
                      />
                    </el-button-group>

                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="1">需求</el-dropdown-item>
                      <el-dropdown-item command="2">缺陷</el-dropdown-item>
                      <el-dropdown-item command="3">任务</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
              </el-row>
            </template>
          </vone-search-wrapper>
          <main
            :style="{
              height:
                nodekey != '-1' ? 'calc(100vh - 405px)' : 'calc(100vh - 272px)',
            }"
          >
            <vxe-table
              ref="connectItemTable"
              class="vone-vxe-table"
              border
              v-loading="loading"
              height="auto"
              auto-resize
              :data="tableData.records"
              @getTableData="getConnetedItems"
              @checkbox-change="onSelectionChange"
            >
              <vxe-column
                type="checkbox"
                width="36"
                align="center"
                fixed="left"
              />
              <vxe-column title="标题" field="name" show-overflow-tooltip>
                <template v-slot="{ row }">
                  <span>{{ row.code + ' ' + row.name }}</span>
                </template>
              </vxe-column>
              <vxe-column title="状态" field="stateCode">
                <template slot-scope="scope">
                  <!-- <span v-if="row.echoMap&&row.echoMap.stateCode" class="tagCustom" :style="{color:row.echoMap.stateCode.color,border:'1px solid currentColor'}">{{ row.echoMap.stateCode.name }}</span>
                  <span v-else class="status-text">{{ row.stateCode }}</span> -->
                  <span
                    v-if="
                      scope.row &&
                      scope.row.echoMap &&
                      scope.row.echoMap.typeCode
                    "
                  >
                    <issueStatus
                      v-if="scope.row.echoMap.typeCode.classify.code == 'ISSUE'"
                      :key="Date.now()"
                      :no-permission="!$permission('project_iteration_flow')"
                      :workitem="scope.row"
                      @changeFlow="getConnetedItems"
                    />

                    <taskStatus
                      v-else-if="
                        scope.row.echoMap.typeCode.classify.code == 'TASK'
                      "
                      :key="Date.now()"
                      :no-permission="!$permission('project_iteration_flow')"
                      :workitem="scope.row"
                      @changeFlow="getConnetedItems"
                    />

                    <defectStatus
                      v-else-if="
                        scope.row.echoMap.typeCode.classify.code == 'BUG'
                      "
                      :key="Date.now()"
                      :no-permission="!$permission('project_iteration_flow')"
                      :workitem="scope.row"
                      @changeFlow="getConnetedItems"
                    />
                  </span>
                  <span v-else>{{ scope.row.typeCode }}</span>
                </template>
              </vxe-column>
              <vxe-column title="优先级" field="priotory" width="100px">
                <template v-slot="{ row }">
                  <span v-if="row.echoMap && row.echoMap.priorityCode"
                    ><i
                      :class="['iconfont', row.echoMap.priorityCode.icon]"
                      :style="{ color: row.echoMap.priorityCode.color }"
                    />{{ row.echoMap.priorityCode.name }}</span
                  >
                  <span v-else>{{ row.priorityCode }}</span>
                </template>
              </vxe-column>
              <vxe-column title="计划完成时间" field="planEtime" width="110px">
                <template v-slot="{ row }">
                  <span>{{ row.planEtime | format }}</span>
                </template>
              </vxe-column>
              <vxe-column title="处理人" field="handleBy" width="100px">
                <template v-slot="{ row }">
                  <span
                    v-if="row.handleBy && row.echoMap && row.echoMap.handleBy"
                  >
                    <vone-user-avatar
                      :avatar-path="row.echoMap.handleBy.avatarPath"
                      :name="row.echoMap.handleBy.name"
                    />
                  </span>
                </template>
              </vxe-column>
              <vxe-column title="负责人" field="leadingBy" width="100px">
                <template v-slot="{ row }">
                  <span
                    v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy"
                  >
                    <vone-user-avatar
                      :avatar-path="row.echoMap.leadingBy.avatarPath"
                      :name="row.echoMap.leadingBy.name"
                    />
                  </span>
                </template>
              </vxe-column>
            </vxe-table>
          </main>
          <vone-pagination
            ref="pagination"
            :total="tableData.total"
            @update="getConnetedItems"
          />
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="关联计划"
      width="600px"
      :model-value="associatedPlanVisible"
      append-to-body
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="associatedPlanForm"
        :model="associatedPlanForm"
        label-position="top"
      >
        <el-form-item
          label="计划"
          prop="id"
          :rules="[
            { required: true, message: '请选择计划', trigger: 'change' },
          ]"
        >
          <vone-tree-select
            v-model="associatedPlanForm.id"
            append-to-body
            search-nested
            :tree-data="newPlanList"
            placeholder="请选择计划"
            style="width: 90%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: right">
        <el-button @click="onClose">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="confirmPlan"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 新增计划对话框 -->
    <createDialog
      v-if="createParam.visible"
      v-model="createParam.visible"
      v-bind="createParam"
      @success="getPlanTree"
    />
    <!-- 编辑弹窗 -->
    <editDialog
      v-if="planParam.visible"
      v-bind="planParam"
      v-model="planParam.visible"
      @success="getPlanTree"
    />
    <!-- 编辑里程碑 -->
    <edit-milepost
      v-if="editMilepostParam.visible"
      v-model="editMilepostParam.visible"
      v-bind="editMilepostParam"
      @success="getPlanTree"
    />
    <!-- 关联计划对话框 -->
    <!-- <connectDialog v-if="connectParam.visible" v-bind="connectParam" v-model="connectParam.visible" @success="getPlanTree" /> -->
    <!-- 新增需求 -->
    <vone-custom-add
      v-if="issueAddParam.visible"
      :key="issueAddParam.key"
      v-model="issueAddParam.visible"
      v-bind="issueAddParam"
      :type-code="'ISSUE'"
      :title="'新增需求'"
      @success="getConnetedItems"
      @initList="getConnetedItems"
    />
    <vone-custom-add
      v-if="taskParamAdd.visible"
      :key="taskParamAdd.key"
      v-model="taskParamAdd.visible"
      v-bind="taskParamAdd"
      :type-code="'TASK'"
      :title="'新增任务'"
      @success="getConnetedItems"
    />
    <!-- 新增缺陷 -->
    <vone-custom-add
      v-if="defectParamAdd.visible"
      :key="defectParamAdd.key"
      v-model="defectParamAdd.visible"
      v-bind="defectParamAdd"
      :type-code="'BUG'"
      :title="'新增缺陷'"
      @success="getConnetedItems"
    />
  </div>
</template>

<script>
import {
  apiAlmProjectPlanNoPage,
  apiAlmPlanInfo,
  getWorkItemsByPage,
  apiAlmPlanCancleSprint,
  apiAlmProjectPlanDel,
  apiAlmPlanLinkedSprint,
  filed,
} from '@/api/vone/project/iteration'
import createDialog from './createDialog'
import editDialog from './editDialog.vue'
// import connectDialog from './connectDialog.vue'
import editMilepost from './edit-milepost.vue'
import { catchErr } from '@/utils'
import dayjs from 'dayjs'
import { list2Tree } from '@/utils/list2Tree'
import { gainTreeList } from '@/utils'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import taskStatus from '@/views/vone/project/common/change-status/index.vue'
import defectStatus from '@/views/vone/project/common/change-status/index.vue'
export default {
  components: {
    // connectDialog,
    createDialog,
    editDialog,
    editMilepost,
    issueStatus,
    taskStatus,
    defectStatus,
  },
  filters: {
    format(value) {
      return value ? dayjs(value).format('YYYY-MM-DD') : ''
    },
  },

  data() {
    return {
      active: 'plan',
      planlist: [],
      loading: false,
      listLoading: false,
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      iterationForm: {},
      stateOption: [
        {
          name: '未开始',
          code: '1',
          color: '#adb0b8',
        },
        {
          name: '进行中',
          code: '2',
          color: '#64befa',
        },
        {
          name: '已完成',
          code: '3',
          color: '#3cb540',
        },
      ],
      tableOptions: {},
      tableData: { records: [] },
      filterList: [
        {
          name: '名称',
          key: 'name',
          type: 'input',
          placeholder: '请输入名称',
        },
        {
          name: '处理人',
          key: 'handleBy',
          type: 'user',
        },
      ],
      formData: {
        name: '',
        handleBy: [],
      },
      nodekey: '-1',
      createParam: {
        visible: false,
      },
      planParam: { visible: false },
      connectParam: { visible: false },
      editMilepostParam: { visible: false },
      issueAddParam: { visible: false }, // 新增需求
      taskParamAdd: { visible: false },
      defectParamAdd: { visible: false }, // 缺陷新增
      selected: [],
      associatedPlanForm: {},
      associatedPlanVisible: false,
      saveLoading: false,
      newPlanList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getPlanTree()
    this.$nextTick(() => {
      this.getConnetedItems()
    })
  },
  mounted() {},
  methods: {
    nodeClick(data) {
      this.nodekey = data.id
      this.getPlanInfo(data.id)
      this.getConnetedItems()
    },
    nodeExpand() {},
    nodeCollapse() {},
    notPlanned() {
      this.$refs['treeRef'].setCurrentKey(null)
      this.nodekey = '-1'
      this.getConnetedItems()
    },

    // 归档
    async fileDel(val) {
      await this.$confirm(`是否进行归档`, '提示', {
        type: 'warning',
      })

      const res = await filed(val.id)
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.$message.success(res.msg)
      this.getPlanTree()
      this.$refs['treeRef'].setCurrentKey(null)
      this.nodekey = '-1'
      this.getConnetedItems()
      this.getPlanInfo(this.nodekey)
    },
    showAdd(command) {
      if (command == 1) {
        // 需求新增
        this.issueAddParam = {
          visible: true,
          sprintId: this.nodekey,
          key: Date.now(),
        }
      } else if (command == 2) {
        // 缺陷新增
        this.defectParamAdd = {
          visible: true,
          sprintId: this.nodekey,
          key: Date.now(),
        }
      } else if (command == 3) {
        // 任务新增
        this.taskParamAdd = {
          visible: true,
          sprintId: this.nodekey,
          key: Date.now(),
        }
      }
    },
    editNode(data) {
      if (data.type.code === 'MILESTONE') {
        this.editMilepostParam = { visible: true, id: data.id }
      } else {
        this.planParam = { visible: true, title: '编辑计划', infoData: data }
      }
    },
    async deleteItem(row) {
      if (row.children) {
        this.$message.warning('当前计划下有子级,不允许直接删除，请先删除子级')
        return
      }

      this.$confirm(`确定删除当前项吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
      })
        .then(() => {
          apiAlmProjectPlanDel([row.id]).then((res) => {
            if (!res.isSuccess) {
              this.$message.warning(res.msg)
              return
            }
            this.$message.success('删除成功')
            this.getPlanTree()
            this.$refs['treeRef'].setCurrentKey(null)
            this.nodekey = '-1'
            this.getConnetedItems()
            this.getPlanInfo(this.nodekey)
          })
        })
        .catch(() => {})
    },
    addPlan() {
      this.createParam = {
        visible: true,
        title: '新建计划',
        dialogType: '',
      }
    },
    // 取消关联工作项
    async disassociate() {
      if (!this.selected.length) {
        this.$message.warning('请选择工作项')
        return
      }

      await this.$confirm(`确定取消关联吗?`, '提示', {
        type: 'warning',
        closeOnClickModal: false,
      })

      const params = this.selected.map((r) => ({
        issueId: r.bizId,
        planId: this.currentNodekey,
        typeClassify:
          r.typeCode && r.echoMap && r.echoMap.typeCode
            ? r.echoMap.typeCode.classify.code
            : '',
      }))

      const res = await apiAlmPlanCancleSprint(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('取消关联迭代成功')
      this.getConnetedItems()
    },
    // 关联工作项
    relation() {
      if (!this.getVxeTableSelectData('connectItemTable').length) {
        this.$message.warning('请选择要关联的需求/任务/缺陷')
        return
      }
      this.associatedPlanVisible = true
    },
    onClose() {
      this.$refs.associatedPlanForm.resetFields()
      this.associatedPlanVisible = false
    },
    async confirmPlan() {
      try {
        await this.$refs.associatedPlanForm.validate()
        const selectedList = this.selected.map((item) => {
          return {
            issueId: item.bizId,
            planId: this.associatedPlanForm.id,
            typeClassify: item.classify.code,
            typeCode: item.typeCode,
          }
        })
        this.saveLoading = true
        const res = await apiAlmPlanLinkedSprint(selectedList)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('关联计划成功')
        this.onClose()
        this.getConnetedItems()
      } catch (e) {
        this.saveLoading = false
      }
    },
    onSelectionChange(selection) {
      this.selected = selection.records
    },
    // 查询项目已关联工作项
    async getConnetedItems() {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const params = {
        ...tableAttr,
        extra: {
          ...this.extraData,
        },
        model: {
          projectId: this.$route.params.id,
          planId: this.nodekey,
        },
      }
      this.loading = true
      const [{ data, isSuccess, msg }, err] = await catchErr(
        getWorkItemsByPage(params)
      )
      this.loading = false
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
    },
    //  查询计划详情
    async getPlanInfo(e) {
      this.formLoading = true
      const res = await apiAlmPlanInfo(e)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.iterationForm = res.data
    },
    // 查询计划树
    async getPlanTree() {
      this.listLoading = true
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        isFiled: false,
      })
      this.listLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.planlist = list2Tree(res.data, { parentKey: 'parentId' })
      const stoneTree = gainTreeList(this.planlist)
      this.newPlanList = stoneTree.filter((r) => r.id != -1)
    },
  },
}
</script>

<style lang="scss" scoped>
.wrapper-box {
  border: 1px solid var(--el-divider);
  border-radius: 4px;
  height: calc(100vh - 162px);
  // overflow: hidden;
}
.tree-style {
  height: 100%;
  overflow-y: hidden;
  background: #ffff;
  border-right: 1px solid var(--el-divider);
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    border-bottom: 1px solid #ebeef5;
    padding: 12px 16px;
    font-size: 16px;
  }

  .title-text {
    line-height: 56px;
    font-weight: 600;
    color: #202124;
  }
  .add-style {
    font-size: 17.5px;
    color: #3e7bfa;
    cursor: pointer;
  }
  .not-planned {
    padding: 0 16px;
    height: 36px;
    line-height: 36px;
    &:hover {
      background-color: var(--hover-bg-color, #f5f6fa);
    }
    cursor: pointer;
    .not-planned-btn {
      width: 100%;
      line-height: 36px;
      border-bottom: 1px solid #f2f3f5;
    }
  }
}
.not-planned-select {
  background-color: var(--hover-bg-color, #f5f6fa);
}
.el-tree {
  height: calc(100vh - 220px);
  padding: 6px 0 16px 0;
  overflow-y: auto;
  overflow-x: hidden;
  :deep(.el-tree-node__content) {
    height: 34px;
    color: #202124;
    .el-tree-node__expand-icon {
      color: var(--main-second-color);
      margin-left: 16px;
      padding: 0;
      font-size: 16px;
      padding-right: 6px;
    }
    .is-leaf {
      color: transparent;
      cursor: default;
      margin-left: 0;
      padding: 6px 0;
    }
    .el-button {
      padding: 0;
    }
  }
}
.el-tree-node__content {
  .operation-icon {
    opacity: 0;
    flex: 1;
    position: absolute;
    right: 0;
    padding-left: 5px;
    .is-disabled {
      color: var(--placeholder-color);
    }
  }
  &:hover {
    .operation-icon {
      opacity: 1;
      padding-left: 5px;
      position: absolute;
      right: 10px;
      background-color: var(--hover-bg-color, #f5f6fa);
      outline: none;
    }
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 16px;
}

.custom-tree-node-list {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 0 16px 0 1px;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current) {
  .el-tree-node__content {
    background-color: #f5f6fa;
  }
}
.group-style {
  margin-left: 22px;
}
.basicHeader {
  background-color: var(--node-cildren-bg-color);
  min-height: 100px;
  padding: 18px;
  border-bottom: 1px solid var(--disabled-bg-color);
  margin: 0 !important;
  .el-col-6 {
    & :hover {
      background: var(--col-hover-bg);
    }

    :deep(.el-select .el-input.is-focus .el-input__inner) {
      background-color: var(--col-hover-bg);
    }

    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;
      i,
      .svg-icon {
        font-size: 42px;
        margin-right: 10px;
      }
    }
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
      & :focus {
        background: var(--main-bg-color);
      }
    }
    :deep(.el-input__suffix) {
      display: none;
    }
    :deep(.el-form-item__label) {
      margin-left: 10px;
    }
    :deep(.el-input--small) {
      font-size: 14px;
    }

    :deep(.el-input.is-disabled .el-input__inner) {
      color: #000 !important;
      font-size: 14px;
    }
    :deep(.el-date-editor.el-input) {
      width: 100%;
    }
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }

  .el-col-24 {
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
      font-weight: bold;
      font-size: 16px;
    }

    :deep(.el-input__inner:focus) {
      background: var(--col-hover-bg);
    }
    & :hover {
      background: var(--col-hover-bg);
    }
  }
}
:deep(.table-operation-view) {
  margin: 0 -16px 16px;
  padding: 17px 16px;
}
.table-box {
  padding: 16px;
}
</style>
