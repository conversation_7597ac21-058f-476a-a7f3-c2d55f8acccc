<template>
  <page-wrapper>
    <el-tabs v-model="active" type="card" class="flowTab">
      <el-tab-pane name="plan" label="项目计划" />
      <el-tab-pane name="program" label="事项规划" />
      <el-tab-pane name="files" label="已归档计划" />
    </el-tabs>
    <div v-if="active === 'plan'" class="settingContainer">
      <span class="leftTitle">全部计划</span>
      <span class="btnContainer">
        <span class="illustration"
          ><span style="background: #adb0b8" />未开始</span
        >
        <span class="illustration"
          ><span style="background: #3e7bfa" />进行中</span
        >
        <span class="illustration" style="margin-right: 130px"
          ><span style="background: #3cb540" />已完成</span
        >
        <el-button
          :loading="tableLoading"
          :disabled="!$permission('project_plan_export')"
          @click="exportFlie"
          >导出</el-button
        >
        <el-button
          :icon="el-icon-setting"
          @click.stop="openAddDialog('MILESTONE')"
          >里程碑</el-button
        >
        <el-button
          :icon="el-icon-setting"
          type="primary"
          @click.stop="openAddDialog('PLAN')"
          >计划</el-button
        >
      </span>
    </div>
    <el-row v-if="active === 'plan'">
      <GanttGraph ref="ganttGraph" />
    </el-row>
    <div v-if="active === 'program'">
      <matter />
    </div>
    <div v-if="active === 'files'">
      <files />
    </div>
  </page-wrapper>
</template>

<script>
import GanttGraph from './gantt.vue'
import Matter from './matter.vue'
import Files from './files'
import { download } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'
export default {
  components: {
    GanttGraph,
    Matter,
    Files,
  },
  data() {
    return {
      active: 'plan',
      tableLoading: false,
    }
  },
  methods: {
    openAddDialog(type) {
      this.$refs.ganttGraph.openCreate(type)
    },
    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true

        download(
          `计划信息.xmind`,
          await apiBaseFileLoad(
            `/api/alm/project/plan/excel/export/${this.$route.params.id}`
          )
        )

        this.tableLoading = false
      } catch (e) {
        this.tableLoading = false
        return
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.leftTitle {
  font-weight: 500;
  font-size: 16px;
}
.settingContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  .illustration {
    display: inline-block;
    margin-right: 30px;
    span {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      margin-right: 10px;
    }
  }
}
</style>
