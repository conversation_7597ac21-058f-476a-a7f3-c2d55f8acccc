<template>
  <div>
    <vone-drawer
      :title="planType === 'add' ? '新建测试计划' : '编辑测试计划'"
      :visible="visible"
      direction="rtl"
      size="lg"
      :show-close="false"
      :before-close="onClose"
      :wrapper-closable="false"
    >
      <!-- 主体部分 -->
      <el-row type="flex" class="ctx">
        <div class="leftForm">
          <el-form
            ref="taskFormLeft"
            :model="taskForm"
            :rules="taskRules"
            label-position="top"
          >
            <el-form-item label="标题" prop="name">
              <el-input v-model="taskForm.name" placeholder="输入计划标题" />
            </el-form-item>
            <el-form-item label="测试类型">
              <el-radio-group v-model="taskForm.type">
                <el-radio label="system">系统测试</el-radio>
                <el-radio label="smoke">冒烟测试</el-radio>
                <el-radio label="regression">回归测试</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <vone-editor
                ref="editor"
                v-model="taskForm.description"
                @input.native="eventDisposalRangeChange(taskForm.description)"
              />
            </el-form-item>
            <el-form-item v-if="planType === 'add'">
              <el-radio-group v-model="taskForm.addCase" class="pt-radio">
                <el-radio label="1" disabled class="radio-label"
                  >包含全部用例</el-radio
                >
                <div>
                  覆盖本项目全部可用用例（共计
                  <span>{{ libCases.length }}</span>
                  个），如果用例库有新增的用例，不会被同步到本计划中。
                </div>
                <el-radio label="2" class="radio-label">手动圈选用例</el-radio>
                <div>
                  手动从用例库中圈选用例，如果用例库有新创建的用例，不会被同步到本计划。
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="taskForm.addCase == 2">
              <el-card class="box-card">
                <div class="text-item">
                  <span>{{ taskForm.projectCaseList.length }}</span>
                  {{ '条用例已选' }}
                  <a
                    v-if="planType == 'add'"
                    href="#"
                    class="selectButton"
                    @click="checkScope"
                    >圈选范围<el-icon><ElIconSetting /></el-icon
                  ></a>
                </div>
              </el-card>
            </el-form-item>
          </el-form>
        </div>
        <div class="ctx-right">
          <el-form
            ref="taskForm"
            :model="taskForm"
            :rules="taskRules"
            label-position="left"
          >
            <el-form-item label="负责人" prop="leadingBy">
              <vone-icon-select
                v-model="taskForm.leadingBy"
                placeholder="请选择人员"
                select-type="user"
                clearable
                filterable
                :data="userList"
                style="width: 100%"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <vone-user-avatar
                    v-if="item.id"
                    :avatar-path="item.avatarPath"
                    :avatar-type="item.avatarType"
                    :show-name="false"
                    height="22px"
                    width="22px"
                  />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
            </el-form-item>
            <!-- <el-form-item label="延期">
                <el-radio-group v-model="taskForm.delay">
                  <el-radio :label="true" :disabled="planType === 'edit'">是</el-radio>
                  <el-radio :label="false" :disabled="planType === 'edit'">否</el-radio>
                </el-radio-group>
              </el-form-item> -->
            <el-form-item label="开始时间" prop="planStime">
              <el-date-picker
                :shortcuts="pickerOptionsStart && pickerOptionsStart.shortcuts"
                :disabled-date="
                  pickerOptionsStart && pickerOptionsStart.disabledDate
                "
                :cell-class-name="
                  pickerOptionsStart && pickerOptionsStart.cellClassName
                "
                v-model="taskForm.planStime"
                type="datetime"
                placeholder="开始时间"
                format="MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="['9:00:00']"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间" prop="planEtime">
              <el-date-picker
                :shortcuts="pickerOptionsEnd && pickerOptionsEnd.shortcuts"
                :disabled-date="
                  pickerOptionsEnd && pickerOptionsEnd.disabledDate
                "
                :cell-class-name="
                  pickerOptionsEnd && pickerOptionsEnd.cellClassName
                "
                v-model="taskForm.planEtime"
                type="datetime"
                placeholder="结束时间"
                format="MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="['18:00:00']"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="状态" prop="stateId">
                <el-select v-model="taskForm.stateId" style="width: 100%">
                  <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.stateId" />
                </el-select>
              </el-form-item> -->
            <el-form-item label="产品" prop="productId">
              <el-select
                v-model="taskForm.productId"
                disabled
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in productList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关联迭代" prop="planId">
              <el-select
                v-model="taskForm.planId"
                filterable
                clearable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in planIdList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="测试环境" prop="envId">
              <el-select
                v-model="taskForm.envId"
                filterable
                clearable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in envList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="用例执行失败必须新增缺陷" prop="bug">
              <el-switch
                v-model="taskForm.bug"
                class="switchStyle"
                active-color="#13ce66"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-row>
      <span slot="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确定</el-button>
      </span>
    </vone-drawer>
    <!-- 新增关联用例 -->
    <relateCase
      v-model="addCaseVisible"
      :library-id="libraryId"
      :case-list="taskForm.projectCaseList"
      :plan-data="planData"
      :tree-data="treeData"
      :user-map="userMap"
      @success="saveProCases"
    />
  </div>
</template>

<script>
import { Right as ElIconRight } from '@element-plus/icons-vue'
import {
  createTestPlan,
  editTestPlan,
  relatePlanCases,
} from '@/api/vone/testmanage/case'
import relateCase from '../../../../test/plan-manager/components/relate-case.vue'
import { apiBaseDictPage } from '@/api/vone/base/dict'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import { apiProjectUserNoPage } from '@/api/vone/project'
import { productListByCondition } from '@/api/vone/project/index'

export default {
  components: {
    relateCase,
    ElIconRight,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    planType: {
      type: String,
      default: 'add',
    },
    libraryId: {
      type: String,
      default: 'add',
    },
    // 树菜单数据
    treeData: {
      type: Array,
      default: () => [],
    },
    // 测试计划数据
    planData: {
      type: Object,
      default: () => {},
    },
    // 已关联用例列表
    caseList: {
      type: Array,
      default: () => [],
    },
    // 项目下所有用例
    libCases: {
      type: Array,
      default: () => [],
    },
    // 当前项目信息
    projectInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.taskForm.planEtime) {
            return (
              time.getTime() < Date.now() - 8.64e7 ||
              time.getTime() > new Date(this.taskForm.planEtime).getTime()
            )
          }
          return time.getTime() < Date.now() - 8.64e7
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          return (
            time.getTime() < Date.now() - 8.64e7 ||
            time.getTime() < new Date(this.taskForm.planStime).getTime()
          )
        },
      },
      addCaseVisible: false, // 添加用例弹窗
      saveLoading: false,
      taskForm: {
        addCase: '2', // 手动添加或圈选
        projectCaseList: [], // 已关联用例
        name: '',
        planKey: '',
        type: 'system',
        delay: false,
        leadingBy: '',
        planStime: '',
        planEtime: '',
        description: `<div>1.<br>2.<br>3.<br></div>`,
        planId: '',
        stateId: '0',
        productId: '',
        systemId: '',
        envId: '',
      },
      statusList: [
        {
          stateId: '0',
          label: '未开始',
        },
        {
          stateId: '1',
          label: '进行中',
        },
        {
          stateId: '2',
          label: '已完成',
        },
      ],
      userList: [], // 用户列表
      userMap: {},
      taskRules: {
        name: [
          {
            required: true,
            message: '请输入标题',
          },
          {
            pattern: '^[^ ]+$',
            message: '不能输入空格',
          },
        ],
        planStime: [
          { required: true, message: '请选择计划开始时间', trigger: 'blur' },
        ],
        planEtime: [
          { required: true, message: '请选择计划结束时间', trigger: 'blur' },
        ],
      },
      envList: [], // 环境列表
      productList: [], // 产品列表
      planIdList: [], // 关联迭代列表
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getAllUsers()
        this.getEnvList()
        this.getAllProductList()
        this.getIssueList()
        this.taskForm.productId = this.projectInfo.hostProductId
        if (this.planType === 'edit') {
          this.taskForm = { ...this.taskForm, ...this.planData }
          this.taskForm.addCase = '2'
          this.taskForm.projectCaseList = [...this.caseList]
        }
      }
    },
  },
  methods: {
    eventDisposalRangeChange(value) {
      // const textLength = this.$refs.editor.$el.innerText.replace(/[|]*\n/, '').length
      // if (textLength >= 1000) {
      //   this.$refs.taskFormLeft.validateField(['description'])
      // } else {
      //   this.$refs.taskFormLeft.clearValidate(['description'])
      // }
    },
    // 查询环境列表
    async getEnvList() {
      const params = {
        current: 1,
        size: 9999,
        extra: {},
        model: { type: 'ENVIRONMENT' },
      }
      const res = await apiBaseDictPage(params)
      if (res.isSuccess) {
        this.envList = res.data.records
      }
    },
    // 查询所有产品
    async getAllProductList() {
      const res = await productListByCondition()
      if (res.isSuccess) {
        this.productList = res.data
      }
    },
    // 查询计划关联迭代列表
    async getIssueList() {
      const params = {
        projectId: this.$route.params.id,
      }
      const res = await apiAlmProjectPlanNoPage(params)
      if (res.isSuccess) {
        this.planIdList = res.data
      }
    },
    // 查询所有人员
    async getAllUsers() {
      const params = {
        projectId: this.$route.params.id,
      }
      const res = await apiProjectUserNoPage(params)
      if (res.isSuccess) {
        this.userList = res.data
        this.userMap = res.data.reduce(
          (r, v) =>
            (r[v.id] = {
              name: v.name,
              avatarPath: v.avatarPath || '',
              avatarType: v.avatarType || '',
            }) && r,
          {}
        )
      }
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.taskFormLeft.resetFields()
      this.$refs.taskForm.resetFields()
      this.taskForm = {
        addCase: '2',
        projectCaseList: [],
        name: '',
        planKey: '',
        type: 'system',
        delay: false,
        leadingBy: '',
        planStime: '',
        planEtime: '',
        description: `<div>1.<br>2.<br>3.<br></div>`,
        planId: '',
        stateId: '0',
        productId: '',
        envId: '',
      }
      this.taskForm.projectCaseList = []
    },
    // 选择用例
    checkScope() {
      this.addCaseVisible = true
    },

    // 保存选择用例
    saveProCases({ caseIds, treeIds, execBy }) {
      this.taskForm.projectCaseList = caseIds
    },
    // 保存
    saveInfo() {
      // 默认关联全部用例
      const projectList =
        this.taskForm.addCase === '1'
          ? this.libCases
          : this.taskForm.projectCaseList
      this.planType === 'add' ? this.addSave(projectList) : this.editSave()
    },
    // 新建保存
    async addSave(projectList) {
      try {
        await this.$refs.taskFormLeft.validate()
        await this.$refs.taskForm.validate()

        const { id } = this.$route.params
        const params = {
          libraryId: this.libraryId,
          delay: this.taskForm.delay,
          description: this.taskForm.description,
          planKey: this.taskForm.planKey,
          leadingBy: this.taskForm.leadingBy,
          name: this.taskForm.name,
          planEtime: this.taskForm.planEtime,
          planStime: this.taskForm.planStime,
          projectId: id,
          rateProgress: 0,
          stateId: this.taskForm.stateId,
          type: this.taskForm.type,
          planId: this.taskForm.planId,
          productId: this.taskForm.productId,
          envId: this.taskForm.envId,
          bug: this.taskForm.bug,
        }
        this.saveLoading = true
        const res = await createTestPlan(params)
        if (res.isSuccess) {
          this.$message.success('新建成功')
          this.saveProjectCase(projectList, res.data.id)
        }
      } catch (error) {
        this.saveLoading = false
        return
      }
    },
    // 关联用例
    async saveProjectCase(projectList, id) {
      try {
        const params = {
          createdBy: '1',
          planId: id || this.planData.id,
          testcaseIds: projectList,
        }
        const res = await relatePlanCases(params)
        this.saveLoading = false
        if (res.isSuccess) {
          this.$emit('success')
          this.onClose()
        }
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 编辑保存
    async editSave() {
      try {
        await this.$refs.taskFormLeft.validate()
        await this.$refs.taskForm.validate()

        const params = {
          libraryId: this.libraryId,
          id: this.planData.id,
          delay: this.planData.delay,
          planKey: this.planData.planKey,
          description: this.taskForm.description,
          leadingBy: this.taskForm.leadingBy,
          name: this.taskForm.name,
          planEtime: this.taskForm.planEtime,
          planStime: this.taskForm.planStime,
          rateProgress: this.planData.rateProgress ?? 0,
          stateId: this.taskForm.stateId,
          type: this.taskForm.type,
          planId: this.taskForm.planId,
          productId: this.taskForm.productId,
          envId: this.taskForm.envId,
          bug: this.taskForm.bug,
        }
        this.saveLoading = true
        const res = await editTestPlan(params)
        this.saveLoading = false
        if (res.isSuccess) {
          this.$message.success('修改成功')
          this.$emit('success')
          this.onClose()
        }
      } catch (e) {
        this.saveLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.switchStyle :deep() {
  .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }
  .el-switch__label--left {
    z-index: 9;
    left: 18px;
  }
  .el-switch__label--right {
    z-index: 9;
    left: -5px;
  }
  .el-switch__label.is-active {
    display: block;
  }
  .el-switch__core {
    width: 54px !important;
  }
  .el-switch__label {
    width: 54px !important;
  }
}

:deep() {
  .w-e-text-container {
    height: 240px;
  }
  .w-e-toolbar .w-e-menu {
    width: 25px;
  }
  .el-drawer__body {
    padding: 0px;
  }
}
.ctx {
  height: calc(100vh - 108px);
  > div {
    overflow: auto;
  }
  .leftForm {
    width: 100%;
    padding: 16px;
  }
}
.ctx-right {
  border-left: 1px solid #dcdfe6;
  width: 400px;
  padding: 16px;
  background-color: #fafafa;
}
.box-card {
  border: 1px solid rgb(201, 207, 215);
  span {
    font-weight: bold;
    padding-right: 4px;
  }
  .selectButton {
    padding-left: 20px;
    font-weight: bold;
    color: rgb(0, 102, 255);
    cursor: pointer;
    opacity: 0.9;
    i {
      vertical-align: center;
      font-size: 16px;
      font-weight: bold;
      color: rgb(0, 102, 255);
      cursor: pointer;
      opacity: 0.9;
    }
  }
}
:deep(.el-dialog__body) {
  .el-form-item {
    margin-bottom: 10px;
  }
}
.empty-senior {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  i {
    margin-right: 10px;
  }
}

.custom-theme-dark {
  .box-card {
    border-color: #495266;
  }
  .ctx-right {
    border-left-color: #495266;
    background-color: #252933;
  }
  .footer {
    border-top-color: #495266;
    background-color: #252933;
  }
}
</style>

<style lang="scss" scoped>
.pt-radio {
  .el-radio {
    position: relative;
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    height: 16px;
    cursor: pointer;
    color: rgb(32, 45, 64);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  }
  .el-radio__inner {
    border: 1px solid rgb(201, 207, 215);
  }
  div {
    font-size: 13px;
    line-height: 16px;
    padding-left: 24px;
    color: rgb(145, 153, 163);
    margin: 8px 0px 16px;
  }
}
</style>
