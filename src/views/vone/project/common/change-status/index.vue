<template>
  <!-- 需求状态流转 -->
  <div>
    <el-dropdown
      ref="dropdown"
      trigger="click"
      placement="bottom"
      :disabled="isDone || noPermission || infoDisabled"
      @command="handleCommand"
    >
      <a @click="findNextNode">
        <span
          class="tagCustom"
          :style="{
            border: `1px solid ${
              workitem.stateCode &&
              workitem.echoMap &&
              workitem.echoMap.stateCode
                ? workitem.echoMap.stateCode.color
                : '#ccc'
            }`,
            color: `${
              workitem.stateCode &&
              workitem.echoMap &&
              workitem.echoMap.stateCode
                ? workitem.echoMap.stateCode.color
                : '#ccc'
            }`,
          }"
        >
          <span>
            <span
              v-if="
                workitem.stateCode &&
                workitem.echoMap &&
                workitem.echoMap.stateCode
              "
              >{{ workitem.echoMap.stateCode.name }}</span
            >
            <span v-else>
              {{ workitem.stateCode }}
            </span>
          </span>

          <el-icon><ElIconSetting /></el-icon>
          <el-icon class="iconfont el-icon--right"
            ><ElIconDirectionDown
          /></el-icon>
        </span>
      </a>
      <el-dropdown-menu slot="dropdown" class="change-status">
        <el-dropdown-item
          v-for="item in nextNode"
          :key="item.id"
          :command="item.stateCode"
        >
          <span
            class="tagItem"
            :style="{
              border: `1px solid ${
                item.stateCode && item.echoMap && item.echoMap.stateCode
                  ? item.echoMap.stateCode.color
                  : '#ccc'
              }`,
              color: `${
                item.stateCode && item.echoMap && item.echoMap.stateCode
                  ? item.echoMap.stateCode.color
                  : '#ccc'
              }`,
            }"
          >
            {{ item.name }}
          </span>
        </el-dropdown-item>
        <el-dropdown-item v-if="nextNode.length > 0" command="workFlow" divided>
          <span style="width: 100%; text-align: center">
            <!-- <i class="iconfont el-icon-gongzuoliu" style="color:#3E7BFA;margin-right:4px;" /> -->
            查看工作流
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-dialog
      title="工作流"
      v-model="flowVisible"
      top="7vh"
      append-to-body
      @before-close="closeFlow"
    >
      <vone-work-flow
        ref="vone-g"
        :xml="xml"
        :show-bar="false"
        hide-text-annotation
        single
        :node-style="nodeStyle"
        :properties-props="{ width: 250 }"
      />
      <div slot="footer">
        <el-button type="primary" @click="closeFlow">确认</el-button>
      </div>
    </el-dialog>

    <requiredFields
      v-if="requireParam.visible"
      v-bind="requireParam"
      v-model="requireParam.visible"
      @success="$emit('changeFlow')"
    />
  </div>
</template>

<script>
import {
  Loading as ElIconLoading,
  DirectionDown as ElIconDirectionDown,
} from '@element-plus/icons-vue'
import { apiAlmFindNextNode, apiAlmStateChange } from '@/api/vone/project/issue'
import { jsonToXml } from '@/views/vone/base/project-config/tab/common/xmlUtils'
import { getFlow } from '@/api/vone/base/work-flow'
import requiredFields from '@/components/CustomEdit/components/require-fields.vue'

export default {
  components: {
    requiredFields,
    ElIconLoading,
    ElIconDirectionDown,
  },
  props: {
    workitem: {
      type: Object,
      default: () => {},
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
    noPermission: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nextNode: [],
      onSearch: false,
      isDone: false,
      flowVisible: false,
      requireParam: { visible: false },
      workItemInfo: {},
      xml: null,
      nodeStyle: {
        width: 100,
        height: 36,
      },
      flowId: null,
    }
  },
  watch: {
    workitem: {
      handler: function (val) {
        this.workItemInfo = JSON.parse(JSON.stringify(this.workitem))
      },
      immediate: true,
    },
  },
  mounted() {
    this.isDone = this.workItemInfo && this.workItemInfo.stateCode == 'DONE'
  },
  methods: {
    closeFlow() {
      this.flowVisible = false
    },
    async getFlowInfo() {
      let nodes = []
      let edges = []
      // 查询任务工作流
      const res = await getFlow(this.flowId)
      if (res.isSuccess) {
        if (
          res.data.workflowNodes.length > 0 &&
          res.data.workflowTransitions.length > 0
        ) {
          this.workflowId = res.data.workflowNodes[0].workflowId
          nodes = res.data.workflowNodes
          edges = res.data.workflowTransitions
          nodes.map((item) => {
            item.nodeType = item.nodeType.code
            item.color = item.echoMap?.stateCode?.color
            item.onlyId = item.id
            item.id = item.code
            delete item.shape
            delete item.size
            delete item.echoMap
          })
          edges.map((item) => {
            item.onlyId = item.id
            item.id = item.code
            item.source = item.sourceAnchor
            item.target = item.targetAnchor
          })
          this.xml = jsonToXml({ nodes, edges })
        }
      }
    },
    async handleCommand(val) {
      if (val === 'workFlow') {
        this.flowVisible = true
        this.getFlowInfo()
        return
      }

      if (this.nextNode.find((r) => r.stateCode == val).echoMap.check.length) {
        const check = this.nextNode.find((r) => r.stateCode == val).echoMap
          .check
        const list = []
        check.forEach((element) => {
          list.push(element.echoMap.field)
        })

        const targetName = this.nextNode.find((r) => r.stateCode == val).name
        this.requireParam = {
          visible: true,
          infoData: this.workItemInfo,
          fields: list,
          sourceNode: this.workItemInfo.stateCode,
          sourceName: this.workItemInfo?.echoMap?.stateCode?.name,
          targetName: targetName,
          targetNode: val,
          typeClassfiy: this.workItemInfo?.echoMap?.typeCode?.classify?.code,
          dataId: this.workItemInfo.id || this.workItemInfo.bizId,
          key: Date.now(),
        }
      } else {
        const id = this.workItemInfo.id || this.workItemInfo.bizId

        // 修改状态
        const flowUrlMap = {
          ISSUE: `/api/alm/alm/requirement/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
          BUG: `/api/alm/alm/bug/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
          TASK: `/api/alm/alm/task/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
          RISK: `/api/alm/alm/risk/transitionState/${id}/${
            this.workItemInfo.stateCode
          }/${val}/${this.$route.params.projectKey ? 'project' : 'program'}`,
          IDEA: `/api/alm/alm/idea/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
        }

        const classify = this.workItemInfo?.echoMap?.typeCode?.classify?.code
        const res = await apiAlmStateChange(flowUrlMap[classify])
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('状态流转成功')
        this.nextNode = []
        this.$emit('changeFlow')
      }
    },
    async findNextNode() {
      try {
        if (this.noPermission || this.isDone || this.infoDisabled) {
          return
        }
        this.onSearch = true

        // 因为在迭代列表中需求id的字段名为bizId
        const id = this.workItemInfo.id || this.workItemInfo.bizId
        const urlMap = {
          ISSUE: `/api/alm/alm/requirement/findNextNode/${id}`,
          BUG: `/api/alm/alm/bug/findNextNode/${id}`,
          TASK: `/api/alm/alm/task/findNextNode/${id}`,
          RISK: `/api/alm/alm/risk/findNextNode/${id}/${
            this.$route.params.projectKey ? 'project' : 'program'
          }`,
          IDEA: `/api/alm/alm/idea/findNextNode/${id}`,
        }
        const classify = this.workItemInfo?.echoMap?.typeCode?.classify?.code
        const res = await apiAlmFindNextNode(urlMap[classify])
        this.onSearch = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        if (!res.data.length) {
          this.$refs.dropdown.hide()
          this.$message.warning('暂无当前节点流转权限')
          this.isDone = true
          return
        }

        this.nextNode = res.data

        this.flowId = res.data[0].workflowId
      } catch (error) {
        this.onSearch = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.change-status {
  .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
    margin: 0;
  }
}
:deep(.bpmn) {
  height: calc(100vh - 270px);
}

// 自定义的tag样式，主要用在平台配置，配置项，状态
.tagItem {
  text-align: center;
  border-radius: 3px;
  padding: 4px 10px;
  // min-width: 80px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
