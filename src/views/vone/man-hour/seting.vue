<template>
  <page-wrapper>
    <header class="header">
      工时设置
      <span>
        <el-button
          v-if="$permission('man_hour_seting_editconfig')"
          :disabled="submitDisabled"
          type="primary"
          @click="submitForm"
          >保存</el-button
        >
      </span>
    </header>
    <main class="main">
      <el-form
        ref="setingForm"
        :disabled="!$permission('man_hour_seting_editconfig')"
        label-position="top"
        :model="setingForm"
      >
        <el-form-item
          v-for="(e, index) in setingForm.setingList"
          :key="e.id"
          :label="e.name"
          :prop="'setingList.' + index + '.value'"
          :rules="e.rules"
        >
          <el-input-number
            v-if="e.type === 'NUMBER'"
            v-model="e.value"
            :precision="1"
            :step="0.5"
            :max="24"
            :min="0.5"
            style="width: 260px"
            controls-position="right"
          />

          <el-radio-group v-if="e.key === 'FILLABLE_PERIOD'" v-model="e.value">
            <el-radio label="nolimit">不限</el-radio>
            <el-radio label="week">最近一周</el-radio>
            <el-radio label="month">最近一月</el-radio>
            <el-radio label="quarter">最近一季度</el-radio>
          </el-radio-group>
          <el-radio-group
            v-if="e.key === 'ENABLE_FILL_WEEKEND'"
            v-model="e.value"
          >
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>

          <template v-if="e.key === 'REMIND_OF_EVERY_DAY'">
            <!-- <el-row type="flex"> -->
            <el-time-select
              :start="
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                } &&
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                }.start
              "
              :end="
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                } &&
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                }.end
              "
              :step="
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                } &&
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                }.step
              "
              :min-time="
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                } &&
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                }.minTime
              "
              :max-time="
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                } &&
                {
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                }.maxTime
              "
              v-model="e.value"
              value-format="HH:mm"
              :prefix-icon="ElIconDate"
              placeholder="选择时间"
              style="width: 260px"
            ></el-time-select>

            <!-- </el-row> -->
            <el-checkbox-group v-model="e.config" style="margin-left: 20px">
              <el-checkbox
                v-for="item in checkData"
                :key="item.id"
                :label="item.id"
              >
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </template>

          <el-checkbox-group v-if="e.key === 'BIZ_STATUS'" v-model="e.value">
            <el-checkbox :label="'0'">未开始</el-checkbox>
            <el-checkbox :label="'0-100'">进行中</el-checkbox>
            <el-checkbox :label="'100'">已完成</el-checkbox>
          </el-checkbox-group>

          <el-radio-group v-if="e.key === 'ENABLE_BILLED'" v-model="e.value">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item />
      </el-form>
    </main>
  </page-wrapper>
</template>

<script>
import { Date as ElIconDate } from '@element-plus/icons-vue'
import {
  getWorkingHoursConfig,
  batchSetWorkingHoursConfig,
} from '@/api/vone/manhour/index'
import _ from 'lodash'
export default {
  data() {
    return {
      setingForm: {
        setingList: [],
      },
      reminmdDay: [],
      checkData: [
        {
          id: '1',
          name: '周一',
        },
        {
          id: '2',
          name: '周二',
        },
        {
          id: '3',
          name: '周三',
        },
        {
          id: '4',
          name: '周四',
        },
        {
          id: '5',
          name: '周五',
        },
        {
          id: '6',
          name: '周六',
        },
        {
          id: '7',
          name: '周日',
        },
      ],
      rules: {
        basics: {
          required: true,
          message: '必填项不能为空',
          trigger: ['change', 'blur'],
        },
        time: {
          required: true,
          trigger: ['change', 'blur'],
          validator: this.checkTime,
        },
      },
      submitDisabled: true,
      listbox: [],
      ElIconDate,
    }
  },
  components: {},
  watch: {
    'setingForm.setingList': {
      handler(n, o) {
        if (_.isEqual(JSON.stringify(n), JSON.stringify(this.listbox))) {
          this.submitDisabled = true
        } else {
          this.submitDisabled = false
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.getWorkingHoursConfFn()
  },
  methods: {
    checkTime(rule, value, callback) {
      if (!value) {
        return callback(new Error('请输入时间'))
      }
      if (typeof value !== 'number') {
        callback(new Error('工时为数字'))
      } else if (value <= 0) {
        callback(new Error('工时应大于0'))
      } else if (value > 24) {
        callback(new Error('工时不能大于24'))
      } else {
        callback()
      }
    },
    async getWorkingHoursConfFn() {
      try {
        const res = await getWorkingHoursConfig()
        res.data.forEach((e) => {
          if (e.key == 'MAX_HOURS_OF_EVERY_DAY') {
            e.value = Number(e.value)
            e.rules = this.rules.time
          } else {
            e.rules = this.rules.basics
          }
          if (e.key == 'BIZ_STATUS') {
            e.value = e.value.split(',') || []
          }
          if (e.key == 'REMIND_OF_EVERY_DAY') {
            e.config = e.config ? JSON.parse(e.config).day : []
          }
        })
        this.setingForm.setingList = res.data
        this.listbox = _.cloneDeep(res.data)
      } catch (e) {
        return
      }
    },
    async submitForm() {
      this.setingForm.setingList.forEach((element) => {
        if (element.key == 'BIZ_STATUS') {
          this.$set(element, 'value', element.value.join(','))
        }
        if (element.key == 'REMIND_OF_EVERY_DAY') {
          this.$set(element, 'config', JSON.stringify({ day: element.config }))
        }
      })

      try {
        await this.$refs.setingForm.validate()
        const res = await batchSetWorkingHoursConfig(this.setingForm.setingList)
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('修改成功')
        this.getWorkingHoursConfFn()
      } catch (e) {
        return
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  height: 56px;
  margin: -16px -16px 0;
  padding: 0 16px;
  line-height: 56px;
  color: var(--main-font-color);
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #f2f3f5;
  display: flex;
  justify-content: space-between;
}
.main {
  padding: 16px;
}
.form-item--width {
  width: 200px;
}
:deep(.el-checkbox-group) {
  margin-left: 0 !important;
}

:deep(.el-form .el-form-item) {
  margin-bottom: 32px;
}
</style>
