<template>
  <main class="story-map">
    <vone-card v-for="row in tableData" :key="row.id" class="story-card">
      <header slot="title" class="story-card__header">
        <el-icon
          class="iconfont"
          style="color: rgb(100, 190, 250); font-size: 20px"
          ><el-icon-icon-yonghugushi
        /></el-icon>
        <span class="title text-over" :title="row.name">{{ row.name }}</span>
      </header>
      <el-row type="flex" class="item" align="middle">
        <span class="collapse-time size12">{{ row.code }}</span>
        <i
          :class="`iconfont ${row.echoMap.priorityCode.icon}`"
          :style="{ color: `${row.echoMap.priorityCode.color}` }"
          style="font-size: 14px"
        />
      </el-row>
      <el-row class="item">
        <span class="collapse-time size12">{{
          row.echoMap.productModuleFunctionId
            ? row.echoMap.productModuleFunctionId.name
            : '未定义'
        }}</span>
      </el-row>
      <el-row class="item">
        <span class="collapse-time size12">{{
          row.echoMap.projectId.name
        }}</span>
      </el-row>
      <footer class="story-card__footer">
        <vone-user-avatar
          :avatar-path="row.echoMap.leadingBy.avatarPath"
          :name="row.echoMap.leadingBy.name"
          width="20px"
          height="20px"
        />
        <span
          :style="{
            color: `${row.echoMap.stateCode.color}`,
            border: `1px solid ${row.echoMap.stateCode.color}`,
          }"
          class="statusItem"
          >{{ row.echoMap.stateCode.name }}</span
        >
      </footer>
    </vone-card>
    <vone-empty v-if="tableData.length == 0" />
  </main>
</template>

<script>
import { IconYonghugushi as ElIconIconYonghugushi } from '@element-plus/icons-vue'
export default {
  components: {
    ElIconIconYonghugushi,
  },
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.story-map {
  // display: grid;
  // grid-template-columns: repeat(6, minmax(204px, 1fr));
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
  padding: 12px 0;
}
.story-card {
  // height: 152px;
  min-width: 204px;
  width: calc((100% - 80px) / 6);
  border-radius: 4px;
  border-color: #f2f3f5;
  box-shadow: none;
  &__header {
    display: flex;
    align-items: center;
    gap: 0 9px;

    .title {
      font-size: 14px;
      line-height: 22px;
      font-weight: 400;
      color: var(--main-font-color);
    }
  }
  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 20px;
    .statusItem {
      height: 18px;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 500;
      border-radius: 2px;
      padding: 0 6px;
    }
  }
}
:deep(.vone-card) {
  border: 1px solid #eaecf0;
}
:deep(.vone-card .el-card__body) {
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  gap: 8px 0;
}

.item {
  height: 18px;
}
.collapse-time {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  padding: 0 4px;
  margin: 0 2px;
  color: #838a99;
  border-radius: 2px;
  background: #f2f3f5;
}
.size12 {
  font-size: 12px;
}
</style>
