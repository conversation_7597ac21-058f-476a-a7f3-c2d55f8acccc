<template>
  <main>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="releaseVersion"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['releaseVersion']"
          @getTableData="getInitTableData"
        />
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          :icon="el-icon-setting"
          @click="releaseFn"
          >新增</el-button
        >
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: $tableHeight }">
      <vxe-table
        ref="releaseVersion"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="发布时间" field="releaseTime" min-width="150">
          <template slot-scope="scope">
            <span>
              {{ scope.row.releaseTime.slice(0, 11) }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="发布人" field="releaseUser" min-width="150">
          <template slot-scope="scope">
            <vone-user-avatar
              :avatar-path="
                getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''
              "
              :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''"
            />
          </template>
        </vxe-column>
        <vxe-column title="发布环境" field="envKey" min-width="150" />
        <vxe-column title="流水线" field="ismain" min-width="150">
          <template slot-scope="scope">
            <span
              v-for="(item, index) in scope.row.echoMap.pipelineList"
              :key="item.id"
              >{{ item.name
              }}{{
                index === scope.row.echoMap.pipelineList.length - 1
                  ? ' '
                  : ' 、'
              }}</span
            >
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <release-dialog v-if="visible" v-model="visible" @success="getsuccess" />
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />
  </main>
</template>

<script>
import ReleaseDialog from '../release-dialog.vue'
import { recordList, productReleaseDel } from '@/api/vone/product/workitem'
import setDataMixin from '@/mixin/set-data'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
export default {
  components: {
    ReleaseDialog,
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'releaseuser',
          name: '发布人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择发布人',
        },
        {
          key: 'envkey',
          name: '发布环境',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择发布环境',
        },
        {
          key: 'date',
          name: '发布时间',
          type: {
            code: 'DATE',
          },
          placeholder: '请选择发布时间',
        },
      ],
      pageLoading: false,
      formData: {
        date: [],
      },
      visible: false,
      tableData: { records: [] },
      tableOptions: {
        isOperation: true, // 表格有操作列时设置
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              disabled: !this.$permission('product_version_del'),
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称 product_version_del
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.deleteById, // 操作事件
            },
          ],
        },
      },
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.releaseUser && row?.echoMap?.releaseUser
      }
    },
  },
  mounted() {
    this.getEnvList()
  },
  methods: {
    getsuccess() {
      this.getInitTableData()
    },
    deleteById(row) {
      this.$confirm(`确定删除该条发布记录吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
      })
        .then(async () => {
          const res = await productReleaseDel([row.id])
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.getInitTableData()
          this.$message.success('删除成功')
        })
        .catch(() => {})
    },
    releaseFn() {
      this.visible = true
    },
    async getInitTableData() {
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }

      this.$set(this.formData, 'productId', this.$route.params.productId)
      this.$set(this.formData, 'productVersionId', this.$route.params.versionId)

      const params = {
        ...pageObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      const res = await recordList(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    async getEnvList() {
      const res = await apiBaseDictNoPage({ type: 'ENVIRONMENT' })
      if (!res.isSuccess) return
      this.setData(this.defaultFileds, 'envkey', res.data)
    },
  },
}
</script>

<style lang="scss" scoped>
.tag {
  padding: 2px 6px;
  border-radius: 2px;
  background: #f0f0f0;
}
</style>
