<template>
  <page-wrapper>
    <div class="title">版本发布路线（示意图）</div>
    <el-timeline style="width: 30%">
      <el-timeline-item timestamp="2018/4/12" placement="top">
        <el-card>
          <div class="card-title textMainColor">
            <div class="">V1.1</div>
            <div class="card-title-r subTitleColor">
              共计
              <span class="textMainColor">123</span>
              条发布内容
            </div>
          </div>
          <div class="content-item">
            <span class="lable">状态</span>
            <span class="content"
              ><el-icon class="color-success"><ElIconSetting /></el-icon
            ></span>
          </div>
          <div class="content-item">
            <span class="lable">发布时间</span>
            <span class="content">2021-10-30</span>
          </div>
          <div class="content-item">
            <span class="lable">环境信息</span>
            <span class="content"
              ><el-tag size="mini" type="success">PRD</el-tag></span
            >
          </div>
          <div class="content-item">
            <span class="lable">流水线</span>
            <span class="content">PRD</span>
          </div>
          <div class="card-title">
            <div class="content-item">
              <span class="lable">工作项</span>
            </div>
            <div>
              <el-icon><ElIconSetting /></el-icon>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
      <el-timeline-item
        :icon="ElIconSuccess"
        color="#3CB540"
        size="large"
        timestamp="2018/4/3"
        placement="top"
      >
        <el-card>
          <div class="card-title textMainColor">
            <div class="">V1.2</div>
            <div class="card-title-r subTitleColor">
              共计
              <span class="textMainColor">123</span>
              条发布内容
            </div>
          </div>
          <div class="content-item">
            <span class="lable">状态</span>
            <span class="content"
              ><el-icon class="color-success"><ElIconSetting /></el-icon
            ></span>
          </div>
          <div class="content-item">
            <span class="lable">发布时间</span>
            <span class="content">2021-10-30</span>
          </div>
          <div class="content-item">
            <span class="lable">环境信息</span>
            <span class="content"
              ><el-tag size="mini" type="success">PRD</el-tag></span
            >
          </div>
          <div class="content-item">
            <span class="lable">流水线</span>
            <span class="content">PRD</span>
          </div>
          <div class="card-title">
            <div class="content-item">
              <span class="lable">工作项</span>
            </div>
            <div>
              <el-icon><ElIconSetting /></el-icon>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
      <el-timeline-item
        :icon="ElIconSuccess"
        color="#3CB540"
        size="large"
        timestamp="2018/4/2"
        placement="top"
      >
        <el-card>
          <div class="card-title textMainColor">
            <div class="">V1.3</div>
            <div class="card-title-r subTitleColor">
              共计
              <span class="textMainColor">123</span>
              条发布内容
            </div>
          </div>
          <div class="content-item">
            <span class="lable">状态</span>
            <span class="content"
              ><el-icon class="color-success"><ElIconSetting /></el-icon
            ></span>
          </div>
          <div class="content-item">
            <span class="lable">发布时间</span>
            <span class="content">2021-10-30</span>
          </div>
          <div class="content-item">
            <span class="lable">环境信息</span>
            <span class="content"
              ><el-tag size="mini" type="success">PRD</el-tag></span
            >
          </div>
          <div class="content-item">
            <span class="lable">流水线</span>
            <span class="content">PRD</span>
          </div>
          <div class="card-title">
            <div class="content-item">
              <span class="lable">工作项</span>
            </div>
            <div>
              <el-icon><ElIconSetting /></el-icon>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
      <el-timeline-item
        :icon="ElIconWarning"
        color="#FA6B57"
        size="large"
        timestamp="2018/4/2"
        placement="top"
      >
        <el-card>
          <div class="card-title textMainColor">
            <div class="">V1.4</div>
            <div class="card-title-r subTitleColor">
              共计
              <span class="textMainColor">123</span>
              条发布内容
            </div>
          </div>
          <div class="content-item">
            <span class="lable">状态</span>
            <span class="content"
              ><el-icon class="color-success"><ElIconSetting /></el-icon
            ></span>
          </div>
          <div class="content-item">
            <span class="lable">发布时间</span>
            <span class="content">2021-10-30</span>
          </div>
          <div class="content-item">
            <span class="lable">环境信息</span>
            <span class="content"
              ><el-tag size="mini" type="success">PRD</el-tag></span
            >
          </div>
          <div class="content-item">
            <span class="lable">流水线</span>
            <span class="content">PRD</span>
          </div>
          <div class="card-title">
            <div class="content-item">
              <span class="lable">工作项</span>
            </div>
            <div>
              <el-icon><ElIconSetting /></el-icon>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </page-wrapper>
</template>

<script>
import {
  Success as ElIconSuccess,
  ArrowRight as ElIconArrowRight,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'
export default {
  data() {
    return {
      tableLoading: false,
      ElIconSuccess,
      ElIconWarning,
    }
  },
  components: {
    ElIconSuccess,
    ElIconArrowRight,
  },
  mounted() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.title {
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 16px;
}
.color {
  color: red;
}
.el-timeline :deep(.el-card__body) {
  padding: 14px 16px;
}
.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .card-title-r {
    line-height: 26px;
    background: #f5f6fa;
    padding: 0 12px;
    border-radius: 2px;
    font-size: 12px;
  }
}
.content-item {
  line-height: 22px;
  .lable {
    color: #8a8f99;
    margin-right: 12px;
  }
}
.color-success {
  color: #3cb540;
}
.work-item {
}
</style>
