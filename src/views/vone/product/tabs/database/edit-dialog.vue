<template>
  <el-dialog
    class="dialogContainer"
    :title="title"
    :model-value="visible"
    width="45%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form
      ref="databaseForm"
      style="min-height: 350px"
      :model="databaseForm"
      label-position="top"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="12">
        <el-col :span="24">
          <el-form-item label="文件名称" prop="name">
            <el-input v-model="databaseForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="文件类型" prop="type">
            <el-select
              v-model="databaseForm.type"
              filterable
              placeholder="请选择文件类型"
            >
              <el-option
                v-for="item in databaseTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="databaseForm.description"
              show-word-limit
              maxlength="1000"
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件" prop="files">
            <span slot="label">
              附件
              <el-tooltip
                class="item"
                effect="dark"
                content="文件最大200MB"
                placement="top-start"
              >
                <el-icon class="iconfont"
                  ><el-icon-tips-question-circle
                /></el-icon>
              </el-tooltip>
            </span>
            <vone-upload
              ref="uploadFile"
              :file-size="200 * 1024 * 1024"
              delete-type="locality"
              :limit="1"
              :multiple="false"
              :files-data="databaseForm.files"
              @onSuccess="fileChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)"
        >取消</el-button
      >
      <el-button type="primary" size="small" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { TipsQuestionCircle as ElIconTipsQuestionCircle } from '@element-plus/icons-vue'
import {
  operationDocument,
  getProductDocumentById,
} from '@/api/vone/product/database'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
export default {
  components: {
    ElIconTipsQuestionCircle,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入文件名称', trigger: 'change' },
        ],
        type: [{ required: true, message: '请选类型', trigger: 'change' }],
        files: [{ required: true, message: '请上传附件', trigger: 'change' }],
      },
      databaseForm: {
        name: '',
        fileId: '',
        files: [],
        type: '',
        description: '',
      },
      databaseTypeList: [],
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.id) {
          this.getDatebaseDetail()
        }
      }
    },
  },
  mounted() {
    this.getDatabaseType()
  },
  methods: {
    async getDatebaseDetail() {
      const res = await getProductDocumentById(this.id)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      this.databaseForm = {
        id: res.data.id,
        name: res.data.name,
        fileId: res.data.fileId,
        files: res.data.echoMap?.fileId ? [res.data.echoMap?.fileId] : [],
        type: res.data.type,
        description: res.data.description,
      }
    },
    fileChange(e) {
      if (e.length > 0) {
        if (!this.databaseForm.name)
          this.$set(this.databaseForm, 'name', e[0].name)
        this.$refs.databaseForm.clearValidate('files')
      }
    },
    getDatabaseType() {
      apiBaseDictNoPage({ type: 'PRODUCT_DOCUMENTS_TYPE' }).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.databaseTypeList = res.data
      })
    },
    changeDate(v) {
      if (this.databaseForm.planStime < v) {
        this.$refs['databaseForm'].clearValidate('planStime')
      }
    },
    // 保存
    save() {
      const file = this.$refs['uploadFile'].uploadFiles
      this.$set(this.databaseForm, 'files', file)
      if (file.length > 0) {
        this.databaseForm.fileId = file[0]?.id
      }
      this.$refs.databaseForm.validate((valid) => {
        if (valid) {
          const param = this.databaseForm
          param.productId = this.$route.params.productId
          if (this.id) {
            // 编辑保存
            operationDocument(param, 'put').then((res) => {
              if (res.isSuccess) {
                this.$message.success('修改成功')
                this.$refs.databaseForm.resetFields()
                this.close()
                this.$emit('success')
              } else {
                this.$message.warning(res.msg)
              }
            })
          } else {
            // 新增保存
            operationDocument(param, 'post').then((res) => {
              if (res.isSuccess) {
                this.$message.success('新增成功')
                this.$refs.databaseForm.resetFields()
                this.close()
                this.$emit('success')
              } else {
                this.$message.warning(res.msg)
              }
            })
          }
        }
      })
    },
    close() {
      this.$refs.databaseForm.resetFields()
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px !important;
}
</style>
