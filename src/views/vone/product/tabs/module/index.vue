<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          table-search-key="productModuleTable"
          :card-options="otherTypeOptions"
          :model="formData"
          show-card
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['productModuleTable']"
          @getTableData="getInitTableData"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between">
          <!-- <span class="ml-16">
              <el-dropdown trigger="click" style="margin-top:2px" @command="(e) => e && e()">
                <el-button>操作<i class="iconfont el-icon-direction-down el-icon--right" /></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
                    {{ item.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span> -->
          <div>
            <el-button-group class="ml-16">
              <el-button
                :icon="el-icon-setting"
                type="primary"
                :disabled="!$permission('product_module_add')"
                @click.stop="addModule"
                >新增</el-button
              >
            </el-button-group>
          </div>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>

    <div v-if="extraData.cardView == 'sunChart'">
      <moduleChart
        :key="extraData.cardView"
        :tab-active="extraData.cardView"
        :data="tableData.records"
        :height="'calc(100vh - 238px)'"
      />
    </div>
    <div v-else-if="extraData.cardView == 'treeChart'">
      <treeTable
        :key="extraData.cardView"
        :tab-active="extraData.cardView"
        :data="tableData.records"
        :extra-data="extraData"
      />
    </div>

    <div v-else :style="{ height: tableHeight }">
      <vxe-table
        ref="productModuleTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        :tree-config="{
          transform: false,
          rowField: 'id',
          children: 'children',
        }"
      >
        <template>
          <vxe-column title="名称" field="name" min-width="150" tree-node>
            <template slot-scope="scope">
              <span class="name-main">
                {{ scope.row.name }}
                <span class="name-operation operation-icon">
                  <el-button
                    :disabled="!$permission('product_module_add')"
                    style="font-size: 18px"
                    size="mini"
                    type="text"
                    :icon="el-icon-setting"
                    @click="addModule(scope.row)"
                  />
                </span>
              </span>
            </template>
          </vxe-column>
          <vxe-column title="类型" field="nodeType" width="120">
            <template slot-scope="scope">
              {{
                scope.row.nodeType == 1
                  ? '模块'
                  : scope.row.nodeType == 2
                  ? '功能'
                  : '——'
              }}
            </template>
          </vxe-column>
          <vxe-column title="产品经理" field="productManager" width="120">
            <template slot-scope="scope">
              <vone-user-avatar
                :avatar-path="
                  getUserInfo(scope.row)
                    ? getUserInfo(scope.row).avatarPath
                    : ''
                "
                :name="
                  getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                "
              />
            </template>
          </vxe-column>
        </template>
        <vxe-column title="研发经理" field="developmentManager" width="120">
          <template slot-scope="scope">
            <vone-user-avatar
              :avatar-path="
                getDevelopUserInfo(scope.row)
                  ? getDevelopUserInfo(scope.row).avatarPath
                  : ''
              "
              :name="
                getDevelopUserInfo(scope.row)
                  ? getDevelopUserInfo(scope.row).name
                  : ''
              "
            />
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.parentId == '0' || !$permission('product_module_edit')
                  "
                  :icon=""el-icon-edit" icon_click"
                  @click="editModule(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.parentId == '0' || !$permission('product_module_del')
                  "
                  :icon=""el-icon-delete" icon_click"
                  @click="delModule(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <!-- 导入数据 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model="importParam.visible"
      @success="getInitTableData"
    />
    <edit-dialog
      v-if="visible"
      :title="title"
      v-model="visible"
      :row-data="rowData"
      :table-data="tableData.records"
      @success="getInitTableData"
    />
  </page-wrapper>
</template>

<script>
import EditDialog from './edit-dialog.vue'
import moduleChart from './module-chart.vue'
import treeTable from './treeTable.vue'
import { download } from '@/utils'
import { dataExport } from '@/api/vone/base/file'
import { getModule, getModuleList, operationModule } from '@/api/vone/product'
export default {
  components: {
    EditDialog,
    moduleChart,
    treeTable,
  },
  data() {
    return {
      extraData: {
        cardView: 'table',
      },
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
        {
          key: 'productManager',
          name: '产品经理',
          type: {
            code: 'USER',
          },
          placeholder: '请选择产品经理',
          multiple: true,
        },
        {
          key: 'developmentManager',
          name: '研发经理',
          type: {
            code: 'USER',
          },
          placeholder: '请选择研发经理',
          multiple: true,
        },
      ],
      tableData: {
        records: [],
      },
      noTreeData: [],
      visible: false,
      rowData: {},
      title: '',
      // tabActive: 'table',
      otherTypeOptions: [
        {
          name: '表格',
          value: 'table',
          icon: 'el-icon-application-view-sheet',
        },
        {
          name: '矩形树图',
          value: 'treeChart',
          icon: 'el-icon-icon-fill-juxing1',
        },
        {
          name: '旭日图',
          value: 'sunChart',
          icon: 'el-icon-application-view-level',
        },
      ],
      pageLoading: false,
      formData: {},
      tableOptions: {
        hideColumns: [], // 默认隐藏列
        isSelection: true, // 表格有多选时设置
        isOperation: true, // 表格有操作列时设置
        isIndex: false, // 列表序号

        operation: {
          isFixed: true, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.editModule, // 操作事件
              disabled: this.editDisabled,
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.delModule, // 操作事件
              disabled: this.delDisabled,
            },
          ],
          moreData: [],
        },
      },
      actions: [
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('source_data_Import'),
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          disabled: !this.$permission('source_data_export'),
          fn: this.exportFlie,
        },
      ],
      // hideTable: true,
      parameter: {},
      importParam: { visible: false },
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.productManager && row?.echoMap?.productManager
      }
    },
    getDevelopUserInfo() {
      return function (row) {
        return row?.developmentManager && row?.echoMap?.developmentManager
      }
    },
    tableHeight() {
      const height = this.extraData?.height - 30 + 'px' || '0px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },

  mounted() {
    this.getModuleList()
  },
  methods: {
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '功能模块',
        url: `/api/product/product/moduleFunction/excel/downloadImportTemplate`,
        importUrl: `/api/product/product/moduleFunction/excel/import`,
        data: { productId: this.$route.params.productId },
      }
    },
    // 导出
    async exportFlie() {
      try {
        this.pageLoading = true

        download(
          `功能模块.xls`,
          await dataExport(`/api/product/product/moduleFunction/excel/export`, {
            productId: this.$route.params.productId,
          })
        )

        this.pageLoading = false
      } catch (e) {
        this.pageLoading = false
        return
      }
    },

    editDisabled(e, t) {
      if (t.parentId == '0' || !this.$permission('product_module_edit')) {
        return true
      } else {
        return false
      }
    },
    delDisabled(e, t) {
      if (t.parentId == '0' || !this.$permission('product_module_del')) {
        return true
      } else {
        return false
      }
    },
    tableRowClassName({ row, rowIndex }) {
      return 'rowstyle'
    },
    getInitTableData() {
      this.$set(this.formData, 'productId', this.$route.params.productId)
      this.parameter = {
        model: {
          ...this.formData,
        },
        extra: {
          ...this.extraData,
        },
      }
      getModule(this.parameter).then((res) => {
        if (res.isSuccess) {
          this.tableData.records = res.data
          this.getModuleList()
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getModuleList() {
      getModuleList({ productId: this.$route.params.productId }).then((res) => {
        if (res.isSuccess) {
          this.noTreeData = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    addModule(row) {
      this.title = '新增'
      if (row) {
        this.rowData = {
          parentId: row.id,
          parentName: row.name,
        }
      } else {
        const item = this.tableData.records[0]
        this.rowData = {
          parentId: item.id,
          parentName: item.name,
        }
      }
      this.visible = true
    },
    editModule(row) {
      this.title = '编辑'
      const parent = this.noTreeData.find((item) => item.id == row.parentId)
      this.rowData = {
        ...row,
        parentName: parent ? parent.name : '',
      }
      this.visible = true
    },
    async delModule(row) {
      await this.$confirm(`确定删除【${row.name}】吗？`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false,
      }).then(async (res) => {
        const { isSuccess, msg } = await operationModule([row.id], 'delete')
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success('删除成功')
        this.getInitTableData()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
.rowstyle {
  &:hover {
    .name-operation {
      display: block;
    }
  }
  .name-operation {
    display: none;
    float: right;
  }
}
</style>
