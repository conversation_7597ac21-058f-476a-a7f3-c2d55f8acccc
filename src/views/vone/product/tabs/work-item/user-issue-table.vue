<template>
  <div>
    <div style="height: calc(100vh - 184rem)">
      <vxe-table
        ref="workUserTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          show-overflow-tooltip
          field="name"
          title="标题"
          min-width="400"
          class-name="name_col"
          fixed="left"
        >
          <template #default="{ row }">
            <div class="name_icon">
              <a class="table_title" @click="showInfo(row)">
                <span
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                >
                  <i
                    :class="`iconfont ${row.echoMap.typeCode.icon}`"
                    :style="{
                      color: `${
                        row.echoMap.typeCode
                          ? row.echoMap.typeCode.color
                          : '#ccc'
                      }`,
                    }"
                  />
                </span>

                {{ row.code + ' ' + row.name }}
              </a>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="是否主办产品">
          <template slot-scope="scope">
            <span :class="{ tag: true, main: scope.row.echoMap.isHost }">{{
              scope.row.echoMap.isHost ? '主办' : '辅办'
            }}</span>
          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="120" sortable>
          <template #default="{ row, rowIndex }">
            <ideaStatus
              v-if="row"
              :key="Date.now()"
              :workitem="row"
              :no-permission="!$permission('reqm_center_idea_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column
          field="sourceCode"
          title="来源"
          show-overflow-tooltip
          width="100"
        >
          <template #default="{ row }">
            <span
              v-if="row.sourceCode && row.sourceCode && row.echoMap.sourceCode"
            >
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column field="priorityCode" title="优先级" width="120" sortable>
          <template #default="{ row }">
            <vone-icon-select
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('reqm_center_idea_priority')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>

        <vxe-column field="handleBy" title="处理人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.handleBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'handleBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.leadingBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'leadingBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.putBy"
                class="remoteuser"
                :default-data="[row.echoMap.putBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'putBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="提出时间" width="120" sortable>
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>

        <vxe-column
          field="expectedTime"
          title="期望完成时间"
          width="135"
          sortable
        >
          <template #default="{ row }">
            <span v-if="row.expectedTime">
              {{ dayjs(row.expectedTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.expectedTime }}</span>
          </template>
        </vxe-column>
        <vxe-column field="tag" title="标签" width="150">
          <template #default="{ row }">
            <span v-for="(item, index) in row.tag" :key="index">
              <el-tag style="margin-right: 6px" type="success">
                {{ item }}
              </el-tag>
            </span>
          </template>
        </vxe-column>

        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm_center_idea_edit')"
                  :icon="el-icon-setting"
                  @click="editIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm_center_idea_del')"
                  :icon="el-icon-setting"
                  @click="deleteIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown
                trigger="click"
                :hide-on-click="false"
                @command="(e) => e && e()"
              >
                <el-button
                  type="text"
                  :icon=""el-icon-more" icon_click"
                />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="item in moreOpear"
                    :key="item.name"
                    :command="() => item.handler(row)"
                    :disabled="item.disabled"
                  >
                    <i :class="item.icon" />
                    <span>{{ item.label }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />

    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      v-model="editAllParam.visible"
      :type-code="'IDEA'"
      @success="getInitTableData"
    />

    <!-- 新增 -->
    <vone-custom-add
      v-if="issueParam.addvisible"
      v-model="issueParam.addvisible"
      v-bind="issueParam"
      :type-code="'IDEA'"
      :title="'新增用户需求'"
      @success="getInitTableData"
    />
    <!-- 编辑 -->
    <vone-custom-edit
      v-if="issueParam.editvisible"
      v-model="issueParam.editvisible"
      v-bind="issueParam"
      :type-code="'IDEA'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 详情 -->
    <vone-custom-info
      v-if="issueInfoParam.editvisible"
      v-model="issueInfoParam.editvisible"
      v-bind="issueInfoParam"
      :type-code="'IDEA'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 导入 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model="importParam.visible"
      @success="getInitTableData"
    />
  </div>
</template>

<script>
import { download } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'
import { productWork } from '@/api/vone/product/workitem'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiAlmIdeaDel } from '@/api/vone/reqmcenter/idea'
import editAll from '@/views/vone/project/common/edit-all'

import ideaStatus from '@/views/vone/project/common/change-status/index.vue'
import { catchErr } from '@/utils'
import { apiAlmIdeaInfo } from '@/api/vone/reqmcenter/idea'
import { editById } from '@/api/vone/project/index'

export default {
  components: {
    editAll,
    ideaStatus,
  },
  props: {
    activeTab: {
      type: String,
      default: '',
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      // formData: {},
      issueParam: {
        visible: false,
      },
      createSimple: false,
      pageLoading: false,
      tableData: { records: [] },
      prioritList: [],
      actions: [
        {
          disabled: !this.$permission('reqm_center_idea_del'),
          name: '批量删除',

          fn: () => {
            this.deleteTableSelect()
          },
        },
        {
          disabled: !this.$permission('reqm_center_idea_edit'),
          name: '批量编辑',

          fn: this.editAll,
        },
        {
          disabled: !this.$permission('reqm_center_idea_Import'),
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
        },
        {
          disabled: !this.$permission('reqm_center_idea_export'),
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
        },
      ],
      editAllParam: { visible: false }, // 批量编辑
      issueInfoParam: {
        // 详情
        visible: false,
      },
      importParam: { visible: false }, // 用户导入
      exportLoading: false,
      tableList: [], // 用于编辑时切换上一个下一个
      moreOpear: [
        {
          type: 'icon', // 为icon则是图标
          label: '复制标题', // 功能名称
          icon: 'iconfont el-icon-application-copy', // icon class
          handler: this.copy, // 操作事件
        },
        {
          disabled: !this.$permission('reqm_center_trackingview'),
          type: 'icon',
          label: '跟踪视图',
          icon: 'iconfont el-icon-application-topology',
          handler: this.getTracking,
        },
      ],
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment',
        },
        {
          label: '活动',
          name: 'active',
        },
      ],
      leftTabs: [
        // {
        //   label: '用户需求',
        //   name: 'IdeaToIdea',
        //   active: false
        // },
        {
          label: '需求',
          name: 'IdeaToIssue',
          active: false,
        },
      ],
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.putBy && row?.echoMap?.putBy
      }
    },
  },
  watch: {
    activeTab: {
      handler: function (val) {
        if (!val) return
        this.getInitTableData()
      },
      // deep: true,
      immediate: true,
    },
  },
  mounted() {
    // this.getInitTableData()
    this.getPrioritList() // 优先级-
  },
  methods: {
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },

    async getInitTableData() {
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      this.$set(this.formData, 'productId', [this.$route.params.productId])
      this.$set(this.formData, 'typeCode', ['IDEA'])
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      }
      const res = await productWork(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (!res.data) {
        return
      }
      res.data.records.forEach((element) => {
        element.tag =
          element.echoMap && element.echoMap.tags
            ? element.echoMap.tags.map((r) => r.name)
            : []
      })

      this.tableData = res.data || {}
      this.tableList = res.data.records // 用于编辑时切换上一个下一个
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '用户需求',
        url: '/api/alm/alm/idea/downloadImportTemplate',
        importUrl: '/api/alm/alm/idea/importProgram',
      }
    },

    // 导出
    async exportFlie() {
      if (this.total > 5000) {
        this.$message.warning(
          '当前导出数据量过多，最大导出条目最多支持5000条，请先选择筛选条件'
        )
        return
      }

      this.exportLoading = true
      download(
        `意向信息.xls`,
        await apiBaseFileLoad('/api/alm/alm/idea/export', this.formData)
      )

      this.exportLoading = false
    },
    // 批量删除
    async deleteTableSelect() {
      this.selectData = this.getVxeTableSelectData('workUserTable')
      if (this.selectData.length > 0) {
        await this.$confirm('确定删除该信息吗?', '删除', {
          type: 'warning',
          customClass: 'delConfirm',
        }).then(async () => {
          const deleteArr = []
          this.selectData.map((item) => deleteArr.push(item.id))
          const res = await apiAlmIdeaDel(deleteArr)
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getInitTableData()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },
    // 批量编辑
    editAll() {
      this.selectData = this.getVxeTableSelectData('workUserTable')
      if (!this.selectData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selectData }
    },
    newIssue() {
      this.issueParam = {
        addvisible: true,
        title: '新增用户需求',
        infoDisabled: false,
      }
    },
    editIssue(row) {
      this.issueParam = {
        editvisible: true,
        title: '编辑用户需求',
        id: row.id,
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      }
    },
    showInfo(row) {
      // 判断有没有编辑权限，有的话展示编辑对话框，没有则展示详情对话框

      this.issueInfoParam = {
        editvisible: true,
        title: '用户需求详情',
        id: row.id,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      }
    },
    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false,
      })

      const { isSuccess, msg } = await apiAlmIdeaDel(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 复制标题到剪贴板
    copy(row) {
      const _this = this
      this.$copyText(`${row.code} ${row.name}`).then(
        function (e) {
          _this.$message.success('复制成功')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    getTracking(row) {
      this.$router.push({
        path: '/reqmcenter/idea/trackingView/' + row.id,
      })
    },
    // 更新当前表格状态
    async editRowStatus(row, index) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIdeaInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(
          res.data,
          'tag',
          res.data.echoMap.tagId
            ? res.data.echoMap.tagId.map((r) => r.name)
            : []
        )
      }

      this.tableData.records.splice(index, 1, res.data)
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id,
      }
      params[t] = e
      const res = await editById('idea', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
  },
}
</script>

<style lang="scss" scoped>
.tag {
  padding: 2px 6px;
  border-radius: 2px;
  background: #f0f0f0;
}
.main {
  background: #ffe0ed;
  border-radius: 2px;
  color: #f53ba1;
}
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
.userList :deep(.el-input__inner) {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
