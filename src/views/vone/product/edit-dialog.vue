<template>
  <el-dialog
    class="dialogContainer"
    :title="title"
    :model-value="visible"
    width="62%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form
      ref="productForm"
      :model="productForm"
      label-position="right"
      :rules="rules"
      label-width="90px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="产品名称" prop="name">
            <el-input v-model="productForm.name" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="产品类型" prop="typeId">
            <el-select
              v-model="productForm.typeId"
              placeholder="请选择产品类型"
            >
              <el-option
                v-for="item in productTypes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关联产品" prop="product">
            <el-select
              v-model="productForm.product"
              collapse-tags
              placeholder="请选择产品"
              multiple
              filterable
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属机构">
            <vone-tree-select
              v-model="productForm.orgId"
              search-nested
              :tree-data="orgData"
              placeholder="请选择机构"
            />
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="productForm.description"
              :rows="5"
              show-word-limit
              type="textarea"
              maxlength="1000"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标识" prop="code">
            <el-input
              v-model="productForm.code"
              placeholder="请输入标识"
              :disabled="id ? true : false"
            />
          </el-form-item>
          <el-form-item label="所属负责人" prop="leadingBy">
            <vone-remote-user v-model="productForm.leadingBy" />
          </el-form-item>
          <!-- <el-form-item label="关联产品集" prop="productset">
              <el-select v-model="productForm.productset" placeholder="请选择产品集" multiple filterable>
                <el-option v-for="item in productsetList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item> -->
          <el-form-item label="LOGO图片">
            <el-upload
              accept=".jpg,.png"
              action="#"
              class="avatar-uploader"
              :show-file-list="false"
              :on-change="handleChange"
            >
              <img
                v-if="productForm.icon"
                :src="productForm.icon"
                class="avatar"
              />
              <el-icon class="avatar-uploader-icon"><el-icon-plus /></el-icon>
              <div slot="tip" class="el-upload__tip">支持上传JPG/PNG图片</div>
            </el-upload>
            <!-- <i class="iconfont el-icon-tips-exclamation-circle" style="position:absolute;bottom:0;right:13.5%;font-size:12px">支持上传JPG/PNG图片</i> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="saveLoading"
        @click="save"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { Plus as ElIconPlus } from '@element-plus/icons-vue'
import {
  getOrgData,
  operationProduct,
  apiProductType,
  queryListByProductId,
  getProductDetail,
} from '@/api/vone/product'
import { gainTreeList } from '@/utils'

import storage from 'store'
export default {
  components: {
    ElIconPlus,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: undefined,
    },
    id: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      productForm: {
        name: '',
        code: '',
        orgId: null,
        description: '',
        typeId: '',
        leadingBy: '',
        productset: [],
        product: [],
        icon: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入产品名称', trigger: 'blur' },
          {
            pattern: '^([^ ]){1,50}$',
            message: '请输入不超过50个除空格外的字符',
          },
        ],
        code: [
          { required: true, message: '请输入产品标识', trigger: 'blur' },
          {
            pattern: '^([a-zA-Z0-9-]){1,30}$',
            message: '请输入不超过30个字母、数字或横线(-)组成的标识',
          },
          {
            pattern: '^(?!-)(?!.*?-$)',
            message: '不能以横线开头或结尾',
          },
        ],
        typeId: [
          { required: true, message: '请输入产品类型', trigger: 'change' },
        ],
      },
      orgData: [],
      productTypes: [],
      productsetList: [], // 产品集
      productList: [], // 产品
      saveLoading: false,
    }
  },
  created() {
    if (this.id) {
      this.getinfo()
    } else {
      const userInfo = storage.get('user')
      this.$set(this.productForm, 'leadingBy', userInfo.id)
      this.$set(this.productForm, 'orgId', userInfo.orgId)
    }
    this.getOrgDatas()
    this.apiProductType()
    this.queryListByProductList()
  },
  methods: {
    handleChange(e) {
      // 判断上传文件是否是图片类型：\w 的释义都是指包含大 小写字母数字和下划线 相当于([0-9a-zA-Z])

      const isLt2M = e.size / 1024 / 1024 < 2
      if (isLt2M) {
        // uploadImgToBase64()返回一个Promise对象，通过.then()获取其数据。其中data.result是图片转成的base64值
        // this.uploadImgToBase64(fileImage.raw).then((data) => {
        //   this.companyForm.value = data.result
        // })

        // FileReader 对象允许Web应用程序异步读取存储在用户计算机上的文件（或原始数据缓冲区）的内容，使用 File 或 Blob 对象指定要读取的文件或数据
        const reader = new FileReader()
        // onload 事件会在页面或图像加载完成后立即发生
        reader.onload = (data) => {
          const res = data.target || data.srcElement
          // this.headInfo.src = res.result
          this.productForm.icon = res.result
        }
        // FileReader对象的readAsDataURL方法可以将读取到的文件编码成Data URL
        reader.readAsDataURL(e.raw)
        // 将上传的头像图片提交给后台
        // this.handleUploadHead()
      } else {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
    },

    async getinfo() {
      const res = await getProductDetail(this.id)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      const params = { ...res.data }
      params['productset'] = res.data?.echoMap?.productset

      params['product'] = res.data?.echoMap?.productInfo

      this.productForm = params
    },
    // async getproductsetList() {
    //   const res = await getqueryList()

    //   if (!res.isSuccess) {
    //     this.$message.warning(res.msg)
    //     return
    //   }
    //   this.productsetList = res.data
    // },
    async queryListByProductList() {
      const res = await queryListByProductId({ id: this.id ? this.id : '' })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.productList = res.data
    },
    // 获取机构
    getOrgDatas() {
      getOrgData().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgData = orgTree
      })
    },
    apiProductType() {
      apiProductType().then((res) => {
        this.productTypes = res.data
      })
    },
    // 保存
    async save() {
      await this.$refs.productForm.validate()
      this.saveLoading = true
      const param = this.productForm
      if (this.id) {
        // 编辑保存
        operationProduct(param, 'put')
          .then((res) => {
            if (res.isSuccess) {
              this.saveLoading = false
              this.$message.success('修改成功')
              this.close()
              this.$emit('success')
            } else {
              this.$message.warning(res.msg)
              this.saveLoading = false
            }
          })
          .catch(() => {
            this.saveLoading = false
          })
      } else {
        // 新增保存
        operationProduct(param, 'post')
          .then((res) => {
            if (res.isSuccess) {
              this.saveLoading = false
              this.$message.success('新增成功')
              this.close()
              this.$emit('success')
            } else {
              this.saveLoading = false
              this.$message.warning(res.msg)
            }
          })
          .catch(() => {
            this.saveLoading = false
          })
      }
    },
    close() {
      this.$refs.productForm.resetFields()
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  border: 1px solid #ccc;
  font-size: 28px;
  color: #8c939d;
  width: 158px;
  height: 158px;
  line-height: 158px;
  text-align: center;
}
.avatar {
  width: 158px;
  height: 158px;
  display: block;
}
:deep(.el-form-item) {
  margin-bottom: 18px !important;
}
:deep(.vue-treeselect__input) {
  height: 32px;
}
</style>
