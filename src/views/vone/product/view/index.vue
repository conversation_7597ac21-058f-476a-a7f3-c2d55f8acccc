<template>
  <div>
    <el-row :gutter="12" type="flex" class="space">
      <el-col :span="24">
        <el-card style="height: 198px">
          <vone-back :title="formInfo.name" @back="backTo">
            <div slot="toolbar">
              <div>
                <el-button
                  :icon="el-icon-setting"
                  :disabled="!$permission('product_count_edit')"
                  type="primary"
                  @click="editInfo"
                  >编辑</el-button
                >
              </div>
            </div>
          </vone-back>
          <el-row type="flex" justify="space-between" style="margin-top: 10px">
            <el-col :span="2" class="iconBox">
              <img
                v-if="formInfo.icon"
                :src="formInfo.icon"
                class="vone-icon"
                style="height: auto"
              />
              <img
                v-else
                src="@/assets/iconsteps/icon-product.png"
                class="vone-icon"
              />
            </el-col>
            <el-col :span="22">
              <vone-desc>
                <vone-desc-item label="标识 :">
                  {{ formInfo.code }}
                </vone-desc-item>
                <vone-desc-item label="负责人 :">
                  <span
                    v-if="
                      formInfo.leadingBy &&
                      formInfo.echoMap &&
                      formInfo.echoMap.leadingBy
                    "
                  >
                    <vone-user-avatar
                      :avatar-path="formInfo.echoMap.leadingBy.avatarPath"
                      :name="formInfo.echoMap.leadingBy.name"
                    />
                  </span>
                  <span v-else> {{ formInfo.leadingBy }}</span>
                </vone-desc-item>
                <vone-desc-item label="所属机构 :">
                  {{ itemName(formInfo, 'orgId') }}
                </vone-desc-item>
              </vone-desc>
              <vone-desc :column="1">
                <vone-desc-item :calc-width="true" label="描述 :">
                  {{ formInfo.description }}
                </vone-desc-item>
              </vone-desc>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <!-- <el-col :span="8">
          <vipprice :info-data="priceS" type="s" @success="getInfoData" />
        </el-col> -->
    </el-row>
    <!-- <el-row :gutter="12" type="flex" class="space">
        <el-col :span="24">
          <projectCards project-type="DELIVERY" title="项目交付案例" />
        </el-col>
      </el-row>
      <el-row :gutter="12" type="flex" class="space">
        <el-col :span="24">
          <projectCards project-type="DEVELOP" title="研发项目" />
        </el-col>
      </el-row> -->
    <el-row :gutter="12" type="flex" class="space" style="height: 241px">
      <el-col :span="8">
        <issueView :id="4" :title="'需求概览'" :require-fun="requirePro" />
      </el-col>
      <el-col :span="8">
        <bugView :id="5" :title="'缺陷概览'" :bug-fun="bugPro" />
      </el-col>
      <el-col :span="8" style="padding-left: 6px; padding-right: 6px">
        <statistics ref="statistics" :count="count" />
      </el-col>
    </el-row>
    <el-row type="flex" style="margin-right: -6px">
      <el-col :span="12">
        <bugTrends ref="trends" :title="'缺陷对比图'" />
      </el-col>
      <el-col :span="12" style="padding-right: 6px; padding-left: 6px">
        <version ref="version" />
        <!-- <projectCards ref="projectCards" project-type="DEVELOP" title="我的项目" @getCount="getCount" /> -->
      </el-col>
    </el-row>
    <!-- <el-row class="space">
        <projectCards ref="projectCards" @getCount="getCount" />
      </el-row> -->
    <edit-dialog
      v-if="projectParam.visible"
      v-bind="projectParam"
      v-model="projectParam.visible"
      @success="getBasic"
    />
  </div>
</template>

<script>
import issueView from '../../project/overview/function/issue-view.vue'
import bugView from '../../project/overview/function/bug-view.vue'
import bugTrends from './count/bug-trends.vue'
import version from './count/version.vue'
import statistics from './count/statistics.vue'

import { requireProductId, bugProductId } from '@/api/vone/project/overview.js'
import { getProductDetail } from '@/api/vone/product/index'
// import projectCards from './count/project-cards.vue'
import EditDialog from '../edit-dialog.vue'
// import quotation from './count/quotation.vue'
// import vipprice from '../tabs/competitors/vip-price.vue'

export default {
  components: {
    bugTrends,
    version,
    bugView,
    issueView,
    // projectCards,
    statistics,
    EditDialog,
    // quotation,
    // vipprice
  },
  data() {
    return {
      requirePro: {},
      bugPro: {},
      count: 0, // 项目数量
      formInfo: {},
      projectParam: { visible: false },
      priceS: {},
    }
  },
  computed: {
    itemName() {
      return function (row, key) {
        return (row[key] && row.echoMap && row.echoMap[key]?.name) || ''
      }
    },
  },
  watch: {
    // projectKey() {
    //   this.$router.push({
    //     name: "projectGroup_detail",
    //     params: { projectKey: this.$route.params.projectKey, projectTypeId: "GROUPS"}
    //   });
    // }
    $route(to, from) {
      // this.name = this.$route.params.name
      // this.code = this.$route.params.projectTypeCode
      this.getRequire()
      this.getBug()
      this.$refs.trends.getProjectOptions()
      this.$refs.version.getData()
      this.$refs.statistics.getCount()
      // this.$refs.projectCards.getProjectListByPro()
      // this.getProductQuotationResultByProductIdFn()
      this.getBasic()
    },
  },

  mounted() {
    this.getRequire()
    this.getBug()
    this.getBasic()
    // this.getProductQuotationResultByProductIdFn()
  },
  methods: {
    getBasic() {
      getProductDetail(this.$route.params.productId).then((res) => {
        if (res.isSuccess) {
          this.formInfo = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    editInfo() {
      this.projectParam = {
        visible: true,
        title: '编辑产品',
        id: this.formInfo.id,
        data: this.formInfo,
      }
    },
    backTo() {
      this.$router.push({
        path: '/product/view',
      })
    },
    getCount(val) {
      this.count = Number(val)
    },
    async getRequire() {
      await requireProductId(this.$route.params.productId).then((res) => {
        if (res.isSuccess) {
          this.requirePro = res.data
          // this.options.series[0].data = res.data.data
        }
      })
    },
    async getBug() {
      await bugProductId(this.$route.params.productId).then((res) => {
        if (res.isSuccess) {
          this.bugPro = res.data
          // this.options.series[0].data = res.data.data
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.space {
  margin-bottom: 10px;
}
.iconBox {
  display: flex;
  justify-content: center;
  align-items: center;
  // 详情头像样式
  .vone-icon {
    width: 64px;
    height: 64px;
  }
}
</style>
