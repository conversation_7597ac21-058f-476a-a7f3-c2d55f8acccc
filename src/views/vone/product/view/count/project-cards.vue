<template>
  <div v-loading="dataLoading" class="projectCards">
    <header class="cardTitle">{{ title }}</header>
    <div
      v-show="projectList.length > 0"
      :class="
        title == '我的项目'
          ? ['projectSwiper', 'myproject']
          : ['projectSwiper', 'otherproject']
      "
    >
      <div class="swiper-wrapper">
        <div v-for="row in projectList" :key="row.id" class="swiper-slide">
          <!-- 骨架屏加载 -->
          <el-skeleton :loading="dataLoading" style="height: 100%">
            <!-- 加载时遮挡 -->
            <template slot="template">
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-items: space-between;
                "
              >
                <el-skeleton-item
                  variant="image"
                  style="width: 60px; height: 60px"
                />
                <div style="width: calc(100% - 60px); padding-left: 4px">
                  <el-skeleton-item variant="h3" style="width: 30%" />
                  <el-skeleton-item variant="text" style="width: 80%" />
                </div>
              </div>
              <div style="margin: 6px 0">
                <el-skeleton-item variant="h3" style="width: 50%" />
                <el-skeleton-item
                  variant="image"
                  style="width: 100%; height: 80px"
                />
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-items: space-between;
                    margin-top: 6px;
                    height: 16px;
                  "
                >
                  <el-skeleton-item variant="text" style="margin-right: 16px" />
                  <el-skeleton-item variant="text" style="width: 30%" />
                </div>
              </div>
            </template>
            <!-- 单个卡片 -->
            <vone-card
              class="cardItem"
              :actions-num="3"
              :show-menu="title == '我的项目' ? true : false"
              :split-menu="true"
              :prop-row="row"
              :actions="actions"
              @click="
                itemProject(
                  row.code,
                  row.typeCode,
                  row.id,
                  row.name,
                  '',
                  'project_overview'
                )
              "
            >
              <template v-slot:icon>
                <svg
                  v-if="row.typeCode == 'AGILE'"
                  class="icon"
                  aria-hidden="true"
                >
                  <use xlink:href="#el-icon-icon-minjie1" />
                </svg>
                <svg
                  v-else-if="row.typeCode == 'WALL'"
                  class="icon"
                  aria-hidden="true"
                >
                  <use xlink:href="#el-icon-icon-pubu1" />
                </svg>

                <svg
                  v-else-if="row.typeCode == 'TEST'"
                  class="icon"
                  aria-hidden="true"
                >
                  <use xlink:href="#el-icon-ceshixiangmu" />
                </svg>
                <svg
                  v-else-if="row.typeCode == 'DEVOPS'"
                  class="icon"
                  aria-hidden="true"
                >
                  <use xlink:href="#el-icon-project-devops" />
                </svg>
                <svg
                  v-else-if="row.typeCode == 'CAR'"
                  class="icon"
                  aria-hidden="true"
                >
                  <use xlink:href="#el-icon-project-car-model" />
                </svg>
                <svg
                  v-else-if="row.typeCode == 'PLATFORM'"
                  class="icon"
                  aria-hidden="true"
                >
                  <use xlink:href="#el-icon-project-platform" />
                </svg>
              </template>
              <template v-slot:title>
                <div class="titleBox text-over">
                  <div class="title">{{ row.name }}</div>
                </div>
              </template>
              <!-- <div class="descBox">
                  <div class="description text-over">描述：{{ row.description }}</div>
                  <div class="description text-over">标识：{{ row.typeCode }}</div>
                </div> -->
              <el-row class="descbox" flex style="font-size: 12px">
                <el-row type="flex" class="userText">
                  <span
                    >描述:

                    <el-tooltip
                      :content="row.description"
                      placement="top-start"
                    >
                      <span>
                        {{
                          row.description && row.description.length > 10
                            ? row.description.slice(0, 10) + '...'
                            : row.description
                        }}
                      </span>
                    </el-tooltip>
                  </span>
                </el-row>
                <el-row>
                  <span>标识:</span>
                  <span style="margin-left: 10px">{{ row.code }}</span>
                </el-row>
              </el-row>
              <template v-slot:desc>
                <!-- 用户信息 -->
                <vone-user-avatar
                  :avatar-path="
                    getUserInfo(row) ? getUserInfo(row).avatarPath : ''
                  "
                  :name="getUserInfo(row) ? getUserInfo(row).name : ''"
                />
              </template>
            </vone-card>
          </el-skeleton>
        </div>
      </div>
      <div class="slider-arrow arrow-prev">
        <el-icon class="iconfont"><el-icon-direction-left /></el-icon>
      </div>
      <div class="slider-arrow arrow-next">
        <el-icon class="iconfont"><el-icon-direction-right /></el-icon>
      </div>
    </div>
    <vone-empty v-show="projectList.length == 0" />
  </div>
</template>

<script>
import {
  DirectionLeft as ElIconDirectionLeft,
  DirectionRight as ElIconDirectionRight,
} from '@element-plus/icons-vue'
import { getProjectProduct } from '@/api/vone/project'
import { getPermission, getRouter } from '@/utils/auth'
import { cloneDeep } from 'lodash'
import { apiProjectAuth } from '@/api/vone/project/index'
import { mapState } from 'vuex'
import { setPermission } from '@/utils/auth'
export default {
  components: {
    ElIconDirectionLeft,
    ElIconDirectionRight,
  },
  props: {
    formInfo: {
      type: Object,
      default: () => ({}),
    },
    title: {
      type: String,
      default: '',
    },
    projectType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dataLoading: false,
      actions: [
        {
          type: 'text',
          text: '迭代',
          icon: 'iconfont el-icon-application-iteration',
          onClick: ({ row }) =>
            this.itemProject(
              row.code,
              row.typeCode,
              row.id,
              row.name,
              'project_iteration_view',
              'project_iteration'
            ),
        },
        {
          type: 'text',
          text: '需求',
          icon: 'iconfont el-icon-application-demand',
          onClick: ({ row }) =>
            this.itemProject(
              row.code,
              row.typeCode,
              row.id,
              row.name,
              'project_issue',
              'project_issue'
            ),
        },
        {
          type: 'text',
          text: '测试',
          icon: 'iconfont el-icon-application-test',
          onClick: ({ row }) =>
            this.itemProject(
              row.code,
              row.typeCode,
              row.id,
              row.name,
              'testm_project_tests_plan_view',
              'testm_project_test_tree'
            ),
        },
      ],
      projectList: [],
      swiper: null,
    }
  },
  computed: {
    ...mapState({
      user: (state) => state.user.appRouterMenu,
    }),
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
  },
  mounted() {
    this.getProjectListByPro()
  },
  methods: {
    async itemProject(code, typeCode, id, name, typeName, nameKey) {
      const { data, isSuccess, msg } = await apiProjectAuth(id)
      const jumpRouter = cloneDeep(data)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      if (!isSuccess) {
        return this.$message('获取用户权限失败，请重新登录')
      }
      if (!data.length) {
        this.$message.warning(
          '当前登录用户【项目角色】查看当前项目信息权限不足,请联系项目经理授权'
        )
        return
      }
      // -------------------------------------------------------------------------------
      // 处理按钮权限
      const hasPermissionList = [] // 接口返回的用来接收新的按钮权限数组
      var findPermission = function (V) {
        V.forEach((item) => {
          // 把传入的数组循环遍历
          if (item.meta.isButton === true) {
            hasPermissionList.push(item.meta.code) // item.meta.isButton 为true 把id添加到新数组
          }
          if (item.children) {
            findPermission(item.children) // 递归调用自身
          }
        })
      }
      findPermission(data) // 调用函数

      const permission = getPermission() // 从登录接口获取的权限按钮数据

      const allPermision = [...new Set([...permission, ...hasPermissionList])] // 去重

      setPermission(allPermision)

      // -------------------------------------------------------------------------------

      const routerMenu = cloneDeep(getRouter())
      const projectSettingMenu = cloneDeep(
        routerMenu
          .find((ele) => ele.name == 'project')
          .children.find((ele) => ele.name == 'project_view')
      )
      var arrayList = []
      routerMenu.map((item) => {
        if (item.name == 'project') {
          item.children = []
          item.children.push(projectSettingMenu)
          item.children = [...item.children, ...data]
          if (item.children) {
            item.children.map((v) => {
              arrayList.push(v.name)
            })
          }
        }
      })
      if (arrayList.indexOf(nameKey) == -1) {
        this.$message.warning('当前项目下暂无此权限信息')
        return
      }
      this.$store.commit('user/set_router', routerMenu)

      // 保存路由信息
      this.$store.dispatch('project/itemProject', typeCode)

      const firstMenuName =
        jumpRouter[0]?.children[0]?.name || jumpRouter[0]?.name
      this.$router.push({
        name: firstMenuName,
        params: { projectKey: code, projectTypeCode: typeCode, id: id },
      })
    },
    initSwiper() {
      // 创建一个swiper实例来实现轮播
      this.swiper = new this.Swiper(
        this.title == '我的项目' ? '.myproject' : '.otherproject',
        {
          slidesPerView: this.title == '我的项目' ? 2 : 4,
          spaceBetween: 16,
          autoplay: false,
          watchOverflow: true,
          // 切换按钮
          navigation: {
            nextEl: '.arrow-next',
            prevEl: '.arrow-prev',
          },
        }
      )
    },
    // 查询产品关联项目
    async getProjectListByPro() {
      try {
        this.dataLoading = true
        const res = await getProjectProduct(this.$route.params.productId, {
          classify: this.projectType,
        })
        this.dataLoading = false
        if (res.isSuccess) {
          this.projectList = res.data
          this.$emit('getCount', res.data.length)
          res.data.length > 0 && this.initSwiper()
        }
      } catch (e) {
        this.dataLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.projectCards {
  position: relative;
  overflow: hidden;
  padding: 16px;
  color: #202124;
  background-color: var(--main-bg-color, #fff);
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgb(96 98 102 / 5%);
  transition: 0.3s;
  .swiper-wrapper {
    :deep(.swiper-slide) {
      margin-right: 16px;
    }
  }

  // width: 325px;
  .cardTitle {
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    font-weight: 700;
    color: #202124;
    margin-bottom: 12px;
  }

  .slider-arrow {
    background: rgba(0, 0, 0, 0.15);
    color: #ffff;
    border: none;
    display: inline-flex;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 15px 4px rgb(96 98 102 / 15%);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    z-index: 12;
    cursor: pointer;
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
  :deep(.swiper-button-lock) {
    display: none;
  }
  .arrow-prev {
    left: 6px;
    right: auto;
  }
  .arrow-next {
    right: 6px;
    left: auto;
  }
  .codeSwiper {
    height: calc(100% - 38px);
  }
}
.cardItem {
  height: 100%;
  padding: 0;
  color: #8a8f99;
  box-shadow: none;
  border: 1px solid #eaecf0;

  :deep(.el-card__body) {
    height: calc(100% - 32px);
  }
  :deep(.el-card__header) {
    padding: 0;
  }
  // 图标样式
  :deep(.vone-card__icon) {
    width: 32px;
    height: 32px;
    line-height: 32px;
    margin-right: 8px;
    justify-content: start;
    .projectIcon {
      position: relative;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: var(--icon-bg-color);
      .svg-icon,
      .iconPic {
        position: absolute;
        top: 6px;
        left: 4px;
        width: 24px;
        height: 24px;
      }
    }
  }
  // 卡片标题
  :deep(.vone-card__title) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .titleBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 32px;
      color: var(--main-font-color);
      font-size: 14px;
      .title {
        margin-right: 4px;
        font-weight: 400;
        min-width: 140px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .star {
        font-size: 18px;
        color: #ffc642;
      }
    }
  }
  .descBox {
    height: calc(100% - 90px);
  }
  .userText {
    height: 35px;
    line-height: 35px;
  }
  .description {
    height: 35px;
    line-height: 35px;
    margin: 8px 0;
    color: var(--auxiliary-font-color);
    font-size: 14px;
    font-weight: 400;
  }
  &:hover {
    :deep(.vone-card__title) {
      transition: none;
      transform: none;
    }
  }
}
// 用户信息样式
.userbox {
  display: flex;
  justify-content: center;
  color: var(--auxiliary-font-color);

  .card-user {
    display: flex;
    align-items: center;
    i {
      font-size: 18px;
      border-radius: 50%;
      margin-right: 8px;
    }
  }
  img {
    width: 20px;
    width: 20px;
    border-radius: 50%;
    margin-right: 8px;
  }
  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

// 深色主题样式
.custom-theme-dark {
  .cardItem {
    border: 1px solid #495266;
  }
  .projectCards {
    color: #8a8f99;

    .cardTitle {
      color: #b5b8bd;
    }
    .slider-arrow {
      background-color: #8a8f99;
      i {
        color: #fff;
      }
    }
  }
  .cardItem {
    :deep(.vone-card__title) {
      .title {
        color: #e6e9f0;
      }
    }
  }
}
</style>
