<template>
  <div>
    <!-- 缺陷状态流转 -->
    <el-dropdown
      ref="dropdown"
      trigger="click"
      :disabled="isDone || !$permission('testm_defect_status_edit')"
      @command="handleCommand"
    >
      <a
        class="tagCustom"
        :style="{
          border: `1px solid ${
            defect.stateCode && stateCode ? stateCode.color : '#ccc'
          }`,
          color: `${defect.stateCode && stateCode ? stateCode.color : '#ccc'}`,
        }"
        @click="findNextNode"
      >
        <span v-if="defect.stateCode && stateCode">{{ stateCode.name }}</span>
        <span v-else>
          {{ defect.stateCode }}
        </span>

        <el-icon><ElIconSetting /></el-icon>
        <el-icon class="iconfont el-icon--right"
          ><ElIconDirectionDown
        /></el-icon>
      </a>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in nextNode"
          :key="item.id"
          :command="item.stateCode"
          >{{ item.name }}</el-dropdown-item
        >
        <el-dropdown-item v-if="nextNode.length > 0" command="workFlow" divided>
          <span style="display: flex; align-items: center">
            <el-icon class="iconfont" style="color: #3e7bfa; margin-right: 4px"
              ><el-icon-gongzuoliu
            /></el-icon>
            查看工作流
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-dialog
      title="工作流"
      v-model="flowVisible"
      top="7vh"
      append-to-body
      @before-close="closeFlow"
    >
      <vone-work-flow
        ref="vone-g"
        :xml="xml"
        :show-bar="false"
        hide-text-annotation
        single
        :node-style="nodeStyle"
        :properties-props="{ width: 250 }"
      />
      <div slot="footer">
        <el-button type="primary" @click="closeFlow">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  Loading as ElIconLoading,
  DirectionDown as ElIconDirectionDown,
  Gongzuoliu as ElIconGongzuoliu,
} from '@element-plus/icons-vue'
import { apiAlmBugFindNextNode, apiAlmBugFlow } from '@/api/vone/project/defect'
import { getFlow } from '@/api/vone/base/work-flow'
import { jsonToXml } from '@/views/vone/base/project-config/tab/common/xmlUtils'
export default {
  components: {
    ElIconLoading,
    ElIconDirectionDown,
    ElIconGongzuoliu,
  },
  props: {
    defect: {
      type: Object,
      default: () => {},
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nextNode: [],
      onSearch: false,
      isDone: false,
      flowVisible: false,
      xml: null,
      nodeStyle: {
        width: 100,
        height: 36,
      },
    }
  },
  computed: {
    stateCode() {
      return this.defect?.echoMap?.stateCode || {}
    },
  },
  mounted() {
    this.isDone = this.defect && this.defect.stateCode == 'CLOSE'
  },
  methods: {
    closeFlow() {
      this.flowVisible = false
    },
    async getFlowInfo() {
      let nodes = []
      let edges = []
      // 查询任务工作流
      const res = await getFlow('4')
      if (res.isSuccess) {
        if (
          res.data.workflowNodes.length > 0 &&
          res.data.workflowTransitions.length > 0
        ) {
          this.workflowId = res.data.workflowNodes[0].workflowId
          nodes = res.data.workflowNodes
          edges = res.data.workflowTransitions
          nodes.map((item) => {
            item.nodeType = item.nodeType.code
            item.color = item.echoMap?.stateCode?.color
            item.onlyId = item.id
            item.id = item.code
            delete item.shape
            delete item.size
            delete item.echoMap
          })
          edges.map((item) => {
            item.onlyId = item.id
            item.id = item.code
            item.source = item.sourceAnchor
            item.target = item.targetAnchor
          })
          this.xml = jsonToXml({ nodes, edges })
        }
      }
    },
    async handleCommand(val) {
      if (val === 'workFlow') {
        this.flowVisible = true
        this.getFlowInfo()
        return
      }
      const res = await apiAlmBugFlow(
        this.defect.id || this.defect.bizId, // 因为在迭代列表中需求id的字段名为bizId
        this.defect.stateCode,
        val
      )
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('状态流转成功')
      this.nextNode = []
      this.$emit('changeFlow')
    },
    async findNextNode() {
      if (this.isDone || !this.$permission('testm_defect_status_edit')) return

      this.onSearch = true
      const res = await apiAlmBugFindNextNode(
        this.defect.id || this.defect.bizId // 因为在迭代列表中需求id的字段名为bizId
      )
      this.onSearch = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      if (!res.data.length) {
        this.$refs.dropdown.hide()
        this.$message.warning('暂无当前节点流转权限')
        return
      }
      this.nextNode = res.data
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.bpmn) {
  height: calc(100vh - 270px);
}
</style>
