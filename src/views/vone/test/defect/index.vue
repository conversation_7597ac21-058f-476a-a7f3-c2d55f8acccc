<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="testm-defect-table"
          :model="formData"
          :table-ref="$refs['testm-defect-table']"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          @getTableData="getInitTableData"
        >
          <!-- <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="标题" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入标题" style="width:100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="处理人" prop="handleBy">
                  <vone-remote-user v-model="formData.handleBy" multiple style="width:100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="状态" prop="stateCode">
                  <el-select v-model="formData.stateCode" clearable style="width:100%" filterable multiple>
                    <el-option v-for="item in stateCodeList" :key="item.key" :label="item.name" :value="item.code" />
                  </el-select>
                </el-form-item>

              </el-col>
              <el-col :span="8">
                <el-form-item label="标签" prop="tagId">
                  <tagSelect v-model="formData.tagId" multiple is-id />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="提出时间" prop="createTime">
                  <el-date-picker v-model="formData.createTime" style="width:100%" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="缺陷类型" prop="typeCode">
                  <el-select v-model="formData.typeCode" clearable style="width:100%" filterable multiple>
                    <el-option v-for="item in typeCodeList" :key="item.key" :label="item.name" :value="item.code" />
                  </el-select>
                </el-form-item>
              </el-col>

            </el-row> -->
        </vone-search-dynamic>
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between" align="middle">
          <!-- 快速新增 -->
          <simpleAddDefect
            v-if="createSimple"
            :defect-type="typeCodeList"
            :env-list="envList"
            style="max-width: 850px"
            @success="getInitTableData"
            @cancel="createSimple = false"
            @createDetail="openNewDefect"
          />

          <el-button-group>
            <el-tooltip content="快速新增" placement="top">
              <el-button
                class="subBtton"
                :disabled="!$permission('test_defect_add')"
                :icon="`iconfont  ${
                  createSimple
                    ? 'el-icon-direction-double-left'
                    : 'el-icon-direction-double-down'
                }`"
                type="primary"
                @click.stop="createSimple = !createSimple"
              />
            </el-tooltip>
            <el-button
              :icon="el-icon-setting"
              :disabled="!$permission('test_defect_add')"
              type="primary"
              @click.stop="openNewDefect"
              >新增</el-button
            >
          </el-button-group>
          <!-- 操作下拉 -->
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"
              ><el-icon class="iconfont"><ElIconSetting /></el-icon
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :disabled="item.disabled"
                :icon="item.icon"
                :command="item.fn"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>

    <div :style="{ height: $tableHeight }">
      <vxe-table
        ref="testm-defect-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" />
        <vxe-column
          field="name"
          title="缺陷标题"
          show-overflow-tooltip
          min-width="500"
          class-name="name_col"
        >
          <template slot-scope="scope">
            <div class="name_icon">
              <a
                class="text-over"
                style="display: inline-block; margin-left: 10px"
                @click="showInfo(scope.row)"
              >
                <span
                  v-if="
                    scope.row.typeCode &&
                    scope.row.echoMap &&
                    scope.row.echoMap.typeCode
                  "
                >
                  <i
                    :class="`iconfont ${scope.row.echoMap.typeCode.icon}`"
                    :style="{
                      color: `${
                        scope.row.echoMap.typeCode
                          ? scope.row.echoMap.typeCode.color
                          : '#ccc'
                      }`,
                    }"
                  />
                </span>
                {{ scope.row.code + ' ' + scope.row.name }}</a
              >
              <!-- 是否延期 -->
              <span
                v-if="scope.row.delay"
                style="position: absolute; left: 2px"
              >
                <el-icon class="color-danger ml-2"
                  ><el-icon-warning-outline
                /></el-icon>
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="100">
          <template slot-scope="scope">
            <defectStatus
              v-if="scope.row"
              :key="Date.now()"
              :workitem="scope.row"
              @changeFlow="getInitTableData"
            />
          </template>
        </vxe-column>
        <vxe-column
          field="requirementId"
          title="关联需求"
          show-overflow-tooltip
          width="100"
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.requirementId &&
                scope.row.echoMap &&
                scope.row.echoMap.requirementId
              "
            >
              {{
                `${scope.row.echoMap.requirementId.code}   ${scope.row.echoMap.requirementId.name}`
              }}
            </span>
            <span v-else>{{ scope.row.requirementId }}</span>
          </template>
        </vxe-column>

        <vxe-column
          field="handleBy"
          title="处理人"
          width="90"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.handleBy &&
                scope.row.echoMap &&
                scope.row.echoMap.handleBy
              "
            >
              <vone-user-avatar
                :avatar-path="scope.row.echoMap.handleBy.avatarPath"
                :name="scope.row.echoMap.handleBy.name"
              />
            </span>
            <span v-else class="noSetting">
              <el-icon class="iconfont" style="font-size: 20px"
                ><ElIconUser
              /></el-icon>
              <span>
                {{ scope.row.handleBy ? scope.row.handleBy : '未设置' }}</span
              >
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="leadingBy"
          title="负责人"
          width="90"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.leadingBy &&
                scope.row.echoMap &&
                scope.row.echoMap.leadingBy
              "
            >
              <vone-user-avatar
                :avatar-path="scope.row.echoMap.leadingBy.avatarPath"
                :name="scope.row.echoMap.leadingBy.name"
              />
            </span>
            <span v-else class="noSetting">
              <el-icon class="iconfont" style="font-size: 20px"
                ><ElIconUser
              /></el-icon>
              <span>
                {{ scope.row.leadingBy ? scope.row.leadingBy : '未设置' }}</span
              >
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="putBy"
          title="提出人"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.putBy && scope.row.echoMap && scope.row.echoMap.putBy
              "
            >
              <vone-user-avatar
                :avatar-path="scope.row.echoMap.putBy.avatarPath"
                :name="scope.row.echoMap.putBy.name"
              />
            </span>
            <span v-else-if="!scope.row.putBy" class="noSetting">
              <el-icon class="iconfont" style="font-size: 20px"
                ><ElIconUser
              /></el-icon>
              <span> {{ scope.row.putBy ? scope.row.putBy : '未设置' }}</span>
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="createTime"
          title="提出时间"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.createTime">
              {{ dayjs(scope.row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ scope.row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="planStime"
          title="计划开始时间"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.planStime">
              {{ dayjs(scope.row.planStime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ scope.row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="planEtime"
          title="计划完成时间"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.planEtime">
              {{ dayjs(scope.row.planEtime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ scope.row.planEtime }}</span>
          </template>
        </vxe-column>

        <vxe-column field="rateProgress" title="进度" width="80">
          <template slot-scope="{ row }">
            <el-tooltip
              placement="top"
              :content="` ${row.rateProgress ? row.rateProgress : 0}%`"
            >
              <el-progress
                :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
                color="var(--main-theme-color,#3e7bfa)"
                :show-text="false"
              />
            </el-tooltip>
          </template>
        </vxe-column>

        <vxe-column field="priorityCode" title="优先级" width="100">
          <template slot-scope="scope">
            <vone-icon-select
              v-model="scope.row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              :no-permission="!$permission('test_defect_edit')"
              class="userList"
              @change="changePriotryStatus(scope.row)"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('test_defect_edit')"
                  :icon="el-icon-setting"
                  @click="editBug(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="复制" placement="top">
                <el-button
                  type="text"
                  :icon="el-icon-setting"
                  @click="titleCopy(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('test_defect_del')"
                  :icon="el-icon-setting"
                  @click="deleteBug(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
      <vone-pagination
        ref="pagination"
        :total="tableData.total"
        @update="getInitTableData"
      />
    </div>

    <!-- 新增 -->
    <vone-custom-add
      v-if="defectParamAdd.visible"
      :key="defectParamAdd.key"
      :type-code="'BUG'"
      v-model="defectParamAdd.visible"
      v-bind="defectParamAdd"
      :title="'新增缺陷'"
      @success="getInitTableData({}, 'edit')"
    />

    <!-- 编辑完整缺陷 -->
    <vone-custom-edit
      v-if="defectParam.visible"
      :key="defectParam.key"
      v-model="defectParam.visible"
      v-bind="defectParam"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({}, 'edit')"
    />

    <!-- 缺陷详情 -->
    <vone-custom-info
      v-if="defectInfoParam.visible"
      :key="defectInfoParam.key"
      v-model="defectInfoParam.visible"
      v-bind="defectInfoParam"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({}, 'edit')"
    />
  </page-wrapper>
</template>

<script>
import {
  apiAlmGetDefectData,
  apiAlmBugAdd,
  apiAlmBugDel,
} from '@/api/vone/project/defect'

import { apiAlmPriorityNoPage, getDefectTypeList } from '@/api/vone/alm/index'

import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'

import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'

import defectStatus from '@/views/vone/project/common/change-status/index.vue'
import simpleAddDefect from '@/views/vone/test/plan-manager/components/add-defect.vue'

import { apiBaseDictNoPage } from '@/api/vone/package'
import tagSelect from '@/components/CustomEdit/components/tag-select'

import { catchErr } from '@/utils'
import setDataMixin from '@/mixin/set-data'
export default {
  components: {
    defectStatus,
    simpleAddDefect,
    tagSelect,
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '标题',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入用例',
        },
        {
          key: 'handleBy',
          name: '处理人',
          type: {
            code: 'USER',
          },
          multiple: true,
          placeholder: '请输入编号',
        },
        {
          key: 'stateCode',
          name: '状态',
          type: {
            code: 'SELECT',
          },
          multiple: true,
          placeholder: '请选择状态',
        },
        {
          key: 'createTime',
          name: '提出时间',
          type: {
            code: 'DATE',
          },
          placeholder: '请选择提出时间',
        },
        {
          key: 'typeCode',
          name: '缺陷类型',
          type: {
            code: 'SELECT',
          },
          multiple: true,
          placeholder: '请选择缺陷类型',
        },
      ],
      formData: {
        handleBy: [],
        priorityCode: [],
        putBy: [],
        stateCode: [],
        typeCode: [],
      },

      createSimple: false,
      selecteTableData: [],
      tableData: {},
      tableLoading: false,

      defectParamAdd: { visible: false }, // 新增
      defectParam: { visible: false }, // 编辑
      defectInfoParam: { visible: false },
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment',
        },
        {
          label: '活动',
          name: 'active',
        },
        {
          label: '工时',
          name: 'workTime',
        },
      ],
      leftTabs: [
        {
          label: '关联缺陷',
          name: 'DefectToDefect',
        },
      ],
      tableList: [],
      userMap: {},
      actions: [
        {
          name: '批量删除',

          fn: this.deleteAll,
          disabled: !this.$permission('test_defect_del_batch'),
        },
      ],
      typeCodeList: [],
      envList: [],
      prioritList: [],
      stateCodeList: [],
      planIdList: [], // 迭代计划
    }
  },
  // 路由离开生命周期函数
  beforeRouteLeave(to, from, next) {
    // 即将跳转的路由地址
    if (to.path != 'project_defect_view') {
      this.$store.state.project.itemName = undefined
      next()
    }
  },
  mounted() {
    this.getInitTableData()
    this.getIssueType()
    this.getEnvList()
    this.getPrioritList()
    this.getAllStatus()
    this.getPlanList()
  },
  methods: {
    async changePriotryStatus(val) {
      const [res, err] = await catchErr(apiAlmBugAdd(val))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.getInitTableData()
    },
    // 查询缺陷类型
    async getIssueType() {
      const [res, err] = await catchErr(getDefectTypeList())
      if (err) return
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },
    // 迭代计划
    async getPlanList() {
      const [res, err] = await catchErr(
        apiAlmProjectPlanNoPage({
          projectId: '0',
        })
      )
      if (err) return
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },
    // 查询环境
    async getEnvList() {
      const [res, err] = await catchErr(
        apiBaseDictNoPage({
          type: 'ENVIRONMENT',
          state: true,
        })
      )
      if (err) return
      if (!res.isSuccess) {
        return
      }
      this.envList = res.data
    },
    // 查优先级
    async getPrioritList() {
      const [res, err] = await catchErr(apiAlmPriorityNoPage())
      if (err) return
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },

    // 查询全部工作流状态
    async getAllStatus() {
      const [res, err] = await catchErr(apiAlmStateNoPage())
      if (err) return
      if (!res.isSuccess) {
        return
      }
      this.stateCodeList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },

    async getInitTableData() {
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
          tableCode: 'testm-defect-table',
        },
        model: { ...this.formData },
      }
      this.tableLoading = true
      if (this.formData.createTime && this.formData.createTime.length > 0) {
        params.model.createTime = {
          start: this.formData.createTime[0],
          end: this.formData.createTime[1],
        }
      }
      const [res, err] = await catchErr(apiAlmGetDefectData(params))
      this.tableLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableData.total = res.data.total
    },

    // 复制标题到剪贴板
    titleCopy(row) {
      const _this = this
      this.$copyText(`${row.code} ${row.name}`).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    openNewDefect() {
      this.createSimple = false
      this.defectParamAdd = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
      }
    },
    editBug(row) {
      this.defectParam = {
        visible: true,
        title: '编辑缺陷',
        key: Date.now(),
        id: row.id,
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      }
    },
    showInfo(row) {
      this.defectInfoParam = {
        visible: true,
        title: '缺陷详情',
        key: Date.now(),
        id: row.id,
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      }
    },

    async deleteBug(row) {
      await catchErr(
        this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning',
        })
          .then(async () => {
            const [{ isSuccess, msg }, err] = await catchErr(
              apiAlmBugDel([row.id])
            )
            if (err) return
            if (!isSuccess) {
              this.loading = false
              this.$message.error(msg)
              return
            }
            this.$message.success('删除成功')
            this.getInitTableData()
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '已取消删除'
            // })
          })
      )
    },
    // 批量删除
    async deleteAll() {
      this.selecteTableData = this.getVxeTableSelectData('testm-defect-table')
      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      await this.$confirm(
        `确定删除 ${this.selecteTableData.length} 个数据吗?`,
        '批量删除',
        {
          type: 'warning',
          closeOnClickModal: false,
          customClass: 'delConfirm',
        }
      )
      this.tableLoading = true
      const selectId = this.selecteTableData.map((r) => r.id)
      const [res, err] = await catchErr(apiAlmBugDel(selectId))
      this.tableLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
:deep() {
  .table-search-view {
    .table-search-main {
      margin: 0;
      white-space: nowrap;
    }
    .el-dropdown {
      white-space: nowrap;
    }
  }
  .userList {
    .el-input__inner {
      border: 0;
    }
    .el-input__icon {
      display: none;
    }
  }
}
.noSetting {
  color: var(--col-no-setting);
}
</style>
