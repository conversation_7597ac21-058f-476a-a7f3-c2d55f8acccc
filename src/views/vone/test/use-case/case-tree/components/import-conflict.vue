<template>
  <!-- vone-custom-table -->
  <el-dialog
    title="用例冲突"
    width="70%"
    top="10vh"
    :model-value="visible"
    :before-close="onClose"
    height="70%"
    :close-on-click-modal="false"
    v-bind="$attrs"
  >
    <el-row>
      <el-col :span="8" class="elStyle l_body">
        <el-tree
          ref="tree"
          :data="treeData"
          node-key="tree_id"
          default-expand-all
          highlight-current
          :expand-on-click-node="false"
          :props="{ label: 'name' }"
          @node-click="treeCaseClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>
            <el-popover
              placement="right"
              trigger="hover"
              :content="data.content"
              @show="getNodeParentPath(data)"
            >
              <div slot="reference">
                <span class="treeNode">{{ data.name }}</span>
                <span v-if="data.tree_id_count" class="badge">{{
                  data.tree_id_count
                }}</span>
              </div>
            </el-popover>
          </span>
        </el-tree>
      </el-col>
      <el-col :span="16" class="elStyle r_body">
        <el-row type="flex" justify="space-between" align="middle">
          <div class="caseTitle">选择用例</div>
          <el-button type="primary" @click="saveRepeatCases">保存</el-button>
        </el-row>
        <el-table
          ref="table"
          :loading="tableLoading"
          table-key="case-table"
          :table-options="tableOptions"
          :table-data="tableData"
          height="450px"
          row-key="id"
          :no-page="true"
          class="vone-table"
          @getTableData="getTableList"
        >
          <el-table-column label="导入用例" prop="name" show-overflow-tooltip>
            <template v-slot:header>
              <el-radio
                v-model="batchUpdate"
                :label="1"
                @change="batchUpdateStatus"
                >导入用例</el-radio
              >
            </template>
            <template v-slot="{ row }">
              <el-radio v-model="row.saveState" :label="1" style="margin: 0">
                <el-icon class="iconfont" style="color: #37cdde"
                  ><el-icon-application-view-list
                /></el-icon>
                <el-icon style="color: #3e7bfa"><el-icon-document /></el-icon>
              </el-radio>
              <el-popover
                v-model="row.showBeforeCase"
                placement="top"
                width="560"
                trigger="click"
                @show="handleShowNew(row)"
              >
                <!-- 用例详情 -->
                <caseForm
                  v-model="row.showBeforeCase"
                  :info-data="row"
                  :node-data="nodeData"
                />

                <a slot="reference" style="margin-left: 4px">{{ row.name }}</a>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            label="原用例"
            prop="originalName"
            show-overflow-tooltip
          >
            <template v-slot:header>
              <el-radio
                v-model="batchUpdate"
                :label="2"
                @change="batchUpdateStatus"
                >原用例</el-radio
              >
            </template>
            <template v-slot="{ row }">
              <el-radio v-model="row.saveState" :label="2" style="margin: 0">
                <el-icon class="iconfont" style="color: #37cdde"
                  ><el-icon-application-view-list
                /></el-icon>
                <el-icon style="color: #3e7bfa"><el-icon-document /></el-icon>
              </el-radio>
              <el-popover
                v-model="row.showAfterCase"
                placement="top"
                width="560"
                trigger="click"
                @show="handleShowOrigin(row)"
              >
                <!-- 用例详情 -->
                <caseForm
                  v-model="row.showAfterCase"
                  :info-data="afterCase"
                  :node-data="nodeData"
                />

                <a slot="reference" style="margin-left: 4px">{{ row.name }}</a>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-row slot="footer" class="dialog-footer">
      <div style="float: left; height: 32px; line-height: 32px">
        <span>批量处理：</span>
        <el-radio v-model="batchUpdateAll" :label="true">导入用例</el-radio>
        <el-radio v-model="batchUpdateAll" :label="false">原用例</el-radio>
      </div>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="submit"
        >导入</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script>
import {
  ApplicationViewList as ElIconApplicationViewList,
  Document as ElIconDocument,
} from '@element-plus/icons-vue'
import {
  getImportRepeatCasesCountIntestm,
  getNodePath,
  getRepeatCaseListIntestm,
  ImportRepeatXmindFileIntestm,
  saveRepeatCasesIntestm,
} from '@/api/vone/testmanage/file'
import { productCaseDetail } from '@/api/vone/testmanage/case'

import caseForm from './case-form.vue'
import { catchErr } from '@/utils'

export default {
  components: {
    caseForm,
    ElIconApplicationViewList,
    ElIconDocument,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    libraryId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableLoading: false,
      btnLoading: false,
      batchUpdate: 0, // 批量更新用例状态
      batchUpdateAll: 'normal', // 批量更新全部用例状态
      treeData: [],
      caseCounts: [], // 冲突用例数组
      nodeData: {}, // 当前节点数据
      afterCase: {}, // 冲突后用例
      tableOptions: {
        isOperation: false, // 表格有操作列时设置
        isIndex: false, // 列表序号
      },
      originStatusTable: [], // 原始状态表格数据
      tableData: {
        records: [],
      },
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 查询左侧树
        this.getTreeData()
      }
    },
  },
  methods: {
    onClose() {
      this.batchUpdateAll = 'normal'
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    // 查询左侧用例分组树
    async getTreeData(nodeData) {
      const [res, err] = await catchErr(
        getImportRepeatCasesCountIntestm(this.libraryId)
      )
      if (err) return
      if (res.isSuccess) {
        this.caseCounts = res.data

        this.treeData = res.data.map((item) => {
          this.$set(item, 'loading', false)
          return item
        })
        this.nodeData = nodeData ?? res.data[0] ?? {}
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.nodeData.tree_id)
        })
        // 查询用例列表
        this.nodeData.tree_id && this.getTableList()
      }
    },
    // 查询节点路径
    async getNodeParentPath(data) {
      if (data.content) return
      const [res, err] = await catchErr(getNodePath(data.tree_id))
      if (err) return
      this.$set(data, 'content', res.data)
    },
    treeCaseClick(data) {
      const list = this.tableData.records
      // 数据是否改变
      const changed = list.some(
        (item, i) => item.saveState !== this.originStatusTable[i].saveState
      )
      if (changed && list.length > 0) {
        this.$confirm('是否保存当前已处理用例数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveRepeatCases(data)
          })
          .catch(() => {
            this.nodeData = data
            this.getTableList()
          })
      } else {
        this.nodeData = data
        this.getTableList()
      }
    },
    // 查询表格数据
    async getTableList() {
      this.tableLoading = true
      const [res, err] = await catchErr(
        getRepeatCaseListIntestm(this.nodeData.tree_id)
      )
      this.tableLoading = false
      if (err) return
      if (res.isSuccess) {
        this.originStatusTable = res.data
        this.tableData.records = res.data.map((item) => {
          this.$set(item, 'showBeforeCase', false)
          this.$set(item, 'showAfterCase', false)
          return item
        })
      }
    },
    // 批量解决冲突用例
    batchUpdateStatus(val) {
      this.tableData.records.forEach((item) => {
        this.$set(item, 'saveState', val)
      })
    },
    // 保存已处理的用例数据
    async saveRepeatCases(nodeData) {
      const currentNode = nodeData.type === 'click' ? this.nodeData : nodeData

      const list = this.tableData.records
      const [res, err] = await catchErr(saveRepeatCasesIntestm(list))
      if (err) return
      if (res.isSuccess) {
        // 修改原始的用例数据
        this.originStatusTable = list.map((item) => {
          return { ...item }
        })
        this.$message.success('保存成功')
        this.getTreeData(currentNode)
        if (nodeData.type !== 'click') {
          this.nodeData = nodeData
          this.$refs.tree?.setCurrentKey(nodeData.id)
        }
      }
    },
    // 显示导入用例
    handleShowNew(row) {
      row.showBeforeCase = true
    },
    // 显示原用例数据
    handleShowOrigin(row) {
      // 查询用例数据
      productCaseDetail(row.id)
        .then((res) => {
          if (res.isSuccess) {
            this.afterCase = res.data
          }
        })
        .catch(() => {
          this.afterCase = {}
        })
    },
    async submit() {
      const counts = this.caseCounts.every((item) => item.tree_id_count === '0')
      if (!counts && this.batchUpdateAll === 'normal') {
        this.$message.error('存在未处理冲突用例，请先处理并保存')
        return
      }
      this.btnLoading = true
      const [res, err] = await catchErr(
        ImportRepeatXmindFileIntestm(this.libraryId, this.batchUpdateAll)
      )
      this.btnLoading = false
      if (err) return
      if (res.isSuccess) {
        this.$message.success('导入成功')
        this.$emit('success')
        this.onClose()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 0;
}
:deep() {
  .pagination {
    margin: 0;
  }
}
.elStyle {
  overflow: auto;
  :deep(.el-tree-node > .el-tree-node__children) {
    min-width: min-content;
  }
}
.l_body {
  padding: 12px 16px;
  height: 532px;
  overflow-y: auto;
}
.r_body {
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
  height: 532px;
  & > .el-row:first-child {
    border-bottom: 1px solid var(--disabled-bg-color, #ebeef5);
    padding: 8px 16px;
    margin-bottom: 16px;
  }
  .caseTitle {
    font-size: 14px;
    font-weight: 600;
    color: var(--main-font-color);
  }
  .vone-table {
    padding: 0 16px 16px;
  }
}
.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 0 4px;
  & > span {
    display: flex;
    align-items: center;
  }

  .treeNode {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .badge {
    height: 15px;
    padding: 2px 6px;
    background: var(--disabled-bg-color);
    border-radius: 14px;
  }
}
</style>
