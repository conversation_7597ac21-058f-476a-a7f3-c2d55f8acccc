<template>
  <!-- 用例树删除 -->
  <el-dialog
    :model-value="visible"
    :close-on-click-modal="false"
    width="456px"
    :close-on-press-escape="false"
    :show-close="false"
    @close="close"
  >
    <!-- <div slot="title">
        确认删除
      </div> -->
    <div class="flexRow">
      <span class="redSpan">
        <el-icon class="iconfont"
          ><el-icon-tips-exclamation-circle-fill
        /></el-icon>
        删除
      </span>
      <a @click="close">
        <el-icon class="iconfont"><ElIconSetting /></el-icon>
      </a>
    </div>
    <section>
      <div class="mainText">确认删除【{{ node.name }}】吗？</div>
      <div class="subText">
        {{ message || '删除后模块下用例，可在回收站中查看' }}
      </div>
    </section>

    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="danger" :loading="deleting" @click="onSave"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  TipsExclamationCircleFill as ElIconTipsExclamationCircleFill,
  TipsClose as ElIconTipsClose,
} from '@element-plus/icons-vue'

import { productCaseTreeDelAll } from '@/api/vone/testmanage/case'

export default {
  components: {
    ElIconTipsExclamationCircleFill,
    ElIconTipsClose,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    node: {
      type: Object,
      default: () => {},
    },
    message: {
      type: String,
      default: null,
    },
    entry: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      deleting: false,
    }
  },
  mounted() {
    console.log(this.node, 'nodenode')
  },
  methods: {
    async onSave() {
      if (this.entry == 'testPlan') {
        this.close()
        this.$emit('sureDel')
        return
      }
      this.deleting = true
      const res = await productCaseTreeDelAll(this.node.id)
      this.deleting = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.close()
      this.$emit('success')
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__header) {
  display: none;
}
:deep(.el-dialog .el-dialog__footer) {
  border-top: none;
}
.flexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .redSpan {
    color: #db2c3a;
    font-size: 18px;
    i {
      font-size: 20px;
    }
  }
}
section {
  padding: 10px 10px 0 25px;

  .mainText {
    line-height: 22px;
    color: #2c2e36;
    margin-bottom: 5px;
  }
  .subText {
    color: #777f8e;
    font-size: 12px;
  }
}
:deep(.el-dialog .el-dialog__body) {
  padding: 20px;
}
</style>
