<template>
  <div class="addWrap">
    <el-form ref="caseFormRef" :model="caseForm" label-position="top">
      <el-row>
        <!-- 左侧表单部分 -->
        <el-col :span="16" class="left_side">
          <el-form-item label="用例标题" prop="name" class="case_title">
            <el-input
              v-model="caseForm.name"
              disabled
              placeholder="请输入用例标题"
            />
            <el-tooltip placement="top" content="用例标题不超过50个字符">
              <el-icon style="margin-left: 4px"
                ><el-icon-warning-outline
              /></el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="编号">
            <el-input
              v-model="caseForm.caseKey"
              disabled
              placeholder="请输入英文标识"
            />
          </el-form-item>
          <el-form-item label="测试意图" prop="intent">
            <el-input
              v-model="caseForm.intent"
              disabled
              placeholder="请输入测试意图"
            />
          </el-form-item>
          <el-form-item label="前置条件" prop="prerequisite">
            <el-input
              v-model="caseForm.prerequisite"
              disabled
              placeholder="请输入前置条件"
              type="textarea"
            />
          </el-form-item>
          <el-form-item label="标签" prop="tabInfo">
            <el-select v-model="caseForm.tabInfo" multiple disabled>
              <el-option
                v-for="item in selectData"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
          <el-row :gutter="20" class="label_line">
            <el-col :span="8">
              <el-form-item label="测试步骤" prop="stepType">
                <div class="basicBox">
                  <el-icon class="basicIcon"><el-icon-document /></el-icon>
                  <div class="basicText">
                    {{ caseForm.stepType === 'text' ? '文本描述' : '步骤描述' }}
                  </div>
                  <el-icon class="basicIcon"><el-icon-caret-bottom /></el-icon>
                  <el-select
                    v-model="caseForm.stepType"
                    disabled
                    placeholder="请选择更改类型"
                    class="select"
                    popper-class="typeSelect"
                  >
                    <el-option
                      v-for="item in stepList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                      <div class="selectItem">
                        <div class="selectLabel">
                          <span>{{ item.label }}</span>
                          <el-icon style="float: right"
                            ><el-icon-document
                          /></el-icon>
                        </div>
                        <div class="selectDesc" v-html="item.desc" />
                      </div>
                    </el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item prop="testStep">
            <el-input
              v-if="caseForm.stepType === 'text'"
              v-model="caseForm.testStep"
              disabled
              placeholder="请输入描述"
              type="textarea"
            />
            <template v-else>
              <div
                v-for="item in stepData"
                :key="item.caseStepNum"
                class="stepList"
              >
                <el-input
                  v-model="item.caseStepDes"
                  disabled
                  placeholder="请输入步骤"
                  class="stepListLeft"
                  type="textarea"
                />
                <el-input
                  v-model="item.expectResult"
                  disabled
                  placeholder="请输入预期"
                  class="stepListRight"
                  type="textarea"
                />
              </div>
            </template>
          </el-form-item>
          <el-form-item
            v-if="caseForm.stepType === 'text'"
            label="预期结果"
            prop="expectedResult"
          >
            <el-input
              v-model="caseForm.expectedResult"
              disabled
              placeholder="请输入预期结果"
            />
          </el-form-item>
        </el-col>
        <!-- 右侧表单部分 -->
        <el-col :span="8">
          <el-form-item label="所属分组">
            <div class="referenceEmit">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#el-application-filejia2" />
              </svg>
              <span class="treeName" :title="nodeData.name">{{
                nodeData.name
              }}</span>
            </div>
          </el-form-item>
          <el-form-item label="维护人">
            <vone-remote-user v-model="caseForm.leadingBy" />
          </el-form-item>
          <el-form-item label="优先级" prop="priority" class="label_line">
            <vone-icon-select
              v-model="caseForm.priority"
              :data="prioritList"
              placeholder="请选择优先级"
              disabled
              class="select level-select"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <div style="display: flex; align-items: center">
                  <i
                    :class="`iconfont ${item.icon}`"
                    :style="{
                      color: item.color,
                      fontSize: '16px',
                      paddingRight: '6px',
                    }"
                  />
                  <span>{{ item.name }}</span>
                </div>
              </el-option>
            </vone-icon-select>
          </el-form-item>
          <el-form-item label="关联需求">
            <el-select
              v-model="caseForm.requirementId"
              placeholder="请输入需求名称"
              clearable
              filterable
              remote
              :remote-method="getRequirementList"
              :loading="requireLoading"
              class="requireSelect"
            >
              <el-option
                v-for="ele in requirementList"
                :key="ele.id"
                :value="ele.id"
                :label="ele.name"
                :title="ele.name"
              >
                {{ `${ele.code}   ${ele.name}` }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer">
      <el-button @click="onClose">关闭</el-button>
    </div>
  </div>
</template>

<script>
import {
  WarningOutline as ElIconWarningOutline,
  Document as ElIconDocument,
  CaretBottom as ElIconCaretBottom,
} from '@element-plus/icons-vue'
import { apiAlmRequirementNoPage } from '@/api/vone/project/issue'
import { queryCase } from '@/api/vone/testTab'
import { debounce } from 'lodash'
// 重置表单属性
const defaultForm = () => {
  return {
    name: '', // 用例名称
    caseKey: '', // 用例标识
    leadingBy: '', // 负责人
    requirementId: '',
    intent: '', // 测试意图
    prerequisite: '', // 前置条件
    stepType: 'text', // 步骤类型 text/subclause
    testStep: '', // 测试步骤
    expectedResult: '', // 预期结果
    priority: null, // 用例级别
  }
}
export default {
  components: {
    ElIconWarningOutline,
    ElIconDocument,
    ElIconCaretBottom,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // 选中的用例数据
    infoData: {
      type: Object,
      default: () => ({}),
    },
    // 选中节点数据
    nodeData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tagOptions: [],
      caseForm: defaultForm(),
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69',
        },
        {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669',
        },
        {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)',
        },
        {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E',
        },
        {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95',
        },
      ],
      // 步骤列表
      stepList: [
        {
          label: '文本描述',
          value: 'text',
          color: '#0085FF',
          desc: `适用于简单的测试场景，没有明确测<br>试步骤。`,
        },
        {
          label: '步骤描述',
          value: 'subclause',
          color: '#E6A23C',
          desc: `适用于需要每一个步骤进行测试的场<br>景，有明确的测试步骤、预期结果。`,
        },
      ],
      // 测试步骤
      stepData: [
        {
          caseStepNum: 1,
          caseStepDes: '',
          expectResult: '',
        },
      ],
      requireLoading: false,
      requirementList: [],
      selectData: [], // 标签列表
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getTgsList()
        this.setCaseForm()
      }
    },
    infoData(val) {
      if (val.id) {
        this.setCaseForm()
      }
    },
  },
  methods: {
    // 查询用例列表
    getTgsList() {
      queryCase({ name: '' }).then((res) => {
        this.selectData = res.data
      })
    },
    // 查需求列表
    getRequirementList: debounce(async function (query) {
      try {
        if (query && query != '') {
          this.requireLoading = true
          const res = await apiAlmRequirementNoPage({ name: query })
          this.requireLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.requirementList = res.data
        }
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),
    // 数据回显
    setCaseForm() {
      this.caseForm = { ...this.caseForm, ...this.infoData }
      // 测试步骤显示
      if (this.caseForm.stepType === 'subclause') {
        this.stepData =
          this.infoData.testStep && JSON.parse(this.infoData.testStep)
      }
    },
    // 恢复初始状态
    onClear() {
      this.stepData = [
        {
          caseStepNum: 1,
          caseStepDes: '',
          expectResult: '',
        },
      ]
      this.$refs.caseFormRef.clearValidate()
    },
    onClose() {
      this.caseForm = defaultForm()
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        this.onClear()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.addWrap {
  .case_title {
    :deep(.el-form-item__content) {
      display: flex;
      align-items: center;
    }
  }
  .left_side {
    border-right: 1px solid var(--disabled-bg-color, #ebeef5);
  }
  // 新增用例样式
  :deep(form) {
    overflow-y: auto;
    overflow-x: hidden;

    .el-form-item--small {
      margin-bottom: 16px;
    }
    .el-col-16 {
      padding-right: 16px;
    }
    .el-col-8 {
      padding-left: 16px;
    }
  }
}
// 步骤测试样式
.stepList {
  .stepListLeft {
    width: 68%;
    margin-right: 2%;
    margin-bottom: 8px;
  }
  .stepListRight {
    width: 25%;
    margin-right: 2%;
    margin-bottom: 8px;
  }
}
.basicBox {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  .basicText {
    margin: 0 6px;
    white-space: nowrap;
  }
  .basicIcon {
    color: #c0c4cc;
  }
  .select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    height: 30px;
    z-index: 99;
    width: 90px;
    cursor: pointer;
  }
}

.referenceEmit {
  display: flex;
  align-items: center;
  .treeName {
    max-width: 200px;
    @include ellipsis;
  }
}
.footer {
  text-align: right;
  border-top: 1px solid #c0c4c0;
  padding: 16px 16px 0;
}
// 测试步骤选择样式
.typeSelect {
  .el-select-dropdown__list .el-select-dropdown__item {
    height: auto;
    .selectItem {
      border-bottom: 1px solid #dcdfe6;
      padding-bottom: 8px;
    }
    &:last-child .selectItem {
      border-bottom: none;
    }
  }
  .selectLabel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > span {
      color: #595959;
    }
  }
  .selectDesc {
    font-weight: normal;
    line-height: 20px;
    color: #8c8c8c;
  }
}
:deep(.level-select) {
  .el-input__prefix {
    display: flex;
    align-items: center;
  }
}
.requireSelect {
  :deep(.el-input__inner) {
    padding-right: 30px;
    @include ellipsis;
  }
}
// 暗色主题样式
.custom-theme-dark {
  .left_side,
  .footer {
    border-color: #495266;
  }
  .basicIcon,
  .basicText {
    color: #e6e9f0;
  }
  .typeSelect {
    .el-select-dropdown__list .el-select-dropdown__item {
      height: auto;
      .selectItem {
        border-color: #495266;
      }
    }
    .selectLabel {
      & > span {
        color: #e6e9f0;
      }
    }
    .selectDesc {
      color: #e6e9f0;
    }
  }
}
</style>

<style lang="scss">
.custom-theme-dark {
  ::-webkit-scrollbar-corner,
  ::-webkit-resizer {
    color: #e6e9f0;
    background-color: #252933;
  }
}
</style>
