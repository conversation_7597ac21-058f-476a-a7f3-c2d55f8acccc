<template>
  <div>
    <div v-loading="leftLoading" class="custom-tree">
      <!-- 默认用例树 -->
      <el-tree
        ref="treeGroupCase"
        node-key="id"
        draggable
        :data="treedata"
        default-expand-all
        :expand-on-click-node="false"
        :current-node-key="currentCasekey"
        :highlight-current="true"
        :filter-node-method="filterNode"
        :allow-drop="collapse"
        :props="defaultProps"
        @node-click="treeCaseClick"
        @node-collapse="treeCaseClick"
        @node-drop="handleDrop"
        @node-expand="treeExpand"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node draggable-node"
          style="width: 100%"
        >
          <span>
            <svg v-if="data.name == '未分组'" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-icon-wenjianjia-weifenzu" />
            </svg>

            <svg v-else-if="node.level !== 1" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-wenjianjia2" />
            </svg>

            <span :class="{ rootNode: node.level == 1 }" class="treeNode">{{
              node.label
            }}</span>
            <span v-if="data.name !== '未分组'">({{ data.num || 0 }})</span>
          </span>

          <span v-if="data.name !== '未分组'" class="operation-icon">
            <el-tooltip content="新增" placement="top">
              <el-button
                type="text"
                size="mini"
                :icon="el-icon-setting"
                :disabled="
                  !$permission('testm_case_tree_add') || node.level >= depth
                "
                @click.stop="() => addNode(data)"
              />
            </el-tooltip>

            <el-tooltip v-if="node.level == 1" content="编辑" placement="top">
              <el-button
                type="text"
                size="mini"
                :icon="el-icon-setting"
                :disabled="!$permission('testm_product_tree_case_edit_batch')"
                @click.stop="() => allCase(data)"
              />
            </el-tooltip>

            <el-tooltip v-if="node.level != 1" content="重命名" placement="top">
              <el-button
                type="text"
                size="mini"
                :icon="el-icon-setting"
                :disabled="!$permission('testm_case_tree_put')"
                @click.stop="() => editNode(data, node)"
              />
            </el-tooltip>

            <el-tooltip
              v-if="node.level != 1 && type !== 'graftTree'"
              content="归档"
              placement="top"
            >
              <el-button
                type="text"
                size="mini"
                :disabled="!$permission('testm_case_tree_file')"
                :icon="el-icon-setting"
                @click="caseArchive(data, node)"
              />
            </el-tooltip>

            <el-tooltip
              v-if="node.level != 1 && type !== 'graftTree'"
              content="导出"
              placement="top"
            >
              <el-button
                type="text"
                size="mini"
                :disabled="!$permission('testm_tree_case_export')"
                :icon="el-icon-setting"
                @click="exportTypeFile(data, node)"
              />
            </el-tooltip>

            <el-tooltip v-if="node.level != 1" content="删除" placement="top">
              <el-button
                type="text"
                size="mini"
                :disabled="!$permission('case_tree_del')"
                :icon="el-icon-setting"
                @click="removeNode(data, node)"
              />
            </el-tooltip>
          </span>
        </span>
      </el-tree>
    </div>
    <!-- 新增编辑分组树弹窗 -->
    <treeAdd
      v-model="menuVisible"
      :flag="nameFlag"
      :data="menuNode"
      :library-id="libraryId"
      @success="submitR(menuNode)"
      @close="() => {}"
    />

    <!-- 删除用例树节点 -->
    <delNodeDialog
      v-if="delNodeParam.visible"
      v-bind="delNodeParam"
      v-model="delNodeParam.visible"
      @success="delSave(delNodeParam)"
    />

    <!-- 批量修改 -->
    <allEdit
      v-if="editVisible"
      v-model="editVisible"
      :dialog-type="dialogType"
      :node-data="menuNode"
      :ids="ids"
      :edit="editType"
      :library-id="libraryIds"
      @success="success"
    />
  </div>
</template>

<script>
import { list2Tree } from '@/utils/list2Tree'
import { catchErr } from '@/utils'

import {
  productTestCaseTreeSwap,
  getTreeByLibrary,
  queryAllCaseNumberOfTree,
} from '@/api/vone/testmanage/case'
import { findProductCaseTreeOfRepository } from '@/api/vone/testplan'

import treeAdd from './components/tree-add.vue'
import allEdit from './all-edit.vue'
import delNodeDialog from './components/del-node-dialog.vue'

export default {
  components: {
    treeAdd,
    allEdit,
    delNodeDialog,
  },
  props: {
    libraryId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      treedata: [],
      libraryIds: '',
      dialogType: '',
      editVisible: false, // 批量修改
      editType: '',
      depth: '',
      menuVisible: false,
      leftLoading: false, // 左侧树加载
      filterText: '',
      currentCasekey: '',
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'leaf',
      },
      nameFlag: '',
      menuNode: {}, // 选中节点数据
      formData: {},
      getCountList: {},
      ids: [],
      delNodeParam: {
        visible: false,
      },
    }
  },

  watch: {
    filterText(val) {
      // 监听的是左侧的计划列表,和下边filterNode方法配合
      this.$refs['treeGroupCase'].filter(val)
    },
  },
  created() {
    this.depth = Number(this.$route.params.depth)
  },
  mounted() {
    this.getCount()
  },
  methods: {
    getTreeNode() {
      return document.querySelectorAll('.draggable-node')
    },
    async getCount(data) {
      const caseId = this.$route.params.caseId
      if (!caseId) return

      const res = await queryAllCaseNumberOfTree(caseId)
      if (!res.isSuccess) {
        return
      }
      if (data) {
        this.setNum(res.data)

        // const node = this.$refs.treeGroupCase.getNode(data.id)
        // this.$nextTick(() => {
        //   this.$set(this.$refs.treeGroupCase.store.nodesMap[node.data.id].data, 'num', 2)
        // })
      } else {
        // this.getCountList = res.data
        this.gettreeData(res.data)
      }
    },
    setNum(data) {
      for (const i in this.$refs.treeGroupCase.store.nodesMap) {
        const cur = data[i]

        this.$set(
          this.$refs.treeGroupCase.store.nodesMap[i].data,
          'num',
          cur ? cur[0].count : 0
        )
      }
    },
    async gettreeData(data) {
      const [res, err] = await catchErr(
        findProductCaseTreeOfRepository(this.$route.params.caseId)
      )
      if (err) return

      if (data) {
        res.data.map((v, w) => {
          for (const i in data) {
            if (v.id == i) {
              v.num = data[i][0].count
            }
          }
        })
      }
      const tree = list2Tree(res.data, { parentKey: 'parentId' })
      this.treedata = tree

      // if (node.level === 0) {
      // 设置默认节点
      this.menuNode = this.treedata?.[0]
      this.currentCasekey = this.treedata?.[0]?.id
      // this.getTableData()

      this.$nextTick(() => {
        this.$refs.treeGroupCase?.setCurrentKey(this.currentCasekey) // 树当前选中
        this.$refs.treeGroupCase?.getNode(this.currentCasekey)?.expand() // 手动展开
        this.$emit('submit', this.menuNode)
      })
      // }

      this.treedata[0].children.unshift({
        id: 0,
        name: '未分组',
        leaf: true,
        treeId: '',
      })
      // 加载拖拽方法
      this.$nextTick(() => {
        // const className = document.querySelectorAll('.draggable-node')
        // this.$emit('treeDrop', className)
      })
    },
    success() {
      this.$emit('submit')
    },
    allCase(row) {
      this.menuNode = row
      this.editType = 'edit'
      this.libraryIds = this.$route.params.caseId || this.libraryId
      this.editVisible = true
    },
    // 删除节点
    delSave(node) {
      this.getCount()
      this.$emit('delNode')
    },
    // 新增和编辑节点
    submitR(node) {
      this.getCount()
    },
    refreshNode(id) {
      const node = this.$refs.treeGroupCase.getNode(id)
      node.loaded = false

      node.expand() // 主动调用展开节点方法,重新查询该节点下的所有子节点
    },

    collapse(draggingNode, dropNode, type) {
      if (dropNode.level == 1) {
        return type == 'inner'
      } else {
        return !draggingNode.data.leaf
      }
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      var obj = {
        orgId: draggingNode.data.id,
        parentId:
          dropType == 'before' ? dropNode.data.parentId : dropNode.data.id,
        preOrgId: this.getChange(
          dropNode.parent.childNodes,
          draggingNode.data.id
        ),
      }
      const res = await productTestCaseTreeSwap(obj).catch((e) => {
        // if (dropType == 'before') {
        //   this.refreshNode(dropNode.data.parentId)
        //   this.refreshNode(draggingNode.data.parentId)
        // } else if (dropType == 'inner') {
        //   this.refreshNode(dropNode.data.id)
        //   this.refreshNode(draggingNode.data.parentId)
        // }
        // this.refreshNode()
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
      } else {
        this.getCount(this.menuNode)
        this.$message.success('移动成功')
      }
    },
    getChange(arr, id) {
      var proOrgId = ''
      arr.forEach((item, i) => {
        if (item.data.id == id) {
          i > 0 && (proOrgId = arr[i - 1].data.id)
        }
      })
      return proOrgId
    },

    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 新增子级节点
    addNode(data) {
      this.menuNode = data
      this.menuVisible = true
      this.nameFlag = 'add'
    },
    // 编辑节点
    async editNode(data, node) {
      this.menuNode = data
      this.menuVisible = true
      this.nameFlag = 'edit'
    },
    // 删除节点
    async removeNode(data) {
      this.delNodeParam = { visible: true, node: data }
    },

    // 点击节点查询数据
    treeCaseClick(data) {
      this.$nextTick(() => {
        this.$refs.treeGroupCase?.setCurrentKey(data.id) // 树当前选中
        this.$emit('submit', data)
      })
      // this.currentCasekey = data.id
    },
    treeExpand(data, node) {
      this.$nextTick(() => {
        this.$refs.treeGroupCase?.setCurrentKey(data.id) // 树当前选中
        this.$refs.treeGroupCase?.getNode(data.id)?.expand() // 手动展开
        this.$emit('submit', data)
      })
      if (node.level === 1) {
        const wrap = document.querySelector('.custom-tree')
        // 滚动到指定位置
        wrap?.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      }
    },

    // 新增左侧分组
    addMenu() {
      this.menuVisible = true
      this.nameFlag = 'add'
    },
  },
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

:deep(.el-tree-node__content) {
  overflow: auto;
  overflow-x: auto;
}
.custom-tree {
  height: calc(100vh - 250px);
  overflow-y: auto;
  width: fit-content;
  min-width: 330px;
  overflow-x: auto;
  white-space: nowrap;
}

:deep(.operation-icon) {
  opacity: 0;
  padding-left: 10px;
  .is-disabled {
    color: var(--placeholder-color);
  }
}
:deep(.el-tree-node__content:hover) {
  .operation-icon {
    opacity: 1;
  }
}

.treeNode {
  margin-left: 3px;
}
</style>
