<template>
  <div class="container">
    <header class="header">
      <el-icon class="iconfont back"><el-icon-direction-back /></el-icon>
      <span class="text">{{ cardTitle }}</span>
    </header>
    <div class="rightSection">
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="testm-case-version"
            :model="formData"
            :table-ref="$refs['testm-case-version']"
            :default-fileds="defaultFileds"
            show-basic
            :extra="extraData"
            @getTableData="getTableData"
          />
        </template>
        <template slot="actions">
          <el-button :disabled="selected.length !== 2" @click="diffVersion"
            >版本比较</el-button
          >
        </template>
        <template slot="fliter">
          <vone-search-filter
            :extra="extraData"
            :model="formData"
            :default-fileds="defaultFileds"
            @getTableData="getTableData"
          />
        </template>
      </vone-search-wrapper>

      <div :style="{ height: $tableHeight }">
        <vxe-table
          ref="testm-case-table"
          class="vone-vxe-table draggTable"
          border
          height="auto"
          show-overflow="tooltip"
          :loading="pageLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column type="checkbox" width="36" fixed="left" align="center" />
          <vxe-column
            title="用例"
            field="name"
            min-width="320px"
            show-overflow-tooltip
          >
            <template v-slot="{ row }">
              <el-icon
                class="iconfont"
                style="
                  color: var(--main-theme-color, #3e7bfa);
                  margin-right: 4px;
                "
                ><el-icon-testcase
              /></el-icon>
              <span>{{ row.caseKey + ' ' + row.name }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="版本"
            field="version"
            show-overflow-tooltip
            width="200"
          >
            <template v-slot="{ row }">
              <span v-if="row.version">{{ row.version }}</span>
              <span v-else> {{ row.version }}</span>
            </template>
          </vxe-column>
          <vxe-column field="priorityCode" title="优先级" width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.priority">
                <i
                  :class="`iconfont ${prioritMap[scope.row.priority].icon}`"
                  :style="{ color: `${prioritMap[scope.row.priority].color}` }"
                />
                {{ prioritMap[scope.row.priority].name }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            title="维护人"
            field="leadingBy"
            show-overflow-tooltip
            width="200"
          >
            <template v-slot="{ row }">
              <vone-user-avatar
                v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy"
                :avatar-path="row.echoMap.leadingBy.avatarPath"
                :name="row.echoMap.leadingBy.name"
              />
            </template>
          </vxe-column>
          <vxe-column
            title="更新人"
            field="updatedBy"
            show-overflow-tooltip
            width="200"
          >
            <template v-slot="{ row }">
              <vone-user-avatar
                v-if="row.updatedBy && row.echoMap && row.echoMap.updateBy"
                :avatar-path="row.echoMap.updateBy.avatarPath"
                :name="row.echoMap.updateBy.name"
              />
            </template>
          </vxe-column>
        </vxe-table>
        <vone-pagination
          ref="pagination"
          :total="tableData.total"
          @update="getTableData"
        />
      </div>
    </div>
    <el-row v-show="diffable" class="diffContainer">
      <el-col :span="6" class="sameBox">
        <header>相同内容</header>
        <section>
          <span v-for="item in sameList" :key="item" class="item">{{
            nameMap[item]
          }}</span>
        </section>
      </el-col>
      <el-col class="diffBox" :span="18">
        <header class="title">不同内容</header>
        <section class="content">
          <div v-for="key in diffList" :key="key" class="diffKey">
            <template v-for="(item, i) in ['nextVer', 'prevVer']">
              <div :key="item" :class="['version', item]">
                <header class="titleBox">
                  <span class="title">{{ nameMap[key] }}</span>
                  <span class="etag">{{ versionData[i].version }}</span>
                </header>
                <section class="detail">
                  <div class="desc">
                    <div v-if="key === 'stepType'" class="title">
                      {{
                        versionData[i].stepType === 'subclause'
                          ? '步骤描述'
                          : '文本描述'
                      }}
                    </div>
                    <template
                      v-if="
                        key in versionData[i].echoMap ||
                        key === 'requirementId' ||
                        key === 'priority'
                      "
                    >
                      <!-- 优先级 -->
                      <div v-if="key === 'priority'">
                        <i
                          :class="`iconfont ${
                            prioritMap[versionData[i].priority].icon
                          }`"
                          :style="{
                            color: `${
                              prioritMap[versionData[i].priority].color
                            }`,
                          }"
                        />
                        {{ prioritMap[versionData[i].priority].name }}
                      </div>
                      <!-- 责任人 -->
                      <div v-if="key === 'leadingBy'">
                        <vone-user-avatar
                          v-if="
                            versionData[i].leadingBy &&
                            versionData[i].echoMap &&
                            versionData[i].echoMap.leadingBy
                          "
                          :avatar-path="
                            versionData[i].echoMap.leadingBy.avatarPath
                          "
                          :name="versionData[i].echoMap.leadingBy.name"
                        />

                        <span
                          v-else-if="!versionData[i].leadingBy"
                          class="noSetting"
                        >
                          <el-icon class="iconfont" style="font-size: 20px"
                            ><el-icon-icon-light-avatar
                          /></el-icon>
                          <span> 未设置</span>
                        </span>
                      </div>
                      <!-- 关联需求 -->
                      <div v-if="key === 'requirementId'">
                        <span
                          v-if="
                            versionData[i].echoMap &&
                            versionData[i].echoMap.requirement
                          "
                          >{{ versionData[i].echoMap.requirement.name }}</span
                        >
                        <span v-else>{{ versionData[i].requirement }}</span>
                      </div>
                      <!-- 所属分组 -->
                      <div v-if="key === 'treeId'">
                        <span
                          v-if="
                            versionData[i].echoMap &&
                            versionData[i].echoMap.treeId
                          "
                          >{{ versionData[i].echoMap.treeId.name }}</span
                        >
                        <span v-else>{{ versionData[i].treeId }}</span>
                      </div>
                    </template>
                    <!-- 测试步骤 -->
                    <template v-else-if="key === 'stepType'">
                      <el-table
                        v-if="versionData[i].stepType === 'subclause'"
                        ref="stepTable"
                        class="vone-table stepTable"
                        :data="versionData[i].stepData"
                      >
                        <el-table-column
                          prop="caseStepDes"
                          label="步骤描述"
                          show-overflow-tooltip
                        />
                        <el-table-column
                          prop="expectResult"
                          label="预期结果"
                          show-overflow-tooltip
                        />
                        <el-table-column prop="result" label="实际结果" />
                        <el-table-column prop="result" label="执行结果">
                          <template v-slot="{ row }">
                            <div class="basicBox">
                              <el-icon style="color: #3cb540"
                                ><el-icon-success
                              /></el-icon>
                              <el-icon style="color: #fa6b57"
                                ><el-icon-error
                              /></el-icon>
                              <el-icon style="color: #bd7ffa"
                                ><el-icon-warning
                              /></el-icon>
                              <el-icon style="color: #ffbf47"
                                ><el-icon-tips-minus-circle-fill
                              /></el-icon>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <span v-else>{{ versionData[i].testStep }}</span>
                    </template>
                    <!-- 默认显示 -->
                    <p v-else class="paragraph">{{ versionData[i][key] }}</p>
                  </div>
                </section>
              </div>
            </template>
            <svg-icon icon-class="img-vs" class="vsicon" />
          </div>
        </section>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  DirectionBack as ElIconDirectionBack,
  Testcase as ElIconTestcase,
  IconLightAvatar as ElIconIconLightAvatar,
  Success as ElIconSuccess,
  Error as ElIconError,
  Warning as ElIconWarning,
  TipsMinusCircleFill as ElIconTipsMinusCircleFill,
} from '@element-plus/icons-vue'
import {
  getCaseHistoryCompare,
  testProductCaseHistory,
} from '@/api/vone/testmanage'
import setDataMixin from '@/mixin/set-data'

const prioritList = [
  {
    name: '最高',
    code: 5,
    icon: 'el-icon-icon-dengji-zuigao2',
    color: '#FA6A69',
  },
  {
    name: '较高',
    code: 4,
    icon: 'el-icon-icon-dengji-jiaogao2',
    color: '#FA8669',
  },
  {
    name: '普通',
    code: 3,
    icon: 'el-icon-icon-dengji-putong2',
    color: 'var(--main-theme-color,#3e7bfa)',
  },
  {
    name: '较低',
    code: 2,
    icon: 'el-icon-icon-dengji-jiaodi2',
    color: '#5ACC5E',
  },
  {
    name: '最低',
    code: 1,
    icon: 'el-icon-icon-dengji-zuidi2',
    color: '#4ECF95',
  },
]
export default {
  components: {
    ElIconDirectionBack,
    ElIconTestcase,
    ElIconIconLightAvatar,
    ElIconSuccess,
    ElIconError,
    ElIconWarning,
    ElIconTipsMinusCircleFill,
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'version',
          name: '版本',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入版本',
        },
        {
          key: 'priority',
          name: '优先级',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择优先级',
        },
      ],
      pageLoading: false,
      tableOptions: {},
      tableData: {},
      formData: {
        version: '',
        priority: '',
      },
      // 优先级
      prioritList: [
        {
          name: '最高',
          key: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69',
        },
        {
          name: '较高',
          key: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669',
        },
        {
          name: '普通',
          key: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)',
        },
        {
          name: '较低',
          key: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E',
        },
        {
          name: '最低',
          key: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95',
        },
      ],
      prioritMap: {},
      selected: [],
      diffable: false,
      sameList: [],
      diffList: [],
      versionData: [],
      stepData: [],
      nameMap: {
        name: '用例标题',
        intent: '测试意图',
        prerequisite: '前置条件',
        stepType: '测试步骤',
        testStep: '步骤描述',
        expectedResult: '预期结果',
        treeId: '所属分组',
        leadingBy: '维护人',
        priority: '优先级',
        requirementId: '关联需求',
        execTime: '用例预估执行时长',
      },
    }
  },
  computed: {
    cardTitle() {
      return this.diffable ? this.versionData[0]?.name || '' : '历史版本'
    },
  },
  mounted() {
    this.prioritMap = this.prioritList.reduce(
      (r, v) => (r[v.code] = v) && r,
      {}
    )
    this.getTableData()
    this.setData(this.defaultFileds, 'priority', this.prioritList)
  },
  methods: {
    onClose() {
      this.diffable ? (this.diffable = false) : this.$router.back()
    },
    async getTableData() {
      const { caseId } = this.$route.params

      this.formData.caseId = caseId
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const params = {
        ...tableAttr,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      this.pageLoading = true
      const res = await testProductCaseHistory(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableData.total = res.data.total
    },
    // 查询版本比较
    async getCaseVersionCompare() {
      const ids = this.selected.map((v) => v.id)
      const { data, isSuccess, msg } = await getCaseHistoryCompare(ids)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.sameList = []
      this.diffList = []
      const [nextVer, prevVer] = data
      data.forEach((ele) => {
        if (ele.stepType === 'subclause') {
          ele.stepData = JSON.parse(ele.testStep)
        }
      })
      this.versionData = data
      // 排除的对比项
      const excludes = [
        'id',
        'version',
        'caseKey',
        'caseId',
        'echoMap',
        'createTime',
        'createdBy',
        'files',
        'libraryId',
        'productId',
        'stateId',
        'updatedBy',
        'testStep',
        'stepData',
        'updateTime',
      ]
      for (const key in nextVer) {
        if (excludes.includes(key)) continue
        const isStep = key === 'stepType'
        if (isStep) {
          // 同类型且内容相同
          nextVer[key] === prevVer[key] &&
          nextVer['testStep'] === prevVer['testStep']
            ? this.sameList.push(key)
            : this.diffList.push(key)
        } else {
          nextVer[key] === prevVer[key]
            ? this.sameList.push(key)
            : this.diffList.push(key)
        }
      }
    },
    diffVersion() {
      this.diffable = true
      this.getCaseVersionCompare()
    },
    // 设置可选版本
    checkSelectable(row) {
      return (
        this.selected.length < 2 || !!this.selected.find((item) => item === row)
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  height: calc(100vh - 86px);
  border-radius: 4px;
  background-color: var(--main-bg-color);
}
.header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0 16px;
  font-weight: 600;
  font-size: 16px;
  height: 56px;
  padding: 16px;
  border-bottom: 1px solid var(--disabled-bg-color, #ebeef5);

  .back {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 30px;
    height: 30px;
    color: var(--main-theme-color, #3e7bfa);
    box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
    border-radius: 16px;
  }
}
.diffContainer {
  height: calc(100% - 56px);
}

.sameBox {
  height: 100%;
  padding: 16px 30px 16px 16px;
  border-right: 1px solid var(--disabled-bg-color, #ebeef5);

  header {
    font-weight: 500;
    color: var(--main-font-color);
  }

  section {
    display: flex;
    flex-direction: column;
    gap: 12px 0;
    height: calc(100% - 28px);
    margin-top: 12px;
    padding: 12px 16px;
    border: 1px solid var(--disabled-bg-color, #ebeef5);
    border-radius: 4px;
    overflow: auto;
  }
}

.diffBox {
  padding: 16px;
  height: 100%;

  header.title {
    margin-bottom: 12px;
    font-weight: 500;
    color: var(--main-font-color);
  }
  section.content {
    display: flex;
    flex-direction: column;
    gap: 16px 0;
    height: calc(100% - 28px);
    overflow-x: hidden;
    overflow-y: auto;

    .diffKey {
      position: relative;
      display: flex;
      justify-content: center;
      gap: 0 12px;
    }
    .version {
      width: calc(50% - 6px);
      border: 1px solid var(--disabled-bg-color);
      border-radius: 4px;

      &.nextVer {
        border-color: #2d6ef7;
        .titleBox {
          color: #2d6ef7;
          border-color: #2d6ef7;
          background-color: #f0f7ff;
        }
        .etag {
          color: #2d6ef7;
          background-color: #d1e6ff;
        }
      }
    }
    .titleBox {
      position: relative;
      height: 40px;
      padding: 9px 12px;
      color: #6b7385;
      background-color: #d9d9d9;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid var(--disabled-bg-color);
    }
    .etag {
      position: absolute;
      top: 0;
      right: 0;
      width: 46px;
      height: 22px;
      line-height: 22px;
      padding: 0 8px;
      text-align: center;
      color: var(--main-font-color);
      border-radius: 0 4px 0 4px;
      background-color: #e6e6e6;
    }
    section.detail {
      // height: 217px;
      min-height: 160px;
      padding: 12px;
      overflow-x: hidden;
      overflow-y: auto;

      .title {
        color: var(--main-font-color);
        font-weight: 500;
        height: 22px;
        line-height: 22px;
        margin-bottom: 8px;
      }

      .desc {
        min-height: 40px;

        .paragraph {
          margin: 8px 0;
        }
      }
    }

    .vsicon {
      position: absolute;
      top: calc(50% - 16px);
      left: calc(50% - 16px);
      font-size: 32px;
    }
  }
}
// 测试步骤表格
.stepTable {
  :deep() {
    .el-table__cell {
      border-right: 1px solid var(--disabled-bg-color, #ebeef5);
    }
    th.el-table__cell {
      background-color: #f2f3f5;
    }
    tr:hover {
      .el-textarea__inner {
        background-color: var(--hover-bg-color);
      }
    }
  }

  .basicBox {
    display: flex;
    justify-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--placeholder-color);
  }
}

:deep() {
  .table-operation-view {
    margin: 0;
    padding: 12px 16px;
    border-bottom: none;
  }
  // 隐藏表头全选
  .el-table__header-wrapper {
    .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
