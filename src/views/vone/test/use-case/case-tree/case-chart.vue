<template>
  <page-wrapper>
    <el-row
      type="flex"
      justify="space-between"
      align="middle"
      style="margin-bottom: 12px"
    >
      <el-tooltip
        :content="`【${caseName}】${caseType}用例库报表`"
        placement="top"
      >
        <span class="title"
          >【{{
            caseName.length > 15
              ? caseName.name.slice(0, 15) + '...'
              : caseName
          }}】{{ caseType }}用例库报表</span
        >
      </el-tooltip>

      <div class="back" @click="back">
        <el-icon class="iconfont"><el-icon-tips-close /></el-icon>
      </div>
    </el-row>

    <!-- 用例数量人员分布 -->
    <excuteCasesBar
      :echart-data="caseScatterData"
      style="margin-bottom: 10px"
    />

    <!-- 用例趋势 -->
    <div style="height: 300px">
      <vone-echarts-card title="总用例趋势">
        <vone-echarts v-if="trendsData && trendsData.date" :options="options" />
        <vone-empty v-else style="height: 300px" />
      </vone-echarts-card>
    </div>
  </page-wrapper>
</template>

<script>
import { TipsClose as ElIconTipsClose } from '@element-plus/icons-vue'
import excuteCasesBar from './components/excute-cases-bar.vue' // 用例执行数量分布
import {
  findAllTrendByLibraryId,
  findCaseNumberUserd,
} from '@/api/vone/testmanage/index'

export default {
  components: {
    excuteCasesBar,
    ElIconTipsClose,
  },
  data() {
    return {
      caseScatterData: {}, // 用例数量
      trendsData: {},
      options: {},
      caseName: '',
      caseType: '',
    }
  },
  mounted() {
    this.getTrendsData()
    this.getTestProjectUsersScatter()
  },
  methods: {
    // 查询项目人员用例分布
    async getTestProjectUsersScatter() {
      this.caseName = this.$route.query.caseName
      this.caseType = this.$route.query.caseType == 'product' ? '产品' : '场景'

      const res = await findCaseNumberUserd(this.$route.params.caseId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      if (res.data) {
        this.caseScatterData = res.data
      }
    },
    // 查询用例趋势
    async getTrendsData() {
      const res = await findAllTrendByLibraryId(this.$route.params.caseId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.trendsData = res.data

      this.options = {
        color: ['#7486eb', '#6ad2a8', '#f68483'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderWidth: 0,
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        legend: {
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          borderRadius: 5,
          data: [
            {
              name: '总数',
              icon: 'rect',
            },
            {
              name: '新增',
              icon: 'rect',
            },
          ],
          textStyle: {
            // 图例文字的样式
            color: '#8A8F99',
          },
        },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: res.data.date,
          boundaryGap: false,
          splitLine: {
            show: false, // 去掉网格线
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#8A8F99', // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5', // 更改坐标轴颜色
            },
          },
        },
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisTick: {
              // y轴刻度线
              show: false,
            },
            axisLine: {
              show: false, // 不显示坐标轴轴线
            },
            splitLine: {
              // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99', // 更改坐标轴文字颜色
              },
            },
          },
        ],
        series: [
          {
            name: '总数',
            type: 'line',
            symbol: 'none',
            data: res.data.total,
            smooth: false, // 关键点，为true是不支持虚线的，实线就用true
            itemStyle: {
              lineStyle: {
                width: 2,
                type: 'dotted', // 'dotted'虚线 'solid'实线
              },
            },
          },
          {
            name: '新增',
            type: 'line',
            // stack: 'Total',
            symbol: 'none',
            data: res.data.add,
            smooth: false, // 关键点，为true是不支持虚线的，实线就用true
            itemStyle: {
              lineStyle: {
                width: 2,
                type: 'dotted', // 'dotted'虚线 'solid'实线
              },
            },
          },
        ],
      }
    },
    back() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 600;
  color: #202124;
}
.back {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 30px;
  height: 30px;
  box-shadow: 0px 4px 12px rgb(32 33 36 / 5%);
  color: var(--main-theme-color, #3e7bfa);
  background: var(--main-bg-color, #fff);
  border-radius: 50%;
  i {
    font-size: 24px;
  }
}
.custom-theme-dark {
  .title {
    color: #fff;
  }
  .back {
    background-color: #333947;
    box-shadow: 0px 4px 12px rgb(0 0 0 / 16%);
  }
}
</style>
