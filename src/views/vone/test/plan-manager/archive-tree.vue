<template>
  <div style="width: 100%; text-align: center">
    <el-input
      v-if="type == 'fileTree' && status"
      v-model="filterText"
      class="tree"
      placeholder="搜索"
      :prefix-icon="ElIconSearch"
    />
    <div v-if="type == 'fileTree' && status" class="file-tree">
      <el-tree
        ref="flietreeGroupCase"
        accordion
        node-key="id"
        :load="archiveCaseLoad"
        lazy
        :current-node-key="currentCasekey"
        :highlight-current="true"
        :filter-node-method="filterNode"
        :props="defaultProps"
        style="padding-left: 8px"
        @node-click="treefileCaseClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <span :title="node.label" class="item">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-icon-wenjianjia-weifenzu" />
            </svg>

            <el-tooltip
              v-if="data.description"
              class="item"
              effect="dark"
              placement="top-start"
            >
              <div slot="content">{{ data.description }}</div>
              <span class="treeNode">{{ node.label }}</span>
            </el-tooltip>
            <span v-else class="treeNode">{{ node.label }}</span>
          </span>
        </span>
      </el-tree>
    </div>

    <div v-else-if="type !== 'fileTree' && !status">
      <svg-icon
        style="width: 240px; height: 168px; margin-top: 70px"
        icon-class="img-huishouzhan"
      />
      <div class="catch">回收站</div>
      <div style="color: #838a99">用来存放用户临时删除的计划</div>
    </div>
    <div v-else>
      <svg-icon
        style="width: 240px; height: 168px; margin-top: 70px"
        icon-class="img-yiguidang"
      />
      <div class="catch">已归档</div>
      <div style="color: #838a99">处理完并且具有保存价值的计划</div>
    </div>
  </div>
</template>

<script>
import { Search as ElIconSearch } from '@element-plus/icons-vue'
import { catchErr } from '@/utils'
import { findTestPlanArchiveGroupTreeByTreeId } from '@/api/vone/testplan'
export default {
  data() {
    return {
      filterText: '',
      currentCasekey: '',
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      ElIconSearch,
    }
  },
  props: {
    libraryId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    // 显示全部树
    status: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    filterText(val) {
      // 监听的是左侧的计划列表,和下边filterNode方法配合
      this.$refs['flietreeGroupCase'].filter(val)
    },
  },
  created() {
    // 查询回收站数据
    this.type == 'catchTree' && this.$emit('submit')
  },
  methods: {
    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 已归档用例树懒加载方法
    async archiveCaseLoad(node, resolve) {
      // 节点id
      const rootId = node.level == 0 ? 0 : node.data.id
      const [res, err] = await catchErr(
        findTestPlanArchiveGroupTreeByTreeId(rootId)
      )
      if (err) return
      if (node.level === 0) {
        const [result, err] = await catchErr(
          findTestPlanArchiveGroupTreeByTreeId(res.data[0].id)
        )
        this.$refs.flietreeGroupCase?.setCurrentKey(result.data[0].id)
        this.$emit('submit', result.data[0])

        return resolve(err ? [] : result.data)
      }
      resolve(res.data)
    },
    refreshNode(id) {
      const node = this.$refs.flietreeGroupCase.getNode(id)
      node.loaded = false

      node.expand() // 主动调用展开节点方法,重新查询该节点下的所有子节点
    },
    // 点击归档节点查询数据
    treefileCaseClick(data) {
      this.refreshNode(data.id)
      // this.getTableData()
      this.$emit('submit', data)
    },
  },
}
</script>

<style lang="scss" scope>
.file-tree {
  margin-top: 11px;
  height: calc(100vh - 215px);
  overflow-y: auto;
  min-width: 100%;
  overflow-x: auto;
  .el-tree-node__content {
    height: 36px;
  }
}

/* .initalTree{
	height: calc(100vh - 220px);
		overflow-y:scroll;
} */
.catch {
  font-weight: 500;
  margin: 12px 0;
}
</style>
