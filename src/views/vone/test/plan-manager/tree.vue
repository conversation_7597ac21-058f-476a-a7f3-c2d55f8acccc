<template>
  <div>
    <el-row type="flex" class="execBySt">
      <el-select
        v-model="formData.execBy"
        style="width: 150px"
        clearable
        @change="getAllExecPlanTree(formData.execBy, filterText)"
      >
        <div v-if="formData.execBy" slot="prefix" class="select-header">
          <vone-user-avatar
            :avatar-path="loginUser.avatarPath"
            :avatar-type="loginUser.avatarType"
            :show-name="false"
            height="22px"
            width="22px"
          />
        </div>
        <el-option
          v-for="item in selectData"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
          <vone-user-avatar
            :avatar-path="item.avatarPath"
            :avatar-type="item.avatarType"
            :show-name="false"
            height="22px"
            width="22px"
          />
          {{ item.name }}
        </el-option>
      </el-select>
      <!-- <el-form-item label="执行人" prop="execBy"> -->
      <!-- <vone-remote-user v-model="formData.execBy"  /> -->
      <!-- </el-form-item> -->
      <span class="blueTag"><span>待执行用例</span>{{ count || 0 }}</span>
    </el-row>
    <div class="caseTreeInput">
      <el-input
        v-model="filterText"
        placeholder="搜索"
        :prefix-icon="ElIconSearch"
      />
    </div>

    <div class="treebox">
      <div v-loading="leftLoading" class="custom-tree">
        <!-- 执行用例树 -->
        <el-tree
          v-show="formData.execBy || filterText"
          ref="treeGroupCase"
          node-key="id"
          draggable
          default-expand-all
          :data="treeData"
          :current-node-key="currentCasekey"
          :highlight-current="true"
          :filter-node-method="filterNode"
          :allow-drop="collapse"
          :props="defaultProps"
          class="initalTree"
          @node-click="treeCaseClick"
          @node-drop="handleDrop"
          @node-expand="treeExpand"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node draggable-node"
            style="width: 100%"
          >
            <span>
              <svg v-if="data.name == '未分组'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-wenjianjia-weifenzu" />
              </svg>

              <svg v-else-if="node.level !== 1" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-wenjianjia2" />
              </svg>

              <span :class="{ rootNode: node.level == 1 }" class="treeNode">{{
                node.label
              }}</span>
              <!-- <span v-if="data.name!=='未分组'" class="operation-icon" /> -->
            </span>
          </span>
        </el-tree>
        <el-tree
          v-show="!formData.execBy && !filterText"
          ref="treeGroupCase"
          node-key="id"
          draggable
          :load="loadNode"
          lazy
          :current-node-key="currentCasekey"
          :highlight-current="true"
          :filter-node-method="filterNode"
          :allow-drop="collapse"
          :props="defaultProps"
          class="initalTree"
          @node-click="treeCaseClick"
          @node-drop="handleDrop"
          @node-expand="treeExpand"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node draggable-node"
            style="width: 100%"
          >
            <span>
              <svg v-if="data.name == '未分组'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-wenjianjia-weifenzu" />
              </svg>
              <svg v-else-if="node.level !== 1" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-wenjianjia2" />
              </svg>

              <span :class="{ rootNode: node.level == 1 }" class="treeNode">{{
                node.label
              }}</span>
            </span>
            <span v-if="data.name !== '未分组'" class="operation-icon">
              <el-button
                type="text"
                size="mini"
                :icon="el-icon-setting"
                :disabled="
                  !$permission('testm_case_tree_add') || node.level >= depth
                "
                @click.stop="() => addNode(data)"
              />

              <el-button
                v-if="node.level != 1"
                type="text"
                size="mini"
                :icon="el-icon-setting"
                :disabled="!$permission('testm_case_tree_put')"
                @click.stop="() => editNode(data, node)"
              />
              <el-button
                v-if="node.level != 1"
                type="text"
                size="mini"
                :icon="el-icon-setting"
                @click.stop="setPermission(data, node)"
              />
              <el-button
                v-if="node.level != 1"
                type="text"
                size="mini"
                :icon="el-icon-setting"
                :disabled="!$permission('case_tree_del')"
                @click.stop="() => removeNode(data, node)"
              />
            </span>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 新增编辑分组树弹窗 -->
    <menuTree
      v-model="menuDialog.visible"
      v-bind="menuDialog"
      @success="submitR(menuDialog.data)"
      @close="() => {}"
    />
    <!-- 权限分配弹窗 -->
    <Division v-model="permissionVisible" :tree-id="menuNode.id" />
    <!-- 删除用例树节点 -->
    <delNodeDialog
      v-if="delNodeParam.visible"
      v-bind="delNodeParam"
      v-model="delNodeParam.visible"
      @sureDel="deleteInfo"
    />
    <!-- @success="delSave(delNodeParam)" -->
  </div>
</template>

<script>
import {
  getAllExecPlan,
  findTestPlanGroupTreeByTreeId,
  updateById,
  deleteTreeById,
} from '@/api/vone/testplan'
import Division from './components/division.vue'
import menuTree from './components/menu-tree.vue'
import storage from 'store'
import { debounce } from 'lodash'
import delNodeDialog from '@/views/vone/test/use-case/case-tree/components/del-node-dialog.vue'

export default {
  components: {
    menuTree,
    Division,
    delNodeDialog,
  },
  props: {
    libraryId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    // 保存的需打开的节点
    activeNodes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      delNodeParam: { visible: false },
      count: 0,
      treeData: [], // 执行人的计划树
      permissionVisible: false, // 权限分配弹窗
      currentPlanKey: '',
      editType: '',
      depth: '',
      menuVisible: false,
      leftLoading: false, // 左侧树加载
      filterText: '',
      currentCasekey: '',
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'leaf',
      },
      nameFlag: '',
      menuNode: {}, // 选中节点数据
      currentNode: {},
      formData: {
        tabName: [],
      },
      selectData: [],
      menuDialog: {
        visible: false,
        menuType: 'add',
        data: null,
      },
      activeData: {},
      activenode: {},
    }
  },
  computed: {
    loginUser() {
      return storage.get('user')
    },
  },
  watch: {
    filterText(val) {
      this.debouncedGettree()

      // 监听的是左侧的计划列表,和下边filterNode方法配合
      // this.$refs['treeGroupCase'].filter(val)
    },
  },
  created() {
    this.depth = Number(this.$route.params.depth)
  },
  mounted() {
    this.selectData.push(this.loginUser)
    // this.rowDrop()
  },
  methods: {
    debouncedGettree: debounce(async function (query) {
      if (!this.filterText && !this.formData.execBy) return
      this.leftLoading = true
      try {
        const res = await getAllExecPlan({
          parentId: this.formData.execBy,
          name: this.filterText,
        })

        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.leftLoading = false
        const tree = res.data ? res.data.tree : ''
        this.treeData = tree || []
        this.count = res.data?.count
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),
    async getAllExecPlanTree(val, name) {
      this.leftLoading = true
      if (val || name) {
        const { data } = await getAllExecPlan({ parentId: val, name: name })
        this.leftLoading = false
        const tree = data ? data.tree : ''
        this.treeData = tree || []
        this.count = data?.count
      } else {
        this.leftLoading = false
        this.count = 0
      }
    },

    // 权限分配
    setPermission(data) {
      this.permissionVisible = true
      this.menuNode = data
    },

    treeExpand(data, node) {
      this.$nextTick(() => {
        this.$refs.treeGroupCase?.setCurrentKey(data?.id) // 树当前选中
        this.$refs.treeGroupCase?.getNode(data?.id)?.expand() // 手动展开
        this.$emit('submit', data)
      })
      if (node.level === 1) {
        const wrap = document.querySelector('.custom-tree')
        // 滚动到指定位置
        wrap?.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      }
    },
    // 删除节点
    delSave(node) {
      this.refreshNode(node.data.parentId)
    },
    // 新增和编辑节点
    submitR(node) {
      if (this.menuDialog.menuType == 'add') {
        this.refreshNode(node?.id)
      } else {
        this.refreshNode(node?.parentId)
      }
    },
    refreshNode(id) {
      const node = this.$refs.treeGroupCase.getNode(id)
      node.loaded = false

      node.expand() // 主动调用展开节点方法,重新查询该节点下的所有子节点
    },
    // 默认显示节点树懒加载方法
    async loadNode(node, resolve) {
      // 节点id
      const rootId = node.level == 0 ? 0 : node?.data?.id

      const res = await findTestPlanGroupTreeByTreeId(rootId)

      if (node.level === 0) {
        // 设置默认节点
        // this.menuNode = res.data[0]
        // this.currentPlanKey = this.menuNode?.id
        // this.getTableData()

        this.$nextTick(() => {
          // this.$refs.treeGroupCase?.setCurrentKey(this.currentPlanKey) // 树当前选中
          this.$refs.treeGroupCase?.getNode(res.data[0]?.id)?.expand() // 手动展开
          this.$emit('submit', this.menuNode)
        })
      }
      if (node.level === 1) {
        res.data.unshift({
          id: 0,
          name: '未分组',
          leaf: true,
          treeId: '',
        })
        // 设置默认节点
        this.menuNode = res.data[1] || {}
        this.currentPlanKey = this.menuNode?.id
        // this.getTableData()

        this.$nextTick(() => {
          this.$refs.treeGroupCase?.setCurrentKey(this.currentPlanKey) // 树当前选中
          // this.$refs.treeGroupCase?.getNode(this.currentPlanKey)?.expand() // 手动展开
          this.$emit('submit', this.menuNode)
        })
      }
      // 打开节点
      if (node.level >= 1) {
        if (this.activeNodes.length > 0) {
          const nodeId = this.activeNodes.pop()
          this.$nextTick(() => {
            //  设置父节点展开
            const node = this.$refs.treeGroupCase?.getNode(nodeId)
            node?.expand()
            // 当前需要选中的节点
            if (this.activeNodes.length === 0) {
              this.$refs.treeGroupCase?.setCurrentKey(nodeId) // 树当前选中
              this.currentNode = node
              this.$emit('submit', node?.data)
            }
          })
        }
      }

      resolve(res.data)
      // 加载拖拽方法
      this.$nextTick(() => {
        const className = document.querySelectorAll('.draggable-node')
        this.$emit('treeDrop', className)
      })
    },

    collapse(draggingNode, dropNode, type) {
      if (dropNode.level == 1) {
        return type == 'inner'
      } else if (draggingNode.data.leaf) {
        return false
      } else if (dropNode.data.leaf) {
        return false
      } else {
        return true
      }
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      var obj = {
        orgId: draggingNode?.data?.id,
        parentId:
          dropType == 'before' ? dropNode.data.parentId : dropNode?.data?.id,
        preOrgId: this.getChange(
          dropNode.parent.childNodes,
          draggingNode?.data?.id
        ),
      }
      const res = await updateById(obj).catch((e) => {
        if (dropType == 'before') {
          this.refreshNode(dropNode.data.parentId)
          this.refreshNode(draggingNode.data.parentId)
        } else if (dropType == 'inner') {
          this.refreshNode(dropNode?.data?.id)
          this.refreshNode(draggingNode.data.parentId)
        }
        // this.refreshNode()
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
      } else {
        this.$message.success('移动成功')
      }
    },
    getChange(arr, id) {
      var proOrgId = ''
      arr.forEach((item, i) => {
        if (item?.data?.id == id) {
          if (i == 0) {
            proOrgId = null
          } else {
            proOrgId = arr[i - 1].data?.id
          }
        }
      })
      return proOrgId
    },

    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 新增子级节点
    addNode(data) {
      this.menuNode = data
      this.menuDialog = { visible: true, data: data, menuType: 'add' }
    },
    // 编辑节点
    async editNode(data, node) {
      this.menuNode = data
      this.menuDialog = { visible: true, data: data, menuType: 'edit' }
    },

    // 删除节点
    async removeNode(data, node) {
      this.activeData = data
      this.activenode = node
      this.delNodeParam = {
        visible: true,
        node: data,
        message: '删除后该节点下计划将移入到回收站',
        entry: 'testPlan',
      }
    },
    async deleteInfo() {
      const res = await deleteTreeById(this.activeData?.id)
      if (res.isSuccess) {
        this.$message.success('删除成功')

        this.delSave(this.activenode)
      }
    },

    // 点击节点查询数据
    treeCaseClick(data, node) {
      this.currentNode = node
      this.$emit('submit', data, this.formData.execBy)
      this.$emit('changeType')
      if (data.name == '未分组') return
      this.refreshNode(data?.id)
    },
    // 新增左侧分组
    addMenu() {
      this.menuVisible = true
      this.nameFlag = 'add'
    },
    // 保存当前触发的节点
    storeNodeMap() {
      let node = this.currentNode
      const currentList = []
      while (node?.level > 1) {
        currentList.push(node?.data?.id)
        node = node.parent
      }
      storage.set('nodeMap', currentList)
    },
  },
}
</script>

<style lang="scss" scoped>
.execBySt {
  display: flex;
  margin: 4px 16px 8px 16px;
  justify-items: center;
  align-items: center;
  justify-content: space-between;
}
.el-select-dropdown__wrap {
  .el-select-dropdown__item {
    display: flex;
    align-items: center;
    .avatar {
      padding-right: 6px;
    }
  }
}

:deep(.select-header) {
  min-width: 28px;
  height: 100%;
  display: flex;
  align-items: center;
  text-align: center;
  font-size: 14px;
  color: #4983f3;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

:deep(.el-tree-node__content) {
  overflow: auto;
  overflow-x: auto;
}
.custom-tree {
  height: calc(100vh - 250px);
  overflow-y: auto;
  width: fit-content;
  min-width: 330px;
  overflow-x: auto;
  white-space: nowrap;
}

:deep(.operation-icon) {
  opacity: 0;
  padding-left: 10px;
  .is-disabled {
    color: var(--placeholder-color);
  }
}
:deep(.el-tree-node__content:hover) {
  .operation-icon {
    opacity: 1;
  }
}

.treebox {
  width: 330px;
  overflow: auto;
  margin-top: 15px;
}
.caseTreeInput {
  padding: 0 16px;
}
.treeNode {
  margin-left: 3px;
}
.blueTag {
  border: 1px solid var(--main-theme-color);
  line-height: 28px;
  border-radius: 2px 0px 0px 2px;
  padding: 0px 5px 0px 0px;
  color: var(--main-theme-color);
  span {
    background: var(--main-theme-color);
    color: #fff;
    padding: 0px 8px;
    display: inline-block;
    margin-right: 5px;
  }
}
</style>
