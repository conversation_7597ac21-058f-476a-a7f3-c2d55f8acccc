<template>
  <vone-simple-add
    ref="simpleAdd"
    v-model.trim="form.name"
    :rules="rules"
    :model="form"
    label="缺陷标题"
    v-model:file-list="fileList"
    :loading="saveLoading"
    @submit="saveSubmit"
    @open="openCreateDetail"
    @cancel="$emit('cancel')"
  >
    <!-- 人员远程搜索 -->
    <vone-remote-user
      v-model="form.userId"
      style="width: 100px; margin: 0 6px"
    />
    <!-- 缺陷类型下拉列表 -->
    <el-dropdown
      v-model="form.typeCode"
      class="dropList"
      trigger="click"
      @command="changeClass"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="defectMap && form.typeCode">
            <el-row type="flex" align="middle">
              <i
                :class="`iconfont ${defectMap[form.typeCode].icon}`"
                :style="{
                  color: `${
                    defectMap[form.typeCode].color
                      ? defectMap[form.typeCode].color
                      : '#ccc'
                  }`,
                }"
              />
              <span> {{ defectMap[form.typeCode].name }} </span>
              <el-icon class="iconfont el-icon--right"
                ><ElIconDirectionDown
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置分类 </span>
            <el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="defectType && defectType.length > 0">
          <el-dropdown-item
            v-for="item in defectType"
            :key="item.code"
            :command="item.code"
          >
            <span v-if="item.icon">
              <i
                :class="`iconfont ${item.icon}`"
                :style="{ color: `${item.color ? item.color : '#ccc'}` }"
              />
            </span>
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选分类" />
      </el-dropdown-menu>
    </el-dropdown>

    <el-dropdown
      v-model="form.priorit"
      trigger="click"
      class="dropList"
      @command="changePriorit"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="prioritMap && form.priorit">
            <i
              class="iconfont"
              :class="prioritMap[form.priorit].icon"
              :style="{
                color: prioritMap[form.priorit].color,
                fontSize: '14px',
              }"
            />
            <span> {{ prioritMap[form.priorit].name }} </span>
            <el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown
            /></el-icon>
          </span>
          <span v-else>
            <span> 未设置优先级 </span>
            <el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="prioritList.length > 0">
          <el-dropdown-item
            v-for="item in prioritList"
            :key="item.code"
            :command="item.code"
          >
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选环境" />
      </el-dropdown-menu>
    </el-dropdown>
    <el-dropdown
      v-model="form.envCode"
      trigger="click"
      class="dropList"
      @command="changeEnv"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="envMap && form.envCode">
            <span> {{ envMap[form.envCode].name }} </span>
            <el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown
            /></el-icon>
          </span>
          <span v-else>
            <span> 未设置环境 </span>
            <el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="envList && envList.length > 0">
          <el-dropdown-item
            v-for="item in envList"
            :key="item.code"
            :command="item.code"
          >
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选环境" />
      </el-dropdown-menu>
    </el-dropdown>
    <el-popover v-if="!noFile" width="517" trigger="hover">
      <vone-upload
        ref="uloadFile"
        biz-type="ISSUE_FILE_UPLOAD"
        @change="onChange"
      />
      <vone-empty v-if="fileList.length === 0" desc="暂无附件" />
      <el-icon
        class="iconfont"
        style="cursor: pointer; color: var(--main-theme-color); margin: 0 12px"
        ><ElIconApplicationAttachment
      /></el-icon>
    </el-popover>
  </vone-simple-add>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  ApplicationAttachment as ElIconApplicationAttachment,
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import storage from 'store'
import { apiAlmBugAdd } from '@/api/vone/project/defect'

import { apiAlmPriorityNoPage } from '@/api/vone/alm'

export default {
  components: {
    ElIconDirectionDown,
    ElIconApplicationAttachment,
  },
  props: {
    testCaseId: {
      type: Number,
      default: null,
    },
    noFile: Boolean,
    // 缺陷分类
    defectType: {
      type: Array,
      default: () => [],
    },
    // 环境列表
    envList: {
      type: Array,
      default: () => [],
    },
    // 用例数据
    caseData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {
        name: '',
        typeCode: '',
        userId: '',
        priorit: '',
        envCode: '',
      },
      fileList: [],
      prioritList: [],
      prioritMap: {},
      defectMap: {}, // 缺陷分类
      envMap: {},
      saveLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入缺陷标题', trigger: 'blur' },
          {
            pattern: '^.{1,250}$',
            message: '请输入不超过250个字符组成的标题',
            trigger: 'change',
          },
        ],
      },
    }
  },
  watch: {
    envList(val) {
      if (val.length > 0) {
        this.envMap = this.envList.reduce((r, v) => (r[v.code] = v) && r, {})

        this.form.envCode = this.envList.filter((v) =>
          v.name.includes('功能测试环境')
        )[0].code
      }
    },
    defectType(val) {
      if (val.length > 0) {
        this.defectMap = this.defectType.reduce(
          (r, v) => (r[v.code] = v) && r,
          {}
        )

        this.form.typeCode = this.defectType.filter((v) =>
          v.name.includes('功能缺陷')
        )[0].code
      }
    },
  },
  mounted() {
    this.getPrioritList()
    if (this.defectType.length > 0) {
      this.defectMap = this.defectType.reduce(
        (r, v) => (r[v.code] = v) && r,
        {}
      )

      this.form.typeCode = this.defectType.filter((v) =>
        v.name.includes('功能缺陷')
      )[0].code
    }
    if (this.envList.length > 0) {
      this.envMap = this.envList.reduce((r, v) => (r[v.code] = v) && r, {})
      this.form.envCode = this.envList.filter((v) =>
        v.name.includes('功能测试环境')
      )[0].code
    }
  },
  methods: {
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.prioritMap = res.data.reduce((r, v) => (r[v.code] = v) && r, {})

      // this.form.priorit = this.prioritList[0].code

      this.form.priorit = this.prioritList.filter((v) =>
        v.name.includes('普通')
      )[0].code
    },
    // 打开新建详细数据弹窗
    openCreateDetail() {
      this.$emit('createDetail')
      this.$emit('cancel')
    },
    // 切换类型
    changeClass(value) {
      this.$set(this.form, 'typeCode', value)
    },
    changeUser(v) {
      this.$set(this.form, 'userId', v)
    },
    // 选择环境
    changeEnv(v) {
      this.$set(this.form, 'envCode', v)
    },
    changePriorit(v) {
      this.form.priorit = v
    },
    onChange(val) {
      this.fileList = val
    },
    // 新增缺陷
    async saveSubmit() {
      const userInfo = storage.get('user')

      const params = {
        sourceCode: 'BASIC_SCENES',
        name: this.form.name,
        typeCode: this.form.typeCode,
        estimateHour: 1,
        priorityCode: this.form.priorit || 'HIGH',
        envCode: this.form.envCode,
        createdBy: userInfo.id,
        putBy: userInfo.id,
        handleBy: this.form.userId,
        leadingBy: userInfo.id,
        projectId: '0',
        testPlanId: this.$route.params.planId,
        testcaseId: this.caseData.id,
        planEtime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        planStime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        files: this.fileList.length ? this.fileList : [],
        description: '',
      }
      this.saveLoading = true
      const res = await apiAlmBugAdd(params)
      this.saveLoading = false
      if (res.isSuccess) {
        this.$message.success('创建成功')
        this.$emit('success')

        this.form = {
          name: '',
          typeCode: this.defectType.filter((v) =>
            v.name.includes('功能缺陷')
          )[0].code,
          userId: userInfo.id,
          priorit: this.prioritList.filter((v) => v.name.includes('普通'))[0]
            .code,
          envCode: this.envList.filter((v) =>
            v.name.includes('功能测试环境')
          )[0].code,
        }
        this.$refs.simpleAdd.reset()
        this.fileList = []
        this.$refs['uloadFile'].resetData()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dropList {
  min-width: 60px;
  margin: 0 6px;
}
.textTitle {
  white-space: nowrap;
}
</style>

<style>
.usersMenu {
  max-height: 350px;
  overflow-y: auto;
}
</style>
