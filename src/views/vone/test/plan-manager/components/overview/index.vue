<template>
  <div>
    <el-card style="margin-bottom: 10px">
      <el-row class="countRow">
        <header style="padding: 0">
          <div class="title">{{ $route.query.planName }}</div>
          <el-icon class="back iconfont"><ElIconSetting /></el-icon>
        </header>
        <el-row class="chartBox">
          <el-row class="item">
            <div class="itemIS">
              <div class="itemNum">{{ data.noExec }}</div>
              <div>未分配</div>
            </div>
          </el-row>
          <el-row class="item">
            <div class="itemIS">
              <div class="itemNum">{{ data.sum }}</div>
              <div>测试用例总数</div>
            </div>
          </el-row>
          <el-row class="item">
            <div class="itemIS">
              <div class="itemNum">{{ data.user }}</div>
              <div>测试人员</div>
            </div>
          </el-row>
          <el-row class="item">
            <div class="itemIS">
              <div class="itemNum">{{ data.execed }}</div>
              <div>已执行用例数</div>
            </div>
          </el-row>
          <el-row class="item">
            <div>
              <div
                style="font-size: 28px; color: var(--main-theme-color, #3e7bfa)"
              >
                {{ data.percent > 0 ? (data.percent * 100).toFixed(2) : 0 }}
                <span style="font-size: 16px; color: #8a8f99">%</span>
              </div>

              <div>执行率</div>
            </div>
          </el-row>
        </el-row>
      </el-row>
    </el-card>

    <el-row :gutter="12" type="flex" style="height: 241px; margin-bottom: 10px">
      <el-col :span="12">
        <testReport title="测试用例执行状态" />
      </el-col>

      <el-col :span="12">
        <caseStatus title="新增缺陷个数" />
      </el-col>
    </el-row>
    <el-row style="height: 291px">
      <caseDoing title="人员分配用例个数" />
    </el-row>
  </div>
</template>

<script>
import { TipsClose as ElIconTipsClose } from '@element-plus/icons-vue'

import testReport from './components/test-report.vue'
import caseStatus from './components/case-status.vue'
import caseDoing from './components/case-doing.vue'

import { getOverview } from '@/api/vone/testplan/overview'
export default {
  components: {
    testReport,
    caseStatus,
    caseDoing,
    ElIconTipsClose,
  },
  data() {
    return {
      data: {},
      visible: false,
      requirePro: {},
      statusFun: {},
      trendsParams: {
        visible: false,
      },
    }
  },
  mounted() {
    this.getOverview()
  },
  methods: {
    backPage() {
      this.$router.back()
    },
    async getOverview() {
      const res = await getOverview(this.$route.params.planId)
      this.data = res.data
    },
    refreshData(val) {
      this.$refs.caseNum?.getData(val)
      this.$refs.casePlan?.getData(val)
      this.$refs.bugNum?.getData(val)
    },
    addReport() {
      this.$router.push({
        name: 'testm_report_list',
      })
    },
    trendsChart() {
      this.trendsParams = {
        visible: true,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.countRow {
  background-color: var(--main-bg-color, #fff);
  border-radius: 6px;
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 12px 16px 16px;
  }
  .title {
    font-weight: 600;
    font-size: 14px;
    color: var(--main-font-color);
  }

  .chartBox {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 160px;
    background-color: #f0f5ff;
    border-radius: 4px;
    .item {
      flex: 1;
      text-align: center;

      .itemIS {
        border-right: 1px solid #c1c8d6;
        margin: auto;
        .itemNum {
          font-size: 28px;
          color: var(--main-theme-color, #3e7bfa);
        }
      }
    }
    .boxItem {
      width: 27.27%;
    }
  }
}
.back {
  cursor: pointer;
  width: 30px;
  height: 30px;
  margin-right: 20px;
  color: var(--main-theme-color, #3e7bfa);
  background-color: var(--hover-bg-color);
  box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
  border-radius: 16px;
  line-height: 30px;
  text-align: center;
}
.custom-theme-dark {
  .chartBox {
    background-color: #333947;
  }
}
</style>
