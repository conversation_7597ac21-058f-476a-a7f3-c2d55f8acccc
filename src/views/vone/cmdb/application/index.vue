<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="application_table"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['application_table']"
          @getTableData="selectAppCompentsDatas"
        />
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          class="ml-16"
          :icon="el-icon-setting"
          :disabled="!$permission('cmdb_application_add')"
          @click="addAplication"
          >新增</el-button
        >
        <el-dropdown @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="selectAppCompentsDatas"
        />
      </template>
    </vone-search-wrapper>
    <main :style="{ height: tableHeight }">
      <vxe-table
        ref="application_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="loading"
        :empty-render="{ name: 'empty' }"
        :data="appDatas.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="名称" field="name" fixed="left">
          <template #default="{ row }">
            <a @click="infoDrawer(row)">{{ row.name }}</a>
          </template>
        </vxe-column>
        <vxe-column
          field="appComponentsTypeKey"
          title="类型"
          class-name="type_column"
        >
          <template #default="{ row }">
            <el-tooltip v-if="row.appComponentsTypeKey" placement="top">
              <div slot="content">{{ row.appComponentsTypeKey }}</div>
              <span>
                <svg-icon
                  v-if="row.appIconName == 'ApacheTomcat'"
                  icon-class="apache"
                />
                <svg-icon
                  v-else-if="row.appIconName == 'WebLogic'"
                  icon-class="weblogic"
                />
                <svg-icon
                  v-else-if="row.appIconName == 'JVM'"
                  icon-class="JVM"
                />
                <svg-icon
                  v-else-if="row.appIconName == 'WebSphere'"
                  icon-class="websphere"
                />
                <svg-icon
                  v-else-if="row.appIconName == 'Nginx'"
                  icon-class="nginx-1"
                />
                <span v-else />
              </span>
            </el-tooltip>
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column field="serverIp" title="IP" />
        <vxe-column field="consolePort" title="端口" />
        <vxe-column title="服务应用" field="SYSTEM_NAME">
          <template #default="{ row }">
            <span v-if="row.echoMap && row.echoMap.application">{{
              row.echoMap.application.name
            }}</span>
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column title="环境" field="ENVIRONMEN_ID">
          <template #default="{ row }">
            <span
              v-if="row.applicationDeploy"
              style="
                background: rgb(232, 252, 250);
                color: rgb(75, 204, 187);
                padding: 4px 8px;
                display: inline-block;
                border-radius: 4px;
              "
              >{{ row.applicationDeploy.envKey }}</span
            >
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column title="下发状态" field="sendStatus">
          <template #default="{ row }">
            <span v-if="row.sendStatus">
              {{ row.sendStatus.desc }}
            </span>
            <span v-else>
              {{ row.sendStatus }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="下发时间" field="sendTime" width="165" sortable />

        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('cmdb_application_edit')"
                :icon="el-icon-setting"
                @click="update(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('cmdb_application_delete')"
                :icon="el-icon-setting"
                @click="deleteById(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown :hide-on-click="false" @command="(e) => e && e(row)">
              <el-button
                type="text"
                :icon="el-icon-setting"
                class="operation-dropdown"
              />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="!$permission('cmdb_application_authority')"
                  :icon="el-icon-setting"
                  :command="() => resDivision(row)"
                >
                  <span>权限分配</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('cmdb_application_send')"
                  :icon="ElIconDocument"
                  :command="() => updataCofig(row)"
                >
                  <span>更新参数文件</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination
      ref="pagination"
      :total="appDatas.total"
      @update="selectAppCompentsDatas"
    />

    <!-- 基本信息抽屉 -->
    <basicInfoDrawer
      v-if="basicDrawerParam.visible"
      v-bind="basicDrawerParam"
      v-model="basicDrawerParam.visible"
      :table-list="tableList"
    />

    <!-- 权限分配抽屉 -->
    <authDivision
      v-if="divisionDrawerParam.visible"
      v-bind="divisionDrawerParam"
      v-model="divisionDrawerParam.visible"
    />

    <sendFile
      v-if="sendFileParam.visible"
      v-bind="sendFileParam"
      v-model="sendFileParam.visible"
    />
  </page-wrapper>
</template>

<script>
import basicInfoDrawer from './basicInfo-drawer'
import sendFile from './send-file.vue'
import authDivision from './auth-division.vue'

import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import {
  apiBaseDeleteAppCompById,
  apiBaseGetApplyList,
  apiBasesendEnvConfig,
} from '@/api/vone/cmdb/application'
import { apiCmdbServerNoPage } from '@/api/vone/cmdb/server'
import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    basicInfoDrawer,
    sendFile,
    authDivision,
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
        {
          key: 'appComponentsTypeKey',
          name: '类型',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择应用组件类型',
        },
        {
          key: 'serverIp',
          name: 'IP',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
        {
          key: 'serverPort',
          name: '端口',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入端口',
        },
        {
          key: 'applicationId',
          name: '服务应用',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择服务应用',
          valueType: 'id',
        },
      ],
      tableList: [],

      formData: {
        name: '',
        appComponentsTypeKey: '',
        serverIp: '',
        serverPort: '',
        applicationId: '',
      },
      loading: false,
      divisionDrawerParam: { visible: false },
      basicDrawerParam: { visible: false },
      sendFileParam: { visible: false },

      appDatas: {},
      applyTypeList: [], // 类型

      tableSelected: [], // 选中的数据
      actions: [
        {
          name: '批量授权',
          // icon: 'iconfont el-icon-application-user-permission',
          fn: () => {
            if (!this.tableSelected.length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.resDivisionAll(this.tableSelected)
          },
          disabled: !this.$permission('cmdb_application_authority'),
        },
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete',
          fn: () => {
            if (!this.tableSelected.length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.deleteById(this.tableSelected.map((i) => i.id))
          },
          disabled: !this.$permission('cmdb_application_delete'),
        },
        {
          name: '批量更新参数文件',
          // icon: 'el-icon-document',
          fn: () => {
            if (!this.tableSelected.length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.updataCofig(this.tableSelected.map((i) => i.id))
          },
          disabled: !this.$permission('cmdb_application_send'),
        },
      ],
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  mounted() {
    // 获取服务应用数据
    this.getApplicationList()
    // this.selectAppCompentsDatas()
    this.getAppComponents()
  },
  methods: {
    // 获取table 选中的行
    selecteTableData(val) {
      this.tableSelected = val
    },
    // 查询服务应用
    async getApplicationList() {
      const { data, isSuccess } = await apiCmdbServerNoPage()
      if (!isSuccess) {
        return
      }

      this.setData(this.defaultFileds, 'applicationId', data)
    },

    // 获取应用组件类型
    async getAppComponents() {
      const params = {
        state: true,
        type: 'APP_COMPONENTS_TYPE', // 应用组件类型
      }
      // 字典批量查询应用组件类型
      const { data, isSuccess } = await apiBaseDictNoPage(params)
      if (!isSuccess) {
        return
      }

      this.setData(this.defaultFileds, 'appComponentsTypeKey', data)
    },
    selectAllEvent({ checked }) {
      this.tableSelected = this.$refs.application_table.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.tableSelected = this.$refs.application_table.getCheckboxRecords()
    },
    // 调用接口获取应用组件列表数据
    async selectAppCompentsDatas() {
      this.loading = true
      let params = {}
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...tableAttr,
        ...sortObj,
        extra: { ...this.extraData },
        model: { ...this.formData },
      }
      const res = await apiBaseGetApplyList(params)
      this.loading = false

      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }

      res.data.records.forEach((element) => {
        element.appIconName = element.appComponentsTypeKey
          ? element.appComponentsTypeKey.split('_')[0]
          : ''
      })

      this.appDatas = res.data
      this.tableList = res.data.records
    },

    infoDrawer(row) {
      this.basicDrawerParam = { visible: true, id: row.id }
    },
    // 新增
    addAplication() {
      this.$router.push({
        name: 'cmdb_application_add',
      })
    },
    // 删除
    async deleteById(row) {
      await this.$confirm('确定删除该应用组件吗?', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
      })

      try {
        const ids = row.id ? [row.id] : row
        this.loading = true
        const { isSuccess, msg } = await apiBaseDeleteAppCompById(ids)
        if (!isSuccess) {
          this.loading = false
          this.$message.error(msg)
          return
        }
        this.$message.success('删除成功')
        this.selectAppCompentsDatas()
      } catch (error) {
        this.loading = false
        return
      }
    },
    // 编辑
    async update(row) {
      this.$router.push({
        // name: 'cmdb_application_edit',
        // params: { id: row.ID }
        path: '/cmdb/application/edit/' + row.id,
      })
    },
    // 更改参数文件
    async updataCofig(updataId) {
      await this.$confirm('更新后会覆盖原有文件,是否继续更新?', '提示', {
        type: 'warning',
        closeOnClickModal: false,
      })
      this.loading = true
      const { data, isSuccess, msg } = await apiBasesendEnvConfig(
        updataId.length ? updataId : [updataId.id]
      )
      this.loading = false
      if (!isSuccess) {
        return this.$message.error(msg)
      }

      // const successNum = data.map(r => r.isSuccess == true).length
      // const failNum = data.map(r => r.isSuccess == false).length
      this.$message.success(`共更新${data.length}个参数文件`)

      this.sendFileParam = { visible: true, tableData: data }

      // this.$message.success('更新成功')
      this.selectAppCompentsDatas()
    },
    // 批量权限分配
    resDivisionAll(table) {
      const appIds = table.map((item) => item.id)
      // this.resDivision(appIds, true)
      this.divisionDrawerParam = {
        visible: true,
        appIds: appIds,
      }
    },
    // 权限分配
    async resDivision(row, isBatch) {
      this.divisionDrawerParam = {
        visible: true,
        id: row.id,
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
