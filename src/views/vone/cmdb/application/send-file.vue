<template>
  <el-dialog
    title="更新参数结果"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
    :before-close="onClose"
  >
    <el-table :data="tableData">
      <el-table-column prop="sendUserName" label="下发人" />
      <el-table-column prop="isSuccess" label="下发结果">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isSuccess" type="success"> 成功 </el-tag>
          <el-tag v-else-if="!scope.row.isSuccess" type="danger"> 失败 </el-tag>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column prop="sendTime" label="下发时间" show-overflow-tooltip />
      <el-table-column prop="message" label="原因" show-overflow-tooltip />
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style></style>
