<template>
  <vone-drawer v-model="visible" size="lg" :before-close="onClose">
    <div slot="title" class="drawer-title">
      <div class="drawer-title-text">
        {{ `应用组件【${appData.name}】详情` }}
        <el-icon class="iconfont nextBtn"
          ><el-icon-yibiaopan-shangyi
        /></el-icon>
        <el-icon class="iconfont nextBtn"><ElIconSetting /></el-icon>
      </div>
    </div>
    <div v-loading="loading" class="pageBox">
      <div class="title" style="margin-bottom: 0">
        <strong> 基本信息 </strong>
      </div>
      <div class="formBox" style="padding-top: 0px; padding-bottom: 10px">
        <vone-desc :column="2">
          <vone-desc-item label="组件类型">
            {{ appData.appComponentsTypeKey }}
          </vone-desc-item>
          <vone-desc-item label="组件IP">
            {{ appData.serverIp }}
          </vone-desc-item>
          <vone-desc-item label="组件端口">
            {{ appData.serverPort }}
          </vone-desc-item>
          <vone-desc-item label="控制台端口">
            {{ appData.consolePort }}
          </vone-desc-item>
          <vone-desc-item label="权限用户名">
            {{ appData.permissionUser }}
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="应用目录">
            {{ appData.publishDir }}
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="部署目录">
            {{ appData.deployDir }}
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="脚本目录">
            {{ appData.scriptDir }}
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="Python目录">
            {{ appData.pythonDir }}
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="描述">
            {{ appData.description }}
          </vone-desc-item>
        </vone-desc>
      </div>
      <div class="title">
        <strong> 关联信息 </strong>
      </div>
      <div class="formBox">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <!-- 高级属性 -->
          <el-tab-pane label="高级属性" name="attribute">
            <div style="height: calc(100vh - 495px)">
              <vxe-table
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="attributeData"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="标识" field="key" />
                <vxe-column field="name" title="名称" />
                <vxe-column field="value" title="值">
                  <template #default="{ row }">
                    {{ row.key == 'INIT_PASSWORD' ? '******' : row.value }}
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
          </el-tab-pane>
          <!-- 服务应用 -->
          <el-tab-pane label="服务应用" name="apply">
            <div style="height: calc(100vh - 495px)">
              <vxe-table
                ref="app-apply-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="applyLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="applyData.records"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="应用名称" field="name" />
                <vxe-column field="type" title="CI/CD类型">
                  <template #default="{ row }">
                    <span v-if="row.type">
                      {{ row.type.desc }}
                    </span>
                    <span v-else>{{ row.type }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="applicationEnvs" title="环境">
                  <template #default="{ row }">
                    <span
                      v-if="row.applicationEnvs && row.applicationEnvs.length"
                    >
                      {{ row.applicationEnvs.map((r) => r.envKey).join(',') }}
                    </span>
                    <span v-else>{{ row.applicationEnvs }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="deployVersion" title="运行版本">
                  <template #default="{ row }">
                    {{ row.deployVersion }}
                  </template>
                </vxe-column>
                <vxe-column field="description" title="描述" />
              </vxe-table>
            </div>
            <vone-pagination
              ref="applyPagination"
              :total="applyData.total"
              style="position: static"
              @update="getApplyPage"
            />
          </el-tab-pane>
          <!-- 参数文件 -->
          <el-tab-pane label="参数文件" name="paramFiles">
            <div style="height: calc(100vh - 495px)">
              <vxe-table
                ref="app-paramFiles-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="paramFilesLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="paramFilesData.records"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="下发时间" field="sendTime" />
                <vxe-column field="sendUserName" title="下发人" />
                <vxe-column field="isSuccess" title="结果">
                  <template #default="{ row }">
                    <span v-if="row.isSuccess" class="success">
                      <el-icon class="mr-1"><ElIconSetting /></el-icon>
                      成功
                    </span>
                    <span v-else class="danger">
                      <i class="el-tips-exclamation-circle-fillmr-1" />
                      失败
                    </span>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
            <vone-pagination
              ref="paramFilesPagination"
              :total="paramFilesData.total"
              style="position: static"
              @update="getParamFilesPage"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
    </div>
  </vone-drawer>
</template>

<script>
import {
  YibiaopanShangyi as ElIconYibiaopanShangyi,
  YibiaopanXiayi as ElIconYibiaopanXiayi,
  Success as ElIconSuccess,
} from '@element-plus/icons-vue'
import {
  apiBaseDataFullInfoById,
  apiCmdbSendFiles,
} from '@/api/vone/cmdb/application'
import {
  apiBaseGetapplication,
  apiBaseSelectDeployConfigBySystemId,
} from '@/api/vone/cmdb/server'
export default {
  components: {
    ElIconYibiaopanShangyi,
    ElIconYibiaopanXiayi,
    ElIconSuccess,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: null,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentIndex: undefined, // 当前数据的索引
      activeName: 'attribute',
      loading: false,
      tableLoading: false,
      header: '',
      appData: {},
      userInstanceDatas: [],
      applicationAppCompentsDatas: [],
      sendHistoryDatas: [],
      attributeData: [], // 高级属性
      tableOptions: {},
      applyLoading: false, // 服务应用列表loading
      formDataApply: {
        // 服务应用查询条件
        appComponentsId: this.id,
      },
      tableApplyOptions: {}, // 服务应用操作
      applyData: {}, // 服务应用
      // -----------------------------------参数文件
      paramFilesLoading: false,
      paramFilesOptions: {},
      paramFilesData: {},
      formDataParams: {
        appComponentsId: this.id,
      },
    }
  },
  mounted() {
    this.currentIndex = this.tableList.findIndex((item) => item.id === this.id)
    this.activeName = 'attribute'
    this.selectAppDataById()
  },
  methods: {
    // 分页查询服务应用
    async getApplyPage() {
      this.applyLoading = true
      let params = {}
      const tableAttr = this.$refs.applyPagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataApply },
      }
      const res = await apiBaseGetapplication(params)
      this.applyLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      res.data.records.map((item) => {
        item.deployVersion = ''
        this.getVersion(item.id).then((res) => {
          item.deployVersion = res
        })
      })
      this.applyData = res.data
    },
    async getVersion(id) {
      let version = ''
      await apiBaseSelectDeployConfigBySystemId(id).then((res) => {
        if (res.isSuccess) {
          version = res.data[0].deployVersion
        } else {
          this.$message.warning(res.msg)
        }
      })
      return version
    },
    // 分页查询参数文件
    async getParamFilesPage() {
      this.paramFilesLoading = true
      let params = {}
      const tableAttr = this.$refs.paramFilesPagination?.pageObj || {
        current: 1,
        size: 20,
      }
      this.$set(tableAttr, 'sort', 'sendTime')
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataParams },
      }
      const res = await apiCmdbSendFiles(params)
      this.paramFilesLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.paramFilesData = res.data
    },
    async selectAppDataById(val) {
      this.loading = true
      const { data, isSuccess, msg } = await apiBaseDataFullInfoById(
        val || this.id
      )
      if (!isSuccess) {
        this.loading = false
        return this.$message.error(msg)
      }

      this.appData = data

      this.loading = false
      // 高级属性
      this.attributeData = data.appComponentsExtends
      this.activeName = 'attribute'
    },
    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.selectAppDataById(this.tableList[this.currentIndex].id)
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++
      this.selectAppDataById(this.tableList[this.currentIndex].id)
    },

    handleClick(tab, event) {
      if (tab.name == 'apply') {
        this.getApplyPage()
      } else if (tab.name == 'paramFiles') {
        this.getParamFilesPage()
      }
    },
    onClose() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.pageBox {
  padding: 20px;
  .title {
    border-left: 3px solid var(--main-theme-color);
    margin-bottom: 10px;
    padding-left: 10px;
    color: var(--main-font-color);
  }
  .formBox {
    padding: 10px;
    padding-bottom: 0;
  }
  :deep(.el-form .el-form-item__label) {
    color: var(--auxiliary-font-color);
  }

  :deep(.el-form-item__content) {
    color: var(--auxiliary-font-color);
    display: flex;
  }
}
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
.success {
  color: var(--finished-color);
}
.danger {
  color: red;
}
</style>
