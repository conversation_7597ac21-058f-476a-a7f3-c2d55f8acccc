<template>
  <div>
    <el-dialog
      title="下发构建参数文件"
      width="60%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" class="mt-3">
        <el-form-item label="持续集成引擎" prop="targetId">
          <el-select v-model="form.targetId" @change="masterChange">
            <el-option
              v-for="item in masterList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="存储方式" prop="functionId">{{
          (master && master.dataStorageText) || '-------'
        }}</el-form-item>
        <el-form-item label="下发方式" prop="sendType">
          <el-select
            v-model="form.sendType"
            placeholder="请选择下发方式"
            :disabled="master == null"
          >
            <!-- || master.dataStorage == 'true' -->
            <el-option label="一键下发" :value="0" />
            <el-option label="指定目标机" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="脚本目录" prop="file">{{
          (master && master.scriptDir) || '-------'
        }}</el-form-item>
        <el-form-item label="目标机IP" prop="ips">
          <el-checkbox-group v-if="master" v-model="form.ips">
            <template v-for="ip in master.ips">
              <el-checkbox
                :key="ip.id"
                :disabled="form.sendType == 0"
                :label="ip.engineIp"
              />
            </template>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="_close">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit()"
          >下发</el-button
        >
      </div>
    </el-dialog>
    <!-- 下发成功弹出对话框表格 -->
    <el-dialog
      title="下发结果"
      v-model="sendDialog.visible"
      :close-on-click-modal="false"
      width="70%"
    >
      <el-collapse>
        <el-collapse-item
          v-for="script in sendDialog.result"
          :key="script.fileName"
        >
          <template slot="title">
            <div class="flex-layout">
              <span class="mr-2">{{ script.fileName }}</span>
              <span class="flex" />
              <el-tag size="small" type="success" class="mr-2"
                >成功：{{ script.success }}</el-tag
              >
              <el-tag size="small" type="danger"
                >失败：{{ script.error }}</el-tag
              >
            </div>
          </template>
          <el-table :data="sendDialog.result">
            <el-table-column
              prop="fileName"
              label="脚本名称"
              width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="hostIp"
              label="服务器IP"
              width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="sendPath"
              label="下发路径"
              show-overflow-tooltip
            />
            <el-table-column prop="success" label="结果" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.success" type="success" effect="dark">
                  <el-icon><el-icon-success /></el-icon>成功
                </el-tag>
                <el-tag v-else type="danger" effect="dark">
                  <el-icon><el-icon-error /></el-icon>失败
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="message"
              label="消息"
              width="100"
              show-overflow-tooltip
            />
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  Success as ElIconSuccess,
  Error as ElIconError,
} from '@element-plus/icons'

import { apiCmdbBuildFileSend } from '@/api/vone/cmdb/server'
export default {
  components: {
    ElIconSuccess,
    ElIconError,
  },
  props: {
    index: {
      default: undefined,
      type: Number,
    },
    masterList: {
      default: () => [],
      type: Array,
    },
    slaveList: {
      default: () => [],
      type: Array,
    },
    buildFileIds: {
      // 选中的要下发的参数文件id集合
      default: () => [],
      type: Array,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      master: null,
      ips: [],
      form: {
        targetId: null,
        functionId: null,
        sendType: null,
        file: null,
        ips: [],
      },
      engineList: [],
      rules: {
        targetId: [{ required: true, message: '请选择持续集成引擎' }],
        functionId: [{ required: false }],
        sendType: [{ required: true }],
        description: [{ required: false }],
        file: [{ required: false }],
      },
      sendDialog: {
        visible: false,
        data: [],
      },
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      this.form = {
        targetId: null,
        functionId: null,
        sendType: null,
        file: null,
        ips: [],
      }
    },
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    // 持续集成下拉框change事件
    masterChange(id) {
      this.master = this.masterList.filter((e) => e.id === id)[0]
      this.form.ips = this.master.ips.map((i) => i.engineIp)
      if ('dataStorage' in this.master) {
        this.form.sendType = 0
      }
    },
    remove() {
      this.$emit('remove', this.index)
    },
    _close() {
      this.$emit('update:visible', false)

      this.$refs.form.resetFields()

      this.master = null

      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    async submit() {
      const buildFileSendDatas = []

      if (this.form.ips.length == 0) {
        return this.$message.warning(
          '未选择要下发的目标机信息，无法下发构建参数文件'
        )
      }
      // const slaves = this.slaveList.filter((e) => e.parentId == this.master.id)

      // 如果是共享则只用下发一个主机
      if (this.master.dataStorage == 'true') {
        const buildFileSendData = {}
        buildFileSendData.engineId = this.master.id
        buildFileSendData.hostIp = this.form.ips[0]
        buildFileSendData.destPath = this.master.scriptDir
        const sendSlaveData = this.master.ips.filter(
          (e) => e.engineIp == this.form.ips[0]
        )[0]

        buildFileSendData.consoleUser = sendSlaveData.engineExtendeds.filter(
          (e) => e.key == 'consoleUser'
        )[0].value
        buildFileSendDatas.push(buildFileSendData)
      } else {
        for (let i = 0; i < this.form.ips.length; i++) {
          const buildFileSendData = {}
          buildFileSendData.engineId = this.master.id
          buildFileSendData.hostIp = this.form.ips[i]
          buildFileSendData.destPath = this.master.scriptDir
          const sendSlaveData = this.master.ips.filter(
            (e) => e.engineIp == this.form.ips[i]
          )[0]

          buildFileSendData.consoleUser = sendSlaveData.engineExtendeds.filter(
            (e) => e.key == 'consoleUser'
          )[0].value
          buildFileSendDatas.push(buildFileSendData)
        }
      }

      try {
        this.loading = true
        const res = await apiCmdbBuildFileSend({
          applicationBuildFileSends: buildFileSendDatas,
          buildFileIds: this.buildFileIds,
        })
        this.loading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        res.data.forEach((item) => {
          item.success = res.data.filter((r) => r.success).length
          item.error = res.data.filter((r) => !r.success).length
        })
        this.sendDialog = {
          visible: true,
          result: res.data,
        }

        this.$emit('success')
        this._close()
      } catch (e) {
        this.loading = false
      }
    },
    close() {
      this.sendDialog.visible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.mr-2 {
  margin-right: 5px;
}
</style>
