<template>
  <!-- 构建配置 -->
  <div>
    <vone-search-wrapper>
      <div v-if="this.$route.params.type != 1" slot="actions">
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button
            >操作<el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in tableMenu"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
              >{{ item.name }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </vone-search-wrapper>
    <main style="height: calc(100vh - 446px)">
      <vxe-table
        ref="buildConfig_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <el-table-column
          v-if="this.$route.params.type != 1"
          type="checkbox"
          width="40"
          fixed="left"
          align="center"
        />
        <vxe-column title="文件名称" field="scriptName" />
        <vxe-column field="scriptWarehousePath" title="参数文件仓库路径" />
        <vxe-column field="state" title="状态">
          <template #default="{ row }">
            <span v-if="row.state && row.state.desc">
              {{ row.state.desc }}
            </span>
            <span v-else>
              {{ row.state }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="参数文件版本号" field="scriptWarehouseVersion" />
        <vxe-column title="最后执行时间" field="updateTime" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('cmdb_server_set')"
                :icon="el-icon-setting"
                @click="edit(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('cmdb_server_set')"
                :icon="el-icon-setting"
                @click="scriptDelete(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown :hide-on-click="false" @command="(e) => e && e(row)">
              <el-button
                type="text"
                :icon="el-icon-setting"
                class="operation-dropdown"
              />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="!$permission('cmdb_server_set')"
                  :icon="ElIconTickets"
                  :command="() => allsed(row)"
                >
                  <span>下发脚本</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('cmdb_server_set')"
                  :icon="ElIconRefresh"
                  :command="() => toHistory(row)"
                >
                  <span>下发历史记录</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('cmdb_server_set')"
                  :icon="ElIconDocumentRemove"
                  :command="() => toDeleteTarget(row)"
                >
                  <span>删除目标机脚本</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTable"
    />

    <!-- <el-row v-if="$route.params.type == 0">
        <div style="float: left">
          <el-button v-show="active > 0" @click="stepFoaward">上一步</el-button>
        </div>
        <div v-if="end" style="float: right">
          <el-button type="primary" @click="saveNext">完成</el-button>
        </div>
        <div v-else style="float: right">
          <el-button type="primary" @click="saveNext">保存并下一步</el-button>
        </div>
      </el-row> -->

    <!-- 对话框 -->
    <el-dialog
      v-if="dialogFormVisible"
      title="构建参数文件"
      width="40%"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="构建参数文件" prop="file">
          <vone-upload
            ref="uploadFile"
            biz-type="CMDB_APPLICATION_BUILD_FILE_UPLOAD"
            :files-data="form.file ? form.file : []"
            :file-title="'上传文件'"
            storage-type="LOCAL"
            :tip="'只支持txt类型文件上传,且文件名不能有空格'"
            :accept="'.txt'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer foot">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button
          type="primary"
          :icon="ElIconUpload2"
          :loading="loadLoading"
          @click="submit"
          >上传</el-button
        >
      </div>
    </el-dialog>

    <!-- 修改对话框 -->

    <buildDialog
      v-if="editParams.visible"
      v-bind="editParams"
      v-model="editParams.visible"
      @success="getTable"
    />

    <!-- 下发对话框 -->

    <sendDialog
      v-bind="sendParams"
      v-model="sendParams.visible"
      @success="getTable"
    />

    <!-- 下发历史 -->
    <historyDialog
      v-if="historyParams.visible"
      v-bind="historyParams"
      v-model="historyParams.visible"
    />

    <!-- 删除目标机对话框 -->
    <deleteDialog
      v-if="deleteParams.visible"
      v-bind="deleteParams"
      v-model="deleteParams.visible"
    />
  </div>
</template>

<script>
import {
  apiCmdbBuildFilePage,
  apiCmdbBuildFileImport,
  apiCmdbBuildFileDel,
} from '@/api/vone/cmdb/server'
import { apiBaseEngineNoPage } from '@/api/vone/base/engine'

import buildDialog from './build/buildDialog'
import sendDialog from './build/sendDialog'
import historyDialog from './build/historyDialog'
import deleteDialog from './build/deleteDialog'

export default {
  components: {
    buildDialog,
    sendDialog,
    historyDialog,
    deleteDialog,
  },
  props: {
    type: {
      type: Number,
      default: 0,
    },
    active: {
      type: Number,
      default: 0,
    },
    configListLength: {
      type: Number,
      default: 0,
    },
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (!this.form.file && !this.file.length) {
        callback(new Error('请选择文件'))
      } else {
        callback()
      }
    }
    return {
      file: [],
      tableData: {},
      tableLoading: false,
      loadLoading: false,
      editParams: { visible: false },
      sendParams: { visible: false },
      historyParams: { visible: false },
      deleteParams: { visible: false },
      tableSelected: [],
      faile: false,
      end: false,
      editVisible: false,
      sendVisible: false,
      historyVisible: false,
      targetDeployVisible: false,
      form: {
        file: null,
      },
      dialogFormVisible: false,
      fileList: [],
      tableMenu: [
        {
          name: '新增',
          fn: this.addFile,
        },
        {
          name: '下发',
          fn: () => {
            if (!this.tableSelected.length) {
              this.$message.warning('请至少选择一条数据')
              return
            }

            const sName = this.tableSelected.map((r) => r.scriptName)
            const res = new Map()
            const filterSName = sName.filter(
              (a) => !res.has(a) && res.set(a, a)
            )
            if (sName.length !== filterSName.length) {
              this.$message.warning('不能批量下发文件名称相同的构建参数文件')
              return
            } else {
              this.allsed(this.tableSelected.map((i) => i.id))
            }
          },
        },
      ],

      rules: {
        file: [{ required: true, validator: validatePass, trigger: 'change' }],
      },
      formData: {
        applicationId: this.$route.params.systemId,
      },
      // 表格配置项
      tableOptions: {
        isOperation: true, // 表格有操作列时设置
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              label: '修改', // 功能名称
              icon: 'iconfont el-icon-application-edit',
              handler: this.edit,
              disabled: !this.$permission('cmdb_server_set'),
            },
            {
              label: '删除',
              icon: 'iconfont el-icon-application-delete',
              handler: this.scriptDelete,
              disabled: !this.$permission('cmdb_server_set'),
            },
          ],
          // 更多操作按钮
          moreData: [
            {
              label: '下发脚本',
              icon: 'el-icon-tickets',
              handler: this.allsed,
              disabled: !this.$permission('cmdb_server_set'),
            },
            {
              label: '下发历史记录',
              icon: 'el-icon-refresh',
              handler: this.toHistory,
              disabled: !this.$permission('cmdb_server_set'),
            },
            {
              label: '删除目标机脚本',
              icon: 'el-icon-document-remove',
              handler: this.toDeleteTarget,
              disabled: !this.$permission('cmdb_server_set'),
            },
          ],
        },
      },
    }
  },
  mounted() {
    this.getTable()

    if (this.configListLength == this.active + 1) {
      this.end = true
    } else {
      this.end = false
    }
  },
  methods: {
    // 文件验证规则
    beforeAvatarUpload(file) {
      const isTxt = file.name.substr(file.name.lastIndexOf('.') + 1) == 'txt'
      const isZip = file.name.substr(file.name.lastIndexOf('.') + 1) == 'txt'
      if (!isTxt || !isZip) {
        this.form.file = null
        this.$message.error('只支持txt或者 zip类型文件上传!')
        return isTxt && isZip
      }
      this.form.file = file
    },
    selectAllEvent({ checked }) {
      this.tableSelected = this.$refs.buildConfig_table.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.tableSelected = this.$refs.buildConfig_table.getCheckboxRecords()
    },
    async getTable() {
      try {
        this.tableLoading = true
        let params = {}
        const tableAttr = this.$refs?.pagination?.pageObj || {
          current: 1,
          size: 20,
        }
        params = {
          ...tableAttr,
          extra: {},
          model: { ...this.formData },
        }
        const res = await apiCmdbBuildFilePage(params)
        this.tableLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.tableData = res.data
      } catch (e) {
        this.tableLoading = false
      }
    },
    addFile() {
      this.fileList = []
      this.form.file = null
      this.dialogFormVisible = true
    },
    httpRequest({ file }) {
      this.form.file = file
    },
    // 上传脚本
    async submit() {
      const file = this.$refs['uploadFile'].uploadFiles
      this.file = this.$refs['uploadFile'].uploadFiles

      try {
        await this.$refs.form.validate()
      } catch (error) {
        return
      }

      try {
        this.loadLoading = true
        const { isSuccess, msg } = await apiCmdbBuildFileImport({
          bulidFilePath: `${file[0].bucket}/${file[0].path}`,
          fileName: file[0].originalFileName,
          applicationId: this.$route.params.systemId,
        })
        this.loadLoading = false
        if (!isSuccess) {
          this.form.file = null
          this.$message.warning(msg)
          return
        }
        this.form.file = null
        this.$message.success(msg)
        this.dialogFormVisible = false
        this.getTable()
      } catch (e) {
        this.loadLoading = false
      }
    },
    // 修改脚本
    edit(row) {
      this.editParams = { visible: true, id: row.id }
    },
    // 删除
    async scriptDelete(row) {
      await this.$confirm('确定删除该构建参数文件?', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
      })
      try {
        const { isSuccess, msg } = await apiCmdbBuildFileDel([row.id])
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success('删除成功')
        this.getTable()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 下发

    // 查询持续集成引擎下拉框数据
    async allsed(row) {
      const [mRes, sRes] = await Promise.all([
        apiBaseEngineNoPage({
          instance: 'JENKINS_MASTER',
        }),
        apiBaseEngineNoPage({
          instance: 'JENKINS_SLAVE',
          queryJenkinsSlave: true,
        }),
      ])

      if (!mRes.isSuccess || !sRes.isSuccess) {
        this.$message.warning(mRes.msg || sRes.msg)
        return
      }

      // format master
      mRes.data.map((item) => {
        if (!item.engineExtendeds) return
        // 处理dataStorage
        const _dataStorage = item.engineExtendeds.filter(
          (a) => a.key === 'dataStorage'
        )[0]

        if (_dataStorage) {
          item.dataStorage = _dataStorage.value
          item.dataStorageText =
            item.dataStorage === 'true' ? '共享存储' : '非共享存储'
        }
        // 处理scriptDir
        const _scriptDir = item.engineExtendeds.filter(
          (a) => a.key === 'scriptDir'
        )[0]

        if (_scriptDir) {
          item.scriptDir = _scriptDir.value
        }

        // 处理IP
        item.ips = sRes.data.filter((s) => s.parentId === item.id)
      })
      sRes.data.map((item) => {
        if (!item.engineExtendeds) return
        // 处理consoleUser
        const _consoleUser = item.engineExtendeds.filter(
          (a) => a.key === 'consoleUser'
        )[0]

        if (_consoleUser) {
          item.consoleUser = _consoleUser.value
        }
      })

      // if (row.length) {

      // }

      this.sendParams = {
        visible: true,
        buildFileIds: row.length ? row : [row.id],
        masterList: mRes.data,
        slaveList: sRes.data,
      }
    },
    // async allsed(ids) {
    //   const [resMaster, resSlave] = await Promise.all([
    //     // 获取该业务系统对应的持续集成引擎列表
    //     // 获取所有的持续集成引擎
    //     apiBaseEngineNoPage({
    //       classify: 'CI_CD',
    //       instance: 'JENKINS_MASTER'
    //     }),
    //     // 获取全部jenkinsSlave引擎
    //     apiBaseEngineNoPage({
    //       parentId: '1455777839923068928',
    //       queryJenkinsSlave: true
    //     })
    //   ])

    //   if (!resMaster.isSuccess || !resSlave.isSuccess) {
    //     this.$message.warning(resMaster.msg || resSlave.msg)
    //     return
    //   }
    //   if (resMaster.data.length == 0) {
    //     return this.$message.error(
    //       '平台未配置持续集成引擎无法下发参数文件'
    //     )
    //   }

    //   // format resMaster
    //   resMaster.data.map((item) => {
    //     if (!item.advancedAttributesDatas) return
    //     // 处理dataStorage
    //     const _dataStorage = item.advancedAttributesDatas.filter(
    //       (a) => a.attributesKey === 'dataStorage'
    //     )[0]

    //     if (_dataStorage) {
    //       item.dataStorage = _dataStorage.attributesValue
    //       item.dataStorageText =
    //         item.dataStorage === 'true' ? '共享存储' : '非共享存储'
    //     }
    //     // 处理scriptDir
    //     const _scriptDir = item.advancedAttributesDatas.filter(
    //       (a) => a.attributesKey === 'scriptDir'
    //     )[0]

    //     if (_scriptDir) {
    //       item.scriptDir = _scriptDir.attributesValue
    //     }

    //     // 处理IP
    //     item.ips = resSlave.data.filter((s) => s.pid === item.id)
    //   })

    //   this.sendParams = {
    //     visible: true,
    //     id: ids,
    //     masterList: resMaster.data,
    //     slaveList: resSlave.data
    //   }
    // },
    // 删除目标机脚本
    toDeleteTarget(row) {
      this.deleteParams = { visible: true, id: row.id }
    },
    // 下发历史
    toHistory(row) {
      this.historyParams = { visible: true, id: row.id }
    },
    stepFoaward() {
      this.$emit('stepFoaward')
    },
    saveNext() {
      this.$emit('success')
    },
  },
}
</script>
