<template>
  <!-- 工程配置 -->
  <div v-loading="tableLoading">
    <vone-search-wrapper v-if="$route.params.type == 2">
      <template slot="search">
        <el-alert
          v-if="isNoRepos"
          title="未配置仓库,不允许配置工程,请先为该服务应用配置仓库 !"
          type="warning"
          show-icon
          :closable="false"
          style="width: 400px"
        />
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          :disabled="isNoRepos"
          :loading="saveLoading"
          @click="saveModule"
          >保存</el-button
        >
      </template>
    </vone-search-wrapper>
    <el-form
      ref="form"
      :auto-layout="false"
      :rules="rules"
      label-width="0"
      :model="baseForm"
    >
      <main style="height: calc(100vh - 410px)">
        <vxe-table
          ref="projectTable"
          class="vone-vxe-table projectTable"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="baseForm.tableData"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
          :span-method="tableSpan"
        >
          <vxe-column
            :visible="warehouseType == 'GIT_LAB' || warehouseType == 'GIT_EE'"
            title="库名称"
            field="name"
            min-width="230"
          >
            <template #default="{ row, rowIndex }">
              <el-form-item :prop="`tableData.${rowIndex}.name`">
                <el-input
                  v-model="row.name"
                  placeholder="请输入库名称"
                  :disabled="$route.params.type == 1 || hasData"
                >
                  <template slot="append">.git</template>
                </el-input>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column
            :visible="warehouseType == 'GIT_LAB' || warehouseType == 'GIT_EE'"
            title="单库单应用"
            field="type"
            min-width="150"
          >
            <template #default="{ row, rowIndex }">
              <el-form-item :prop="`tableData.${rowIndex}.name`">
                <el-radio-group
                  v-model="row.type"
                  :disabled="$route.params.type == 1 || hasData"
                  @change="typeChange(row)"
                >
                  <el-radio :label="'ONLY_ONE'">是</el-radio>
                  <el-radio :label="'MANY'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column field="moduleName" title="工程名称" min-width="150">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.moduleName`"
                :rules="rules.moduleName"
              >
                <el-input
                  v-model="row.moduleName"
                  placeholder="请输入工程名称"
                  :disabled="$route.params.type == 1"
                />
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column
            :visible="warehouseType == 'GIT_LAB' || warehouseType == 'GIT_EE'"
            title="工程相对路径"
            field="modulePath"
            min-width="150"
          >
            <template #default="{ row, rowIndex }">
              <template v-if="row.type == 'ONLY_ONE'">
                <span>----</span>
              </template>
              <template v-else>
                <el-form-item
                  :prop="`tableData.${rowIndex}.modulePath`"
                  :rules="rules.modulePath"
                >
                  <el-input
                    v-model="row.modulePath"
                    placeholder="工程相对路径"
                    :disabled="$route.params.type == 1"
                  />
                </el-form-item>
              </template>
            </template>
          </vxe-column>
          <vxe-column title="工程包名称" field="productName" min-width="220">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.productName`"
                :rules="rules.productName"
              >
                <el-input
                  v-model="row.productName"
                  placeholder="请输入不超过100个字符"
                  :disabled="$route.params.type == 1"
                />
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column title="发布包类型" field="productType" min-width="160">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.productType`"
                :rules="rules.productType"
              >
                <el-select
                  v-model="row.productType"
                  style="width: 100%"
                  placeholder="请选择打包方式"
                  :disabled="$route.params.type == 1"
                >
                  <el-option
                    v-for="(item, index) in releaseList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column
            v-show="$route.params.type != 1"
            title="打包方式"
            field="packType"
            min-width="160"
          >
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.packType`"
                :rules="rules.packaging"
              >
                <el-select
                  v-model="row.packType"
                  style="width: 100%"
                  placeholder="请选择打包方式"
                  :disabled="$route.params.type == 1"
                >
                  <el-option
                    v-for="(item, index) in packageList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column title="工程描述" field="description" min-width="200">
            <template #default="{ row }">
              <el-form-item>
                <el-input
                  v-model="row.description"
                  placeholder="请输入不超过100个字符"
                  :disabled="$route.params.type == 1"
                />
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column
            :visible="$route.params.type != 1"
            title="操作"
            fixed="right"
            align="left"
            width="80"
          >
            <template #default="{ row, rowIndex }">
              <transition name="el-fade-in">
                <el-button
                  v-if="row.type != 'ONLY_ONE'"
                  type="text"
                  :icon="el-icon-setting"
                  @click="addRow(rowIndex)"
                />
              </transition>
              <el-button
                type="text"
                :icon="el-icon-setting"
                @click="deleteRow(rowIndex)"
              />
            </template>
          </vxe-column>
        </vxe-table>
      </main>
    </el-form>
  </div>
</template>

<script>
import { apiBaseSaveModule } from '@/api/vone/cmdb/server'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import { apiCmdbSelectModuleBySystemId } from '@/api/vone/cmdb/application'

import _ from 'lodash'

// const getBaseForm = () => ({
//   name: '',
//   type: 'ONLY_ONE',
//   tableData: []
// })

export default {
  props: {
    active: {
      type: Number,
      default: 0,
    },
    applicationInfo: {
      type: Object,
      default: () => {},
    },
    gitData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      saveLoading: false,
      tableLoading: false,
      hasData: false,
      activeIndex: this.active,
      configType: undefined,
      warehouseType: undefined, // 仓库配置，代码仓库引擎类型
      isEdit: false,
      baseForm: {
        tableData: [
          //   {{}
          //   type: 'ONLY_ONE'
          // }
        ],
      },
      rules: {
        name: { required: true, message: '请输入库名称' },
        moduleName: { required: true, message: '请输入工程名称' },
        productName: { required: true, message: '请输入工程包名称' },
        modulePath: { required: true, message: '请输入工程相对路径' },
        packaging: { required: true, message: '请选择打包方式' },
        productType: { required: true, message: '请选择发布包类型' },
      },
      TYPE: this.$route.params.type,
      single: true,
      releaseList: [],
      packageList: [],
      isNoRepos: false,
    }
  },
  mounted() {
    // if (this.applicationInfo.adress == '--') {
    this.isNoRepos = this.applicationInfo.adress == '--'
    // this.$message.error('未配置仓库,不允许配置工程,请先为该服务应用配置仓库')
    // return
    // }

    // 从父页面editServer传过来的仓库配置数据，根据仓库引擎类型判断
    this.warehouseType = this.gitData?.engineInstanceKey
    this.getEnumList()
    // this.getBaseData()
    // 回显数据
    this.getInfo()
    this.$nextTick(() => {
      this.$refs.projectTable.refreshColumn()
    })
  },
  methods: {
    // 枚举查询发布包类型/打包方式
    async getEnumList() {
      const [releaseRes, packageRes] = await Promise.all([
        apiBaseDictEnumList(['ApplicationModuleProductType']),
        apiBaseDictEnumList(['ApplicationModulePackType']),
      ])

      if (!releaseRes.isSuccess || !packageRes.isSuccess) {
        return
      }
      // 发布包类型
      this.releaseList = releaseRes.data.ApplicationModuleProductType.filter(
        (e) => e.value != 'DIR'
      )
      // 打包方式
      this.packageList = packageRes.data.ApplicationModulePackType.filter(
        (e) => e.value != 'INCREMENT'
      )
    },
    tableSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 1 || columnIndex == 0) {
        if (rowIndex === 0) {
          return { rowspan: this.baseForm.tableData.length, colspan: 1 }
        }
        return { rowspan: 0, colspan: 0 }
      }
    },
    typeChange(row) {
      if (row.type === 'ONLY_ONE' && this.baseForm.tableData.length > 1) {
        this.baseForm.tableData = _.dropRight(
          this.baseForm.tableData,
          this.baseForm.tableData.length - 1
        )
      }
    },
    // 点击+号图标添加新的一栏
    async addRow(index) {
      if (this.baseForm.tableData.length) {
        await this.$refs.form.validate()
      }
      const obj = {}
      this.baseForm.tableData.splice(index + 1, 0, obj)
    },
    // 点击减号删除一行
    deleteRow(index) {
      this.baseForm.tableData = this.baseForm.tableData.filter(
        (v, i) => i !== index
      )
      this.$nextTick(() => {
        if (this.baseForm.tableData.length == 0) {
          this.hasData = false
          this.baseForm.tableData = [
            {
              type: 'ONLY_ONE',
            },
          ]
          // this.$refs.form.reset()
          this.isEdit = false
        }
      })
    },

    async getInfo() {
      this.tableLoading = true
      const res = await apiCmdbSelectModuleBySystemId(
        this.$route.params.systemId
      )
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      res.data.forEach((element) => {
        element.productType = element.productType.code
        element.packType = element.packType.code
        element.type = element.type.code
      })

      if (res.data.length > 0) {
        this.isEdit = true
        this.hasData = true
        this.baseForm.tableData = res.data
      } else {
        this.hasData = false
        this.baseForm.tableData = [
          {
            type: 'ONLY_ONE',
          },
        ]
        // this.baseForm.tableData.push({})
      }

      this.$nextTick(() => {
        this.$refs.form?.clearValidate()
      })
    },

    // 保存
    async saveModule() {
      await this.$refs.form.validate()
      this.saveLoading = true

      this.baseForm.tableData.forEach((element) => {
        element.name =
          this.baseForm.tableData[0].type == 'MANY' &&
          this.baseForm.tableData.length > 1
            ? this.baseForm.tableData[0].name
            : element.name

        element.type =
          this.baseForm.tableData[0].type == 'MANY' &&
          this.baseForm.tableData.length > 1
            ? this.baseForm.tableData[0].type
            : element.type
        element.id.indexOf('row_') > -1 && delete element.id
      })

      // const applicationModules = this.baseForm.tableData.map(r => {
      //   return {
      //     ...pick(r, ['id', 'moduleName', 'productName', 'modulePath', 'packType', 'productType', 'description', 'type']),

      //     name: this.warehouseType == 'GIT_LAB' ? r.name : r.moduleName
      //     // type: this.baseForm.type
      //   }
      // })
      const res = await apiBaseSaveModule({
        applicationId: this.$route.params.systemId,
        applicationModules: this.baseForm.tableData,
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.getInfo()
      this.$emit('success', 'projectConfig')
    },
    stepFoaward() {
      this.$emit('stepFoaward')
    },
    async saveNext() {
      await this.saveModule()
      // this.activeIndex++;
    },
  },
}
</script>

<style lang="scss" scoped>
.projectTable {
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
}
</style>
