<template>
  <div v-loading="depotLoading" class="warehouse">
    <vone-search-wrapper v-if="$route.params.type == 2">
      <template slot="actions">
        <el-button type="primary" :loading="saveLoading" @click="submitInfo"
          >保存</el-button
        >
      </template>
    </vone-search-wrapper>

    <!-- 仓库配置 -->
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-position="top"
      style="width: 50%"
    >
      <el-form-item label="代码仓库类型" prop="engineInstanceKey">
        <el-select
          v-model="ruleForm.engineInstanceKey"
          placeholder="请选择代码仓库类型"
          :disabled="$route.params.type == 1"
          @change="engineCategoryIdChange"
        >
          <!-- :disabled="$route.params.type == 1" -->
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代码仓库引擎" prop="engineId">
        <el-select
          v-if="$route.params.type == 1"
          v-model="ruleForm.engineId"
          disabled
          placeholder="请选择代码仓库引擎"
          @change="codeEngineChange"
        >
          <el-option
            v-for="(item, index) in engineList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select
          v-else
          v-model="ruleForm.engineId"
          :disabled="ruleForm.engineInstanceKey ? false : true"
          placeholder="请选择代码仓库引擎"
          @change="codeEngineChange"
        >
          <el-option
            v-for="(item, index) in engineList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="isSvn || isGit"
        v-show="ruleForm.engineId"
        :label="isSvn ? '仓库名称' : '组或用户名'"
        prop="codeRepPath"
      >
        <el-row type="flex">
          <el-select
            v-model="ruleForm.codeRepPath"
            filterable
            placeholder="请选择仓库名称"
            :disabled="$route.params.type == 1"
          >
            <el-option
              v-for="(item, index) in engineIdNameList"
              :key="index"
              :label="item.name"
              :value="item.path"
            >
              {{ `${item.name} ( ${item.path} )` }}
              <el-tag
                :type="item.type == 'users' ? 'warning' : ''"
                style="float: right"
                >{{ item.type == 'users' ? 'users' : 'groups' }}</el-tag
              >
            </el-option>
          </el-select>
          <template v-if="isSvn">
            <el-button
              v-if="this.$route.params.type != 1"
              :icon="ElIconPlus"
              class="ml-3"
              @click="showAddSvnModel = true"
              >新增仓库</el-button
            >
          </template>

          <template v-else-if="isGit">
            <el-button
              v-if="this.$route.params.type != 1"
              :icon="ElIconPlus"
              style="margin-left: 10px"
              @click="addGroup"
              >新增组或用户</el-button
            >
          </template>
        </el-row>
      </el-form-item>
    </el-form>

    <el-row v-if="$route.params.type == 0" class="tabFooter">
      <div style="float: left">
        <el-button v-show="active > 0" @click="stepFoaward">上一步</el-button>
      </div>
      <div style="float: right">
        <el-button type="primary" @click="saveNext">保存并下一步</el-button>
      </div>
    </el-row>
    <!-- 对话框 -->

    <warehouseConfigAddSvn
      v-if="showAddSvnModel"
      v-model="showAddSvnModel"
      :engine-id="ruleForm.engineId"
      :strage="strage"
      :url="url"
      @success="loadCodeRepPath"
    />
  </div>
</template>

<script>
import { Plus as ElIconPlus } from '@element-plus/icons-vue'
import {
  apiCmdbSelectCodeRepByCategoryIdAndEngineId,
  apiBuniesSelectWarehouseBySystemId,
  apiCmdbUpdateWarehouseConfig,
} from '@/api/vone/cmdb/server'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import { apiBaseEngineNoPage } from '@/api/vone/base/engine'

import warehouseConfigAddSvn from './warehouse-config-add-svn.vue'

import pick from 'lodash/pick'
export default {
  data() {
    return {
      saveLoading: false,
      showAddSvnModel: false,
      showAddUserModel: false,
      showAddGroupModel: false,
      depotLoading: false,
      isDisabled: true,
      modalVisible: false,
      strage: null,
      ruleForm: {
        engineId: null,
        engineCategoryId: '',
        codeRepPath: null,
      },
      typeList: [],
      typeListMap: [],
      activeIndex: this.active,
      engineIdNameList: [],
      rules: {
        engineInstanceKey: [{ required: true, message: '请选择代码仓库类型' }],
        engineId: [
          { required: true, message: '请选择代码仓库引擎', trigger: 'blur' },
        ],
        codeRepPath: [{ required: true, message: '请选择组或用户名' }],
      },
      engineList: [],
      engineListMap: {},
      isGit: true,
      isSvn: false,
      ElIconPlus,
    }
  },
  components: {
    warehouseConfigAddSvn,
  },
  props: {
    active: {
      type: Number,
      default: 0,
    },
    adress: {
      type: String,
      default: '',
    },
    applicationInfo: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    // isGit() {
    //   return (
    //     // this.ruleForm.engineCategoryId === 'GitLab' && this.ruleForm.engineId
    //   )
    // },
    // isSvn() {
    //   return (
    //     this.ruleForm.engineCategoryId === 'Subversion' &&
    //     this.ruleForm.engineId
    //   )
    // },
    url() {
      const engine = this.engineListMap[this.ruleForm.engineId]
      return engine ? engine.engineUrl : null
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    addGroup() {
      if (!this.$permission('code_library_view')) {
        this.$message.warning(
          '当前登录用户没有【查看代码库】权限，请联系管理员分配权限'
        )
        return
      }
      this.$router.push({
        name: 'code_library',
      })
    },
    addUser() {},
    async init() {
      await this.loadEngineTypeList()
      await this.getFormData()
      this.$refs.ruleForm?.clearValidate()
    },
    // 查询回显数据
    async getFormData() {
      this.depotLoading = true
      const res = await apiBuniesSelectWarehouseBySystemId(
        this.$route.params.systemId
      )
      this.depotLoading = false

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data) {
        this.ruleForm = res.data
        this.$set(
          this.ruleForm,
          'codeRepPath',
          this.applicationInfo.codeRepPath
        )
      } else {
        this.ruleForm = {
          engineInstanceKey: '',
          engineId: '',
          codeRepPath: '',
        }
      }

      if (this.ruleForm.engineId) {
        this.loadCodeRepPath(this.ruleForm.engineId)
      }
      if (this.ruleForm.engineInstanceKey) {
        this.loadEngineList(this.ruleForm.engineInstanceKey)
      }
    },
    // 查询代码仓库类型下拉框数据
    async loadEngineTypeList() {
      const res = await apiBaseDictNoPage({ parentId: '1455378001788141568' })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.typeListMap = res.data.reduce(
        (r, v, k) => (r[v.dictKey] = v) && r,
        {}
      )
      this.typeList = res.data
    },
    // 代码仓库类型下拉框change事件
    async engineCategoryIdChange(val) {
      this.loadEngineList(val)
    },
    /**
     * 加载引擎
     */
    async loadEngineList(val) {
      // const { engineCategoryId } = this.applicationInfo
      // if (!engineCategoryId) return
      // const type = this.typeListMap[engineCategoryId]
      const res = await apiBaseEngineNoPage({
        instance: val,
      })
      if (!res.isSuccess) {
        return
      }

      this.engineList = res.data
      this.engineListMap = res.data.reduce((r, v) => (r[v.id] = v) && r, {})
    },
    // 代码引擎仓库类型
    async codeEngineChange(value) {
      // if (this.ruleForm.codeRepPath) {
      //   this.ruleForm.codeRepPath = null
      // }
      this.loadCodeRepPath(value)
    },
    // 查询仓库名称下拉框数据---组或用户
    async loadCodeRepPath(value) {
      // const { engineId, engineCategoryId } = this.applicationInfo
      // if (!engineId || !engineCategoryId) return
      const res = await apiCmdbSelectCodeRepByCategoryIdAndEngineId(value)
      if (!res.isSuccess) {
        return
      }
      if (res.data) {
        res.data.groups.forEach((element) => {
          element.type = 'groups'
        })

        res.data.users.forEach((element) => {
          element.type = 'users'
        })

        const userAndGroup = res.data.groups.concat(res.data.users)
        this.engineIdNameList = userAndGroup
      } else {
        this.engineIdNameList = []
      }

      // if (name) {
      //   this.ruleForm.codeRepPath = name
      // }
    },

    async submitInfo() {
      try {
        await this.$refs.ruleForm.validate()
      } catch (error) {
        return
      }
      this.saveLoading = true

      // const { engineCategoryId } = this.applicationInfo
      // const type = this.typeListMap[engineCategoryId]

      const formData = {
        applicationId: this.$route.params.systemId,
        ...this.ruleForm,
        // engineTypeid: type.parentKey
      }

      const res = await apiCmdbUpdateWarehouseConfig(
        pick(formData, [
          'applicationId',
          'engineId',
          'engineInstanceKey',
          'codeRepPath',
        ])
      )
      this.saveLoading = false

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')

      this.$emit('success')
    },
    stepFoaward() {
      this.$emit('stepFoaward')
    },
    async saveNext() {
      await this.submitInfo()
    },
  },
}
</script>

<style lang="scss" scoped>
.warehouse {
  height: calc(100vh - 355px);
}
</style>
