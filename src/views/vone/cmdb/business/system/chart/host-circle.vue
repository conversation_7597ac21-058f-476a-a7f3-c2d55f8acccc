<template>
  <div>
    <el-card style="height: 100%">
      <div slot="header">
        <span>服务器数量</span>
        <a @click="showDialog">
          <el-icon class="iconfont" style="float: right; padding: 3px 0"
            ><el-icon-application-setting
          /></el-icon>
        </a>
      </div>
      <div>
        <div v-if="noData" class="noData">
          <vone-empty />
        </div>
        <div v-if="!noData" class="echart">
          <vone-echarts :options="circle" />
        </div>
      </div>
    </el-card>

    <!-- 对话框 -->
    <el-dialog
      title="筛选条件"
      width="40%"
      v-model="dialogFormVisible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form">
        <el-form-item label="服务应用" prop="systemId">
          <el-select
            v-model="form.systemId"
            placeholder="请选择服务应用"
            @change="searchEnv"
          >
            <el-option
              v-for="(item, index) in serveList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="环境" prop="envId">
          <el-select v-model="form.envId" placeholder="请选择环境" multiple>
            <el-option
              v-for="(item, index) in envList"
              :key="index"
              :label="item.name"
              :value="item.dictKey"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ApplicationSetting as ElIconApplicationSetting } from '@element-plus/icons-vue'
import { apiBaseAppserversCount } from '@/api/vone/cmdb/business'
export default {
  components: {
    ElIconApplicationSetting,
  },
  props: {
    envList: {
      type: Array,
      default: () => [],
    },
    serveList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      noData: false,
      circle: null,
      form: {},
    }
  },
  created() {},
  mounted() {
    this.getCircleChartData()
  },
  methods: {
    onClose() {
      this.dialogFormVisible = false
      this.$refs.form.resetFields()
    },
    // 根据iP获取环形图表使用率数据

    async getCircleChartData() {
      this.dialogFormVisible = false
      const { data, success, message } = await apiBaseAppserversCount({
        businessSystemId: this.$route.params.id,
        systemId: this.form.systemId === undefined ? '' : this.form.systemId,
        envId: this.form.envId ? this.form.envId.join(',') : '',
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      if (JSON.stringify(data) == '{}') {
        this.noData = true
        return
      }
      const newData = []
      const legendName = ['可用', '不可用']
      if (data) {
        newData.push(
          { name: '可用', value: data.usable || 0, ids: '1' },
          { name: '不可用', value: data.disabled || 0, ids: '0' }
        )
        this.circle = {
          color: ['#3ea5f1', '#F4BD00'],
          tooltip: {
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: '#fff',
            borderColor: 'none',
            extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
            textStyle: {
              color: '#53565C',
            },
          },
          legend: {
            orient: 'vertical',
            right: 30,
            top: 40,
            bottom: 20,
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
              // 图例文字的样式
              color: '#8A8F99',
            },
            data: legendName,
            formatter: (name) => {
              for (var i in newData) {
                if (newData[i].name === name) {
                  return name + '  ' + newData[i].value
                }
              }
            },
          },
          series: [
            {
              // name: "",
              type: 'pie',
              center: ['35%', '50%'],
              radius: ['45%', '60%'],
              avoidLabelOverlap: false,
              legendHoverLink: true,
              hoverOffset: 3,
              stillShowZeroSum: true,
              label: {
                normal: {
                  show: false,
                  position: 'center',
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '18',
                    fontWeight: 'bold',
                  },
                  formatter: '{c}',
                },
              },
              labelLine: {
                normal: {
                  show: false,
                },
              },
              data: newData,
            },
          ],
        }
      } else {
        this.noData = true
      }
    },
    showDialog() {
      this.dialogFormVisible = true
    },
    // 根据服务应用查环境
    async searchEnv() {
      this.$set(this.form, 'envId', '')
      this.$emit('changeApp', this.form.systemId)
    },
    // 对话框保存
    async saveInfo() {
      await this.getCircleChartData()
      this.onClose()
    },
  },
}
</script>
