<template>
  <div>
    <el-card style="height: 100%">
      <div slot="header">
        <span>应用组件数量</span>
        <a @click="showDialog">
          <el-icon class="iconfont" style="float: right; padding: 3px 0"
            ><el-icon-application-setting
          /></el-icon>
        </a>
      </div>
      <div>
        <div v-if="noData" class="noData">
          <vone-empty />
        </div>
        <div v-if="!noData" class="echart">
          <vone-echarts :options="circle" />
        </div>
      </div>
    </el-card>
    <!-- 对话框 -->
    <el-dialog
      title="筛选条件"
      width="40%"
      v-model="dialogFormVisible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="服务应用" prop="systemId">
          <el-select v-model="form.systemId" placeholder="请选择服务应用">
            <el-option
              v-for="(item, index) in serveList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="saveInfo">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ApplicationSetting as ElIconApplicationSetting } from '@element-plus/icons-vue'
import { apiBaseAppcompentsCount } from '@/api/vone/cmdb/business'
export default {
  components: {
    ElIconApplicationSetting,
  },
  props: {
    envList: {
      type: Array,
      default: () => [],
    },
    serveList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      circle: null,
      noData: false,
      dialogFormVisible: false,
      form: {
        systemId: undefined,
      },
      rules: {
        systemId: [{ required: true, message: '请选择服务应用' }],
      },
    }
  },
  created() {},
  mounted() {
    this.getCircleChartData()
  },
  methods: {
    onClose() {
      this.dialogFormVisible = false
      this.$refs.form.resetFields()
    },
    // 根据iP获取环形图表使用率数据

    async getCircleChartData() {
      this.dialogFormVisible = false
      const { data, success, message } = await apiBaseAppcompentsCount({
        businessSystemId: this.$route.params.id,
        systemId: this.form.systemId,
      })
      if (!success) {
        this.$message.warning(message)
      }
      // if (JSON.stringify(data) === "{}") {
      //
      // }

      if (Object.keys(data).length == 0) {
        this.noData = true
        // return;
      } else {
        this.noData = false

        const newData = []
        const legendName = []

        if (Object.keys(data).length > 0) {
          for (var i in data) {
            legendName.push(i)
            newData.push({ name: i, value: data[i] })
          }
        }
        const colors = [
          '#1790ff',
          '#10c2c3',
          '#30c25b',
          '#facc14',
          '#f04764',
          '#8443e0',
        ]
        //   if (legendName.length > 6) {
        //     for (var j = 6; j < newData.length; j++) {
        //       var randColor = getRandomColor();
        //       colors.push(randColor);
        //     }
        //   }
        this.circle = {
          color: colors,
          tooltip: {
            trigger: 'item',
            axisPointer: {
              type: 'line',
            },
            backgroundColor: '#fff',
            borderColor: 'none',
            extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
            textStyle: {
              color: '#53565C',
            },
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 30,
            top: 40,
            bottom: 20,
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
              // 图例文字的样式
              color: '#8A8F99',
            },
            data: legendName,
            formatter: (name) => {
              for (var i in newData) {
                if (newData[i].name === name) {
                  return name + '  ' + newData[i].value
                }
              }
            },
          },
          series: [
            {
              // name: "",
              type: 'pie',
              center: ['35%', '50%'],
              radius: ['45%', '60%'],
              avoidLabelOverlap: false,
              legendHoverLink: true,
              hoverOffset: 3,
              stillShowZeroSum: true,
              label: {
                normal: {
                  show: false,
                  position: 'center',
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '18',
                    fontWeight: 'bold',
                  },
                  formatter: '{c}',
                },
              },
              labelLine: {
                normal: {
                  show: false,
                },
              },
              data: newData,
            },
          ],
        }
      }
    },
    showDialog() {
      this.dialogFormVisible = true
    },
    // 对话框保存
    async saveInfo() {
      try {
        await this.$refs.form.validate()
      } catch (error) {
        return
      }
      await this.getCircleChartData()
      this.onClose()
    },
  },
}
</script>
