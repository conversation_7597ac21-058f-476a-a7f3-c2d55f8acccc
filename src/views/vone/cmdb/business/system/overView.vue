<template>
  <page-wrapper>
    <el-card>
      <el-row type="flex" justify="space-between">
        <el-col :span="4">
          <el-tooltip
            effect="dark"
            content="近30天内部署流水线执行次数"
            placement="top"
          >
            <div class="box">
              <div class="iconBox bgc1">
                <el-icon class="icon"><ElIconSetting /></el-icon>
              </div>
              <div class="text">
                <p>{{ frequency }}次</p>
                <p>发布频率</p>
              </div>
            </div>
          </el-tooltip>
        </el-col>
        <el-col :span="4">
          <el-tooltip
            effect="dark"
            content="近30天内每次部署流水线执行时长的平均值"
            placement="top"
          >
            <div class="box">
              <div class="iconBox bgc2">
                <el-icon class="icon"><ElIconSetting /></el-icon>
              </div>
              <div class="text">
                <p>{{ averTime }}</p>
                <p>平均发布时间</p>
              </div>
            </div>
          </el-tooltip>
        </el-col>
        <el-col :span="4">
          <el-tooltip
            effect="dark"
            content="近30天内自动化测试通过率"
            placement="top"
          >
            <div class="box">
              <div class="iconBox bgc3">
                <el-icon class="icon"><ElIconSetting /></el-icon>
              </div>
              <div class="text">
                <p>{{ through0ut }}</p>
                <p>测试通过率</p>
              </div>
            </div>
          </el-tooltip>
        </el-col>
        <el-col :span="4">
          <el-tooltip
            effect="dark"
            content="近30天内需求变更数量"
            placement="top"
          >
            <div class="box">
              <div class="iconBox bgc4">
                <el-icon class="icon"><ElIconSetting /></el-icon>
              </div>
              <div class="text">
                <p>{{ through0ut }}</p>
                <p>交付吞吐率</p>
              </div>
            </div>
          </el-tooltip>
        </el-col>
        <el-col :span="4">
          <el-tooltip
            effect="dark"
            content="近30天内部署流水线执行成功的比例"
            placement="top"
          >
            <div class="box">
              <div class="iconBox bgc5">
                <el-icon class="icon"><ElIconSetting /></el-icon>
              </div>
              <div class="text">
                <p>{{ successRata }}</p>
                <p>上线成功率</p>
              </div>
            </div>
          </el-tooltip>
        </el-col>
      </el-row>
    </el-card>
    <el-row :gutter="16" class="flexBox">
      <el-col :span="12" class="item">
        <publishRate
          style="height: 100%"
          :env-list="envList"
          :serve-list="serveList"
          @changeApp="getEnvList"
        />
      </el-col>
      <el-col :span="12" class="item">
        <averTime
          style="height: 100%"
          :env-list="envList"
          :serve-list="serveList"
          @changeApp="getEnvList"
        />
      </el-col>
    </el-row>
    <el-row :gutter="16" class="flexBox">
      <el-col :span="8" class="item">
        <hostCircle
          style="height: 100%"
          :env-list="envList"
          :serve-list="serveList"
          @changeApp="getEnvList"
        />
      </el-col>
      <el-col :span="8" class="item">
        <appCircle
          style="height: 100%"
          :env-list="envList"
          :serve-list="serveList"
        />
      </el-col>
      <el-col :span="8" class="item">
        <dbCircle
          style="height: 100%"
          :env-list="envList"
          :serve-list="serveList"
        />
      </el-col>
    </el-row>
  </page-wrapper>
</template>

<script>
import {
  Brush as ElIconBrush,
  Time as ElIconTime,
  CircleCheck as ElIconCircleCheck,
  Document as ElIconDocument,
  Help as ElIconHelp,
} from '@element-plus/icons-vue'
import {
  apiBasePublishFrequency,
  apiBaseAverageReleaseTime,
  apiBaseDeliveryThroughput,
  apiBaseOnlineSuccessRate,
  apiBaseDictEnvComponentBySystemId,
  apiBaseDictEnvByBusinessSystem,
  // apiBaseGetapplication
} from '@/api/vone/cmdb/business'

import publishRate from './chart/publish-rate'
import averTime from './chart/aver-time'
import hostCircle from './chart/host-circle'
import appCircle from './chart/app-circle'
import dbCircle from './chart/db-circle'

export default {
  components: {
    publishRate,
    averTime,
    hostCircle,
    appCircle,
    dbCircle,
    ElIconBrush,
    ElIconTime,
    ElIconCircleCheck,
    ElIconDocument,
    ElIconHelp,
  },
  data() {
    return {
      asyncData: [],
      frequency: '',
      averTime: '',
      through0ut: '',
      successRata: '',
      envList: [],
      serveList: [],
    }
  },
  mounted() {
    this.getFrequency()
    this.getTime()
    this.getThroughOut()
    this.getSuccessRate()
    this.getEnvList()
    // this.getApplicationList()
  },
  methods: {
    //   发布频率
    async getFrequency() {
      const { data, success, message } = await apiBasePublishFrequency({
        businessId: this.$route.params.id,
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.frequency = data
    },
    //   平均时间
    async getTime() {
      const { data, success, message } = await apiBaseAverageReleaseTime({
        businessId: this.$route.params.id,
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.averTime = data
    },
    //   交付吞吐率
    async getThroughOut() {
      const { data, success, message } = await apiBaseDeliveryThroughput({
        businessId: this.$route.params.id,
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.through0ut = data
    },
    //   上线成功率
    async getSuccessRate() {
      const { data, success, message } = await apiBaseOnlineSuccessRate({
        businessId: this.$route.params.id,
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.successRata = data
    },
    // 查询环境下拉框数据
    async getEnvList(val) {
      let result = {}
      // 如果val有值是查询的服务应用的环境字典，没有则查询业务系统的环境字典
      if (val) {
        result = await apiBaseDictEnvComponentBySystemId({
          ids: val,
        })
      } else {
        result = await apiBaseDictEnvByBusinessSystem(this.$route.params.id)
      }

      if (result && !result.success) {
        this.$message.error(result.message)
        return
      }

      this.envList = result.data
    },
    // 获取服务系统
    // async getApplicationList() {
    //   const { data, success, message } = await apiBaseGetapplication({
    //     businessSystemId: this.$route.params.id
    //   })
    //   if (!success) {
    //     this.$message.warning(message)
    //     return
    //   }

    //   this.serveList = data
    // }
  },
}
</script>

<style lang="scss" scoped>
.box {
  border: 1px solid #ebeef5;
  border-radius: 2%;
  :deep([class^='el-icon-']),
  [class*=' el-icon-'] {
    line-height: 70px;
  }

  .iconBox {
    height: 70px;
    line-height: 70px;
    text-align: center;
    width: 100%;
    // height: 50%;
  }

  .text {
    text-align: center;
    p {
      padding: 0 20px;
      color: #909399;
      margin: 5px 0;
    }
    p:first-child {
      font-size: 22px;
      font-weight: bold;
      //   color: #000;
    }
  }
}

.bgc1 {
  background-color: #60a0fc;
}
.bgc2 {
  background-color: #f5b257;
}
.bgc3 {
  background-color: #3ace7c;
}
.bgc4 {
  background-color: #f08050;
}
.bgc5 {
  background-color: #835be9;
}
.icon {
  font-size: 38px;
  font-weight: bold;
  color: #fff;
  width: 100%;
}

.flexBox {
  margin: 10px 0 10px 0;
  display: flex;
  align-items: stretch;
  flex-direction: row;
  .item {
    flex: 1;
  }
}
</style>
