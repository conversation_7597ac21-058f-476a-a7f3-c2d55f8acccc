<template>
  <el-col :span="8">
    <el-card style="min-height: 300px">
      <div slot="header">
        <span>快捷入口</span>
      </div>
      <div class="entrance">
        <el-row :gutter="12" class="virtual_machine">
          <el-col :span="12">
            <el-card shadow="hover">
              <el-row :gutter="24" class="virtual_machine">
                <el-col :span="24">
                  <a href="#" @click="add">
                    <div>
                      <el-icon><ElIconSetting /></el-icon>
                    </div>
                    <div>新增</div>
                  </a>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover" class="sti-card is-right">
              <el-row :gutter="24" class="virtual_machine">
                <el-col :span="24">
                  <a href="#" @click="refresh('refresh', $event)">
                    <div>
                      <el-icon><ElIconSetting /></el-icon>
                    </div>
                    <div>刷新</div>
                  </a>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <el-row :gutter="24" class="virtual_machine">
                <el-col :span="24">
                  <a href="#" @click="refresh('export', $event)">
                    <div>
                      <el-icon><ElIconSetting /></el-icon>
                    </div>
                    <div>导出</div>
                  </a>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover" class="sti-card">
              <el-row :gutter="24" class="virtual_machine">
                <el-col :span="24">
                  <a href="#" @click="refresh('synchronize', $event)">
                    <div>
                      <el-icon><ElIconSetting /></el-icon>
                    </div>
                    <div>同步</div>
                  </a>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </el-col>
</template>

<script>
import {
  DocumentAdd as ElIconDocumentAdd,
  RefreshRight as ElIconRefreshRight,
  EditDownload as ElIconEditDownload,
  Refresh as ElIconRefresh,
} from '@element-plus/icons-vue'
export default {
  components: {
    ElIconDocumentAdd,
    ElIconRefreshRight,
    ElIconEditDownload,
    ElIconRefresh,
  },
  data() {
    return {
      value: '',
      options: {},
      option: {},
      noData: false,
    }
  },
  created() {
    // this.getList()
  },
  methods: {
    add(e) {
      // if (this.$auth('CMDB_host_add')) {
      e.preventDefault()
      this.$router.push({
        name: 'cmdb_host_add',
      })
      // }
    },
    refresh(value, e) {
      if (value == 'export') {
        e.preventDefault()
        this.$store.dispatch('setEnterType', 'export')
        this.$router.push({ name: 'cmdb_host_list' })
      }
      if (value == 'refresh') {
        e.preventDefault()
        this.$store.dispatch('setEnterType', 'refresh')
        this.$router.push({ name: 'cmdb_host_list' })
      }
      if (value == 'synchronize') {
        e.preventDefault()
        this.$store.dispatch('setEnterType', 'synchronize')
        this.$router.push({ name: 'cmdb_host_list' })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 0 !important;
}
.entrance {
  .virtual_machine {
    .el-col {
      padding: 0px !important;
    }
    a {
      display: inline-block;
      div {
        margin: 8px 0;
      }
      i {
        font-size: 32px;
        color: rgb(24, 144, 255);
        line-height: 1;
      }
    }

    .el-card {
      border-radius: 0;
      padding: 32px;
    }
  }
  .el-card {
    border-bottom: none;
    border-right: none;
    cursor: pointer;
    .virtual_machine {
      .el-col {
        text-align: center;
      }
    }
  }
  .sti-card {
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
  }
  .is-right {
    border-bottom: none;
  }
}
</style>
