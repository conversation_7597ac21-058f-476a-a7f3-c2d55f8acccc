<template>
  <el-col :span="8">
    <el-card style="min-height: 300px">
      <div slot="header">
        <span>最近访问的主机</span>
      </div>
      <el-row>
        <vone-empty v-if="!historyList.length" />
        <el-col
          v-for="(item, index) in historyList"
          v-else
          :key="index"
          :span="24"
        >
          <el-button :icon="ElIconLink" type="text" @click="history(item)">{{
            item
          }}</el-button>
        </el-col>
      </el-row>
    </el-card>
  </el-col>
</template>

<script>
import { Link as ElIconLink } from '@element-plus/icons-vue'
import { getSelectAccessHostInfo } from '@/api/vone/cmdb/host'
export default {
  data() {
    return {
      historyList: [],
      ElIconLink,
    }
  },
  created() {
    this.getHistoryList()
  },
  methods: {
    history(item) {
      this.$router.push(`/cmdb/resources/host/ssh/${item}`)
    },
    async getHistoryList() {
      const { success, message, data } = await getSelectAccessHostInfo()
      if (!success) {
        return this.$message.error(message)
      }
      if (data.length > 5) {
        this.historyList = data.slice(0, 5)
      } else {
        this.historyList = data
      }
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 20px 50px;
}
</style>
