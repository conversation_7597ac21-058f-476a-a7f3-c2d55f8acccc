<template>
  <el-col :span="8">
    <div class="my_host" style="min-height: 300px">
      <el-card>
        <el-row :gutter="12" class="virtual_machine">
          <el-col :span="18">
            <a href="#" @click="myHostList">{{ myHost }}</a>
            <div>我维护的主机</div>
          </el-col>
          <el-col :span="6"
            ><el-icon><ElIconSetting /></el-icon
          ></el-col>
        </el-row>
      </el-card>
      <el-card>
        <el-row :gutter="12" class="virtual_machine">
          <el-col :span="18">
            <a href="#" @click="allHost">{{ departmentNum }}</a>
            <div>所在部门主机总数</div>
          </el-col>
          <el-col :span="6"
            ><el-icon><ElIconSetting /></el-icon
          ></el-col>
        </el-row>
      </el-card>
    </div>
  </el-col>
</template>

<script>
import { Position as ElIconPosition } from '@element-plus/icons-vue'
import {
  getHostCountByAccendantId,
  getHostCountByOrgId,
} from '@/api/vone/cmdb/host'
export default {
  components: {
    ElIconPosition,
  },
  data() {
    return {
      myHost: 0,
      departmentNum: 0,
      noData: false,
    }
  },
  created() {
    this.getList()
    this.getDepartment()
  },
  methods: {
    async getList() {
      const { success, message, data } = await getHostCountByAccendantId()
      if (!success) {
        return this.$message.error(message)
      }
      this.myHost = data
    },
    async getDepartment() {
      const { success, message, data } = await getHostCountByOrgId()
      if (!success) {
        return this.$message.error(message)
      }
      this.departmentNum = data
    },
    myHostList(e) {
      e.preventDefault()
      this.$router.push({
        name: 'cmdb_host_list',
        query: { type: 'accendant' },
      })
    },
    allHost(e) {
      e.preventDefault()
      this.$router.push({
        name: 'cmdb_host_list',
        query: { type: 'org' },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.my_host {
  .virtual_machine {
    .el-col {
      padding-top: 10px;
    }
    a {
      font-size: 24px;
    }
    i {
      font-size: 56px;
      color: rgb(24, 144, 255);
      line-height: 1;
    }
  }
  .el-card {
    padding: 18px;
    // height: 144px !important;
  }
  .el-card:first-child {
    margin-bottom: 10px;
  }
}
</style>
