<template>
  <vone-drawer v-model="visible" size="lg" :before-close="onClose">
    <div slot="title" class="drawer-title">
      <div class="drawer-title-text">
        {{ `服务器【${basicData.ip}】详情` }}
        <el-icon class="iconfont nextBtn"
          ><el-icon-yibiaopan-shangyi
        /></el-icon>
        <el-icon class="iconfont nextBtn"><ElIconSetting /></el-icon>
      </div>
    </div>
    <div v-loading="loading" class="pageBox">
      <div class="title" style="margin-bottom: 0">
        <strong> 基本信息 </strong>
      </div>
      <div class="formBox" style="padding-top: 0px; padding-bottom: 10px">
        <vone-desc :column="2">
          <vone-desc-item label="主机名">
            {{ basicData.name }}
          </vone-desc-item>
          <vone-desc-item label="IP">
            {{ basicData.ip }}
          </vone-desc-item>
          <vone-desc-item label="来源">
            <span v-if="basicData.createType">
              {{ basicData.createType.desc }}</span
            >
            <span v-else>--</span>
          </vone-desc-item>
          <vone-desc-item label="操作系统">
            {{ basicData.operatingSystem }}
          </vone-desc-item>
          <vone-desc-item label="系统版本">
            {{ basicData.operatingSystemVersion }}
          </vone-desc-item>
          <vone-desc-item label="CPU核数">
            {{ basicData.cpuNum }}
          </vone-desc-item>
          <vone-desc-item label="内存容量">
            {{ basicData.memSize !== null ? basicData.memSize + 'M' : '--' }}
          </vone-desc-item>
          <vone-desc-item label="服务器类型">
            <span v-if="basicData.type">{{ basicData.type.desc || '--' }}</span>
            <span v-else>--</span>
          </vone-desc-item>
          <vone-desc-item label="状态轮巡策略">
            <span v-if="basicData.patrolStrategy">{{
              basicData.patrolStrategy + '分钟'
            }}</span>
            <span v-else>--</span>
          </vone-desc-item>
          <vone-desc-item :calc-width="true" label="描述">
            {{ basicData.description }}
          </vone-desc-item>
        </vone-desc>
      </div>

      <div class="title">
        <strong> 关联信息 </strong>
      </div>
      <div class="formBox">
        <el-tabs v-model="tabActive" type="card" @tab-click="handleClick">
          <!-- 扩展属性 -->
          <el-tab-pane label="高级属性" name="attribute">
            <main style="height: calc(100vh - 495px)">
              <vxe-table
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="attributeData"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="标识" field="key" min-width="180" />
                <vxe-column field="name" title="名称" />
                <vxe-column field="value" title="值" />
              </vxe-table>
            </main>
          </el-tab-pane>
          <!-- 服务应用 -->
          <el-tab-pane label="服务应用" name="apply">
            <main style="height: calc(100vh - 485px)">
              <vxe-table
                ref="host-apply-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="applyLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="applyData.records"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="应用名称" field="name" />
                <vxe-column field="type" title="CI/CD类型">
                  <template #default="{ row }">
                    <span v-if="row.type">
                      {{ row.type.desc }}
                    </span>
                    <span v-else>{{ row.type }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="applicationEnvs" title="环境">
                  <template #default="{ row }">
                    <span
                      v-if="row.applicationEnvs && row.applicationEnvs.length"
                    >
                      {{ row.applicationEnvs.map((r) => r.envKey).join(',') }}
                    </span>
                    <span v-else>{{ row.applicationEnvs }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="description" title="描述" />
              </vxe-table>
            </main>
            <vone-pagination
              ref="applyPagination"
              style="position: static"
              :total="applyData.total"
              @update="getApplyPage"
            />
          </el-tab-pane>
          <!-- 本地脚本 -->
          <el-tab-pane label="本地脚本" name="script">
            <main style="height: calc(100vh - 485px)">
              <vxe-table
                ref="host-script-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="scriptLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="scriptData.records"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="脚本名称" field="scriptName" />
                <vxe-column field="scriptPath" title="脚本存放路径" />
                <vxe-column field="sender" title="下发人" width="100" />
                <vxe-column field="updateTime" title="下发时间" width="120" />
              </vxe-table>
            </main>
            <vone-pagination
              ref="scriptPagination"
              style="position: static"
              :total="scriptData.total"
              @update="getHostIpScript"
            />
          </el-tab-pane>
          <!-- 应用组件 -->
          <el-tab-pane label="应用组件" name="components">
            <main style="height: calc(100vh - 485px)">
              <vxe-table
                ref="host-components-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="componentsLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="componentsData.records"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="组件名称" field="name" min-width="180" />
                <vxe-column
                  field="appComponentsTypeKey"
                  title="组件类型"
                  min-width="180"
                />
                <vxe-column field="sendStatus" title="状态" width="130" />
                <vxe-column field="serverPort" title="组件端口">
                  <template #default="{ row }">
                    <span v-if="row.sendStatus">{{ row.sendStatus.desc }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="description" title="描述" />
              </vxe-table>
            </main>
            <vone-pagination
              ref="componentsPagination"
              style="position: static"
              :total="componentsData.total"
              @update="getComponents"
            />
          </el-tab-pane>
          <!-- 数据库组件 -->
          <el-tab-pane label="数据库组件" name="db">
            <main style="height: calc(100vh - 485px)">
              <vxe-table
                ref="host-db-table"
                class="vone-vxe-table"
                border
                resizable
                height="auto"
                :loading="dbLoading"
                show-overflow="tooltip"
                :empty-render="{ name: 'empty' }"
                :data="dbData.records"
                :column-config="{ minWidth: '120px' }"
                row-id="id"
              >
                <vxe-column title="组件名称" field="name" />
                <vxe-column field="dbComponentsTypeKey" title="组件类型" />
                <vxe-column field="updateTime" title="更新时间" />
                <vxe-column field="serverPort" title="组件端口" />
                <vxe-column field="description" title="描述" />
              </vxe-table>
            </main>
            <vone-pagination
              ref="dbPagination"
              style="position: static"
              :total="componentsData.total"
              @update="getDbData"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
    </div>
  </vone-drawer>
</template>

<script>
import {
  YibiaopanShangyi as ElIconYibiaopanShangyi,
  YibiaopanXiayi as ElIconYibiaopanXiayi,
} from '@element-plus/icons-vue'

import {
  apiCmdbGetHostById,
  apiPiplineHostIpScript,
} from '@/api/vone/cmdb/host'

import { apiBaseGetapplication } from '@/api/vone/cmdb/server'
import { apiBaseGetApplyList } from '@/api/vone/cmdb/application'
import { apiCmdbDbSeletByParams } from '@/api/vone/cmdb/database'

export default {
  components: {
    ElIconYibiaopanShangyi,
    ElIconYibiaopanXiayi,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: null,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tabActive: 'attribute',
      loading: false,
      basicData: {},
      currentIndex: undefined, // 当前数据的索引
      attributeData: [], // 扩展属性
      // ----------------------------------------------服务应用
      formDataApply: {
        hostId: this.id,
      },
      tableApplyOptions: {},
      applyData: {},
      applyLoading: false,
      // -----------------------------------------------本地脚本
      tableScriptOptions: {},
      scriptData: {},
      formDataScript: {},
      scriptLoading: false,
      // ------------------------------------------------应用组件
      componentsLoading: false,
      tableComponentsOptions: {},
      componentsData: {},
      formDataComponents: {
        hostId: this.id,
      },
      // ------------------------------------------------数据库组件
      dbLoading: false,
      tableDbOptions: {},
      dbData: {},
      formDataDb: {
        hostId: this.id,
      },
    }
  },
  mounted() {
    this.currentIndex = this.tableList.findIndex((item) => item.id === this.id)
    this.tabActive = 'attribute'
    this.getBasicInfo()
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name == 'script') {
        this.getHostIpScript()
      } else if (tab.name == 'apply') {
        this.getApplyPage()
      } else if (tab.name == 'components') {
        this.getComponents()
      } else if (tab.name == 'db') {
        this.getDbData()
      }
    },
    // 分页查询服务应用
    async getApplyPage() {
      this.applyLoading = true
      let params = {}
      const tableAttr = this.$refs.applyPagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataApply },
      }
      const res = await apiBaseGetapplication(params)
      this.applyLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.applyData = res.data
    },
    // 分页查询应用组件
    async getComponents() {
      this.componentsLoading = true
      let params = {}
      const tableAttr = this.$refs.componentsPagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataComponents },
      }
      const res = await apiBaseGetApplyList(params)
      this.componentsLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.componentsData = res.data
    },
    // 分页查询数据库组件
    async getDbData() {
      this.dbLoading = true
      let params = {}
      const tableAttr = this.$refs.dbPagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataDb },
      }
      const res = await apiCmdbDbSeletByParams(params)
      this.dbLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.dbData = res.data
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    // 基本信息
    async getBasicInfo(val) {
      this.loading = true
      const { data, isSuccess, msg } = await apiCmdbGetHostById(val || this.id)
      if (!isSuccess) {
        this.loading = false
        return this.$message.error(msg)
      }
      this.basicData = data
      this.tabActive = 'attribute'

      this.attributeData = data.hostExtends
      this.loading = false
    },
    // 本地脚本
    async getHostIpScript() {
      this.scriptLoading = true
      let params = {}
      this.$set(this.formDataScript, 'hostIp', this.basicData.ip)
      const tableAttr = this.$refs.scriptPagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formDataScript },
      }
      const res = await apiPiplineHostIpScript(params)
      this.scriptLoading = false

      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.scriptData = res.data
    },

    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getBasicInfo(this.tableList[this.currentIndex].id)
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++
      this.getBasicInfo(this.tableList[this.currentIndex].id)
    },
  },
}
</script>

<style lang="scss" scoped>
.pageBox {
  padding: 20px;
  .title {
    border-left: 3px solid var(--main-theme-color);
    margin-bottom: 10px;
    padding-left: 10px;
  }
  .formBox {
    padding: 10px;
    padding-bottom: 0;
  }
  :deep(.el-form .el-form-item__label) {
    color: var(--auxiliary-font-color);
  }
  :deep(.el-form-item__content) {
    display: flex;
  }
}
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>
