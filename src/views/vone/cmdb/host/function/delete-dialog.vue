<template>
  <div class="deleteDialog">
    <el-dialog
      title="删除结果"
      width="50%"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-collapse>
        <el-collapse-item v-model="result">
          <template slot="title">
            <div class="flex-layout">
              <!-- <span>{{ script.ip }}</span> -->
              <span class="flex" />
              <el-tag size="small" type="success"
                >成功：{{
                  result.filter((r) => r.success == true).length
                }}</el-tag
              >
              &nbsp; &nbsp;
              <el-tag size="small" type="danger"
                >失败：{{
                  result.filter((r) => r.success == false).length
                }}</el-tag
              >
            </div>
          </template>
          <el-table :data="result">
            <el-table-column prop="ip" label="服务器IP" />
            <el-table-column prop="success" label="结果">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.success" type="success" effect="dark">
                  <el-icon><el-icon-success /></el-icon>成功
                </el-tag>
                <el-tag v-else type="danger" effect="dark">
                  <el-icon><el-icon-error /></el-icon>失败
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="message"
              label="消息"
              width="100"
            />
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer">
        <el-button @click="onClose">关闭</el-button>&nbsp;
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  Success as ElIconSuccess,
  Error as ElIconError,
} from '@element-plus/icons-vue'
export default {
  components: {
    ElIconSuccess,
    ElIconError,
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    result: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  watch: {
    visible(v) {
      if (!v) return
    },
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.deleteDialog {
  :deep(.el-dialog__body) {
    max-height: 400px;
    overflow: auto;
  }
}
</style>
