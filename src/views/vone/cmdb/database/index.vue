<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="database_table"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['database_table']"
          @getTableData="selectDbCompentsDatas"
        />
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          class="ml-16"
          :icon="el-icon-setting"
          :disabled="!$permission('cmdb_database_add')"
          @click="addDbCompents"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="selectDbCompentsDatas"
        />
      </template>
    </vone-search-wrapper>
    <main :style="{ height: tableHeight }">
      <vxe-table
        ref="database_table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="dbDatas.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="名称" field="name" fixed="left">
          <template #default="{ row }">
            <a @click="showBasicDrawer(row)">{{ row.name }}</a>
          </template>
        </vxe-column>
        <vxe-column field="serverIp" title="IP" />
        <vxe-column field="serverPort" title="端口" />
        <vxe-column title="类型" field="dbComponentsTypeKey">
          <template #default="{ row }">
            <el-tooltip v-if="row.dbComponentsTypeKey" placement="top">
              <div slot="content">{{ row.dbComponentsTypeKey }}</div>
              <span>
                <svg-icon
                  v-if="row.appIconName == 'MySQL'"
                  icon-class="mysql"
                />
                <svg-icon
                  v-else-if="row.appIconName == 'DB2'"
                  icon-class="db2"
                />
                <svg-icon
                  v-else-if="row.appIconName == 'Oracle'"
                  icon-class="oracle"
                />
                <svg-icon v-else icon-class="wait_cir" />
              </span>
            </el-tooltip>
            <span v-else>--</span>
          </template>
        </vxe-column>
        <vxe-column title="安装路径" field="installationPath" />

        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('cmdb_database_edit')"
                :icon="el-icon-setting"
                @click="updateDbCompents(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('cmdb_database_delete')"
                :icon="el-icon-setting"
                @click="deleteById(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown
              trigger="click"
              :hide-on-click="false"
              @command="(e) => e && e(row)"
            >
              <el-button
                type="text"
                :icon="el-icon-setting"
                class="operation-dropdown"
              />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="!$permission('cmdb_database_authority')"
                  :icon="el-icon-setting"
                  :command="() => resDivision(row)"
                >
                  <span>权限分配</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination
      ref="pagination"
      :total="dbDatas.total"
      @update="selectDbCompentsDatas"
    />
    <!-- 基本信息抽屉 -->
    <detailDrawer
      v-if="basicDrawerParam.visible"
      :id="basicDrawerParam.id"
      v-model="basicDrawerParam.visible"
      :table-list="tableList"
    />
    <!-- 权限分配抽屉 -->
    <authDivision
      v-if="divisionDrawerParam.visible"
      v-bind="divisionDrawerParam"
      v-model="divisionDrawerParam.visible"
    />
  </page-wrapper>
</template>

<script>
import detailDrawer from './detailDb'
import authDivision from './auth-division.vue'

import {
  apiCmdbDbSeletByParams,
  apiCmdbDbDeleteById,
} from '@/api/vone/cmdb/database'

import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    detailDrawer,
    authDivision,
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
        {
          key: 'dbComponentsTypeKey',
          name: '类型',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择类型',
        },
        {
          key: 'serverIp',
          name: 'IP',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入IP',
        },
        {
          key: 'serverPort',
          name: '端口',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入端口',
        },
      ],
      tableList: [],
      tableLoading: false,
      basicDrawerParam: { visible: false },
      divisionDrawerParam: { visible: false },
      loading: true,
      formData: {
        name: '',
        dbComponentsTypeKey: '',
        serverIp: '',
        serverPort: '',
      },
      dbDatas: {},
      tableSeletData: [], // 选中的数据
      actions: [
        {
          name: '批量授权',
          fn: () => {
            if (!this.tableSeletData.length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.resDivisionAll(this.tableSeletData)
          },
          disabled: !this.$permission('cmdb_database_authority'),
        },
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete',
          fn: () => {
            if (!this.tableSeletData.length) {
              return this.$message.warning('请至少选择一条数据')
            }
            this.deleteById(this.tableSeletData.map((i) => i.id))
          },
          disabled: !this.$permission('cmdb_database_delete'),
        },
      ],
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  mounted() {
    this.seletDbType()
    // this.selectDbCompentsDatas()
  },
  methods: {
    // 获取table 选中的行
    selecteTableData(val) {
      this.tableSeletData = val
    },
    // 获取类型下拉框数据
    async seletDbType() {
      const res = await apiBaseDictNoPage({
        type: 'DB_COMPONENTS_TYPE',
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'dbComponentsTypeKey', res.data)
    },
    selectAllEvent({ checked }) {
      this.tableSeletData = this.$refs.database_table.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.tableSeletData = this.$refs.database_table.getCheckboxRecords()
    },
    // 获取数据库列表数据
    async selectDbCompentsDatas() {
      this.tableLoading = true
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      const res = await apiCmdbDbSeletByParams(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      res.data.records.forEach((element) => {
        element.appIconName = element.dbComponentsTypeKey
          ? element.dbComponentsTypeKey.split('_')[0]
          : ''
      })

      this.dbDatas = res.data
      this.tableList = res.data.records
    },

    // 弹出数据库组件信息抽屉
    showBasicDrawer(row) {
      this.basicDrawerParam = { visible: true, id: row.id }
    },
    // 新增功能
    addDbCompents() {
      this.$router.push({
        name: 'cmdb_database_add',
      })
    },
    // 编辑功能
    updateDbCompents(row) {
      this.$router.push({
        name: 'cmdb_database_edit',
        params: { id: row.id },
      })
    },
    // 批量权限分配
    resDivisionAll(table) {
      const dbIds = table.map((item) => item.id)
      // this.resDivision(dbIds, true)
      this.divisionDrawerParam = {
        visible: true,
        dbIds: dbIds,
      }
    },
    // 权限分配
    async resDivision(row, isBatch) {
      this.divisionDrawerParam = {
        visible: true,
        id: row.id,
      }
    },
    // 删除功能
    async deleteById(row) {
      await this.$confirm('确定删除该数据库组件吗?', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
      })
      const { isSuccess, msg } = await apiCmdbDbDeleteById(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        return this.$message.error(msg)
      }
      this.$message.success('删除成功')
      // this.submitSearch();
      this.selectDbCompentsDatas()
    },
  },
}
</script>
