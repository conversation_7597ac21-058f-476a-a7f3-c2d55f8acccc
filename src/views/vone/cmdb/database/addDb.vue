<template>
  <vone-edit-wrapper v-loading="loading" show-footer>
    <!-- 基本配置 -->

    <vone-card-wrapper title="基本配置">
      <el-form
        ref="basicForm"
        :model="basicForm"
        label-position="top"
        :rules="basicFormRules"
      >
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item prop="name" label="名称">
              <el-input
                v-model="basicForm.name"
                placeholder="请输入长度不超过32个字符的名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="dbComponentsTypeKey" label="类型">
              <el-select
                v-if="id !== undefined"
                v-model="basicForm.dbComponentsTypeKey"
                disabled
                clearable
                placeholder="请选择"
                style="width: 100%"
                @change="dbTypeChange"
              >
                <el-option
                  v-for="(item, index) in dbTypeDatas"
                  :key="index"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
              <el-select
                v-else
                v-model="basicForm.dbComponentsTypeKey"
                clearable
                placeholder="请选择"
                style="width: 100%"
                @change="dbTypeChange"
              >
                <el-option
                  v-for="(item, index) in dbTypeDatas"
                  :key="index"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="hostId" label="服务器IP">
              <el-select
                v-if="id !== undefined"
                v-model="basicForm.hostId"
                disabled
                style="width: 100%"
                clearable
                placeholder="请选择服务器IP"
              >
                <el-option
                  v-for="(item, index) in hostDatas"
                  :key="index"
                  :label="item.ip"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else
                v-model="basicForm.hostId"
                clearable
                style="width: 100%"
                placeholder="请选择服务器IP"
              >
                <el-option
                  v-for="(item, index) in hostDatas"
                  :key="index"
                  :label="item.ip"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="serverPort" label="服务端口">
              <el-input
                v-model.trim="basicForm.serverPort"
                :disabled="!!id"
                placeholder="请输入服务端口"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col v-else :span="6">
              <el-form-item prop="serverPort" label="服务端口">
                <el-input v-model="basicForm.serverPort" placeholder="请输入服务端口" />
              </el-form-item>
            </el-col> -->
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item prop="installationPath" label="安装路径">
              <el-input
                v-model="basicForm.installationPath"
                placeholder="请输入数据库安装路径"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="description" label="描述">
              <el-input
                v-model="basicForm.description"
                placeholder="请输入描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </vone-card-wrapper>
    <!-- 高级属性配置 -->
    <vone-card-wrapper v-if="levelTemplete.length > 0" title="高级属性配置">
      <!-- <el-button slot="actions">新增</el-button> -->
      <el-form
        ref="levelForm"
        label-position="top"
        :rules="levelFormRules"
        :model="levelForm"
      >
        <el-row :gutter="24">
          <template>
            <el-col
              v-for="(item, index) in levelTemplete"
              :key="index"
              :span="6"
            >
              <el-form-item :prop="item.key" :label="item.name">
                <el-input
                  v-if="item.key === 'INIT_PASSWORD'"
                  v-model="levelForm[item.key]"
                  :maxlength="item.maxlength"
                  show-password
                  :placeholder="item.name"
                />
                <el-input
                  v-else
                  v-model="levelForm[item.key]"
                  onkeyup="this.value=this.value.replace(/[, ]/g,'')"
                  :maxlength="item.maxlength"
                  :placeholder="item.name"
                />
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </vone-card-wrapper>
    <!-- 实例配置 -->
    <vone-card-wrapper title="实例配置">
      <template slot="actions">
        <el-button
          size="mini"
          type="primary"
          :icon="el-icon-setting"
          @click.native.prevent="
            addRow(exampleData.length - 1, exampleData, 'exampleData')
          "
        >
          新增
        </el-button>
      </template>
      <vxe-table
        class="vone-vxe-table"
        border
        resizable
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="exampleData"
        :column-config="{ minWidth: '120px' }"
        row-id="id"
      >
        <vxe-column title="实例名称" field="name">
          <template #default="{ row }">
            <el-input
              v-model="row.name"
              placeholder="请输入不超过50个字符的实例名称"
            />
          </template>
        </vxe-column>
        <vxe-column field="description" title="实例描述">
          <template #default="{ row }">
            <el-input
              v-model="row.description"
              placeholder="请输入不超过250个字符的属性描述"
            />
          </template>
        </vxe-column>
        <vxe-column title="操作" width="100">
          <template #default="{ rowIndex }">
            <!-- <el-button type="primary" icon="el-icon-plus" @click.native.prevent="addRow(scope.$index, exampleData,'exampleData')" /> -->

            <el-tooltip class="tooltipItem" content="删除" placement="top">
              <el-button
                style="padding: 0"
                :disabled="exampleData.length <= 1"
                size="mini"
                type="text"
                :icon="el-icon-setting"
                @click.native.prevent="
                  deleteExample(rowIndex, exampleData, 'exampleData')
                "
              />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </vone-card-wrapper>
    <!-- 用户配置 -->
    <vone-card-wrapper title="用户配置">
      <template slot="actions">
        <el-button
          size="mini"
          type="primary"
          :icon="el-icon-setting"
          @click.native.prevent="
            addRow(userData.length - 1, userData, 'userData')
          "
        >
          新增
        </el-button>
      </template>
      <vxe-table
        class="vone-vxe-table"
        border
        resizable
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="userData"
        :column-config="{ minWidth: '120px' }"
        row-id="id"
      >
        <vxe-column title="用户名称" field="userName">
          <template #default="{ row }">
            <el-input
              v-model="row.userName"
              placeholder="请输入不超过100个字符的属性标识"
            />
          </template>
        </vxe-column>
        <vxe-column field="password" title="用户密码">
          <template #default="{ row }">
            <el-input
              v-model="row.password"
              type="password"
              placeholder="请输入不超过100个字符的属性名称"
            />
          </template>
        </vxe-column>
        <vxe-column field="password" title="用户描述">
          <template #default="{ row }">
            <el-input
              v-model="row.description"
              placeholder="请输入不超过100个字符的属性描述"
            />
          </template>
        </vxe-column>
        <vxe-column field="userExample" title="关联实例">
          <template #default="{ row }">
            <el-select
              v-model="row.userExample"
              multiple
              clearable
              placeholder="请选择关联实例"
              @change="userData = [...userData]"
            >
              <el-option
                v-for="(item, index) in _exampleData"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column title="操作" width="100">
          <template #default="{ rowIndex }">
            <!-- <el-button type="primary" icon="el-icon-plus" @click.native.prevent="addRow(scope.$index, exampleData,'exampleData')" /> -->

            <el-tooltip class="tooltipItem" content="删除" placement="top">
              <el-button
                style="padding: 0"
                :disabled="userData.length <= 1"
                size="mini"
                type="text"
                :icon="el-icon-setting"
                @click.native.prevent="
                  deleteRow(rowIndex, userData, 'userData')
                "
              />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </vone-card-wrapper>
    <!-- 应用配置 -->
    <vone-card-wrapper title="应用配置">
      <template slot="actions">
        <el-button
          size="mini"
          type="primary"
          :icon="el-icon-setting"
          @click.native.prevent="
            addRow(applicationDbCompDatas.length - 1, applicationDbCompDatas)
          "
        >
          新增
        </el-button>
      </template>
      <vxe-table
        class="vone-vxe-table"
        border
        resizable
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="applicationDbCompDatas"
        :column-config="{ minWidth: '120px' }"
        row-id="id"
      >
        <vxe-column title="服务应用" field="applicationId">
          <template #default="{ row }">
            <el-select
              v-model="row.applicationId"
              clearabled
              placeholder="请选择服务应用"
              @change="applicationIdChange(row)"
            >
              <el-option
                v-for="(item, index) in systemList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="envKey" title="环境">
          <template #default="{ row }">
            <el-select
              v-model="row.envKey"
              clearable
              placeholder="请选择环境"
              :disabled="!row.envList"
              @change="applicationDbCompDatas = [...applicationDbCompDatas]"
            >
              <el-option
                v-for="(item, index) in row.envList"
                :key="index"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="applicationType" title="应用类型" />
        <vxe-column field="applicationDesc" title="应用描述" />
        <vxe-column title="操作" width="100">
          <template #default="{ rowIndex }">
            <!-- <el-button
                type="primary"
                icon="el-icon-plus"
                @click.native.prevent="
                  addRow(scope.$index, applicationDbCompDatas)
                "
              /> -->

            <el-tooltip class="tooltipItem" content="删除" placement="top">
              <el-button
                style="padding: 0"
                :disabled="applicationDbCompDatas.length <= 1"
                size="mini"
                type="text"
                :icon="el-icon-setting"
                @click.native.prevent="
                  deleteRow(rowIndex, applicationDbCompDatas)
                "
              />
            </el-tooltip>
            <!-- <el-button
                type="danger"
                icon="el-icon-minus"
                :disabled="applicationDbCompDatas.length<=1"
                @click.native.prevent="
                  deleteRow(scope.$index, applicationDbCompDatas)
                "
              /> -->
          </template>
        </vxe-column>
      </vxe-table>
    </vone-card-wrapper>

    <el-row slot="footer">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" @click="save">确定</el-button>
    </el-row>
  </vone-edit-wrapper>
</template>

<script>
import {
  apiCmdbDbSave,
  apiCmdbDbSeletById,
  apiCmdbDbExtendTemplete,
} from '@/api/vone/cmdb/database'
import { apiCmdbHostNoPage } from '@/api/vone/cmdb/host'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import {
  apiCmdbServerNoPage,
  apiBaseSelectBaseDatagById,
} from '@/api/vone/cmdb/server'

import { pick } from 'lodash/object'

export default {
  data() {
    return {
      id: this.$route.params.id,
      loading: false,

      businessSystemList: [],
      basicForm: {
        name: '',
        serverPort: '',
        installationPath: '',
        description: '',
      }, // 基本配置
      levelForm: {}, // 高级配置
      levelTemplete: [],
      applyList: [],
      desList: [],
      basicFormRules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过32个字符的名称',
            max: 32,
          },
        ],
        dbComponentsTypeKey: [
          {
            required: true,
            message: '请选择数据库组件类型',
          },
        ],
        hostId: [{ required: true, message: '请选择服务器IP' }],
        serverPort: [
          { required: true, message: '请输入服务端口号' },

          // {
          //   pattern: '^([0-9]|[1-9]\\d{1,3}|[1-7]\\d{4}|6[0-7]{2}[0-3][0-7])$',
          //   message: '请输入合法的服务端口号'
          // }
        ],
        installationPath: [
          {
            required: true,
            message: '请输入数据库安装路径',
          },
          {
            pattern:
              "^(([a-zA-Z]:(((\\\\(?! )[^\\/:*?<>\\''|\\\\]+)+\\\\?)|(\\\\)?)\\s*[^\\\\\\/])|[\\/][\\w-.]*[^\\\\\\/]){0,250}$",
            message:
              '数据库安装路径不合法,不能以“\\”或“/”结尾，不能为磁盘根目录且长度不能超过250',
          },
        ],
      },
      levelFormRules: {},
      exampleData: [
        {
          id: new Date().valueOf(),
          name: '',
          description: '',
        },
      ],
      userData: [
        {
          id: new Date().valueOf(),
          userName: '',
          password: '',
          description: '',
          userExample: [],
        },
      ],
      applicationDbCompDatas: [
        {
          id: new Date().valueOf(),
          businessapplicationId: '',
          applicationId: undefined,
          envKey: undefined,
          applicationType: '',
          applicationDesc: '',
          appUser: '',
        },
      ],

      dbTypeDatas: [], // 类型:
      hostDatas: [], // 服务器IP
      envList: [],
      systemList: [],
    }
  },
  computed: {
    _exampleData() {
      return this.exampleData.filter((v) => v.name)
    },
    _userData() {
      return this.userData.filter((v) => v.userName)
    },
    _id() {
      return `${this.basicForm.hostId}${this.basicForm.serverPort}`
    },
  },
  mounted() {
    this.seletDbType()
    this.selectHostData()
    // 获取服务应用数据
    this.getSystemList()

    if (this.id) {
      this.loadCurrentDbInfo()
    }
  },
  methods: {
    async loadCurrentDbInfo() {
      this.loading = true
      const res = await apiCmdbDbSeletById(this.id)
      if (!res.isSuccess) {
        this.loading = false
        return this.$message.error(res.msg)
      }
      this.basicForm = res.data
      // 查询高级属性
      this.dbTypeChange(this.basicForm.dbComponentsTypeKey)
      // 实例配置
      this.exampleData = res.data.dbComponentsInstances.length
        ? res.data.dbComponentsInstances
        : [{}]
      // 用户配置
      res.data.dbComponentsUsers.forEach((u) => {
        u.userExample = res.data.dbComponentsUserInstances
          .filter((ui) => ui.dbComponentsUserId === u.id)
          .map((ui) => ui.dbComponentsInstanceId)
      })
      this.userData = res.data.dbComponentsUsers.length
        ? res.data.dbComponentsUsers
        : [{}]
      // 高级属性数据回显
      const levelForm = {}
      res.data.dbComponentsExtends.forEach((a) => {
        levelForm[a.key] = a.value
      })
      this.levelForm = levelForm

      res.data.applicationDbs.forEach(async (d) => {
        await this.applicationIdChange(d, true)
      })

      // 应用配置
      this.applicationDbCompDatas = res.data.applicationDbs.length
        ? res.data.applicationDbs
        : [{}]
    },
    // 点击+号图标添加新的一栏
    async addRow(index, rows, str) {
      rows.splice(index + 1, 0, {})

      if (str == 'exampleData') {
        for (let index = 0; index < rows.length; index++) {
          // const element = array[index];
          rows[index].id = `${new Date().valueOf()}${index}`
        }
      }

      if (str == 'userData') {
        for (let index = 0; index < rows.length; index++) {
          // const element = array[index];
          rows[index].id = `${new Date().valueOf()}${index}8`
        }
      }
    },
    // addExampleRow() {

    // },
    // 点击减号删除一行
    deleteRow(index, rows) {
      rows.splice(index, 1)
    },
    /**
     * 删除实例，并移除用户配置中已关联的实例
     */
    deleteExample(index, rows) {
      const { name } = rows[index]
      this.deleteRow(index, rows)

      this.userData.forEach((u) => {
        if (u.userExample) {
          u.userExample = u.userExample.filter((e) => e !== name)
        }
      })
    },
    // 保存
    async save() {
      try {
        await Promise.all([this.$refs.basicForm.validate()])
      } catch (error) {
        return error
      }
      // this.loading = true
      const dbCompentsData = {}

      // 拼接基本信息
      // const id = this._id

      dbCompentsData.id = this.basicForm.id
      dbCompentsData.name = this.basicForm.name
      dbCompentsData.dbComponentsTypeKey = this.basicForm.dbComponentsTypeKey
      dbCompentsData.hostId = this.basicForm.hostId
      dbCompentsData.serverPort = this.basicForm.serverPort
      dbCompentsData.installationPath = this.basicForm.installationPath
      dbCompentsData.description = this.basicForm.description

      // 拼接高级属性信息

      const dbComponentsExtends = this.levelTemplete.map((r, i) => ({
        key: r.key,
        value: this.levelForm[r.key],
      }))
      dbCompentsData.dbComponentsExtends = dbComponentsExtends

      // 拼接实例信息
      const dbComponentsInstances = this.exampleData.map((r) => ({
        id: r.id,
        name: r.name,
        // dbComponentsId: id,
        // dbComponentsTypeKey: '',
        description: r.description,
      }))
      dbCompentsData.dbComponentsInstances = dbComponentsInstances

      // 拼接用户信息
      const dbComponentsUsers = this.userData.map((r) => ({
        id: r.id,
        // dbComponentsId: id,
        userName: r.userName,
        password: r.password,
        description: r.description,
      }))

      dbCompentsData.dbComponentsUsers = dbComponentsUsers

      // 拼接用户和实例关系信息
      const dbComponentsUserInstances = [].concat.apply(
        [],
        this.userData.map((u) => {
          return u.userExample
            ? u.userExample.map((e) => ({
                dbComponentsUserId: u.id,
                dbComponentsInstanceId: e,
              }))
            : []
        })
      )

      dbCompentsData.dbComponentsUserInstances = dbComponentsUserInstances

      // 拼接服务应用关系信息

      const hasData = this.applicationDbCompDatas.filter((r) => r.applicationId)

      const applicationDbs = hasData.length
        ? this.applicationDbCompDatas.map((r) => ({
            // applicationId: r.applicationId,
            ...pick(r, ['applicationId', 'envKey']),
          }))
        : []
      dbCompentsData.applicationDbs = applicationDbs
      const res = await apiCmdbDbSave(dbCompentsData)
      // this.loading = false
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.$message.success('保存配置')
      this.$router.go(-1)
    },
    // 获取类型下拉框数据
    async seletDbType() {
      this.loading = true
      const res = await apiBaseDictNoPage({
        type: 'DB_COMPONENTS_TYPE',
      })
      this.loading = false
      if (!res.isSuccess) {
        return
      }
      this.dbTypeDatas = res.data
    },
    // 获取IP下拉框数据
    async selectHostData() {
      this.loading = true
      const res = await apiCmdbHostNoPage()
      this.loading = false
      if (!res.isSuccess) {
        return
      }
      this.hostDatas = res.data
    },
    // 基本设置类型下拉框change事件
    async dbTypeChange(val) {
      this.loading = true
      const res = await apiCmdbDbExtendTemplete(val)
      if (!res.isSuccess) {
        this.loading = false
        return
      }
      this.levelTemplete = res.data
      const levelRoule = res.data.map((r) => ({
        required: r.notEmpty,
        message: r.message,
        max: r.maxlength,
        pattern: r.regexp,
        key: r.key,
      }))

      levelRoule.forEach((item) => {
        if (!this.levelFormRules[item.key]) {
          this.levelFormRules[item.key] = [item]
        } else {
          this.levelFormRules[item.key].push(item)
        }
      })
      this.loading = false
    },
    // 查询所有服务应用
    async getSystemList() {
      const res = await apiCmdbServerNoPage()
      if (!res.isSuccess) {
        return
      }
      this.systemList = res.data
    },
    // 服务应用下拉框change事件
    async applicationIdChange(row, isInit) {
      const res = await apiBaseSelectBaseDatagById(row.applicationId)
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }

      const ENV = res.data.applicationEnvs.length
        ? res.data.applicationEnvs.map((r) => ({
            code: r.echoMap.envKey?.code,
            name: r.echoMap.envKey?.name,
          }))
        : []
      this.$set(row, 'envList', ENV)

      this.$set(row, 'applicationType', res.data.type.desc)
      this.$set(row, 'applicationDesc', res.data.description)
      this.applicationDbCompDatas = [...this.applicationDbCompDatas]
    },
    cancle() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
.wrapper {
  overflow-y: auto;
}
:deep() {
  .vone-vxe-table .vxe-table--body-wrapper {
    height: initial;
  }
}
</style>
