<template>
  <div v-loading="loading">
    <el-card shadow="never">
      <vone-back :title="`【${information.name}】需求跟踪视图 `">
        <div slot="toolbar">
          <div>
            <el-dropdown
              trigger="click"
              :hide-on-click="false"
              @command="commandFn"
            >
              <el-button type="primary"> 配置 </el-button>
              <el-dropdown-menu slot="dropdown" class="track-dropdown-menu">
                <el-dropdown-item
                  v-for="item in configureList"
                  :key="item.id"
                  :command="item.id"
                >
                  <span>{{ item.name }}</span>
                  <el-icon><ElIconSetting /></el-icon>
                  <el-icon><ElIconSetting /></el-icon>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </vone-back>
      <el-row type="flex" justify="space-between" style="margin-top: 10px">
        <vone-desc>
          <vone-desc-item label="计划完成时间">
            {{ information.expectedTime }}
          </vone-desc-item>
          <vone-desc-item label="提出时间">
            {{ information.createTime }}
          </vone-desc-item>
          <vone-desc-item label="处理人">
            <vone-user-avatar
              :avatar-path="information.echoMap.handleBy.avatarPath"
              :name="information.echoMap.handleBy.name"
            />
          </vone-desc-item>
          <vone-desc-item label="负责人">
            <vone-user-avatar
              :avatar-path="information.echoMap.leadingBy.avatarPath"
              :name="information.echoMap.leadingBy.name"
            />
          </vone-desc-item>
          <vone-desc-item label="已完成工作项">
            <el-tag
              >{{ information.finishNum }} / {{ information.allNum }}</el-tag
            >
          </vone-desc-item>
        </vone-desc>
      </el-row>
    </el-card>
    <div class="contentBox">
      <div id="trackDom" ref="trackDom" />
    </div>

    <vone-custom-edit
      v-if="issueParam.editvisible"
      v-model="issueParam.editvisible"
      v-bind="issueParam"
      :type-code="typeCode"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />
  </div>
</template>

<script>
import { Check as ElIconCheck, Close as ElIconClose } from '@element-plus/icons-vue'
import G6 from '@antv/g6'
import {
  getTraceViewByIdeaId,
  getTraceViewBaseInfoByIdeaId,
} from '@/api/vone/reqmcenter/idea'
import fonts from '@/assets/iconfont/iconfont.json'
// import edit from '../components/reqm-edit-dialog.vue'
import dayjs from 'dayjs'

/**
 * 计算显示的字符串
 * @param {string} str 要裁剪的字符串
 * @param {number} maxWidth 最大宽度
 * @param {number} fontSize 字体大小
 */
const fittingString = (str, maxWidth, fontSize) => {
  if (!str) return ''
  const ellipsis = '...'
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
  let currentWidth = 0
  let res = str
  const pattern = new RegExp('[\u4E00-\u9FA5]+')
  str.split('').forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return
    if (pattern.test(letter)) {
      currentWidth += fontSize
    } else {
      currentWidth += G6.Util.getLetterWidth(letter, fontSize)
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      res = `${str.substr(0, i)}${ellipsis}`
    }
  })
  return res
}
const configureList = [
  {
    id: '1',
    name: '依赖关系',
    status: true,
  },
  {
    id: '2',
    name: '计划完成时间',
    status: true,
  },
  {
    id: '3',
    name: '是否延期',
    status: true,
  },
  {
    id: '4',
    name: '负责人',
    status: true,
  },
  {
    id: '5',
    name: '状态',
    status: true,
  },
]
export default {
  components: {
    ElIconCheck,
    ElIconClose,
  },
  data() {
    return {
      statusid: null,
      configureList,
      treeData: {},
      treeGraph: null,
      issueParam: {
        visible: false,
      },
      loading: false,
      information: {
        echoMap: {
          handleBy: {},
          leadingBy: {},
          typeCode: {},
        },
      },
      fonts,
      rightTabs: [
        {
          label: '活动',
          name: 'active',
        },
        {
          label: '评论',
          name: 'comment',
        },
      ],
      leftTabs: [
        {
          label: '关联需求',
          name: 'requiret',
        },
      ],
      typeCode: '',
      timer: null,
    }
  },
  mounted() {
    const tooltip = new G6.Tooltip({
      offsetX: 10,
      offsetY: 20,
      shouldBegin: (evt) => {
        if (
          evt.target.get('name') === 'relationship' ||
          evt.target.get('name') === 'title'
        ) {
          return true
        }
        return false
      },
      getContent(e) {
        const outDiv = document.createElement('div')
        outDiv.style.minWidth = '100px'
        if (e.target.get('name') === 'relationship') {
          var li = ''
          if (
            e.item.getModel().relation &&
            e.item.getModel().relation.length > 0
          ) {
            e.item.getModel().relation.forEach((e) => {
              li += `<li><span style="display: inline-block;height: 10px; width: 10px; border-radius:2px;margin-right: 10px;background: ${
                e.relationType.code == 'DEPEND'
                  ? '#B7EB8F'
                  : e.relationType.code == 'AFFECT'
                  ? '#ADC6FF'
                  : '#FFD591'
              };"></span>${e.name}</li>`
            })
            outDiv.innerHTML = `
    <ul>
      ${li}
    </ul>`
          } else {
            outDiv.innerHTML = '暂无关联信息'
          }
        } else if (e.target.get('name') === 'title') {
          outDiv.innerHTML = e.item.getModel().name
        }
        return outDiv
      },
      itemTypes: ['node'],
    })
    // 配置
    const defaultConfig = {
      width: this.$refs.trackDom.clientWidth,
      height: this.$refs.trackDom.clientHeight,
      modes: {
        default: ['zoom-canvas', 'drag-canvas'],
      },
      fitView: true, // 画布自适应
      animate: false,
      layout: {
        type: 'compactBox',
        direction: 'TB',
        getHeight: () => {
          return 150
        },
        getWidth: () => {
          return 248
        },
      },
      maxZoom: 1,
      // 节点样式
      defaultNode: {
        type: 'tracking',
        style: {
          fill: '#0095FF',
          background: {
            fill: 'red',
            stroke: 'red',
            padding: 0,
            radius: 2,
            lineWidth: 10,
          },
        },
        labelCfg: {
          style: {
            fill: '#fff',
            fontSize: 20,
            fontWeight: 800,
          },
        },
      },
      // 线的样式
      defaultEdge: {
        type: 'polyline',
        size: 2,
        style: {
          stroke: '#8862FA',
        },
      },
    }

    this.registerFn()
    // 画布主体
    this.treeGraph = new G6.TreeGraph({
      container: 'trackDom', // 制定画布的容器
      plugins: [tooltip],
      ...defaultConfig,
    })
    this.treeGraph.on('node:click', (e) => {
      if (!e.item.getModel().isproject) {
        this.typeCode = e.item.getModel().echoMap.typeCode.classify.code
        this.showInfo(e.item._cfg.id)
      }
    })
    this.getSourceList()
    this.getTraceViewBaseInfoByIdeaId()
    window.addEventListener('resize', this.windowResizeChange)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.windowResizeChange)
  },
  methods: {
    windowResizeChange() {
      if (!this.treeGraph || this.treeGraph.get('destroyed')) return
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.treeGraph.changeSize(
          this.$refs.trackDom.clientWidth,
          this.$refs.trackDom.clientHeight
        )
        this.treeGraph.fitView()
      }, 200)
    },
    getInitTableData() {},
    getIcon(type) {
      if (!type) return ''
      const matchIcon = this.fonts.glyphs.find((e) => {
        if ('el-icon-' + e.font_class == type) {
          e.unicode = String.fromCodePoint(e.unicode_decimal)
        }
        return 'el-icon-' + e.font_class == type
      })
      return matchIcon?.unicode
    },
    async getTraceViewBaseInfoByIdeaId() {
      const { ideaId } = this.$route.params
      const res = await getTraceViewBaseInfoByIdeaId(ideaId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.information = res.data
    },
    showInfo(e) {
      this.issueParam = {
        editvisible: true,
        title: '用户需求详情',
        id: e,
        // infoDisabled: true,
        tableList: [],
        rowTypeCode: e.typeCode,
        stateCode: e.stateCode,
      }
    },
    async getSourceList() {
      const { ideaId } = this.$route.params
      this.loading = true
      const res = await getTraceViewByIdeaId(ideaId)
      if (!res.isSuccess) {
        this.$messtatussage.warning(res.msg)
        return
      }
      this.loading = false
      if (res.data.idea.children && res.data.idea.children.length > 0) {
        res.data.idea.children.forEach((e) => {
          e.isproject = true
        })
      }
      this.treeData = res.data.idea
      this.treeGraph.data(this.treeData) // 加载数据
      this.treeGraph.render() // 渲染
    },
    async commandFn(event) {
      this.configureList.forEach((e) => {
        if (e.id == event) {
          e.status = !e.status
        }
      })
      await this.treeGraph.render()
      // this.treeGraph.refresh()
    },
    // 自定义节点和边
    registerFn() {
      const that = this
      G6.registerNode('tracking', {
        shapeType: 'tracking',
        draw(cfg, group) {
          const selectedList = that.configureList.filter(
            (e) => e.status == true
          )

          // 自定义模板
          const rectConfig = {
            width: 248,
            height: 96,
            fill: '#fff',
            shadowOffsetX: 0,
            shadowOffsetY: 4,
            shadowColor: 'rgba(32, 33, 36, 0.05)',
            shadowBlur: 12,
            radius: 2,
          }
          // 初始化模板
          const rect = group.addShape('rect', {
            attrs: {
              x: 0,
              y: 0,
              ...rectConfig,
            },
            name: 'box',
          })
          // 规定文字位置，对齐方式
          const textConfig = {
            textAlign: 'left',
            textBaseline: 'top',
          }
          if (cfg.isproject) {
            group.addShape('rect', {
              attrs: {
                ...textConfig,
                x: 0,
                y: 0,
                width: 248,
                height: 34,
                fill:
                  !cfg.name && cfg.id.split('-')[0] == 'node'
                    ? '#AEB5C21F'
                    : '#64BEFA1F',
                radius: 2,
              },
            })
            // icon
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 16,
                y: 10,
                // height: 16,
                // width: 16,
                fontFamily: 'iconfont',
                text: '\ue68c',
                fontSize: 14,
                fill:
                  !cfg.name && cfg.id.split('-')[0] == 'node'
                    ? '#AEB5C2'
                    : '#64BEFA',
              },
              name: 'icon-shape',
            })
            // 项目名称
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 40,
                y: 10,
                width: 200,
                height: 20,
                fontSize: 14,
                fontWeight: 600,
                fill:
                  !cfg.name && cfg.id.split('-')[0] == 'node'
                    ? '#AEB5C2'
                    : '#64BEFA',
                text:
                  !cfg.name && cfg.id.split('-')[0] == 'node'
                    ? '未分组'
                    : fittingString(cfg.name, 192, 14),
              },
              name: 'title',
            })
            // 描述
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 16,
                y: 44,
                height: 12,
                text:
                  !cfg.name && cfg.id.split('-')[0] == 'node'
                    ? '未被规划到项目中的工作项'
                    : cfg.description
                    ? fittingString(cfg.description, 212, 12)
                    : '暂无描述',
                fontSize: 12,
                fill: '#8A8F99',
              },
            })
            const isShow = that.configureList.find((e) => e.id == '4')?.status
            if (isShow && cfg.name && cfg.id.split('-')[0] != 'node') {
              // 头像
              group.addShape('image', {
                attrs: {
                  ...textConfig,
                  x: 16,
                  y: 70,
                  width: 20,
                  height: 20,
                  img: require(`@/assets/avatar/${
                    cfg.echoMap?.leadingBy?.avatarPath
                      ? cfg.echoMap?.leadingBy?.avatarPath
                      : 'avatar1'
                  }.png`),
                },
                name: 'icon',
              })
              // 人员
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 44,
                  y: 74,
                  height: 12,
                  text: cfg.echoMap?.leadingBy?.name
                    ? cfg.echoMap?.leadingBy?.name
                    : '无',
                  fontSize: 12,
                  fill: '#202124',
                },
              })
              rect.attr({
                height: 102,
              })
            } else {
              rect.attr({
                height: 70,
              })
            }
          } else {
            if (cfg.echoMap.typeCode?.icon) {
              // icon
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 16,
                  y: 12,
                  height: 16,
                  width: 16,
                  fontFamily: 'iconfont',
                  text: that.getIcon(cfg.echoMap.typeCode?.icon),
                  fontSize: 16,
                  fill: cfg.echoMap.typeCode?.color,
                },
                name: 'icon-shape',
              })
            }
            // 名称
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: cfg.echoMap.typeCode?.icon ? 38 : 16,
                y: 12,
                width: 200,
                height: 20,
                fontSize: 14,
                fill: '#202124',
                text: fittingString(cfg.name, 200, 16),
              },
              name: 'title',
            })
            var numberBBox = {
              width: 0,
            }
            // 编号
            const numberbox = group.addShape('rect', {
              attrs: {
                ...textConfig,
                x: 16,
                y: 40,
                width: 30,
                height: 18,
                fill: '#ADB0B81F',
                radius: 2,
              },
              name: 'status',
            })
            const number = group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 20,
                y: 43,
                width: 30,
                height: 12,
                fontSize: 12,
                fill: '#ADB0B8',
                text: cfg.code,
              },
            })
            numberBBox = number.getBBox()
            numberbox.attr({
              width: numberBBox.width + 8,
            })
            // 优先级
            if (cfg.echoMap.priorityCode?.icon) {
              // icon
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 32 + numberBBox.width,
                  y: 41,
                  height: 18,
                  width: 16,
                  fontFamily: 'iconfont',
                  text: that.getIcon(cfg.echoMap.priorityCode?.icon),
                  fontSize: 16,
                  fill: cfg.echoMap.priorityCode?.color,
                },
                name: 'icon-shape',
              })
            }
            var relationshipboxBBox = {
              width: 0,
            }
            var plannedtimeboxBBox = {
              width: 0,
            }
            selectedList.forEach((e, index) => {
              if (e.id == '1') {
                const relationshipbox = group.addShape('rect', {
                  attrs: {
                    ...textConfig,
                    x: 16,
                    y: 67,
                    width: 30,
                    height: 18,
                    fill: '#ADB0B81F',
                    radius: 2,
                  },
                  name: 'relationship',
                })
                var relationshipBBox = {
                  width: 0,
                }
                // 依赖关系
                const relationshipName = group.addShape('text', {
                  attrs: {
                    ...textConfig,
                    x: 20,
                    y: 69,
                    height: 12,
                    text:
                      !cfg.relation || cfg.relation.length == 0
                        ? '关系：无'
                        : '关系：',
                    fontSize: 12,
                    fill: '#ADB0B8',
                  },
                  name: 'relationship',
                })
                relationshipBBox = relationshipName.getBBox()
                // 依赖关系
                if (cfg.relation) {
                  const x = relationshipBBox.width + 16
                  cfg.relation.forEach((e, i) => {
                    group.addShape('rect', {
                      attrs: {
                        ...textConfig,
                        x: x + i * 12,
                        y: 67 + index * 18 + 5,
                        height: 8,
                        width: 8,
                        fill:
                          e.relationType.code == 'DEPEND'
                            ? '#B7EB8F'
                            : e.relationType.code == 'AFFECT'
                            ? '#ADC6FF'
                            : '#FFD591',
                        radius: 1,
                      },
                      name: 'relationship',
                    })
                  })
                }
                relationshipbox.attr({
                  width:
                    relationshipBBox.width +
                    8 +
                    (cfg.relation && cfg.relation.length > 0
                      ? cfg.relation.length * 8
                      : 0),
                })
                relationshipboxBBox = relationshipbox.getBBox()
              } else if (e.id == '2') {
                const plannedtimebox = group.addShape('rect', {
                  attrs: {
                    ...textConfig,
                    x: 16,
                    y: 93,
                    width: 30,
                    height: 18,
                    fill: '#ADB0B81F',
                    radius: 2,
                  },
                })
                var plannedtimeBBox = {
                  width: 0,
                }
                // 计划完成时间
                const plannedtime = group.addShape('text', {
                  attrs: {
                    ...textConfig,
                    x: 20,
                    y: 96,
                    height: 12,
                    text: cfg.planEtime
                      ? dayjs(cfg.planEtime).format('YYYY-MM-DD') + ' 计划完成'
                      : '暂未设置计划完成时间',
                    fontSize: 12,
                    fill: '#ADB0B8',
                  },
                })
                plannedtimeBBox = plannedtime.getBBox()
                plannedtimebox.attr({
                  width: plannedtimeBBox.width + 8,
                })
                plannedtimeboxBBox = plannedtimebox.getBBox()
              } else if (e.id == '3') {
                // 延期
                group.addShape('text', {
                  attrs: {
                    ...textConfig,
                    x:
                      plannedtimeboxBBox.width > 0
                        ? 16 + plannedtimeboxBBox.width + 12
                        : 20,
                    y: 96,
                    height: 12,
                    fontFamily: 'iconfont',
                    text: cfg.delay ? '\ue6b8' : '',
                    fontSize: 14,
                    fill: '#EA6362',
                  },
                })
              } else if (e.id == '4') {
                const isExhibition = selectedList.some((item) => {
                  return item.id == '2' || item.id == '3'
                })
                // 头像
                group.addShape('image', {
                  attrs: {
                    ...textConfig,
                    x: 16,
                    y: isExhibition ? 117 : 96,
                    width: 20,
                    height: 20,
                    img: require(`@/assets/avatar/${
                      cfg.echoMap?.leadingBy?.avatarPath
                        ? cfg.echoMap?.leadingBy?.avatarPath
                        : 'avatar1'
                    }.png`),
                  },
                  name: 'icon',
                })
                // 人员
                group.addShape('text', {
                  attrs: {
                    ...textConfig,
                    x: 44,
                    y: isExhibition ? 121 : 100,
                    height: 12,
                    text: cfg.echoMap?.leadingBy?.name
                      ? cfg.echoMap?.leadingBy?.name
                      : '无',
                    fontSize: 12,
                    fill: '#202124',
                  },
                })
              } else if (e.id == '5') {
                const isExhibition = selectedList.some((item) => {
                  return item.id == '2' || item.id == '3'
                })
                var statusBBox = {
                  width: 0,
                }
                // 状态
                const statusbox = group.addShape('rect', {
                  attrs: {
                    ...textConfig,
                    y: isExhibition ? 117 : 96,
                    width: 30,
                    height: 18,
                    fill: cfg.echoMap?.stateCode?.color,
                  },
                  name: 'status',
                })
                const status = group.addShape('text', {
                  attrs: {
                    ...textConfig,
                    y: isExhibition ? 120 : 99,
                    height: 12,
                    fontSize: 12,
                    fill: '#FFFFFF',
                    text: cfg.echoMap?.stateCode?.name,
                  },
                })
                statusBBox = status.getBBox()
                status.attr({
                  x: 248 - 16 - statusBBox.width,
                })
                statusbox.attr({
                  x: 248 - 16 - statusBBox.width - 4,
                  width: statusBBox.width + 8,
                })
              }
            })
            const isExhibition = selectedList.some((item) => {
              return item.id == '2' || item.id == '3'
            })
            const isExhibitionLast = selectedList.some((item) => {
              return item.id == '4' || item.id == '5'
            })
            if (isExhibition && isExhibitionLast) {
              rect.attr({
                height: 149,
              })
            } else {
              rect.attr({
                height: 129,
              })
            }
            if (cfg.echoMap.planId) {
              // 迭代
              const iterationbox = group.addShape('rect', {
                attrs: {
                  ...textConfig,
                  x:
                    relationshipboxBBox.width > 0
                      ? relationshipboxBBox.width + 24
                      : 16,
                  y: 67,
                  width: 30,
                  height: 18,
                  fill: '#ADB0B81F',
                  radius: 2,
                },
              })
              var iterationBBox = {
                width: 0,
              }
              const iteration = group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x:
                    relationshipboxBBox.width > 0
                      ? relationshipboxBBox.width + 28
                      : 20,
                  y: 70,
                  height: 12,
                  text: fittingString(
                    cfg.echoMap.planId?.name,
                    248 - 16 * 2 - relationshipboxBBox.width - 12,
                    12
                  ),
                  fontSize: 12,
                  fill: '#ADB0B8',
                },
              })
              iterationBBox = iteration.getBBox()
              iterationbox.attr({
                width: iterationBBox.width + 8,
              })
            }
          }
          return rect
        },
        getAnchorPoints() {
          return [
            [0.5, 0], // 上侧中间
            [0.5, 1], // 下侧中间
          ]
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.mydropdown {
  position: absolute;
  top: 198px;
  right: 32px;
  // z-index: 9999;
  :deep(.el-dropdown-menu) {
    width: 200px;
  }
}
#trackDom {
  width: 100%;
  min-height: calc(100vh - 228px);
}
.track-dropdown-menu {
  width: 200px;
  .el-dropdown-menu__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.circular {
  display: inline-block;
  height: 10px;
  width: 10px;
  border-radius: 5px;
  background: red;
}
.information {
  display: flex;
  border-bottom: 1px solid var(--input-border-color);
  .information-left {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    color: var(--main-font-color);
    margin-bottom: 12px;
  }
  .el-descriptions {
    :deep(.el-descriptions__body) {
      background-color: var(--main-bg-color);
    }
    :deep(.el-descriptions-item) {
      padding-bottom: 12px;
      .descriptions-label {
        font-size: 14px;
        color: var(--auxiliary-font-color);
      }
      .descriptions-content {
        font-size: 14px;
        color: var(--main-font-color);
      }
    }
  }
}
:deep(.avatar img) {
  width: 20px;
  height: 20px;
}
.contentBox {
  background-color: #fff;
  // border-bottom: 1px solid var(--el-divider);
}
</style>
