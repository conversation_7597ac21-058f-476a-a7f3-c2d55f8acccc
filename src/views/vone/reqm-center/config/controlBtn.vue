<template>
  <div class="vab-control">
    <!-- <div @click="fullscreen">
        <i class="iconfont el-icon-direction-fullscreen" />
        全屏
      </div> -->
    <div @click="amplification">
      <el-icon class="iconfont"><ElIconSetting /></el-icon>
      放大
    </div>
    <div @click="narrow">
      <el-icon class="iconfont"><ElIconSetting /></el-icon>
      缩小
    </div>
    <!-- <div @click="reset">
        <i class="iconfont el-icon-application-renew" />
        重置
      </div> -->
  </div>
</template>

<script>
import {
  DirectionEnlarge as ElIconDirectionEnlarge,
  DirectionUnenlarge as ElIconDirectionUnenlarge,
} from '@element-plus/icons-vue'
export default {
  components: {
    ElIconDirectionEnlarge,
    ElIconDirectionUnenlarge,
  },
  name: 'Control',
  data() {
    return {}
  },
  methods: {
    amplification() {
      this.$emit('control', 'amplification')
    },
    narrow() {
      this.$emit('control', 'narrow')
    },
    reset() {
      this.$emit('control', 'reset')
    },
    fullscreen() {
      this.$emit('control', 'fullscreen')
    },
  },
}
</script>

<style lang="scss" scoped>
.vab-control {
  box-sizing: border-box;
  position: absolute;
  right: 50px;
  bottom: 50px;
  z-index: 99;
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #f7f7fa;
  & > div {
    display: flex;
    flex-direction: column;
    padding: 6px;
    align-items: center;
    justify-content: center;
    &:hover {
      cursor: pointer;
      background-color: var(--main-hover-theme-color);
    }
    i {
      font-size: 22px;
      margin-bottom: 2px;
    }
  }
}
</style>
