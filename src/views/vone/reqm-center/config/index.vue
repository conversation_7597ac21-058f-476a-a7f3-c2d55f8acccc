<template>
  <div class="container">
    <header>
      <span>
        <div class="returnbox">
          <el-icon class="iconfont"><ElIconArrowLeft /></el-icon>
        </div>
        <strong>
          {{ issueInfo.code }}
          {{ issueInfo.name }}
        </strong>
      </span>

      <el-dropdown trigger="click" :hide-on-click="false">
        <el-button>
          显示
          <el-icon class="iconfont"><ElIconDirectionDown /></el-icon>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <a class="refresh" @click="refresh">恢复默认</a>
          <el-dropdown-item
            v-for="(item, index) in actions"
            :key="index"
            :icon="item.icon"
            :command="item"
          >
            <el-row class="flexRow" type="flex" justify="space-between">
              <span v-if="item.isGroup" class="textGroup">
                {{ item.name }}
              </span>
              <span v-else>
                {{ item.name }}
              </span>
              <el-switch
                v-if="!item.isGroup"
                v-model="item.isOpen"
                active-color="#3E7BFA"
                inactive-color="#BCC2CB"
                @change="(val) => handleClick(val, item)"
              />
              <!-- <el-switch v-if="!item.isGroup" v-model="item.isOpen" size="mini" /> -->
            </el-row>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </header>
    <div id="container" ref="container" v-loading="pageLoading" />
    <Control @control="controlClick" />
  </div>
</template>

<script>
import {
  DirectionBack as ElIconDirectionBack,
  DirectionDown as ElIconDirectionDown,
} from '@element-plus/icons-vue'

const nameMap = {
  relationRequirements: '关联',
  dependRequirements: '前置依赖',
  affectRequirements: '后置影响',
}

function pushNonEmptyArrays(obj) {
  const result = []
  for (const key in obj) {
    if (Array.isArray(obj[key]) && obj[key].length > 0) {
      result.push([
        {
          key: key,
          name: nameMap[key],
          value: obj[key],
        },
      ])
    }
  }
  return result
}

import LogicFlow from '@logicflow/core'
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'
import Control from './controlBtn'

import NodeArr from './loficflow'
// import data from './data.js'
import sequence from './edges/customEdge'

import { apiAlmIssueInfo } from '@/api/vone/project/issue'
import { showIssueTopology } from '@/api/vone/alm'
import { DagreLayout } from '@antv/layout'
import _ from 'lodash'

function getRootEdges(lf, edges, currentData, arr) {
  if (currentData.id == edges[0]?.sourceNodeId) {
    return
  }
  const rootEdges = edges?.filter((item) => {
    return item.targetNodeId == currentData.id
  })
  if (rootEdges.length > 0) {
    arr.push(...rootEdges)
    rootEdges.forEach((item) => {
      const currentData = lf.getNodeDataById(item.sourceNodeId)
      const edges = lf.graphModel.getNodeEdges(item.sourceNodeId)
      getRootEdges(lf, edges, currentData, arr)
    })
  }
}

export default {
  components: {
    Control,
    ElIconDirectionBack,
    ElIconDirectionDown,
  },
  data() {
    return {
      actions: [
        {
          id: 1,
          name: '父级',
          isGroup: false,
          isOpen: true,
          key: 'parentRequirement',
          type: 'parentNode',
        },
        {
          id: 2,
          name: '子级',
          isGroup: false,
          isOpen: true,
          type: 'autoNode',
          key: 'childRequirements',
        },
        {
          id: 3,
          name: '关联',
          isGroup: false,
          isOpen: true,
          type: 'assignNode',
          key: 'total',
        },
        {
          id: 4.4,
          name: '关联',
          isGroup: true,
          isOpen: true,
          key: 'group',
        },
        {
          id: 5,
          name: '需求',
          isGroup: false,
          isOpen: true,
          type: 'startNode',
          key: 'relation',
        },
        // {
        //   id: 6,
        //   name: '用户需求',
        //   isGroup: false,
        //   isOpen: true,
        //   type: 'switchNode',
        //   key: 'ideas'
        // },
        // {
        //   id: 7,
        //   name: '测试用例',
        //   isGroup: false,
        //   isOpen: true,
        //   type: 'cycleNode',
        //   key: 'testCases'
        // },
        {
          id: 8,
          name: '任务',
          isGroup: false,
          isOpen: true,
          type: 'taskNode',
          key: 'tasks',
        },
        // {
        //   id: 9,
        //   name: '代码',
        //   isGroup: false,
        //   isOpen: true,
        //   type: 'recordNode',
        //   key: 'codeRepositories'
        // }
      ],
      issueInfo: {},
      lf: {},
      topoData: {},
      originData: {},
      option: {
        rankdir: 'TB',
      },
      pageLoading: false,
    }
  },
  created() {
    this.getInfo()
    this.getTopology()
  },
  methods: {
    back() {
      this.$router.go(-1)
    },
    controlClick(val) {
      switch (val) {
        case 'amplification':
          this.lf.zoom(true)
          break
        case 'narrow':
          this.lf.zoom(false)
          break
        case 'reset':
          this.lf.resetZoom()
          this.lf.focusOn()
          break
        case 'fullscreen':
          this.lf.fullScreen()
          break
        default:
          break
      }
    },
    async getTopology() {
      const res = await showIssueTopology(this.$route.params.requireId)

      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }

      if (!res.data) return
      this.getItemState(res.data)

      this.$set(res.data.requirement, 'originNode', true)
      const childreData = res.data.childRequirements.length
        ? res.data.childRequirements.map((r, index) => ({
            id: r.id,
            type: 'autoNode',
            x: 250 + index * 250,
            y: 780,
            properties: r,
          }))
        : []

      const childEdge = childreData.length
        ? childreData.map((r, index) => ({
            id: `edge_child_${index}`,
            type: 'sequence',
            text: { value: '子级', x: 250, y: 500 },
            sourceNodeId: 'origin_1',
            targetNodeId: r.id,
            startPoint: { x: 250, y: 420 },
            endPoint: { x: childreData[index].x, y: 710 },
          }))
        : []

      const parentNode = res.data.parentRequirement
        ? [res.data.parentRequirement].map((r) => ({
            id: 'parent_1',
            type: 'parentNode',
            x: 250,
            y: 100,
            properties: r,
          }))
        : []

      const parentEdge = parentNode.length
        ? parentNode.map((r, index) => ({
            id: 'edge_1',
            type: 'sequence',
            sourceNodeId: 'origin_1',
            targetNodeId: 'parent_1',
            text: { value: '父级' },
          }))
        : []

      const nodes = Number(
        res.data.relation.affectRequirements.length +
          res.data.relation.dependRequirements.length +
          res.data.relation.relationRequirements.length
      )

      const totalNum =
        nodes +
        res.data.ideas?.length +
        res.data.testCases?.length +
        res.data.codeRepositories?.length

      const totalNode =
        totalNum != 0
          ? [
              {
                id: 'total_1',
                type: 'assignNode',
                x: 650,
                y: 350,
                properties: {
                  list: [
                    {
                      id: 1,
                      name: '需求',
                      total:
                        res.data.relation.relationRequirements.length +
                        res.data.relation.dependRequirements.length +
                        res.data.relation.affectRequirements.length,
                    },
                    // {
                    //   id: 2,
                    //   name: '用户需求',
                    //   total: res.data.ideas?.length || 0
                    // },
                    // {
                    //   id: 3,
                    //   name: '测试用例',
                    //   total: res.data?.testCases?.length || 0
                    // },
                    // {
                    //   id: 4,
                    //   name: '代码库',
                    //   total: res.data.codeRepositories?.length || 0
                    // },
                    {
                      id: 5,
                      name: '任务',
                      total: res.data.tasks?.length || 0,
                    },
                  ],
                },
              },
            ]
          : []

      const totalEdge = totalNode.length
        ? totalNode.map((r, index) => ({
            id: 'edge_2',
            type: 'sequence',
            sourceNodeId: 'origin_1',
            targetNodeId: 'total_1',
            text: { value: '关联' },
          }))
        : []

      const relitionNode =
        nodes != 0
          ? [
              {
                id: 'issue_1',
                type: 'startNode',
                x: 1070,
                y: 50,
                properties: {
                  affectRequirements: res.data.relation.affectRequirements,
                  dependRequirements: res.data.relation.dependRequirements,
                  relationRequirements: res.data.relation.relationRequirements,
                },
              },
            ]
          : []

      const relitionEdge = relitionNode.length
        ? relitionNode.map((r, index) => ({
            id: 'edge_3',
            type: 'sequence',
            sourceNodeId: 'total_1',
            targetNodeId: 'issue_1',
            startPoint: { x: 730, y: 350 },
            endPoint: { x: 1010, y: 60 },
            pointsList: [
              { x: 730, y: 350 },
              { x: 890, y: 350 },
              { x: 890, y: 15 },
              { x: 1010, y: 15 },
            ],
          }))
        : []

      const ideaNode = res.data.ideas.length
        ? res.data.ideas.map((r, index) => ({
            id: 'idea_1',
            type: 'switchNode',
            x: 1200,
            y: 190,
            properties: {
              ideaList: res.data.ideas,
            },
          }))
        : []

      const ideaEdge = ideaNode.length
        ? ideaNode.map((r, index) => ({
            id: 'edge_4',
            type: 'sequence',
            sourceNodeId: 'total_1',
            targetNodeId: 'idea_1',
            startPoint: { x: 730, y: 350 },
            endPoint: { x: 1012, y: 210 },
            pointsList: [
              { x: 730, y: 350 },
              { x: 890, y: 350 },
              { x: 890, y: 160 },
              { x: 1012, y: 160 },
            ],
          }))
        : []

      const caseNode = res.data.testCases
        ? res.data.testCases.map((r, index) => ({
            id: 'case_1',
            type: 'cycleNode',
            x: 1200,
            y: 325,
            properties: {
              list: res.data.testCases ? res.data.testCases : [],
            },
          }))
        : []

      const caseEdge = caseNode.length
        ? caseNode.map((r, index) => ({
            id: 'edge_5',
            type: 'sequence',
            sourceNodeId: 'total_1',
            targetNodeId: 'case_1',
            startPoint: { x: 730, y: 350 },
            endPoint: { x: 1012, y: 350 },
            pointsList: [
              { x: 730, y: 350 },
              { x: 890, y: 350 },
              { x: 890, y: 300 },
              { x: 1012, y: 300 },
            ],
          }))
        : []

      const codeNode = res.data.codeRepositories.length
        ? res.data.codeRepositories.map((r, index) => ({
            id: 'code_1',
            type: 'recordNode',
            x: 1200,
            y: 465,
            properties: {
              list: res.data.codeRepositories,
            },
          }))
        : []

      const codeEdge = codeNode.length
        ? codeNode.map((r, index) => ({
            id: 'edge_6',
            type: 'sequence',
            sourceNodeId: 'total_1',
            targetNodeId: 'code_1',
            startPoint: { x: 730, y: 350 },
            endPoint: { x: 1012, y: 420 },
            pointsList: [
              { x: 730, y: 350 },
              { x: 890, y: 350 },
              { x: 890, y: 440 },
              { x: 1012, y: 440 },
            ],
          }))
        : []

      const taskNode = res.data.tasks.length
        ? res.data.tasks.map((r, index) => ({
            id: 'task_1',
            type: 'taskNode',
            x: 1200,
            y: 600,
            properties: {
              list: res.data.tasks,
            },
          }))
        : []

      const taskEdge = taskNode.length
        ? taskNode.map((r, index) => ({
            id: 'edge_7',
            type: 'sequence',
            sourceNodeId: 'total_1',
            targetNodeId: 'task_1',
            startPoint: { x: 730, y: 350 },
            endPoint: { x: 1012, y: 660 },
            pointsList: [
              { x: 730, y: 350 },
              { x: 890, y: 350 },
              { x: 890, y: 570 },
              { x: 1012, y: 570 },
            ],
          }))
        : []

      const array = pushNonEmptyArrays(res.data.relation)

      const rightNode = array.length
        ? array.map((r, index) => ({
            id: `right_${index}`,
            type: 'relationNode',
            x: 1750,
            y: 50 + index * 200,
            properties: r,
          }))
        : []

      const rightEdge = rightNode.length
        ? rightNode.map((r, index) => ({
            id: `edge_right_${index}`,
            type: 'sequence',
            sourceNodeId: 'issue_1',
            targetNodeId: r.id,
            startPoint: { x: 1130, y: 20 },
            endPoint: { x: 1560, y: rightNode[index].y - 30 },
            pointsList: [
              { x: 1130, y: 20 },
              { x: 1480, y: 20 },
              { x: 1480, y: rightNode[index].y - 30 },
              { x: 1560, y: rightNode[index].y - 30 },
            ],
          }))
        : []

      const data = {
        nodes: [
          // 父节点
          ...parentNode,
          // 源节点
          {
            id: 'origin_1',
            type: 'originNode',
            x: 250,
            y: 350,
            properties: res.data.requirement,
          },
          // 统计
          ...totalNode,

          // 需求
          ...relitionNode,
          // 用户需求
          ...ideaNode,
          // 测试用例
          ...caseNode,
          // 代码库
          ...codeNode,
          ...childreData,
          ...rightNode,
          ...taskNode,
        ],
        edges: [
          ...parentEdge,
          ...totalEdge,
          ...relitionEdge,
          ...ideaEdge,
          ...caseEdge,
          ...codeEdge,
          ...childEdge,
          ...rightEdge,
          ...taskEdge,
        ],
      }
      this.topoData = data
      this.originData = data

      this.initlogicFlow()
    },
    // renderGraphData(){

    // },
    async getInfo() {
      const res = await apiAlmIssueInfo(this.$route.params.requireId)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.issueInfo = res.data
    },
    async initlogicFlow() {
      this.lf = new LogicFlow({
        container: this.$refs.container,
        // 不允许编辑
        isSilentMode: true,
        hoverOutline: false,
      })
      Object.values(NodeArr).forEach((item) => {
        this.lf.register({
          type: item.type,
          view: item.view,
          model: item.model,
        })
      })
      this.lf.register(sequence)
      this.lf.setDefaultEdgeType('sequence')
      // 注册鼠标进入节点事件
      this.lf.on('node:mouseenter', async ({ data, e }) => {
        // 需要开启动画的边
        const animationEdges = []
        // 获取当前节点相关联的边
        const edges = this.lf.graphModel.getNodeEdges(data.id)
        if (data.id == edges[0]?.sourceNodeId) {
          return
        }
        // 获取当前节点子节点指向当前节点的边，直至根节点
        await getRootEdges(this.lf, edges, data, animationEdges)
        animationEdges.forEach((item) => {
          this.lf.setElementZIndex(item.id, 'top')
          item.isAnimation = true
        })
      })
      // 注册鼠标离开节点事件
      this.lf.on('node:mouseleave', async ({ data, e }) => {
        // 需要开启动画的边
        const animationEdges = []
        // 获取当前节点相关联的边
        const edges = this.lf.graphModel.getNodeEdges(data.id)
        if (data.id == edges[0]?.sourceNodeId) {
          return
        }
        // 获取当前节点子节点指向当前节点的边，直至根节点
        await getRootEdges(this.lf, edges, data, animationEdges)
        animationEdges.forEach((item) => {
          this.lf.setElementZIndex(item.id, 0)
          item.isAnimation = false
        })
      })
      // 通过layoutOptimiza优化布局
      // this.layoutOptimiza({}, this.data)
      this.lf.render(this.topoData)
      this.lf.zoom(0.9)
    },
    // 优化布局
    layoutOptimiza(option = {}, data) {
      const { nodes = [], edges = [] } = data
      // 使用DagreLayout生成布局数据，得到edges和nodes的位置
      const layoutInstance = new DagreLayout({
        type: 'dagre',
        rankdir: 'TB',
        nodesep: 70,
        ranksep: 70,
        ...option,
      })
      const layoutData = layoutInstance.layout({
        nodes: nodes.map((node) => ({
          id: node.id,
          model: node,
        })),
        edges: edges.map((edge) => ({
          source: edge.sourceNodeId,
          target: edge.targetNodeId,
          model: edge,
        })),
      })
      const newGraphData = {
        nodes: [],
        edges: [],
      }
      layoutData.nodes.forEach((node) => {
        const data = node.model || {}
        data.x = node.x
        data.y = node.y
        data.width = 220
        data.height = 140
        newGraphData.nodes.push(data)
      })
      layoutData.edges.forEach((edge) => {
        // @ts-ignore: pass edge data
        const { model } = edge
        const data = { ...model }
        data.pointsList = this.calcPointsList(model, newGraphData.nodes)
        if (data.pointsList) {
          const first = data.pointsList[0]
          const last = data.pointsList[data.pointsList.length - 1]
          data.startPoint = { x: first.x, y: first.y }
          data.endPoint = { x: last.x, y: last.y }
        } else {
          data.startPoint = undefined
          data.endPoint = undefined
        }
        newGraphData.edges.push(data)
      })
      // 将布局重绘到画布上
      this.lf.render(newGraphData)
    },
    pointFilter(points) {
      const allPoints = points
      let i = 1
      while (i < allPoints.length - 1) {
        const pre = allPoints[i - 1]
        const current = allPoints[i]
        const next = allPoints[i + 1]
        if (
          (pre.x === current.x && current.x === next.x) ||
          (pre.y === current.y && current.y === next.y)
        ) {
          allPoints.splice(i, 1)
        } else {
          i++
        }
      }
      return allPoints
    },
    calcPointsList(model, nodes) {
      const pointsList = []
      if (this.option.rankdir === 'TB') {
        const newSourceNodeData = nodes.find(
          (node) => node.id === model.sourceNodeId
        )
        const newTargetNodeData = nodes.find(
          (node) => node.id === model.targetNodeId
        )
        if (newSourceNodeData.x > newTargetNodeData.x) {
          // 向左上连线
          pointsList.push({
            x: newSourceNodeData.x,
            y: newSourceNodeData.y + newSourceNodeData.height / 2,
          })
          pointsList.push({
            x: newSourceNodeData.x,
            y:
              newTargetNodeData.y -
              newTargetNodeData.height / 2 -
              (model.offset || 50),
          })
          pointsList.push({
            x: newSourceNodeData.x - newTargetNodeData.x,
            y:
              newTargetNodeData.y -
              newTargetNodeData.height / 2 -
              (model.offset || 50),
          })
          pointsList.push({
            x: newSourceNodeData.x - newTargetNodeData.x,
            y: newTargetNodeData.y - newTargetNodeData.height / 2,
          })
          return this.pointFilter(pointsList)
        } else {
          // 向右上连线
          pointsList.push({
            x: newSourceNodeData.x,
            y: newSourceNodeData.y + newSourceNodeData.height / 2,
          })
          pointsList.push({
            x: newSourceNodeData.x,
            y:
              newTargetNodeData.y -
              newTargetNodeData.height / 2 -
              (model.offset || 50),
          })
          pointsList.push({
            x:
              newSourceNodeData.x + (newTargetNodeData.x - newSourceNodeData.x),
            y:
              newTargetNodeData.y -
              newTargetNodeData.height / 2 -
              (model.offset || 50),
          })
          pointsList.push({
            x:
              newSourceNodeData.x + (newTargetNodeData.x - newSourceNodeData.x),
            y: newTargetNodeData.y - newTargetNodeData.height / 2,
          })
          return this.pointFilter(pointsList)
        }
      }
      return undefined
    },
    refresh() {
      this.actions.forEach((element) => {
        element.isOpen = true
      })
      this.getTopology()
    },
    getItemState(data) {
      const list = []
      const allData = _.omit(data, [
        'requirement',
        'relation',
        'parentRequirement',
      ])
      Object.keys(allData).map((key) => {
        if (data[key] && data[key].length) {
          list.push(key)
        }
      })
      const nodes = Number(
        data.relation.affectRequirements.length +
          data.relation.dependRequirements.length +
          data.relation.relationRequirements.length
      )
      if (nodes != 0) {
        list.push('total')
      }

      if (data.parentRequirement) {
        list.push('parentRequirement')
      }

      const groupList = ['relation', 'ideas', 'testCases', 'codeRepositories']

      list.forEach((element) => {
        if (groupList.indexOf(element) == 1) {
          list.push('group')
        }
      })

      const hJson = this.actions.filter((i, j) => list.indexOf(i.key) !== -1)
      this.actions = hJson
    },
    handleClick(e, item) {
      if (e == false) {
        const types = [
          'assignNode',
          'switchNode',
          'startNode',
          'recordNode',
          'cycleNode',
          'relationNode',
        ]
        // 如果是统计节点的取消,同时取消关联的所有节点
        if (item.key == 'total') {
          const leftNode = this.topoData.nodes.filter(
            (r) => types.indexOf(r.type) == -1
          )
          this.$set(this.topoData, 'nodes', leftNode)
          this.$set(
            this.topoData,
            'edges',
            this.topoData.edges.filter(
              (r) =>
                r.sourceNodeId != 'total_1' &&
                r.targetNodeId != 'total_1' &&
                r.sourceNodeId != 'issue_1'
            )
          )

          this.actions.forEach((element) => {
            if (types.includes(element.type)) {
              this.$set(element, 'isOpen', false)
            }
          })
        } else {
          const id = this.topoData.nodes.find((r) => r.type == item.type).id
          this.$set(
            this.topoData,
            'nodes',
            this.topoData.nodes.filter((r) => r.type != item.type)
          )
          this.$set(
            this.topoData,
            'edges',
            this.topoData.edges.filter(
              (r) => r.sourceNodeId != id && r.targetNodeId != id
            )
          )
        }
        this.initlogicFlow()
      } else {
        // 如果是统计节点的开启,默认开始关联的所有节点
        if (item.key == 'total') {
          const relationTypes = [
            'switchNode',
            'startNode',
            'recordNode',
            'cycleNode',
          ]
          this.actions.forEach((element) => {
            if (relationTypes.includes(element.type)) {
              this.$set(element, 'isOpen', true)
            }
          })
        }

        this.getTopology()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #fff;
  header {
    line-height: 52px;
    height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #ebeef5;
    .returnbox {
      cursor: pointer;
      height: 32px;
      line-height: 34px;
      text-align: center;
      width: 32px;
      background: #fff;
      box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
      border-radius: 50%;
      display: inline-block;
      margin-right: 16px;
      .iconfont {
        color: var(--main-theme-color, #3e7bfa);
      }
    }
    strong {
      font-size: 16px;
    }
  }

  #container {
    width: 100%;
    height: calc(100vh - 132px);
    padding: 16px;
    :deep(.lf-graph) {
      foreignObject {
        overflow: visible !important;
      }
    }
  }
}

.flexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-switch {
    margin-left: 20px;
  }
}
.refresh {
  margin: 10px;
  color: var(--main-theme-color);
  display: inline-block;
}
.textGroup {
  font-size: 12px;
  color: var(--font-second-color);
}
</style>
