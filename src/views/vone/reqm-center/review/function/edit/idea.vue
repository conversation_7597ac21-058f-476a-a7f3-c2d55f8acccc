<template>
  <!-- vone-custom-table -->
  <el-card>
    <div slot="header">
      {{ `共有${total}个关联的需求` }}
      <div style="float: right">
        <el-button
          type="text"
          :icon="el-icon-setting"
          @click="addTask = 0"
          >新增</el-button
        >
        <el-button type="text" :icon="ElIconLink" @click="addTask = 1"
          >关联已有需求</el-button
        >
      </div>
    </div>

    <simpleAddIssue
      v-if="addTask == 0"
      no-file
      :type-code="'ISSUE'"
      :issue-id="id"
      style="margin-bottom: 20px"
      @success="init"
      @cancel="addTask = null"
    />
    <el-row v-if="addTask == 1" :gutter="24">
      <el-col :span="20">
        <el-form :model="taskForm">
          <el-form-item prop="taskId">
            <el-select
              v-model="taskForm.taskId"
              placeholder="请输入需求名称"
              clearable
              filterable
              remote
              :remote-method="getIssueList"
              :loading="requireLoading"
              class="requireSelect"
              @focus="setOptionWidth"
            >
              <el-option
                v-for="ele in taskList"
                :key="ele.id"
                :value="ele.id"
                :label="ele.name"
                :style="{ width: selectOptionWidth }"
                :title="ele.name"
              >
                {{ `${ele.code}   ${ele.name}` }}
              </el-option>
            </el-select>
            <!-- <el-select v-model="taskForm.taskId" placeholder="请选择需求" multiple filterable clearable>
                <el-option v-for="item in taskList" :key="item.id" :label="`${item.code}  ${item.name}`" :value="item.id" />
              </el-select> -->
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" @click="saveTask">保存</el-button>
      </el-col>
    </el-row>

    <!-- 关联需求列表 -->
    <div class="contain">
      <el-table
        ref="idea-issue"
        :loading="tableLoading"
        :table-options="tableOptions"
        row-key="id"
        table-key="idea-issue"
        :table-data="tableData"
        height="calc(100vh - 418px)"
        @getTableData="getTableData"
      >
        <template>
          <el-table-column
            prop="name"
            label="需求名称"
            show-overflow-tooltip
            fixed
          >
            <template slot-scope="{ row }">
              <div class="name_icon">
                <a class="table_title" @click="toRequire(row.name)">
                  {{ row.code + ' ' + row.name }}</a
                >
              </div>

              <el-table-column label="处理人" width="120" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <span
                    v-if="row.handleBy && row.echoMap && row.echoMap.handleBy"
                  >
                    <vone-user-avatar
                      :avatar-path="row.echoMap.handleBy.avatarPath"
                      :name="row.echoMap.handleBy.name"
                    />
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="stateCode" label="状态" width="90">
                <template slot-scope="scope">
                  <div
                    :style="{
                      textAlign: 'center',
                      borderRadius: '5px',
                      border: `1px solid ${
                        scope.row.stateCode &&
                        scope.row.echoMap &&
                        scope.row.echoMap.stateCode
                          ? scope.row.echoMap.stateCode.color
                          : '#ccc'
                      }`,
                      width: '80px',
                      color: `${
                        scope.row.stateCode &&
                        scope.row.echoMap &&
                        scope.row.echoMap.stateCode
                          ? scope.row.echoMap.stateCode.color
                          : '#ccc'
                      }`,
                    }"
                  >
                    <span>
                      <span
                        v-if="
                          scope.row.stateCode &&
                          scope.row.echoMap &&
                          scope.row.echoMap.stateCode
                        "
                        >{{ scope.row.echoMap.stateCode.name }}</span
                      >
                      <span v-else>
                        {{ scope.row.stateCode }}
                      </span>
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="projectId"
                label="所属项目"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span
                    v-if="row.projectId && row.echoMap && row.echoMap.projectId"
                  >
                    {{ row.echoMap.projectId.name }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="planEtime"
                label="计划完成时间"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{
                    scope.row.planEtime
                      ? scope.row.planEtime.substring(0, 11)
                      : ''
                  }}
                </template>
              </el-table-column>
            </template>
          </el-table-column></template
        ></el-table
      >
    </div></el-card
  >
</template>

<script>
// import { iderTorequire } from '@/api/vone/project/issue.js'
import { apiAlmRequirementNoPage } from '@/api/vone/project/issue'
import simpleAddIssue from './simple-add.vue'
import { debounce } from 'lodash'
import { apiAlmPutIdeaAndIssue } from '@/api/vone/reqmcenter/idea'
import { apiAlmIssuePage } from '@/api/vone/project/issue'

export default {
  components: {
    simpleAddIssue,
  },
  props: {
    id: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      total: '',
      addTask: null,
      taskList: [],
      taskForm: {},
      selectOptionWidth: '',
      requireLoading: false,
      tableLoading: false,
      // tableOptions: {},
      tableOptions: {
        isOperation: true,
        isSelection: true,
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            {
              // disabled: !this.$permission('reqm_center_idea_del'),
              type: 'icon', // 为icon则是图标
              label: '取消关联', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.deleteIssue, // 操作事件
            },
          ],
          // 更多操作按钮
          moreData: [],
        },
      },
      tableData: {},
      formData: {},
    }
  },
  mounted() {
    this.getTableData()
    // this.getIssueList()
  },
  methods: {
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    toRequire(val) {
      this.$store.dispatch('project/getName', val)
      this.$router.push({
        name: 'reqm_center_list',
      })
    },
    async getTableData() {
      this.tableLoading = true
      let params = {}

      this.$set(this.formData, 'ideaId', [this.id])
      const tableAttr = this.$refs['idea-issue'].exportTableQueryData()
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData },
      }
      const res = await apiAlmIssuePage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.total = res.data.total
    },

    // 查需求列表
    getIssueList: debounce(async function (query, requireId) {
      try {
        if (query && query != '') {
          this.requireLoading = true
          const res = await apiAlmRequirementNoPage({
            name: query,
            parentId: requireId,
          })
          this.requireLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.taskList = res.data.filter((r) => r.ideaId == null)
        }
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),
    init() {
      this.addTask = null
      this.getTableData()
      // this.$emit('initList')
      //
    },
    // 用户需求关联需求
    async saveTask() {
      const res = await apiAlmPutIdeaAndIssue({
        ideaId: this.id,
        requirementId: this.taskForm.taskId,
        correlatedType: 'ADD',
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('关联成功')
      this.addTask = null
      this.$set(this.taskForm, 'taskId', '')
      this.getTableData()
    },
    async deleteIssue(val) {
      this.$confirm(
        `你确定要删除【 ${val.name} 】和用户需求的关联吗?`,
        '删除',
        {
          confirmButtonText: '确认',
          type: 'warning',
          closeOnClickModal: false,
        }
      )
        .then(async () => {
          const { isSuccess, msg } = await apiAlmPutIdeaAndIssue({
            ideaId: this.id,
            requirementId: val.id,
            correlatedType: 'DELETE',
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success('取消关联成功')
          this.getTableData()
          // this.$emit('initList')
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  border-left: 3px solid var(--main-theme-color, #3e7bfa);
  font-weight: bold;
  & > div span:first-child {
    font-weight: bold;
  }
  & > div span:nth-child(2) {
    font-size: 12px;
    font-weight: normal;
  }
  & > div {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.tableC {
  max-height: 329px;
  overflow-y: auto;
  overflow-x: hidden;
}

:deep(.el-card__body) {
  overflow-x: auto;
  // display: flex;
  // flex-direction: column;
  // align-items:stretch;
}
.contain {
  min-width: 740px;
}
</style>
