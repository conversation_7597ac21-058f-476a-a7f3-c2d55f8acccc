<template>
  <vone-simple-add
    ref="simpleAdd"
    v-model.trim="form.name"
    :rules="rules"
    :model="form"
    label="标题"
    v-model:file-list="fileList"
    :loading="saveLoading"
    @submit="saveSubmit"
    @open="openCreateDetail"
    @cancel="$emit('cancel')"
  >
    <!-- 项目 -->
    <el-dropdown
      v-model="form.projectId"
      class="dropList minWidth"
      trigger="click"
      @command="changeProject"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="projectMap && projectMap[form.projectId]">
            <el-row type="flex">
              <!-- <i :class="`iconfont ${projectMap[form.typeCode].icon}`" :style="{ color:`${projectMap[form.typeCode].color ? projectMap[form.typeCode].color : '#ccc'}`}" class="iconStyle" /> -->
              <span>
                {{
                  projectMap[form.projectId].name.length > 7
                    ? projectMap[form.projectId].name.substr(0, 7) + '...'
                    : projectMap[form.projectId].name
                }}
              </span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置项目 </span>
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="projectList && projectList.length > 0">
          <el-dropdown-item
            v-for="item in projectList"
            :key="item.id"
            :command="item.id"
          >
            <span v-if="item.icon">
              <i
                :class="`iconfont ${item.echoMap.typeCode.icon}`"
                :style="{
                  color: `${
                    item.echoMap.typeCode.color
                      ? item.echoMap.typeCode.color
                      : '#ccc'
                  }`,
                }"
              />
            </span>
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选项目" />
      </el-dropdown-menu>
    </el-dropdown>

    <!-- 需求类型下拉列表 -->
    <el-dropdown
      v-model="form.typeCode"
      class="dropList minWidth"
      trigger="click"
      @command="changeClass"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="defectMap && defectMap[form.typeCode]">
            <el-row type="flex">
              <i
                :class="`iconfont ${defectMap[form.typeCode].icon}`"
                :style="{
                  color: `${
                    defectMap[form.typeCode].color
                      ? defectMap[form.typeCode].color
                      : '#ccc'
                  }`,
                }"
                class="iconStyle"
              />
              <span> {{ defectMap[form.typeCode].name }} </span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置分类 </span>
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="typeCodeList && typeCodeList.length > 0">
          <el-dropdown-item
            v-for="item in typeCodeList"
            :key="item.code"
            :command="item.code"
          >
            <span v-if="item.icon">
              <i
                :class="`iconfont ${item.icon}`"
                :style="{ color: `${item.color ? item.color : '#ccc'}` }"
              />
            </span>
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选分类" />
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 人员下拉列表 -->

    <vone-remote-user
      v-model="form.handleBy"
      :default-data="defaultDataByputBy"
      class="minWidth"
    />

    <el-popover v-if="!noFile" width="517" trigger="hover">
      <vone-upload
        ref="uloadFile"
        biz-type="IDEA_FILE_UPLOAD"
        @change="onChange"
      />
      <div slot="upload" />
      <vone-empty v-if="fileList.length === 0" desc="暂无附件" />
      <el-icon
        class="iconfont"
        style="cursor: pointer; color: var(--main-theme-color); margin: 0 12px"
        ><el-icon-application-attachment
      /></el-icon>
    </el-popover>
  </vone-simple-add>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  ApplicationAttachment as ElIconApplicationAttachment,
} from '@element-plus/icons-vue'

import { apiAlmGetTypeNoPage } from '@/api/vone/alm/index'

import { apiAlmSaveIssue } from '@/api/vone/project/issue'

import { apiAlmProjectNoPage } from '@/api/vone/project/index'

import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import dayjs from 'dayjs'

export default {
  components: {
    ElIconDirectionDown,
    ElIconApplicationAttachment,
  },
  props: {
    testCaseId: {
      type: Number,
      default: null,
    },
    planId: {
      type: String,
      default: '',
    },
    issueId: {
      // 需求id,用于需求关联任务时,保存时传需求id
      type: String,
      default: undefined,
    },
    noFile: Boolean,

    typeCode: {
      // 分类枚举值,只作为查询分类的入参,ISSUE,BUG,TASK,RISK,及区分保存时调哪个接口
      type: String,
      default: undefined,
    },
    noEpic: Boolean, // 史诗拆分用户故事,需求新增时,过滤掉史诗
  },
  data() {
    return {
      projectMap: {},
      projectList: [],
      defaultDataByputBy: [],
      form: {
        name: '',
        typeCode: '',
        handleBy: '',
        envCode: '',
      },
      userList: [],
      typeCodeList: [], // 用户需求分类
      typeList: [], // 分类下拉框
      envList: [], // 环境
      fileList: [],
      defectMap: {}, // 缺陷分类
      userMap: {},
      envMap: {},
      saveLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          {
            pattern: '^.{1,250}$',
            message: '请输入不超过250个字符组成的标题',
            trigger: 'change',
          },
        ],
      },
    }
  },
  computed: {
    projectKey() {
      return this.$route.params.projectKey
    },
  },
  mounted() {
    // this.getIssueType()
    this.getUser()
    this.getProjectList()
  },
  methods: {
    onChange(val) {
      this.fileList = val
    },
    async getIssueType(val) {
      const res = await apiAlmGetTypeNoPage(val, 'ISSUE')
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.typeCodeList = res.data
      this.defectMap = this.typeCodeList.reduce(
        (r, v) => (r[v.code] = v) && r,
        {}
      )
      this.$set(this.form, 'typeCode', res.data[0].code)
    },
    // 打开新建详细数据弹窗
    openCreateDetail() {
      this.$emit('createDetail')
      this.$emit('cancel')
    },
    // 切换类型
    changeClass(value) {
      this.$set(this.form, 'typeCode', value)
    },
    // 切换项目
    changeProject(value) {
      this.$set(this.form, 'projectId', value)
      this.getIssueType(value)
    },
    changeUser(v) {
      this.$set(this.form, 'handleBy', v)
    },
    // 查询人员
    async getUser() {
      const res = await apiBaseAllUserNoPage()

      if (!res.isSuccess) {
        return
      }
      this.userList = res.data
      this.userMap = this.userList.reduce((r, v) => (r[v.id] = v) && r, {})

      this.defaultDataByputBy = res.data.filter(
        (r) => r.id == this.userList[0].id
      )
      this.$set(this.form, 'handleBy', this.userList[0].id)
    },
    async saveSubmit() {
      const params = {
        // name: this.form.name,
        // typeCode: this.form.typeCode,
        // createdBy: this.form.handleBy,
        // putBy: this.form.handleBy,
        // handleBy: this.form.handleBy,
        // leadingBy: this.form.handleBy,
        // planEtime: dayjs().add(240, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        // planStime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        // description: '',
        // files: this.fileList.length ? this.fileList : []
        name: this.form.name,
        typeCode: this.form.typeCode,
        estimateHour: 1,
        priorityCode: 'HIGH',
        createdBy: this.form.handleBy,
        putBy: this.form.handleBy,
        handleBy: this.form.handleBy,
        leadingBy: this.form.handleBy,
        projectId: this.form.projectId,
        planEtime: dayjs().add(240, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        planStime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        description: '',
        // requirementId: this.issueId ? this.issueId : null,
        // parentId: this.issueId,
        ideaId: this.issueId,
        files: this.fileList.length ? this.fileList : [],
      }
      this.saveIssue(params)
    },
    async saveIssue(params) {
      this.saveLoading = true
      const res = await apiAlmSaveIssue(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('创建成功')
      this.$emit('success')
      this.form = {
        name: '',
        typeCode: this.typeCodeList[0].code,
        userId: this.userList[0].id,
      }
      this.fileList = []
      // this.$refs['uloadFile'].resetData()
    },
    async getProjectList() {
      const res = await apiAlmProjectNoPage()
      if (!res.isSuccess) {
        return
      }
      this.projectList = res.data
      this.projectMap = res.data.reduce((r, v) => (r[v.id] = v) && r, {})
      this.$set(this.form, 'projectId', res.data[0].id)
      this.getIssueType(this.form.projectId)
    },
  },
}
</script>

<style lang="scss" scoped>
.dropList {
  min-width: 60px;
  margin: 0 6px;
}
.textTitle {
  white-space: nowrap;
}
.el-icon--right {
  margin-top: 9px;
}
.iconStyle {
  margin-top: 10%;
  margin-left: 5px;
  margin-right: 5px;
}
:deep(.el-dropdown-menu) {
  height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  // white-space: nowrap;
  //  overflow: hidden;
}
:deep(.el-select) {
  width: 20%;
}
:deep(.el-row--flex) {
  margin: 0 3px;
}
.minWidth {
  min-width: 100px;
}
</style>
