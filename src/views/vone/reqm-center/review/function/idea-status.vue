<template>
  <!-- 需求状态流转 -->
  <div>
    <el-dropdown
      ref="dropdown"
      trigger="click"
      :disabled="infoDisabled || noPermission"
      @command="handleCommand"
    >
      <a @click="findNextNode">
        <span
          class="tagCustom"
          :style="{
            border: `1px solid ${
              idea.stateCode && idea.echoMap && idea.echoMap.stateCode
                ? idea.echoMap.stateCode.color
                : '#ccc'
            }`,
            color: `${
              idea.stateCode && idea.echoMap && idea.echoMap.stateCode
                ? idea.echoMap.stateCode.color
                : '#ccc'
            }`,
          }"
        >
          <span>
            <span
              v-if="idea.stateCode && idea.echoMap && idea.echoMap.stateCode"
              >{{ idea.echoMap.stateCode.name }}</span
            >
            <span v-else>
              {{ idea.stateCode }}
            </span>
          </span>

          <el-icon><ElIconSetting /></el-icon>
          <el-icon class="iconfont el-icon--right"
            ><ElIconDirectionDown
          /></el-icon>
        </span>
      </a>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in nextNode"
          :key="item.id"
          :command="item.stateCode"
          >{{ item.name }}</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import {
  Loading as ElIconLoading,
  DirectionDown as ElIconDirectionDown,
} from '@element-plus/icons-vue'
import {
  apiAlmIdeaFindNextNode,
  apiAlmIdeaFlow,
} from '@/api/vone/reqmcenter/idea'
export default {
  components: {
    ElIconLoading,
    ElIconDirectionDown,
  },
  props: {
    idea: {
      type: Object,
      default: () => {},
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
    noPermission: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nextNode: [],
      onSearch: false,
    }
  },
  methods: {
    async handleCommand(val) {
      const res = await apiAlmIdeaFlow(this.idea.id, this.idea.stateCode, val)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('状态流转成功')
      this.$emit('changeFlow')
    },
    async findNextNode() {
      if (this.noPermission || this.infoDisabled) {
        return
      }
      this.onSearch = true
      const res = await apiAlmIdeaFindNextNode(this.idea.id).catch(() => {
        this.onSearch = false
        this.$refs.dropdown.hide()
        return
      })
      this.onSearch = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      if (!res.data.length) {
        this.$refs.dropdown.hide()
        this.$message.warning('暂无当前节点流转权限')
        return
      }
      this.nextNode = res.data
    },
  },
}
</script>
