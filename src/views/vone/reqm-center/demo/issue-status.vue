<template>
  <!-- 需求状态流转 -->
  <div>
    <el-dropdown
      ref="dropdown"
      trigger="click"
      :disabled="infoDisabled"
      @command="handleCommand"
    >
      <a @click="findNextNode">
        <div
          :style="{
            textAlign: 'center',
            borderRadius: '5px',
            border: `1px solid ${
              issue.stateCode && issue.echoMap && issue.echoMap.stateCode
                ? issue.echoMap.stateCode.color
                : '#ccc'
            }`,
            width: '80px',
            color: `${
              issue.stateCode && issue.echoMap && issue.echoMap.stateCode
                ? issue.echoMap.stateCode.color
                : '#ccc'
            }`,
          }"
        >
          <span>
            <span
              v-if="issue.stateCode && issue.echoMap && issue.echoMap.stateCode"
              >{{ issue.echoMap.stateCode.name }}</span
            >
            <span v-else>
              {{ issue.stateCode }}
            </span>
          </span>

          <el-icon><ElIconSetting /></el-icon>
          <el-icon class="iconfont el-icon--right"
            ><ElIconDirectionDown
          /></el-icon>
        </div>
      </a>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in nextNode"
          :key="item.id"
          :command="item.stateCode"
          >{{ item.name }}</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import {
  Loading as ElIconLoading,
  DirectionDown as ElIconDirectionDown,
} from '@element-plus/icons-vue'
import {
  apiAlmIssueFindNextNode,
  apiAlmRequirementFlow,
} from '@/api/vone/project/issue'
export default {
  components: {
    ElIconLoading,
    ElIconDirectionDown,
  },
  props: {
    issue: {
      type: Object,
      default: () => {},
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nextNode: [],
      onSearch: false,
    }
  },
  methods: {
    async handleCommand(val) {
      const res = await apiAlmRequirementFlow(
        this.issue.id || this.issue.bizId, // 因为在迭代列表中需求id的字段名为bizId
        this.issue.stateCode,
        val
      )
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('状态流转成功')
      this.$emit('changeFlow')
    },
    async findNextNode() {
      this.onSearch = true
      const res = await apiAlmIssueFindNextNode(
        this.issue.id || this.issue.bizId // 因为在迭代列表中需求id的字段名为bizId
      )
      this.onSearch = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      if (!res.data.length) {
        this.$refs.dropdown.hide()
        this.$message.warning('暂无当前节点流转权限')
        return
      }
      this.nextNode = res.data
    },
  },
}
</script>
