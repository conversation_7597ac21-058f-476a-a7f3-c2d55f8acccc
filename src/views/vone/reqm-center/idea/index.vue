<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="reqm-idea-table"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :show-column-sort="true"
          :extra="extraData"
          :table-ref="$refs['reqm-idea-table']"
          @getTableData="getInitTableData"
          @showPopovers="showPopovers"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" align="middle">
          <simpleAddIssue
            v-if="createSimple"
            style="margin-right: 16px"
            @success="getInitTableData"
            @cancel="createSimple = false"
          />

          <el-button-group>
            <el-tooltip content="快速新增" placement="top">
              <el-button
                :disabled="!$permission('reqm_center_idea_add')"
                class="subBtton"
                :icon="`iconfont  ${
                  createSimple
                    ? 'el-icon-direction-double-left'
                    : 'el-icon-direction-double-down'
                }`"
                type="primary"
                @click.stop="createSimple = !createSimple"
              />
            </el-tooltip>
            <el-button
              :disabled="!$permission('reqm_center_idea_add')"
              :icon="el-icon-setting"
              type="primary"
              @click.stop="newIssue"
              >新增</el-button
            >
          </el-button-group>
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"
              ><el-icon class="iconfont"><el-icon-application-more /></el-icon
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
                >{{ item.name }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-if="defaultFileds.length"
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>

    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="reqm-idea-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @resizable-change="
          ({ column }) => resizableChangeEvent(column, 'reqm-idea-table')
        "
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          show-overflow-tooltip
          field="name"
          title="标题"
          min-width="400"
          class-name="name_col custom-title-style"
          show-overflow="ellipsis"
          fixed="left"
        >
          <template #default="{ row }">
            <el-tooltip
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${
                      row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'
                    }`,
                  }"
                />
                <span class="custom-title-style-text">{{
                  row.code + ' ' + row.name
                }}</span>
              </span>
            </el-tooltip>
            <span
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' 0px',
                right: '10px',
                display: copyRow && copyRow.id == row.id ? 'block' : '',
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="(e) => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button
                  type="text"
                  :icon="el-icon-setting"
                />
                <el-dropdown-menu
                  slot="dropdown"
                  class="custom-title-copy-dropdown"
                >
                  <el-dropdown-item
                    :icon="el-icon-setting"
                    command="title"
                  >
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="el-icon-setting"
                    command="code"
                  >
                    <span>复制标题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="100" sortable>
          <template #default="{ row, rowIndex }">
            <ideaStatus
              v-if="row"
              :key="Date.now()"
              :workitem="row"
              :no-permission="!$permission('reqm_center_idea_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>

        <vxe-column field="handleBy" title="处理人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.handleBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'handleBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.leadingBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'leadingBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.putBy"
                class="remoteuser"
                :default-data="[row.echoMap.putBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'putBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="sourceCode"
          title="来源"
          show-overflow-tooltip
          width="100"
        >
          <template #default="{ row }">
            <span
              v-if="row.sourceCode && row.sourceCode && row.echoMap.sourceCode"
            >
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" width="120" sortable>
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>

        <vxe-column
          field="expectedTime"
          title="期望完成时间"
          width="135"
          sortable
        >
          <template #default="{ row }">
            <span v-if="row.expectedTime">
              {{ dayjs(row.expectedTime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.expectedTime }}</span>
          </template>
        </vxe-column>

        <vxe-column field="priorityCode" title="优先级" width="100" sortable>
          <template #default="{ row }">
            <vone-icon-select
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('reqm_center_idea_priority')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-for="(item, index) in row.tag" :key="index">
              <el-tag style="margin-right: 6px" type="success">
                {{ item }}
              </el-tag>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm_center_idea_edit')"
                  :icon="el-icon-setting"
                  @click="editIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm_center_idea_del')"
                  :icon="el-icon-setting"
                  @click="deleteIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown
                trigger="click"
                :hide-on-click="false"
                @command="(e) => e && e()"
              >
                <el-button
                  type="text"
                  :icon=""el-icon-more" icon_click"
                />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :icon="el-icon-setting"
                    :command="() => titleCopy(row, 'code')"
                  >
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="el-icon-setting"
                    :command="() => titleCopy(row, 'title')"
                  >
                    <span>复制标题</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :disabled="!$permission('reqm_center_trackingview')"
                    :icon="el-icon-setting"
                    :command="() => getTracking(row)"
                  >
                    <span>跟踪视图</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />
    <!-- 编辑 -->
    <vone-custom-edit
      v-if="issueParam.editvisible"
      v-model="issueParam.editvisible"
      v-bind="issueParam"
      :type-code="'IDEA'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 新增 -->
    <vone-custom-add
      v-if="issueParam.addvisible"
      v-model="issueParam.addvisible"
      v-bind="issueParam"
      :type-code="'IDEA'"
      :title="'新增用户需求'"
      @success="getInitTableData"
    />

    <!-- 详情 -->
    <vone-custom-info
      v-if="issueInfoParam.editvisible"
      v-model="issueInfoParam.editvisible"
      v-bind="issueInfoParam"
      :type-code="'IDEA'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      v-model="editAllParam.visible"
      :type-code="'IDEA'"
      @success="getInitTableData"
    />

    <!-- 导入 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model="importParam.visible"
      @success="getInitTableData"
    />
    <!-- 导出 -->
    <!-- <exportDialog v-if="exportParam.visible" v-bind="exportParam" v-model="exportParam.visible" :type-code="'IDEA'" :form-data="formData" /> -->
  </page-wrapper>
</template>

<script>
const list = [
  {
    id: '-1',
    name: '未设置',
  },
]
import { apiAlmIdeaPage, apiAlmIdeaDel } from '@/api/vone/reqmcenter/idea'
import simpleAddIssue from './function/simple-add-issue.vue'

import ideaStatus from '@/views/vone/project/common/change-status/index.vue'

// import exportDialog from '@/views/vone/reqm-center/common-sub/export-dialog.vue'
// 筛选

import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { productListByCondition } from '@/api/vone/project/index'
import { download } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'

// import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'

import { catchErr } from '@/utils'
import editAll from '../../project/common/edit-all'
import { apiAlmIdeaAddOrEdit, apiAlmIdeaInfo } from '@/api/vone/reqmcenter/idea'
import { editById, getWorkItemState } from '@/api/vone/project/index'

import _ from 'lodash'
export default {
  components: {
    ideaStatus,
    simpleAddIssue,
    editAll,
    // exportDialog
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '标题',
          type: {
            code: 'INPUT',
          },
          isBasic: true,
          placeholder: '请输入标题',
        },
        {
          key: 'sourceCode',
          name: '来源',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择来源',
          multiple: true,
        },
        {
          key: 'productId',
          name: '所属产品',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择所属产品',
          multiple: true,
          valueType: 'id',
        },
        {
          key: 'priorityCode',
          name: '优先级',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择优先级',
          multiple: true,
        },
        {
          key: 'stateCode',
          name: '状态',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择状态',
          multiple: true,
        },
        {
          key: 'putBy',
          name: '提出人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择提出人',
          multiple: true,
        },
        {
          key: 'handleBy',
          name: '处理人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择处理人',
          multiple: true,
        },
        {
          key: 'leadingBy',
          name: '负责人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择负责人',
          multiple: true,
        },
        {
          key: 'tagId',
          name: '标签',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择标签',
          multiple: true,
        },
      ],
      tableList: [], // 用于编辑时切换上一个下一个
      selectOptionWidth: '',
      sourceList: [], // 需求来源
      projectIdList: [], // 归属项目
      productIdList: [], // 归属产品
      stateList: [], // 状态
      prioritList: [], // 优先级
      createSimple: false,
      formData: {
        projectId: this.$route.params.id,
      },
      tableLoading: true,
      tableData: {},
      actions: [
        {
          disabled: !this.$permission('reqm_center_idea_del'),
          name: '批量删除',

          fn: () => {
            this.deleteTableSelect()
          },
        },
        {
          disabled: !this.$permission('reqm_center_idea_edit'),
          name: '批量编辑',

          fn: this.editAll,
        },
        {
          disabled: !this.$permission('reqm_center_idea_Import'),
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
        },
        {
          disabled: !this.$permission('reqm_center_idea_export'),
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
        },
      ],
      selectData: [],
      issueParam: {
        visible: false,
      },
      issueInfoParam: {
        // 详情
        visible: false,
      },
      importParam: { visible: false }, // 用户导入

      exportLoading: false,
      editAllParam: { visible: false }, // 批量编辑
      putByData: list,
      handleBytData: list,
      leadingByData: list,
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment',
        },
        {
          label: '活动',
          name: 'active',
        },
      ],
      leftTabs: [
        // {
        //   label: '用户需求',
        //   name: 'IdeaToIdea',
        //   active: false
        // },
        {
          label: '需求',
          name: 'IdeaToIssue',
          active: false,
        },
      ],
      exportParam: { visible: false },
      showTag: false,
      copyRow: null,
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  watch: {
    $route: {
      handler(val) {
        if (val.query && val.query.queryId) {
          this.showInfo({ id: val.query.queryId })
        }
      },
      // 一进页面就执行
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  mounted() {
    this.getPrioritList() // 优先级-
    this.productList() // 归属产品-
    this.getSourceList() // 来源-
    this.getStateList() // 状态-
  },
  created() {
    const params = this.$route.params

    if (params) {
      if (params.type == 'comment') {
        this.showInfo({ id: params.businessId })
      }
    }
  },
  methods: {
    resizableChangeEvent(column, refName) {
      if (column.field == 'name') {
        this.$refs[refName].refreshColumn()
      }
    },
    customCopy(command) {
      const _this = this
      const message = command == 'title' ? this.copyRow.code : this.copyRow.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
      this.copyRow = null
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row
      } else {
        this.copyRow = null
      }
    },
    // 数据源接口返回数据以后,把值塞到筛选器模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id,
      }
      params[t] = e
      const res = await editById('idea', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
    showPopovers() {
      this.showTag = true
    },
    getTracking(row) {
      this.$router.push({
        path: '/reqmcenter/idea/trackingView/' + row.id,
      })
    },
    // 移除筛选项
    deleteform() {},
    // 更新当前表格状态
    async editRowStatus(row, index) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIdeaInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(
          res.data,
          'tag',
          res.data.echoMap.tagId
            ? res.data.echoMap.tagId.map((r) => r.name)
            : []
        )
      }

      this.tableData.records.splice(index, 1, res.data)
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    async userLists(val) {
      this.$set(val, 'tagId', val.tag)
      const params = _.omit(val, ['tag'])
      const [res, err] = await catchErr(apiAlmIdeaAddOrEdit(params))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.getInitTableData()
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '用户需求',
        url: '/api/alm/alm/idea/downloadImportTemplate',
        importUrl: '/api/alm/alm/idea/importProgram',
      }
    },

    // 导出
    async exportFlie() {
      if (this.total > 5000) {
        this.$message.warning(
          '当前导出数据量过多，最大导出条目最多支持5000条，请先选择筛选条件'
        )
        return
      }
      // this.exportParam = { visible: true, title: '用户需求', url: '/api/alm/alm/idea/export' }

      this.exportLoading = true
      download(
        `意向信息.xls`,
        await apiBaseFileLoad('/api/alm/alm/idea/export', this.formData)
      )

      this.exportLoading = false
    },

    // 查询需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'IDEA',
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.defaultFileds, 'sourceCode', res.data)
      this.sourceList = res.data
    },

    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.productIdList = res.data
      this.setData(this.defaultFileds, 'productId', res.data)
    },
    // 查状态
    async getStateList() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'IDEA',
      })
      if (!res.isSuccess) {
        return
      }

      this.stateList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }

      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
    },

    // 获取table 选中的行
    selecteTableData(val) {
      this.selectData = val
    },
    // 批量删除
    async deleteTableSelect() {
      this.selectData = this.getVxeTableSelectData('reqm-idea-table')
      if (this.selectData.length > 0) {
        await this.$confirm('确定删除该信息吗?', '删除', {
          type: 'warning',
          customClass: 'delConfirm',
        }).then(async () => {
          const deleteArr = []
          this.selectData.map((item) => deleteArr.push(item.id))
          const res = await apiAlmIdeaDel(deleteArr)
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getInitTableData()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },
    // 初始化进入页面列表
    async getInitTableData(val) {
      this.tableLoading = true

      let params = {}
      if (this.$route.query.ideaName) {
        this.$set(this.formData, 'name', this.$route.query.ideaName)
      }
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      const [res, err] = await catchErr(apiAlmIdeaPage(params))
      this.tableLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach((element) => {
        element.tag =
          element.tagId &&
          element.tagId.length &&
          element.echoMap &&
          element.echoMap.tagId
            ? element.echoMap.tagId.map((r) => r.name)
            : []
      })
      this.tableData = res.data
      this.tableList = res.data.records // 用于编辑时切换上一个下一个
      this.total = Number(res.data.total) // 数据总条数,用于导出判断数据量大于5000时,不允许直接导
    },
    // 复制标题到剪贴板
    titleCopy(row, type) {
      const _this = this
      const message = type == 'code' ? row.code : row.name
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    newIssue() {
      this.issueParam = {
        addvisible: true,
        title: '新增用户需求',
        infoDisabled: false,
      }
    },
    editIssue(row) {
      this.issueParam = {
        editvisible: true,
        title: '编辑用户需求',
        id: row.id,
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      }
    },
    showInfo(row) {
      // 判断有没有编辑权限，有的话展示编辑对话框，没有则展示详情对话框

      this.issueInfoParam = {
        editvisible: true,
        title: '用户需求详情',
        id: row.id,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      }
    },
    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false,
      })

      const { isSuccess, msg } = await apiAlmIdeaDel(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 批量编辑
    editAll() {
      this.selectData = this.getVxeTableSelectData('reqm-idea-table')
      if (!this.selectData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selectData }
    },
  },
}
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-main) {
  padding: 0;
  margin-left: 12px;
}

.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.search {
  display: inline-block;
  // margin-right:20px;
}
.form-card {
  position: relative;
  &:hover {
    background-color: var(--hover-bg-color);
    .delete {
      display: block;
    }
  }
  .delete {
    display: none;
    font-size: 18px;
    position: absolute;
    right: 0;
    cursor: pointer;
  }
}
.noSetting {
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.drawer .vone-el-drawer__layout {
  overflow: hidden;
}
</style>

<style>
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
