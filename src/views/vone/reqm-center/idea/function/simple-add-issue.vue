<template>
  <vone-simple-add
    ref="simpleAdd"
    v-model.trim="form.name"
    :rules="rules"
    :model="form"
    label="标题"
    v-model:file-list="fileList"
    :loading="saveLoading"
    @submit="saveSubmit"
    @open="openCreateDetail"
    @cancel="$emit('cancel')"
  >
    <!-- 需求类型下拉列表 -->
    <el-dropdown
      v-model="form.typeCode"
      class="dropList"
      trigger="click"
      @command="changeClass"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="defectMap && defectMap[form.typeCode]">
            <el-row type="flex">
              <i
                :class="`iconfont ${defectMap[form.typeCode].icon}`"
                :style="{
                  color: `${
                    defectMap[form.typeCode].color
                      ? defectMap[form.typeCode].color
                      : '#ccc'
                  }`,
                }"
                class="iconStyle"
              />
              <span> {{ defectMap[form.typeCode].name }} </span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置分类 </span>
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
        </span>
      </el-row>
      <el-dropdown-menu slot="dropdown">
        <template v-if="typeCodeList && typeCodeList.length > 0">
          <el-dropdown-item
            v-for="item in typeCodeList"
            :key="item.code"
            :command="item.code"
          >
            <span v-if="item.icon">
              <i
                :class="`iconfont ${item.icon}`"
                :style="{ color: `${item.color ? item.color : '#ccc'}` }"
              />
            </span>
            {{ item.name }}
          </el-dropdown-item>
        </template>
        <vone-empty v-else desc="无可选分类" />
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 人员下拉列表 -->
    <vone-remote-user v-model="form.handleBy" class="remoteUser" />

    <el-popover v-if="!noFile" width="517" trigger="hover">
      <vone-upload
        ref="uloadFile"
        biz-type="IDEA_FILE_UPLOAD"
        @change="onChange"
      />
      <div slot="upload" />
      <vone-empty v-if="fileList.length === 0" desc="暂无附件" />
      <el-icon
        class="iconfont"
        style="cursor: pointer; color: var(--main-theme-color); margin: 0 12px"
        ><el-icon-application-attachment
      /></el-icon>
    </el-popover>
  </vone-simple-add>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  ApplicationAttachment as ElIconApplicationAttachment,
} from '@element-plus/icons-vue'

import { getAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { apiAlmIdeaAddOrEdit } from '@/api/vone/reqmcenter/idea'

import dayjs from 'dayjs'
import storage from 'store'

export default {
  components: {
    ElIconDirectionDown,
    ElIconApplicationAttachment,
  },
  props: {
    testCaseId: {
      type: Number,
      default: null,
    },
    planId: {
      type: String,
      default: '',
    },
    issueId: {
      // 需求id,用于需求关联任务时,保存时传需求id
      type: String,
      default: undefined,
    },
    noFile: Boolean,

    typeCode: {
      // 分类枚举值,只作为查询分类的入参,ISSUE,BUG,TASK,RISK,及区分保存时调哪个接口
      type: String,
      default: undefined,
    },
    noEpic: Boolean, // 史诗拆分用户故事,需求新增时,过滤掉史诗
  },
  data() {
    return {
      form: {
        name: '',
        typeCode: '',
        handleBy: '',
        envCode: '',
      },

      typeCodeList: [], // 用户需求分类
      typeList: [], // 分类下拉框
      envList: [], // 环境
      fileList: [],
      defectMap: {}, // 缺陷分类

      envMap: {},
      saveLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          {
            pattern: '^.{1,250}$',
            message: '请输入不超过250个字符组成的标题',
            trigger: 'change',
          },
        ],
      },
    }
  },
  computed: {
    projectKey() {
      return this.$route.params.projectKey
    },
  },
  mounted() {
    this.getIssueType()
    // 新增时默认赋值当前登录人
    const userInfo = storage.get('user')
    this.$set(this.form, 'handleBy', userInfo.id)
  },
  methods: {
    onChange(val) {
      this.fileList = val
    },
    async getIssueType() {
      const res = await getAlmGetTypeNoPage('IDEA')
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      this.defectMap = this.typeCodeList.reduce(
        (r, v) => (r[v.code] = v) && r,
        {}
      )
      this.$set(this.form, 'typeCode', this.typeCodeList[0].code)
    },
    // 打开新建详细数据弹窗
    openCreateDetail() {
      this.$emit('createDetail')
      this.$emit('cancel')
    },
    // 切换类型
    changeClass(value) {
      this.$set(this.form, 'typeCode', value)
    },
    changeUser(v) {
      this.$set(this.form, 'handleBy', v)
    },

    async saveSubmit() {
      const params = {
        name: this.form.name,
        typeCode: this.form.typeCode,
        createdBy: this.form.handleBy,
        putBy: this.form.handleBy,
        handleBy: this.form.handleBy,
        leadingBy: this.form.handleBy,
        planEtime: dayjs().add(240, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        planStime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        description: '',
        files: this.fileList.length ? this.fileList : [],
        priorityCode: 'HIGH',
      }
      this.saveIssue(params)
    },
    async saveIssue(params) {
      this.saveLoading = true
      const res = await apiAlmIdeaAddOrEdit(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('创建成功')
      const userInfo = storage.get('user')
      this.$emit('success')
      this.form = {
        name: '',
        typeCode: this.typeCodeList[0].code,
        userId: userInfo.id,
      }
      this.fileList = []
      this.$refs['uloadFile'].resetData()
    },
  },
}
</script>

<style lang="scss" scoped>
.dropList {
  min-width: 60px;
  margin: 0 6px;
}
.textTitle {
  white-space: nowrap;
}
.el-icon--right {
  margin-top: 9px;
}
.iconStyle {
  margin-top: 10%;
  margin-left: 5px;
  margin-right: 5px;
}
:deep(.el-dropdown-menu) {
  height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  // white-space: nowrap;
  //  overflow: hidden;
}

.remoteUser {
  width: 25%;
}
</style>
