<template>
  <div>
    <div style="height: calc(100vh - 208px)">
      <vxe-table
        ref="xTree"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        row-id="id"
        :tree-config="{
          transform: true,
          lazy: true,
          hasChild: 'hasChild',
          loadMethod: load,
        }"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          show-overflow-tooltip
          field="name"
          title="标题"
          min-width="400"
          class-name="name_col"
          tree-node
          fixed="left"
        >
          <template slot-scope="{ row }">
            <div class="name_icon">
              <a class="table_title" @click="showInfo(row)">
                <span
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                >
                  <i
                    :class="`iconfont ${row.echoMap.typeCode.icon}`"
                    :style="{
                      color: `${
                        row.echoMap.typeCode
                          ? row.echoMap.typeCode.color
                          : '#ccc'
                      }`,
                    }"
                  />
                  {{ row.code + ' ' + row.name }}
                </span>
              </a>
              <span v-if="row.delay" style="position: absolute; left: -13px">
                <el-tooltip
                  :show-after="500"
                  content="当前工作项已延期"
                  placement="top-start"
                >
                  <el-icon class="color-danger ml-2"
                    ><el-icon-warning-outline
                  /></el-icon>
                </el-tooltip>
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="100" sortable>
          <template #default="{ row, rowIndex }">
            <issueStatus
              v-if="row"
              :workitem="row"
              :no-permission="!$permission('reqm_center_require_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column field="handleBy" title="处理人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.handleBy"
                class="remoteuser"
                :default-data="[row.echoMap.handleBy]"
                :disabled="!$permission('reqm-center-require-edit')"
                @change="workitemChange(row, $event, 'handleBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.putBy"
                class="remoteuser"
                :default-data="[row.echoMap.putBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'putBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="createTime"
          title="创建时间"
          width="120"
          show-overflow-tooltip
          sortable
        >
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.leadingBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('reqm_center_idea_edit')"
                @change="workitemChange(row, $event, 'leadingBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column
          field="planEtime"
          title="计划完成时间"
          show-overflow-tooltip
          width="135"
          sortable
        >
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column field="rateProgress" title="进度" width="80" sortable>
          <template slot-scope="{ row }">
            <el-tooltip placement="top" :content="`${row.rateProgress}%`">
              <el-progress
                :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
                :color="'var(--main-theme-color,#3e7bfa)'"
                :show-text="false"
              />
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="priorityCode" title="优先级" width="100" sortable>
          <template #default="{ row }">
            <vone-icon-select
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('reqm_center_require_priority')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column
          field="sourceCode"
          title="需求来源"
          show-overflow-tooltip
          width="100"
        >
          <template #default="{ row }">
            <span
              v-if="row.sourceCode && row.sourceCode && row.echoMap.sourceCode"
            >
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column
          show-overflow-tooltip
          field="projectId"
          title="归属项目"
          width="90"
        >
          <template #default="{ row }">
            <span v-if="row.projectId && row.echoMap && row.echoMap.projectId">
              {{ row.echoMap.projectId.name }}
            </span>
            <span v-else>{{ row.projectId }}</span>
          </template>
        </vxe-column>
        <vxe-column
          show-overflow-tooltip
          field="planId"
          title="归属计划"
          width="90"
        >
          <template #default="{ row }">
            <span v-if="row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-for="(item, index) in row.tag" :key="index">
              <el-tag style="margin-right: 6px" type="success">
                {{ item }}
              </el-tag>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm-center-require-edit')"
                  :icon="el-icon-setting"
                  @click="editIssue(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="复制" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm-center-require-del')"
                  :icon="el-icon-setting"
                  @click="titleCopy(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('reqm-center-require-del')"
                  :icon="el-icon-setting"
                  @click="deleteIssue(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />
    <!-- 编辑 -->
    <vone-custom-edit
      v-if="issueParam.visible"
      :key="issueParam.key"
      v-model="issueParam.visible"
      v-bind="issueParam"
      :type-code="'ISSUE'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
    />

    <!-- 详情 -->
    <vone-custom-info
      v-if="issueInfoParam.visible"
      :key="issueInfoParam.key"
      v-model="issueInfoParam.visible"
      v-bind="issueInfoParam"
      :type-code="'ISSUE'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
    />
  </div>
</template>

<script>
const list = [
  {
    id: '-1',
    name: '未设置',
  },
]
import { requirementDel } from '@/api/vone/reqmcenter/require'
import { requirementAddOrEdit } from '@/api/vone/reqmcenter/require'
import { apiAlmIssueInfo } from '@/api/vone/project/issue'
import { catchErr } from '@/utils'

import {
  requirementTree,
  selectRequirementByIdeaIdOrRequirementId,
} from '@/api/vone/reqmcenter/require'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import _ from 'lodash'

export default {
  components: {
    issueStatus,
  },
  props: {
    showView: {
      type: String,
      default: '',
    },
    typeCodeList: {
      type: Array,
      default: () => [],
    },
    sourceList: {
      type: Array,
      default: () => [],
    },
    prioritList: {
      type: Array,
      default: () => [],
    },
    stateList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {},
      demoDiolog: false, // 配置弹框

      activeView: '',

      tableLoading: false,
      tableData: {},
      tableSelected: [],
      issueParam: {
        visible: false,
        demoDiolog: false,
      },
      putByData: list,
      handleByData: list,
      leadingByData: list,
      headerList: [], // 表格列头
      columnsList: [], // 显示列
      filterList: [], // 筛选条件
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment',
        },
        {
          label: '活动',
          name: 'active',
        },
      ],
      leftTabs: [
        {
          label: '需求',
          name: 'IssueToIssue',
        },
      ],
      issueInfoParam: {
        // 详情
        visible: false,
      },
    }
  },

  mounted() {
    this.getInitTableData()
    this.activeView = this.showView
  },
  methods: {
    changeView(tab) {
      this.$emit('change', tab.name)
    },
    getName() {
      this.$store.dispatch('project/getName', '')
    },
    // 更新当前表格状态
    async editRowStatus(row, index) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIssueInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(
          res.data,
          'tag',
          res.data.echoMap.tagId
            ? res.data.echoMap.tagId.map((r) => r.name)
            : []
        )
      }
      this.tableData.records.splice(index, 1, res.data)
    },
    async userLists(val) {
      this.$set(val, 'tagId', val.tag)
      const params = _.omit(val, ['tag'])
      const res = await requirementAddOrEdit(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.getInitTableData()
    },
    // 复制标题到剪贴板
    titleCopy(row) {
      const _this = this
      this.$copyText(`${row.code} ${row.name}`).then(
        function (e) {
          _this.$message.success('复制成功')
        },
        function (e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    newIssue() {
      this.issueParam = {
        visible: true,
        title: '新增需求',
        key: Date.now(),
        infoDisabled: false,
      }
    },
    editIssue(row) {
      this.issueParam = {
        visible: true,
        title: '编辑需求',
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId,
      }
    },
    showInfo(row) {
      this.issueInfoParam = {
        visible: true,
        title: '需求详情',
        id: row.id,
        key: Date.now(),
        infoDisabled: true,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId,
      }
    },
    load(tree) {
      return new Promise((resolve) => {
        setTimeout(() => {
          selectRequirementByIdeaIdOrRequirementId({ id: tree.row.id }).then(
            (res) => {
              const list = res.data || []

              if (res.data.length == 0) {
                tree.row.hasChild = false
              }
              resolve(list)
            }
          )
        }, 500)
      })
    },

    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      })

      const { isSuccess, msg } = await requirementDel(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 初始化进入页面列表
    async getInitTableData() {
      this.tableLoading = true
      let params = {}
      this.pageLoading = true
      if (this.$store.state.project.itemName) {
        this.$set(this.formData, 'name', this.$store.state.project.itemName)
      }
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData },
      }
      const res = await requirementTree(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      res.data.records.forEach((item) => {
        item.tag =
          item.tagId && item.tagId.length && item.echoMap && item.echoMap.tagId
            ? item.echoMap.tagId.map((r) => r.name)
            : []

        if (item.typeCode == 'SOR' || item.typeCode == 'EPIC') {
          item.hasChild = true
        }
      })
      this.tableData = res.data
      this.tableData.total = res.data.total
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.vone-vxe-table .vxe-tree-cell a .iconfont) {
  display: inline-block;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.name_col) {
  padding-left: 10px;
  .cell {
    display: flex;
    div.name_icon {
      width: 85%;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  margin-left: 5px;
  display: inline-block;
  max-width: 90%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 5px;
}
.vone-tabs:deep(.el-tabs__header) {
  margin: 0 !important;
}
:deep(.table-search) {
  line-height: 32px;
  height: 32px;
}

.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
</style>
