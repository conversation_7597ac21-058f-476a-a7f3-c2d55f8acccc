<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          show-card
          :card-options="cardOptions"
          table-search-key="reqm-require-table"
          :model="formData"
          show-basic
          :extra="extraData"
          :default-fileds="defaultFileds"
          :table-ref="tableRef"
          :board-columns="boardColumns"
          @getTableData="getInitTableData"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" align="middle">
          <el-popover
            ref="simplePopover"
            placement="bottom-end"
            width="600"
            trigger="click"
          >
            <simpleAddIssue @success="simpleSuccess" @cancel="cancleSimple" />
            <el-button
              slot="reference"
              :disabled="!$permission('reqm-center-require-add')"
              class="subBtton"
              :icon="el-icon-setting"
              type="primary"
              @click.stop="createSimple = !createSimple"
            />
          </el-popover>
          <el-button
            :disabled="!$permission('reqm-center-require-add')"
            :icon="el-icon-setting"
            type="primary"
            class="newItemBtn"
            @click.stop="newIssue"
            >新增</el-button
          >

          <el-dropdown
            v-if="extraData.cardView != 'billboard'"
            trigger="click"
            @command="(e) => e && e()"
          >
            <el-button class="btnMore"
              ><el-icon class="iconfont"><el-icon-application-more /></el-icon
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-if="defaultFileds.length"
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>

    <main>
      <section v-if="extraData.cardView == 'billboard'">
        <IssueBillBoard
          ref="billboard"
          :form-data="formData"
          :extra-data="extraData"
        />
      </section>
      <section v-else-if="extraData.cardView == 'tree'">
        <IssueTree ref="treeTable" :priorit-list="prioritList" />
      </section>
      <section v-else>
        <IssueTable
          ref="table"
          :table-data="tableData"
          :priorit-list="prioritList"
          :extra-data="extraData"
          @success="getInitTableData"
          @refreshStatus="editRowStatus"
        />
      </section>
    </main>

    <!-- 新增 -->
    <vone-custom-add
      v-if="issueParam.addvisible"
      :key="issueParam.key"
      v-model="issueParam.addvisible"
      v-bind="issueParam"
      :title="'新增需求'"
      :type-code="'ISSUE'"
      @success="getInitTableData"
    />

    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      v-model="editAllParam.visible"
      :type-code="'ISSUE'"
      is-req
      @success="getInitTableData"
    />

    <!-- 导入 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model="importParam.visible"
      @success="getInitTableData"
    />

    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      v-model="editAllParam.visible"
      :type-code="'ISSUE'"
      is-req
      @success="getInitTableData"
    />
  </page-wrapper>
</template>

<script>
import { mapState } from 'vuex'
import { requirementDel, findByClassify } from '@/api/vone/reqmcenter/require'

import { apiAlmIssueInfo } from '@/api/vone/project/issue'

import { apiAlmPriorityNoPage, almReqmList } from '@/api/vone/alm/index'
import { apiBaseFileLoad } from '@/api/vone/base/file'

import { download, catchErr } from '@/utils'
import simpleAddIssue from './function/simple-add-issue.vue'
import editAll from './function/edit-all.vue'
import IssueTable from './tabs/table.vue'
import IssueBillBoard from './tabs/billboard.vue'
import IssueTree from './tabs/treeTable.vue'
import { getWorkItemState } from '@/api/vone/project/index'

import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { productListByCondition } from '@/api/vone/project/index'

export default {
  components: {
    editAll,
    simpleAddIssue,
    IssueTable,
    IssueBillBoard,
    IssueTree,
  },
  data() {
    return {
      tableRef: null,
      boardColumns: [],
      cardOptions: [
        { name: '表格', value: 'table' },
        { name: '看板', value: 'billboard' },
        { name: '层级', value: 'tree' },
      ],
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '标题',
          type: {
            code: 'INPUT',
          },
          isBasic: true,
          placeholder: '请输入标题',
        },
        {
          key: 'sourceCode',
          name: '来源',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择来源',
          multiple: true,
        },
        {
          key: 'typeCode',
          name: '类型',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择类型',
          // multiple: true
        },
        {
          key: 'productId',
          name: '所属产品',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择所属产品',
          multiple: true,
          valueType: 'id',
        },
        {
          key: 'priorityCode',
          name: '优先级',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择优先级',
          multiple: true,
        },
        {
          key: 'stateCode',
          name: '状态',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择状态',
          multiple: true,
        },
        {
          key: 'putBy',
          name: '提出人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择提出人',
          multiple: true,
        },
        {
          key: 'handleBy',
          name: '处理人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择处理人',
          multiple: true,
        },
        {
          key: 'leadingBy',
          name: '负责人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择负责人',
          multiple: true,
        },
        {
          key: 'tagId',
          name: '标签',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择标签',
          multiple: true,
        },
      ],
      tableList: [], // 用于编辑时切换上一个下一个
      formData: {},
      sourceList: [], // 需求来源
      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 需求分类
      createSimple: false,
      selectData: [],
      tableData: {},
      issueParam: {
        visible: false,
        demoDiolog: false,
      },
      actions: [
        {
          name: '批量删除',

          fn: this.deleteTableSelect,
          disabled: !this.$permission('reqm-center-require-del'),
        },
        {
          name: '批量编辑',

          fn: this.editAll,
          disabled: !this.$permission('reqm-center-require-edit'),
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('reqm_center_issue_import'),
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('reqm_center_issue_export'),
        },
      ],
      importParam: { visible: false }, // 用户导入
      exportLoading: false,
      editAllParam: { visible: false }, // 批量编辑
      bColumns: [
        {
          id: 1,
          title: '编号',
          code: 'code',
          visible: true,
        },
        {
          id: 2,
          title: '规模',
          code: 'time',
          visible: true,
        },
        {
          id: 3,
          title: '优先级',
          code: 'priorityCode',
          visible: true,
        },
        {
          id: 4,
          title: '计划完成时间',
          code: 'planEtime',
          visible: true,
        },
        {
          id: 5,
          title: '处理人',
          code: 'handleBy',
          visible: true,
        },
        {
          id: 6,
          title: '状态',
          code: 'stateCode',
          visible: true,
        },
        {
          id: 7,
          title: '延期',
          code: 'delay',
          visible: true,
        },
      ],
    }
  },
  computed: {
    ...mapState({
      requierName: (state) => state.project.requierName,
    }),
  },
  watch: {
    extraData: {
      handler(val) {
        this.$nextTick(() => {
          if (val.cardView == 'billboard') {
            this.boardColumns = this.bColumns
            this.tableRef = null
          } else if (val.cardView == 'tree') {
            this.boardColumns = []
            this.tableRef = this.$refs.treeTable.$refs['xTree']
          } else {
            this.boardColumns = []
            this.tableRef = this.$refs.table.$refs['reqm-req-table']
          }
        })
      },
      immediate: true,
      deep: true,
    },
    '$route.params.id': {
      handler(val) {
        if (this.$route.params.type == 'comment') {
          this.showInfo({ id: this.$route.params.businessId })
        }
      },
      immediate: true,
      deep: true,
    },
    $route: {
      handler(val) {
        if (val.query && val.query.showDialog) {
          const { queryId } = val.query
          this.showInfo({ id: queryId })
        }
      },
      immediate: true,
      deep: true,
    },
  },
  async mounted() {
    this.getIssueType() // 需求分类
    this.getStateList() // 状态
    this.getPrioritList() // 优先级

    this.getSourceList()
    this.productList()
  },
  beforeDestroy() {
    this.$store.dispatch('project/getName', '')
  },
  created() {
    const params = this.$route.params
    if (params) {
      if (params.type == 'comment') {
        this.showInfo({ id: params.businessId })
      }
    }
  },
  methods: {
    cancleSimple() {
      this.createSimple = false
      this.$refs['simplePopover'].doClose()
    },
    simpleSuccess() {
      this.$refs['simplePopover'].doClose()
      this.getInitTableData()
    },
    // 数据源接口返回数据以后,把值塞到筛选器模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },
    // 更新当前表格状态
    async editRowStatus(row, index) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmIssueInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(
          res.data,
          'tag',
          res.data.echoMap.tagId
            ? res.data.echoMap.tagId.map((r) => r.name)
            : []
        )
      }
      this.tableData.records.splice(index, 1, res.data)
    },

    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '需求',
        url: '/api/alm/alm/requirement/excel/downloadImportTemplate',
        importUrl: '/api/alm/alm/requirement/excel/import',
      }
    },

    // 导出
    async exportFlie() {
      try {
        this.exportLoading = true
        download(
          `需求信息.xls`,
          await apiBaseFileLoad(
            '/api/alm/alm/requirement/excel/export',
            this.formData
          )
        )
        this.exportLoading = false
      } catch (e) {
        this.exportLoading = false
        return
      }
    },

    // 查询需求分类
    async getIssueType() {
      const res = await findByClassify('ISSUE')
      if (!res.isSuccess) {
        return
      }

      this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },

    // 查状态
    async getStateList() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'ISSUE',
      })
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
    },
    // 批量删除
    async deleteTableSelect() {
      this.selectData =
        this.extraData.cardView == 'table'
          ? this.$refs.table.getVxeTableSelectData('reqm-req-table')
          : this.$refs.treeTable.getVxeTableSelectData('xTree')
      if (this.selectData.length > 0) {
        await this.$confirm(
          `确定删除 ${this.selectData.length} 个数据吗?`,
          '批量删除',
          {
            type: 'warning',
            customClass: 'delConfirm',
          }
        ).then(async () => {
          const deleteArr = []
          this.selectData.map((item) => deleteArr.push(item.id))
          const res = await requirementDel(deleteArr)
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.$message.success(res.msg)
          this.getInitTableData()
        })
      } else {
        this.$message.warning('请勾选要删除的信息')
      }
    },
    // 初始化进入页面列表
    async getInitTableData() {
      if (this.extraData.cardView == 'billboard') {
        this.$refs.billboard.getBoardForProject()
        return
      }
      if (this.extraData.cardView == 'tree') {
        this.$refs.treeTable.getInitTableData()
        return
      }

      if (this.extraData.cardView == 'table') {
        this.$refs.table.tableLoading = true
      }

      let params = {}
      if (this.requierName) {
        this.$set(this.formData, 'name', this.requierName)
      }

      const tableAttr = this?.$refs?.table?.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      const res = await almReqmList(params)
      if (this.extraData.cardView == 'table') {
        this.$refs.table.tableLoading = false
      }
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach((element) => {
        element.tag =
          element.tagId &&
          element.tagId.length &&
          element.echoMap &&
          element.echoMap.tagId
            ? element.echoMap.tagId.map((r) => r.name)
            : []
      })
      this.tableData = res.data
      this.tableData.total = res.data.total
    },

    newIssue() {
      this.issueParam = {
        addvisible: true,
        title: '新增需求',
        key: Date.now(),
        infoDisabled: false,
      }
    },

    // 批量编辑
    editAll() {
      this.selectData =
        this.extraData.cardView == 'table'
          ? this.$refs.table.getVxeTableSelectData('reqm-req-table')
          : this.$refs.treeTable.getVxeTableSelectData('xTree')
      if (!this.selectData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selectData }
    },
    // 查询需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'ISSUE',
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.defaultFileds, 'sourceCode', res.data)
      this.sourceList = res.data
    },

    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.productIdList = res.data
      this.setData(this.defaultFileds, 'productId', res.data)
    },
  },
}
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-main) {
  padding: 0;
  margin-left: 12px;
}
:deep(.vone-tabs .el-tabs__item) {
  padding: 0 10px !important;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-main) {
  padding: 0;
  margin-left: 12px;
}

:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.search {
  display: inline-block;
  // margin-right:20px;
}
.vone-tabs:deep(.el-tabs__header) {
  margin: 0 !important;
}

.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>

<style>
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
