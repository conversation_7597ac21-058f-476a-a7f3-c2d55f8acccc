<template>
  <vone-drawer
    v-loading="loading"
    :title="title"
    v-model="visible"
    size="sm"
    :before-close="onClose"
    :wrapper-closable="false"
  >
    <div class="vone-drawer-main">
      <el-form
        ref="appForm"
        v-loading="formLoading"
        :model="appForm"
        :rules="appRules"
      >
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="appForm.name" placeholder="请输入应用名称" />
        </el-form-item>

        <el-form-item label="应用标识" prop="code">
          <el-input
            v-model="appForm.code"
            placeholder="请输入应用标识"
            :disabled="id ? true : false"
          />
        </el-form-item>

        <el-form-item label="应用路由" prop="path">
          <el-input v-model="appForm.path" placeholder="请输入应用路由" />
        </el-form-item>

        <el-form-item label="图标" prop="icon">
          <el-input v-model="appForm.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="appForm.sort"
            :min="0"
            style="width: 100%"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="应用描述" prop="description">
          <el-input
            v-model="appForm.description"
            type="textarea"
            placeholder="请输入应用描述"
          />
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" :loading="saveLoading" @click="saveInfo"
        >保存</el-button
      >
      <el-button @click="onClose">取消</el-button>
    </div>
  </vone-drawer>
</template>

<script>
import {
  apiBaseMenuAddApp,
  apiBaseMenuGetById,
  apiBaseMenuEditApp,
} from '@/api/vone/base/meun'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    id: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      default: null,
    },
    dataMax: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      formLoading: false,
      appForm: {
        component: 'Layout',
        code: '',
        icon: 'el-icon-vone-reqmcenter',
        sort: 0,
        isGeneral: false,
        parentId: 0,
        state: true,
        isHide: false,
        isButton: false,
      },
      appRules: {
        name: [
          { required: true, message: '请输入应用名称' },
          {
            max: 20,
            message: '输入内容不能超过20个字符',
          },
        ],
        code: [
          { required: true, message: '请输入应用标识' },
          {
            pattern: '^[a-zA-Z0-9_-]{1,50}$',
            message: '只允许输入1到50位字母、数字、中划线和下划线',
          },
        ],
        path: [{ required: true, message: '请输入应用路由' }],
        icon: [{ required: false, message: '请选择图片' }],
        description: [
          { required: false, message: '请输入描述' },
          {
            max: 255,
            message: '输入内容不能超过255个字符',
          },
        ],
      },
      groupList: [],
    }
  },
  mounted() {
    if (this.id) {
      this.getDetailsById()
    } else {
      this.$set(this.appForm, 'sort', this.dataMax + 5)
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.appForm.resetFields()
    },
    // 查详情
    async getDetailsById() {
      this.formLoading = true
      const res = await apiBaseMenuGetById(this.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.appForm = res.data
    },
    async saveInfo() {
      await this.$refs.appForm.validate()
      if (this.id) {
        this.editApp()
        return
      }

      try {
        this.saveLoading = true
        const res = await apiBaseMenuAddApp({ ...this.appForm })
        this.saveLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    async editApp() {
      try {
        this.saveLoading = true
        const res = await apiBaseMenuEditApp({
          ...this.appForm,
        })
        this.saveLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('修改成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },

    handleChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (isLt2M) {
        this.uploadImgToBase64(file.raw).then((data) => {
          this.appForm.image = data.result
        })
      } else {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
    },
    uploadImgToBase64(file) {
      // 核心方法，将图片转成base64字符串形式
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function () {
          // 图片转base64完成后返回reader对象
          resolve(reader)
        }
        reader.onerror = reject
      })
    },
    beforeUpload(file) {
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
.vone-drawer-main {
  padding: 0px 20px;
  overflow-x: hidden;
  overflow-y: auto !important;
}
</style>
