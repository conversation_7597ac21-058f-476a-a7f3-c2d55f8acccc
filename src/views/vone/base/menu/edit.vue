<template>
  <div class="tree">
    <el-tree
      ref="tree"
      node-key="id"
      :data="menuTree"
      :props="defaultProps"
      default-expand-all
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
    >
      <span slot-scope="{ node, menuTree }" class="custom-tree-node">
        <span>{{ node.label }}</span>
        <span class="operation-icon">
          <el-button
            type="text"
            size="mini"
            :icon="el-icon-setting"
            :disabled="!$permission('base_menu_add')"
            @click.stop="() => add(node)"
          />
          <el-button
            type="text"
            size="mini"
            :icon="el-icon-setting"
            :disabled="!$permission('base_menu_edit')"
            @click.stop="() => edit(node)"
          />
          <el-button
            v-if="node.level != 1"
            type="text"
            size="mini"
            :icon="el-icon-setting"
            :disabled="!$permission('base_menu_delete')"
            @click.stop="() => remove(node, menuTree)"
          />
        </span>
      </span>
    </el-tree>
    <vone-drawer
      :title="menuTitle"
      size="md"
      v-model="visible"
      :before-close="onClose"
      :wrapper-closable="false"
    >
      <div class="vone-drawer-main">
        <el-form
          ref="meunForm"
          v-loading="formLoading"
          :model="meunForm"
          :rules="formRule"
          label-position="top"
        >
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="父级">
                <el-input v-model="meunForm.parentName" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="名称" prop="name">
                <el-input
                  v-model="meunForm.name"
                  placeholder="请输入菜单名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="标识" prop="code">
                <el-input
                  v-model="meunForm.code"
                  placeholder="请输入菜单标识"
                  :disabled="menuTitle == '编辑菜单' ? true : false"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="路由URL" prop="path">
                <el-input v-model="meunForm.path" placeholder="请输入路由URL" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否外部菜单" prop="isExternal">
                <el-radio-group
                  v-model="meunForm.isExternal"
                  @input="externalChange"
                >
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="meunForm.isExternal" :span="24">
              <el-form-item label="外部菜单url" prop="externalUrl">
                <el-input
                  v-model="meunForm.externalUrl"
                  placeholder="请输入外部菜单url"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="组件" prop="component">
                <el-input
                  v-model="meunForm.component"
                  :disabled="meunForm.isExternal"
                  placeholder="请输入组件名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图标" prop="icon">
                <el-input v-model="meunForm.icon" placeholder="请输入图标" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="sort">
                <el-input-number
                  v-model="meunForm.sort"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="描述" prop="description">
                <el-input
                  v-model="meunForm.description"
                  type="textarea"
                  placeholder="请输入描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="状态" prop="state">
                <el-switch
                  v-model="meunForm.state"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="隐藏子菜单" prop="isHide">
                <el-switch
                  v-model="meunForm.isHide"
                  inactive-text="否"
                  active-text="是"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="功能" prop="isButton">
                <el-switch
                  v-model="meunForm.isButton"
                  inactive-text="否"
                  active-text="是"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button type="primary" :loading="saveLoading" @click="onSubmit"
          >保存</el-button
        >
        <el-button @click="onClose">取消</el-button>
      </div>
    </vone-drawer>
  </div>
</template>

<script>
import {
  apiBaseMenuGetById,
  apiBaseMenuAddApp,
  apiBaseMenuTree,
  apiBaseMenuDelApp,
  apiBaseMenuEditApp,
} from '@/api/vone/base/meun'
export default {
  data() {
    return {
      visible: false,
      saveLoading: false,
      formLoading: false,
      menuTitle: '',
      isEdit: false,
      menuTree: [],
      meunForm: {
        parentName: '',
        name: '',
        code: '',
        path: '',
        isExternal: false,
        externalUrl: '',
        component: '',
        icon: '',
        sort: '0',
        description: '',
        state: true,
        isHide: false,
        isButton: false,
      },
      formRule: {
        name: [
          { required: true, message: '请输入名称' },
          {
            max: 20,
            message: '输入内容不能超过20个字符',
          },
        ],
        path: [{ required: true, message: '请输入url' }],
        code: [
          { required: true, message: '请输入菜单标识' },
          {
            pattern: '^[a-zA-Z0-9_-]{1,50}$',
            message: '只允许输入1到50位字母、数字、中划线和下划线',
          },
        ],
        description: [
          { required: false, message: '请输入描述' },
          {
            max: 255,
            message: '输入内容不能超过255个字符',
          },
        ],
      },
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },
  watch: {
    $route() {
      this.getMeun()
    },
  },
  mounted() {
    this.getMeun()
  },
  methods: {
    handleNodeClick(data) {
      this.$refs.tree.setCurrentKey(data.id)
      this.$emit('handleNodeClick', data)
    },

    // 查菜单树
    async getMeun() {
      this.formLoading = true
      const res = await apiBaseMenuTree()
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.menuTree = res.data.filter((r) => r.id == this.$route.params.id)
    },
    add(node) {
      this.visible = true
      this.menuTitle = '新增菜单'
      this.isEdit = false
      this.$set(this.meunForm, 'parentId', node.data.id)
      this.$set(this.meunForm, 'parentName', node.label)
    },
    edit(node) {
      this.visible = true
      this.menuTitle = '编辑菜单'
      this.isEdit = true
      this.getDetailsById(node)
    },
    async remove(node) {
      if (node.data.children && node.data.children.length) {
        // thisad
        this.$message.warning(
          `当前菜单【${node.data.name}】下有子级菜单,请先删除子级菜单`
        )
        return
      }
      await this.$confirm(
        `删除 【${node.data.name}】前请先取消角色权限`,
        '提示',
        {
          type: 'warning',
        }
      )
      const res = await apiBaseMenuDelApp([node.data.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.getMeun()
    },
    onClose() {
      this.$refs.meunForm.resetFields()
      this.visible = false
      // this.$emit('update:visible', false)
    },
    // 查详情
    async getDetailsById(node) {
      this.formLoading = true
      const res = await apiBaseMenuGetById(node.data.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.meunForm = res.data
      // 设置父级菜单名称
      this.$set(this.meunForm, 'parentName', node.parent.label)
      this.$set(this.meunForm, 'isExternal', !!this.meunForm.isExternal)
    },
    async onSubmit() {
      await this.$refs.meunForm.validate()
      if (this.isEdit) {
        this.editApp()
        return
      }

      try {
        this.saveLoading = true
        const res = await apiBaseMenuAddApp({ ...this.meunForm })
        this.saveLoading = false
        if (!res.isSuccess) {
          this.saveLoading = false
          return this.$message.error(res.msg)
        }

        this.$message.success('保存成功')
        this.onClose()
        this.getMeun()
      } catch (e) {
        this.saveLoading = false
      }
    },
    async editApp() {
      try {
        this.saveLoading = true
        const res = await apiBaseMenuEditApp(this.meunForm)
        this.saveLoading = false
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('保存成功')
        this.onClose()
        this.getMeun()
      } catch (e) {
        this.saveLoading = false
      }
    },
    externalChange(val) {
      this.$set(this.meunForm, 'component', val ? 'EmbeddedPage' : '')
    },
  },
}
</script>

<style lang="scss" scoped>
.vone-drawer-main {
  padding: 0px 20px;
  overflow-x: hidden;
  overflow-y: auto !important;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .operation-icon {
    // display: none;
    i {
      padding-left: 6px;
      font-size: 16px;
      color: #409eff;
    }
  }
}

:deep(.el-tree .el-tree-node__content) {
  height: 36px;
  padding: 0px 12px 0 5px;
}
.el-tree {
  color: #202124;
}
.custom-theme-dark {
  .el-tree {
    color: #e6e9f0;
  }
}
</style>

<style lang="scss">
.tree {
  .el-tree-node__content {
    height: 36px;
    color: #202124;
  }
  .el-tree-node__content > .el-tree-node__expand-icon {
    margin-left: 14px;
    font-size: 16px;
    color: #202124;
  }
  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
  }
  .operation-icon {
    display: none;
  }
  .el-tree-node__content:hover {
    .operation-icon {
      display: block;
    }
  }
}
.custom-theme-dark {
  .el-tree-node__content {
    color: #e6e9f0;
  }
  .tree {
    .el-tree-node__content > .el-tree-node__expand-icon {
      color: #e6e9f0;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent;
    }
  }
}
:deep(.vone-el-drawer__layout) {
  padding: 20px;
  overflow-y: auto !important;
  height: calc(100vh - 128px);
}
</style>
