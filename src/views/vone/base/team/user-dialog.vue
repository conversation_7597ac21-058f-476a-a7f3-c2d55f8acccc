<template>
  <el-dialog
    :title="'添加成员'"
    :model-value="visible"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <main class="context">
      <aside class="user-context">
        <header>
          <el-tabs
            v-model="tabActive"
            class="vone-tab-line"
            @tab-click="changeTab"
          >
            <template v-for="item in types">
              <el-tab-pane :key="item.key" :label="item.tab" :name="item.key" />
            </template>
          </el-tabs>
          <div class="filterbox">
            <div style="width: 60%">
              <!--机构-->
              <vone-tree-select
                v-if="tabActive == 'org'"
                v-model="orgSearchId"
                style="width: 100%"
                search-nested
                :tree-data="orgDatas"
                placeholder="请选择机构"
              />
              <!--角色-->
              <el-select
                v-if="tabActive == 'role'"
                v-model="roleId"
                multiple
                style="width: 100%"
                placeholder="请选择角色"
                @change="changeRole"
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <!--团队-->
              <vone-tree-select
                v-if="tabActive == 'team'"
                v-model="teamId"
                style="width: 100%"
                search-nested
                :tree-data="teamList"
                placeholder="请选择团队"
                @select="changeTeam"
                @input="clearSelect"
              />
            </div>

            <!--搜索-->
            <el-input
              v-model="filter"
              style="margin-left: 8px; width: 40%"
              placeholder="搜索用户"
            />
          </div>
        </header>
        <section v-if="leftUsers.length" class="user-list">
          <el-checkbox
            v-model="allChecked"
            :indeterminate="isIndeterminate"
            @change="allCheckChange"
            >全选</el-checkbox
          >
          <el-checkbox-group
            v-model="selectUsers"
            v-loading="loadingData"
            @change="checkChange"
          >
            <virtual-list
              :style="{ 'overflow-y': 'auto' }"
              data-key="id"
              :data-sources="leftUsers"
              :data-component="checkUser"
            />
          </el-checkbox-group>
        </section>
        <div v-else class="nothing">暂无数据</div>
      </aside>
      <section class="selected-user">
        <header>
          已选: <span class="count">{{ selectedUserData.length }}</span> 个
        </header>
        <ul class="selected_list">
          <li v-for="u in selectedUserData" :key="u.id" class="item">
            <div class="selected_list_content">
              <vone-user-avatar
                :avatar-path="u.avatarPath"
                :name="u.name"
                :show-name="true"
                width="20px"
                height="20px"
              />
              <span v-if="u.account" style="padding-left: 5px">{{
                `(${u.account})`
              }}</span>
            </div>
            <el-icon class="iconfont delete"><ElIconSetting /></el-icon>
          </li>
        </ul>
      </section>
    </main>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { TipsClose as ElIconTipsClose } from '@element-plus/icons-vue'
import {
  addUserListById,
  teamList,
  getUserListByIdNoPage,
} from '@/api/vone/base/team'
import { orgList } from '@/api/vone/base/org'
import { batchQuery } from '@/api/vone/base/role'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import checkUser from './checkbox-user.vue'
import VirtualList from 'vue-virtual-scroll-list'
import { gainTreeList } from '@/utils'
export default {
  components: {
    VirtualList,
    ElIconTipsClose,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orgId: {
      type: String,
      default: '',
    },
    clickNode: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loadingData: false,
      filter: '',
      timer: null,
      checkUser: checkUser,
      selectUsers: [],
      allUsers: [],
      leftUsers: [],
      tabActive: 'org',
      types: [
        {
          tab: '机构',
          key: 'org',
        },
        {
          tab: '平台角色',
          key: 'role',
        },
        {
          tab: '组织团队',
          key: 'team',
        },
      ],
      orgDatas: [],
      orgSearchId: null,
      roleId: '',
      roleList: [],
      teamId: null,
      teamList: [],
      allChecked: false,
      checkList: [],
      isIndeterminate: false,
    }
  },
  computed: {
    selectedUserData() {
      return this.checkList.filter((v) => this.selectUsers.indexOf(v.id) > -1)
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.selectUsers = []
        this.getUserList()
      }
    },
    filter(val) {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.leftUsers = val
          ? this.allUsers.filter((v) => v.name.indexOf(val) > -1)
          : [...this.allUsers]
      }, 500)
    },
    orgSearchId(val) {
      this.getUserList(val)
    },
  },
  mounted() {
    this.tabActive = 'org'
    this.leftUsers = []
    this.filter = ''
    this.getOrgList()
    this.getRoleList()
    this.getTeamList()
  },
  methods: {
    checkboxChange(val) {
      const checkIds = this.checkList.map((item) => item.id)
      this.leftUsers.map((item, index) => {
        if (val.indexOf(item.id) > -1 && checkIds.indexOf(item.id) == -1) {
          this.checkList.push(item)
        }
      })
      this.checkList = this.checkList.filter(
        (v) => this.selectUsers.indexOf(v.id) > -1
      )
    },
    // 改变全选状态
    checkStateFn() {
      const checkedCount = this.leftUsers.filter(
        (v) => this.selectUsers.indexOf(v.id) > -1
      ).length
      this.allChecked = checkedCount === this.leftUsers.length
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.leftUsers.length
    },
    checkChange(val) {
      this.checkStateFn()
      this.checkboxChange(val)
    },
    allCheckChange(e) {
      const checkIds = this.checkList.map((item) => item.id)
      if (e) {
        this.selectUsers = [
          ...checkIds,
          ...this.leftUsers.map((item) => item.id),
        ]
        this.checkboxChange(this.selectUsers)
      } else {
        const checkId = this.leftUsers.map((item) => item.id)
        this.selectUsers = checkIds.filter((item) => {
          return checkId.indexOf(item) == -1
        })
      }
      this.isIndeterminate = false
    },
    changeTab(e) {
      if (this.tabActive == 'org') {
        this.getUserList(this.orgSearchId || '')
      } else if (this.tabActive == 'role') {
        this.changeRole(this.roleId || [])
      } else {
        const params = this.teamId ? { id: this.teamId } : this.teamList[0]
        this.changeTeam(params)
      }
      this.checkStateFn()
    },
    // 查询所有机构
    getOrgList() {
      orgList().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgDatas = orgTree
      })
    },
    // 查询所有角色
    getRoleList() {
      batchQuery().then((res) => {
        if (res.isSuccess) {
          this.roleList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 查询团队
    getTeamList() {
      teamList().then((res) => {
        this.teamList = gainTreeList(res.data)
      })
    },

    // 选择角色
    changeRole(val) {
      const data = []
      val.map((item) => {
        data.push({
          roleId: item,
        })
      })
      this.getUserList(data)
    },
    // 选择团队
    async changeTeam(val) {
      const res = await getUserListByIdNoPage(val.id)
      if (res.isSuccess) {
        this.allUsers = [...res.data]
        this.leftUsers = res.data
        this.checkStateFn()
      }
    },
    async clearSelect(val) {
      if (!val) {
        this.teamList?.[0] && this.changeTeam(this.teamList[0])
      }
    },
    async getUserList(id) {
      this.loadingData = true
      const params = {
        state: true,
        readonly: false,
      }
      if (this.tabActive == 'org') {
        params.orgId = id || ''
      } else if (this.tabActive == 'role') {
        params.userRoles = id || []
      }
      const res = await apiBaseAllUserNoPage(params)
      if (res.isSuccess) {
        this.allUsers = [...res.data]
        this.leftUsers = res.data
      }
      this.checkStateFn()
      this.loadingData = false
    },
    close() {
      this.$emit('update:visible', false)
      this.tabActive = 'org'
      this.orgSearchId = null
      this.teamId = null
      this.roleId = ''
    },
    deleteUser(user) {
      this.selectUsers =
        this.selectUsers.length > 1
          ? this.selectUsers.filter((v) => v != user.id)
          : []
    },
    async save() {
      if (this.selectUsers.length == 0) {
        this.$message.warning('请选择用户')
        return
      }
      const res = await addUserListById(this.clickNode.id, this.selectUsers)
      if (res.isSuccess) {
        this.$message.success('添加成功')
        this.$emit('update:visible', false)
        this.$emit('success')
      } else {
        this.$message.error('添加失败')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.info_header {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 38px;
  padding: 8px 16px;
  margin-bottom: 16px;
  color: #777f8e;
  background: #f5f6f7;
  border: 1px solid var(--solid-border-color);
  border-radius: 2px;
}

.context {
  display: flex;
  border: 1px solid var(--solid-border-color);
  border-radius: 4px;
  height: 100%;
  overflow: hidden;
}

.user-context {
  width: 360px;
  height: 100%;
  border-right: 1px solid var(--solid-border-color);
  header {
    padding: 8px 16px 8px 16px;
  }
  .nothing {
    // height: 258px;
    text-align: center;
    color: var(--auxiliary-font-color);
  }
  :deep() {
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      // height: 315px;
      overflow-y: auto;
      height: calc(100% - 36px);
    }

    .el-checkbox {
      display: flex;
      align-items: center;
      min-height: 36px;
      margin: 0;
      padding-left: 16px;

      &:hover {
        background-color: #f5f6f7;
      }
    }
  }
}
.selected-user {
  flex: 1;
  header {
    padding: 16px 16px 8px 16px;
    color: var(--font-main-color);
  }
}

.selected_list {
  display: flex;
  flex-direction: column;
  height: 520px;
  overflow-y: auto;

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 36px;
    padding: 0 20px;

    &:hover {
      background-color: #f5f6f7;
    }
    .selected_list_content {
      width: 280px;
      display: flex;
      align-items: center;
      color: var(--font-second-color);
    }
  }

  .delete {
    cursor: pointer;
  }
}
.vone-tab-line {
  margin-bottom: 16px;
}
:deep(.vone-tab-line .el-tabs__item) {
  height: 36px;
  line-height: 36px;
}
:deep(.vone-tab-line .el-tabs__item.is-top:nth-child(2)) {
  margin-left: 0px;
}
.filterbox {
  display: flex;
}
:deep(.vue-treeselect__placeholder, .vue-treeselect__single-value) {
  line-height: 32px;
}
:deep(.vue-treeselect .vue-treeselect__input-container) {
  line-height: 28px;
}
:deep(.vue-treeselect--single .vue-treeselect__input) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-dialog__body) {
  height: 600px;
  overflow: hidden;
}
:deep(.vue-treeselect--append-to-body) {
  width: 400px;
}
.user-list {
  height: calc(100% - 100px);
}
</style>
