<template>
  <div class="list-item-main">
    <el-checkbox :label="source.id">
      <div class="check-content">
        <vone-user-avatar
          v-if="source"
          :avatar-path="source.avatarPath"
          :name="source.name"
          :show-name="true"
          width="20px"
          height="20px"
        />
        <span
          v-if="source.account"
          style="padding-left: 5px; color: var(--font-second-color)"
          >{{ `(${source.account})` }}</span
        >
      </div>
    </el-checkbox>
  </div>
</template>

<script>
export default {
  props: {
    source: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.list-item-main {
  .check-content {
    display: flex;
    align-items: center;
  }
  .el-checkbox {
    font-weight: normal;
  }
}
</style>
