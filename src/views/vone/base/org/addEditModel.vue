<template>
  <el-dialog
    :title="editTitle"
    :model-value="dialogVisible"
    width="600px"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form
      ref="form"
      v-loading="formLoading"
      :rules="rules"
      :model="org"
      label-position="top"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item
            v-if="org.parentId != 0"
            label="上级机构"
            prop="parentId"
          >
            <el-select
              v-model="org.parentId"
              placeholder="请选择上级机构"
              style="width: 100%"
              :disabled="editTitle == '编辑' && org.type == 'ORG_TYPE'"
            >
              <el-option
                v-for="item in allOrgList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构名称" prop="name">
            <el-input
              v-model.trim="org.name"
              placeholder="请输入名称"
              :disabled="editTitle == '编辑' && org.type == 'ORG_TYPE'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构编码" prop="code">
            <el-input
              v-model.trim="org.code"
              placeholder="请输入机构编码"
              :disabled="editTitle == '编辑'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构类型" prop="type">
            <el-select
              v-model="org.type"
              placeholder="请选择类型"
              :disabled="editTitle == '编辑'"
            >
              <el-option
                v-for="item in orgType"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="user">
            <vone-remote-user
              v-model="org.leadingBy"
              placeholder="请选择负责人"
              :disabled="org.parentId == 0"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="org.description"
              type="textarea"
              placeholder="描述"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSave"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  editOrg,
  addOrg,
  apiBaseOrgNoPage,
  apiBaseOrgInfo,
} from '@/api/vone/base/org'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    editTitle: {
      type: String,
      default: '',
    },
    parentCode: {
      type: String,
      default: null,
    },
    parentId: {
      type: String,
      default: '',
    },
    treeId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      srcList: [],
      rules: {
        parentId: [
          {
            required: true,
            message: '请选择上级机构',
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
        ],
        code: [
          { required: true, message: '请输入应用标识' },
          {
            pattern: '^[a-zA-Z0-9-_@#$%&*]{1,50}$',
            message: '请输入1~50位字母、数字、特殊符号-_@#$%&*',
          },
        ],
        type: [
          {
            required: true,
            message: '请选择机构类型',
            trigger: 'change',
          },
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change',
          },
        ],
      },
      org: {
        id: '',
        name: '',
        code: '',
        parentId: this.parentId,
        state: true,
        leadingBy: '',
        type: '',
        description: '',
        sort: 0,
      },
      allOrgList: [],
      loading: false,
      formLoading: false,
      orgType: [],
      fileList: [],
      uploadFiles: [],
      imageUrl: '',
    }
  },
  mounted() {
    this.getOrgTypes()
    this.getAllOrg()
    if (this.editTitle == '编辑') {
      this.getInfo()
    }
  },
  methods: {
    getOrgTypes() {
      apiBaseDictNoPage({ type: 'DATA_TYPE' }).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        if (this.editTitle == '新增') {
          const orgTypeData =
            res.data?.filter((item) => item.code != 'ORG_TYPE') || []
          this.orgType = orgTypeData
          this.$set(this.org, 'type', orgTypeData?.[0]?.code || '')
        } else {
          this.orgType = res.data
        }
      })
    },
    async getInfo() {
      this.formLoading = true
      const res = await apiBaseOrgInfo(this.treeId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.org = res.data
      this.formLoading = false
    },
    async getAllOrg() {
      const res = await apiBaseOrgNoPage()
      if (!res.isSuccess) {
        return
      }
      this.allOrgList = res.data
    },
    async onSave() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      this.loading = true
      if (this.editTitle == '新增') {
        this.org.parentCode = this.parentCode
        addOrg(this.org)
          .then(async (res) => {
            this.loading = false
            if (res.isSuccess) {
              this.$message.success('保存成功')
              this.$emit('success')
              this.$emit('update:dialogVisible', false)
            } else {
              this.$message.warning(res.msg)
            }
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        editOrg(this.org)
          .then(async (res) => {
            this.loading = false
            if (res.isSuccess) {
              this.$message.success('修改成功')
              this.$emit('success')
              this.$emit('update:dialogVisible', false)
            } else {
              this.$message.warning(res.msg)
            }
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    close() {
      this.$emit('update:dialogVisible', false)
      this.$refs.form.resetFields()
    },
    handlePictureCardPreview(file) {
      this.$refs.upload.clearFiles()
    },
    handleRemove(file) {
      this.imageUrl = ''
      this.$refs.upload.clearFiles()
    },
    handleEdit() {
      this.$refs.upload.$el.querySelector('input').click()
    },
    fileUploadSuccess(respones, file, fileList) {
      this.imageUrl = URL.createObjectURL(file.raw)
      if (respones) {
        if (respones.isSuccess) {
          file.id = respones.data.id
          this.uploadFiles.push(respones.data)
        } else {
          fileList.map((item, index) => {
            if (item.uid === file.uid) {
              fileList.splice(index, 1)
            }
          })
          this.$message.warning(respones.msg)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.el-upload-box {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  position: relative;
  background: var(--content-bg-disabled-color);
  overflow: hidden;
  .avatar {
    width: 100%;
  }
}
.smallbox {
  height: 16rem;
  width: 16rem;
  position: absolute;
  top: 34rem;
  left: 10rem;
  z-index: 9999;
}
:deep(.addbox) {
  width: 80px;
  height: 80px;
  line-height: 88px;
  background: var(--content-bg-disabled-color);
  border-radius: 4px;
  border: unset;
  i {
    font-size: 24px;
  }
}
:deep(.el-upload-list__item-actions) {
  font-size: 16px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  height: 80px;
  width: 80px;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  display: none;
  line-height: 80px;
  .iconfont {
    color: #fff;
  }
}
.el-upload-box:hover {
  :deep(.el-upload-list__item-actions) {
    display: flex;
  }
}
</style>
