<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="log-table"
          :model="formData"
          :default-fileds="defaultFileds"
          :table-ref="$refs['log-table']"
          show-basic
          :extra="extraData"
          @getTableData="getLogList"
        />
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          :icon="ElIconIconfont elIconApplicationDelete"
          :disabled="!$permission('authority_optLog_delete')"
          @click="clearLog"
          >清理日志</el-button
        >
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getLogList"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="log-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="标题" field="description" min-width="150">
          <template slot-scope="scope">
            <a @click="showInfo(scope.row)">{{ scope.row.description }}</a>
          </template>
        </vxe-column>
        <vxe-column title="日志类型" field="type" width="100">
          <template slot-scope="scope">
            <span style="color: #05a660">{{ scope.row.type.desc }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作人" field="userName" width="120" />
        <vxe-column title="请求方法" field="actionMethod" width="120" />
        <vxe-column title="IP地址" field="requestIp" width="120" />
        <vxe-column title="请求地址" field="requestUri" min-width="120" />
        <vxe-column title="请求类型" field="httpMethod" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.httpMethod.desc == 'POST' ? 'success' : 'danger'"
              >{{ scope.row.httpMethod.desc }}</el-tag
            >
          </template>
        </vxe-column>
        <vxe-column title="查询耗时" field="consumingTime" width="100">
          <template slot-scope="scope">
            {{ scope.row.consumingTime }}毫秒
          </template>
        </vxe-column>
        <vxe-column title="操作时间" field="createTime" width="180" />
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getLogList"
    />
    <!-- 查询日志详情 -->
    <vone-drawer
      :title="title"
      size="40%"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
    >
      <el-form v-loading="formLoading" :model="detailForm">
        <el-form-item>
          <div>
            <el-card header="参数">
              <vone-empty v-if="!detailForm.params" />
              <json-viewer v-else :value="detailForm.params" class="viewer" />
            </el-card>
          </div>
        </el-form-item>
        <el-form-item>
          <div>
            <el-card header="详情">
              <vone-empty v-if="!detailForm.result" />
              <json-viewer v-else :value="detailForm.result" class="viewer" />
            </el-card>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
      </div>
    </vone-drawer>
    <!-- 清理日志 -->
    <el-dialog
      title="清理日志"
      v-model="logVisible"
      width="30%"
      :close-on-click-modal="false"
      @closed="close"
    >
      <el-form ref="clearLogForm" :model="clearLogForm" :rules="logFormRules">
        <el-form-item label="清理类型" prop="type">
          <el-select v-model="clearLogForm.type" placeholder="请选择">
            <el-option label="一个月前" value="1" />
            <el-option label="一个季度前" value="2" />
            <el-option label="半年前" value="3" />
            <el-option label="1年前" value="4" />
            <el-option label="保留最新1千条" value="5" />
            <el-option label="保留最新1万条" value="6" />
            <el-option label="保留最新3万条" value="7" />
            <el-option label="保留最新10万条" value="8" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="sureClear(clearLogForm)"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </page-wrapper>
</template>

<script>
import {
  apiBaseLogList,
  apiBaseGetLogInfo,
  apiBaseOptLogClear,
} from '@/api/vone/base/log'

import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import * as dayjs from 'dayjs'

export default {
  filters: {
    getDuration(my_time) {
      if (!my_time) return ''
      var days = my_time / 1000 / 60 / 60 / 24
      var daysRound = Math.floor(days)
      var hours = my_time / 1000 / 60 / 60 - 24 * daysRound
      var hoursRound = Math.floor(hours)
      var minutes = my_time / 1000 / 60 - 24 * 60 * daysRound - 60 * hoursRound
      var minutesRound = Math.floor(minutes)
      var seconds =
        my_time / 1000 -
        24 * 60 * 60 * daysRound -
        60 * 60 * hoursRound -
        60 * minutesRound
      // var sum = `${daysRound}天${hoursRound}时${minutesRound}分${seconds}秒`
      var sum = `${seconds}秒`
      return sum
    },
  },
  data() {
    return {
      loginForm: {
        date: [
          dayjs().add(-1, 'M').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
      },
      logFormRules: {
        type: [
          { required: true, message: '请选择清理类型', trigger: 'change' },
        ],
      },
      extraData: {},
      defaultFileds: [
        {
          key: 'types',
          name: '日志类型',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择日志类型',
          multiple: true,
        },
        {
          key: 'httpMethods',
          name: '请求类型',
          type: {
            code: 'SELECT',
          },
          multiple: true,
          placeholder: '请选择请求类型',
          optionList: [
            {
              code: 'GET',
              name: 'GET',
              id: '1',
            },
            {
              code: 'POST',
              name: 'POST',
              id: '2',
            },
            {
              code: 'PUT',
              name: 'PUT',
              id: '3',
            },
            {
              code: 'DELETE',
              name: 'DELETE',
              id: '4',
            },
            {
              code: 'OPTIONS',
              name: 'OPTIONS',
              id: '5',
            },
          ],
        },

        {
          key: 'userId',
          name: '操作人',
          type: {
            code: 'USER',
          },
          placeholder: '请选择操作人',
          multiple: false,
        },
        {
          key: 'requestUri',
          name: '请求地址',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入请求地址',
        },
        {
          key: 'date',
          name: '操作时间',
          type: {
            code: 'DATE',
          },
          placeholder: '请选择操作时间',
        },
      ],
      formData: {
        types: [],
        userName: '',
        actionMethod: '',
        requestUri: '',
        httpMethods: [],
        startOptDate: '',
        endOptDate: '',
        createdBy: '',
        createTime: '',
        date: [
          dayjs().add(-1, 'M').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
      },

      tableData: {},
      showAll: false,
      tableLoading: false,
      formLoading: false,

      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      // 表格配置项
      tableOptions: {
        isColSort: false, // 列是否排序
        isSelection: false, // 表格有多选时设置
        isOperation: false, // 表格有操作列时设置
        isIndex: false, // 列表序号
      },
      title: '',
      dialogFormVisible: false,
      logVisible: false, // 清理日志
      clearLogForm: {},
      detailForm: {
        params: '',
        exDetail: '',
      },
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  mounted() {
    // this.getLogList()
    this.getTypeList()
  },
  methods: {
    clearLog() {
      this.logVisible = true
    },
    close() {
      this.logVisible = false
      this.$refs.clearLogForm.resetFields()
    },
    async sureClear() {
      try {
        await this.$refs.clearLogForm.validate()
      } catch (error) {
        return
      }
      const res = await apiBaseOptLogClear(this.clearLogForm.type)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('清理成功')
      this.close()
      this.getLogList()
    },
    getName(id, items) {
      var name
      items.forEach((item) => {
        if (id === item.id) {
          name = item.name
        }
      })
      return name
    },
    async getLogList() {
      this.tableLoading = true

      if (this.formData.date) {
        this.$set(this.formData, 'startOptDate', this.formData.date[0])
        this.$set(this.formData, 'endOptDate', this.formData.date[1])
      } else {
        this.$set(this.formData, 'startOptDate', '')
        this.$set(this.formData, 'endOptDate', '')
      }

      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...pageObj,
        ...sortObj,
        extra: { ...this.extraData },
        model: { ...this.formData },
      }
      const res = await apiBaseLogList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },

    // 查日志类型
    async getTypeList() {
      const res = await apiBaseDictEnumList(['OptLogTypeEnum'])

      if (!res.isSuccess) {
        return
      }

      res.data.OptLogTypeEnum.forEach((element) => {
        element.name = element.label
        element.code = element.value
        element.id = element.value
      })
      this.setData(this.defaultFileds, 'types', res.data.OptLogTypeEnum)
    },
    // 搜索操作
    confirm() {
      this.getLogList()
      // this.$refs['log-table'].searchTable(this.logForm)
    },
    // 重置操作
    resetForm() {
      this.logForm = {
        type: '',
        userName: '',
        actionMethod: '',
        requestUri: '',
        httpMethod: '',
        startTime: '',
        finishTime: '',
        createdBy: '',
        createTime: '',
      }
      // 查询表单数据
      this.getLogList()
      // this.$refs['log-table'].searchTable(this.logForm)
    },
    // 切换操作
    triggerSearch() {
      this.showAll = !this.showAll
    },
    showInfo(row) {
      this.dialogFormVisible = true
      this.getLogInfo(row.id)
    },
    async getLogInfo(val) {
      this.formLoading = true
      const res = await apiBaseGetLogInfo(val)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.detailForm = res.data
      this.title = `【${this.detailForm.description}】详情`
    },
  },
}
</script>

<style lang="scss" scoped>
.align {
  text-align: center;
}
.searchCard {
  position: relative;
  margin-bottom: 20px;

  :deep(.el-date-editor.el-input),
  .el-date-editor.el-input__inner {
    width: 100%;
  }
}

.btnContainer {
  margin-bottom: 10px;
}

.arrow {
  position: absolute;
  left: 50%;
  bottom: 0px;
  transform: translateX(-8px);

  i {
    cursor: pointer;
    &:hover {
      color: #66b1ff;
    }
  }
}
:deep(.vone-el-drawer__layout) {
  padding: 20px;
  overflow-y: auto !important;
  height: calc(100vh - 128px);
}

.viewer {
  background: none !important;
}
</style>
