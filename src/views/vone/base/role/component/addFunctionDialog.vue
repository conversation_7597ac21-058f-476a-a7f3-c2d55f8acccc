<template>
  <el-drawer
    :title="title"
    size="40%"
    v-model="visible"
    :before-close="onClose"
    class="drawer"
    append-to-body
    :wrapper-closable="false"
  >
    <el-form
      ref="nodeDetail"
      v-loading="formLoading"
      :model="nodeDetail"
      :rules="formRule"
      label-width="80px"
    >
      <el-form-item label="上级菜单">
        <el-select
          v-model="nodeDetail.menuId"
          placeholder="请选择上级菜单"
          style="width: 100%"
          disabled
        >
          <el-option
            v-for="item in allMenuList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <!-- <el-input v-model="nodeDetail.menuId" disabled /> -->
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="nodeDetail.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input v-model="nodeDetail.code" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="nodeDetail.description" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <div class="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSubmit"
        >确定</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import {
  apiBaseMenuAddFunction,
  apiBaseMenuFunctionGetById,
  apiBaseMenuEditFunction,
  apiBaseMenuNoPage,
} from '@/api/vone/base/meun'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    menuId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      title: '新增功能权限',
      nodeDetail: {
        menuId: this.menuId || this.id,
        name: '',
        code: '',
        description: '',
      },
      formLoading: false,
      formRule: {
        name: [{ required: true, message: '请输入名称' }],
        path: [{ required: true, message: '请输入url' }],
        description: [{ required: false, message: '请输入描述', max: 255 }],
      },
      saveLoading: false,
      allMenuList: [],
    }
  },
  watch: {
    visible(val) {
      if (val && this.id) {
        this.title = '编辑功能权限'
        this.getDetailsById()
      } else {
        this.title = '新增功能权限'
        this.nodeDetail.menuId = this.menuId
      }
    },
  },
  created() {
    this.getAllMenu()
  },
  methods: {
    async getAllMenu() {
      const res = await apiBaseMenuNoPage()
      if (!res.isSuccess) {
        return
      }
      this.allMenuList = res.data
    },
    onClose() {
      this.$refs.nodeDetail.resetFields()
      this.$emit('update:visible', false)
    },
    // 查详情
    async getDetailsById() {
      this.formLoading = true
      const res = await apiBaseMenuFunctionGetById(this.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.nodeDetail = res.data || {}
    },
    async onSubmit() {
      if (this.id) {
        this.editApp()
      } else {
        try {
          await this.$refs.nodeDetail.validate()
          this.saveLoading = true
          const res = await apiBaseMenuAddFunction(this.nodeDetail)
          this.saveLoading = false
          if (!res.isSuccess) {
            this.$message.error(res.msg)
            return
          }

          this.$message.success(res.msg)
          this.onClose()
          this.$emit('refreshTable', this.nodeDetail)
        } catch (e) {
          this.saveLoading = false
        }
      }
    },
    async editApp() {
      try {
        await this.$refs.nodeDetail.validate()
        this.saveLoading = true
        const res = await apiBaseMenuEditFunction({
          ...this.nodeDetail,
          id: this.id,
        })
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }

        this.$message.success(res.msg)
        this.onClose()
        this.$emit('refreshTable')
      } catch (e) {
        this.saveLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.footer {
  text-align: right;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 16px 20px;
  background-color: var(--main-bg-color, #fff);
  border-top: 1px solid var(--disabled-bg-color, #ebeef5);
}
.drawer {
  :deep(.el-drawer__body) {
    height: 100%;
    overflow-y: auto;
    padding-right: 20px;
    padding-bottom: 74px;
  }
}
</style>
