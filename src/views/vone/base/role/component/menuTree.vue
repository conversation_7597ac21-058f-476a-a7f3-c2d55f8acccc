<template>
  <div style="padding: 0px 10px">
    <el-tree
      ref="tree"
      node-key="id"
      :data="menuTree"
      show-checkbox
      :props="defaultProps"
      default-expand-all
      :default-checked-keys="menuIdList"
      @node-click="handleNodeClick"
    >
      <span slot-scope="{ node }" class="custom-tree-node">
        <span>{{ node.label }}</span>
      </span>
    </el-tree>

    <el-drawer
      :title="menuTitle"
      size="40%"
      v-model="visible"
      :before-close="onClose"
      :wrapper-closable="false"
    >
      <el-form
        ref="meunForm"
        v-loading="formLoading"
        :model="meunForm"
        :rules="formRule"
        label-width="80px"
      >
        <el-form-item label="上级ID">
          <el-input v-model="meunForm.parentId" disabled />
        </el-form-item>
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="meunForm.name" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="路由URL" prop="path">
          <el-input v-model="meunForm.path" placeholder="请输入路由URL" />
        </el-form-item>
        <el-form-item label="组件">
          <el-input v-model="meunForm.component" placeholder="请输入组件名称" />
        </el-form-item>
        <el-form-item label="组件路径">
          <el-input v-model="meunForm.subRoute" placeholder="请输入组件路径" />
        </el-form-item>
        <el-form-item label="图标">
          <el-input v-model="meunForm.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="meunForm.state"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="通用菜单">
          <el-switch
            v-model="meunForm.isGeneral"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number
            v-model="meunForm.sort"
            :min="0"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="meunForm.description" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div class="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSubmit"
          >确定</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  apiBaseMenuGetById,
  apiBaseMenuAddApp,
  apiBaseMenuTree,
  apiBaseMenuDelApp,
  apiBaseMenuEditApp,
} from '@/api/vone/base/meun'
export default {
  props: {
    menuIdList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      saveLoading: false,
      formLoading: false,
      menuTitle: '',
      isEdit: false,
      menuTree: [],
      meunForm: {},
      formRule: {
        name: [{ required: true, message: '请输入名称' }],
        path: [{ required: true, message: '请输入url' }],
        description: [{ required: false, message: '请输入描述', max: 255 }],
      },
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },
  watch: {
    menuIdList(val) {
      if (val.length > 0) {
        // 设置选中树节点
        this.$refs.tree && this.$refs.tree.setCheckedNodes(val)
      }
    },
  },
  mounted() {
    this.getMeun()
  },
  methods: {
    // 节点点击
    handleNodeClick(data) {
      this.$refs.tree.setCurrentKey(data.id)
      this.$emit('handleNodeClick', data)
    },
    // 节点选中的数组,父级组件查询选中菜单数据
    getTreeSelected() {
      return this.$refs.tree.getCheckedKeys()
    },
    // 查菜单树
    async getMeun() {
      this.formLoading = true
      const res = await apiBaseMenuTree()
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.menuTree = res.data
    },
    add(node) {
      this.visible = true
      this.menuTitle = '新增菜单'
      this.$set(this.meunForm, 'parentId', node.data.id)
    },
    edit(node) {
      this.visible = true
      this.menuTitle = '编辑菜单'
      this.isEdit = true
      this.getDetailsById(node.data.id)
    },
    async remove(node) {
      await this.$confirm(`确定删除 【${node.data.name}】菜单吗?`, '删除', {
        type: 'warning',
      })
      const res = await apiBaseMenuDelApp([node.data.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.getMeun()
    },
    onClose() {
      this.$refs.meunForm.resetFields()
      this.visible = false
      // this.$emit('update:visible', false)
    },
    // 查详情
    async getDetailsById(id) {
      this.formLoading = true
      const res = await apiBaseMenuGetById(id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.meunForm = res.data
    },
    async onSubmit() {
      if (this.isEdit) {
        this.editApp()
      } else {
        await this.$refs.meunForm.validate()
        this.saveLoading = true
        const res = await apiBaseMenuAddApp(this.meunForm)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
        } else {
          this.$message.success(res.msg)
        }

        this.onClose()
        this.getMeun()
      }
    },
    async editApp() {
      await this.$refs.meunForm.validate()
      this.saveLoading = true
      const res = await apiBaseMenuEditApp(this.meunForm)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      } else {
        this.$message.success(res.msg)
      }
      this.onClose()
      this.getMeun()
    },
  },
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.footer {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 16px 20px;
  background-color: var(--main-bg-color, #fff);
  border-top: 1px solid var(--disabled-bg-color, #ebeef5);
}
</style>
