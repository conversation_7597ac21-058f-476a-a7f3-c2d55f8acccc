<template>
  <el-dialog
    class="dialogContainer"
    :title="title"
    :model-value="dialogVisible"
    width="456px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form ref="addForm" :model="addForm" :rules="rules" label-position="top">
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="addForm.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="角色编号" prop="code">
        <el-input
          v-model="addForm.code"
          placeholder="请输入编号"
          :disabled="nowRow.id ? true : false"
        />
      </el-form-item>
      <el-form-item v-if="type == 'platFormRole'" label="组织机构" prop="orgId">
        <vone-tree-select
          v-model="addForm.orgId"
          search-nested
          :tree-data="orgData"
          placeholder="请选择机构"
        />
      </el-form-item>
      <el-form-item
        v-if="type == 'projectRole'"
        label="项目类型"
        prop="typeCode"
      >
        <el-select
          v-model="addForm.typeCode"
          filterable
          :disabled="nowRow.id ? true : false"
        >
          <el-option
            v-for="mode in projectTypes"
            :key="mode.id"
            :label="mode.name"
            :value="mode.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="addForm.description"
          type="textarea"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="addConfirm"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { apiAlmProjectRoleAdd } from '@/api/vone/project/setting'
import { getAllProjectType } from '@/api/vone/project'
import { addRole, editRoleApi } from '@/api/vone/base/role'
import { orgList } from '@/api/vone/base/org'
import { gainTreeList } from '@/utils'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'platFormRole',
    },
    nowRow: {
      type: Object,
      default: null,
    },
    actionType: {
      type: String,
      default: 'add',
    },
  },
  data() {
    return {
      loading: false,
      orgData: [],
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,50}$',
            message: '请输入不超过50位由字母、数字或者下划线组成的名称',
            trigger: 'change',
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
        ],
        code: [
          {
            required: true,
            message: '请输入角色编号',
            trigger: 'blur',
          },
          {
            pattern: '^([a-zA-Z0-9-]){1,30}$',
            message: '请输入不超过30个字母、数字或横线(-)组成的标识',
          },
          {
            pattern: '^(?!-)(?!.*?-$)',
            message: '不能以横线开头或结尾',
          },
        ],
        typeCode: [
          {
            required: true,
            message: '请选择项目类型',
            trigger: 'blur',
          },
        ],
        orgId: [
          {
            required: false,
            message: '请选择组织机构',
            trigger: 'blur',
          },
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change',
          },
        ],
      },
      addForm: {
        orgId: null,
        typeCode: '',
        name: '',
        code: '',
        state: true,
        description: '',
      },
      projectTypes: [],
    }
  },
  computed: {
    title() {
      return this.actionType === 'add' ? '新增角色' : '编辑角色'
    },
  },

  mounted() {
    if (this.type == 'platFormRole') {
      this.getOrgList()
    } else {
      this.getProjectType()
    }
    // 编辑时数据回显
    if (this.nowRow.id) {
      this.addForm = this.nowRow
    }
  },
  methods: {
    // 查询所有机构
    async getOrgList() {
      await orgList().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgData = orgTree
      })
    },
    // 查询项目类型
    getProjectType() {
      getAllProjectType().then((res) => {
        this.projectTypes = res.data || []
      })
    },
    // 保存
    async addConfirm() {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }
      const param = this.addForm
      let addApi = null
      if (this.type == 'platFormRole') {
        addApi = this.nowRow.id ? editRoleApi : addRole
      } else {
        param.projectId = ''
        param.type = true
        addApi = apiAlmProjectRoleAdd
      }
      this.loading = true
      addApi(param)
        .then((res) => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('操作成功')
            this.close()
            this.$emit('success')
          } else {
            this.$message.warning(res.msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    close() {
      // this.$emit('success')
      this.$emit('update:dialogVisible', false)
      this.$refs.addForm.resetFields()
    },
  },
}
</script>
