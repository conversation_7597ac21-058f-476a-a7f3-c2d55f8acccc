<template>
  <el-dialog
    :close-on-click-modal="false"
    title="分配角色成员"
    :model-value="dialogVisible"
    :width="width"
    top="50px"
    append-to-body
    @close="close"
  >
    <el-form ref="form" :model="userRole" :rules="rules" label-position="right">
      <el-form-item prop="userIdList">
        <el-transfer
          v-model="userRole.userIdList"
          :data="userList"
          :filter-method="filterMethod"
          :props="{
            key: 'id',
            label: 'name',
          }"
          :render-content="renderFunc"
          :right-default-checked="userRole.userIdList"
          filter-placeholder="用户名"
          filterable
          :titles="['全部用户', '已选用户']"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button :disabled="disabled" type="primary" @click="submitForm"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  getRoleUsers,
  saveRoleForUser,
  getAllUsersInfo,
} from '@/api/vone/base/role'

export default {
  name: 'UserRoleEdit',
  props: {
    // 弹窗显示
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    // 当前角色
    role: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      userRole: this.initUserRole(),
      screenWidth: 0,
      width: this.initWidth(),
      userList: [],
      roleUserData: {}, // 当前用户信息
      disabled: false,
      rules: {},
      renderFunc(h, option) {
        const title = h('span', {}, [option.account, ' - ', option.name])
        return h(
          'el-tooltip',
          {
            props: {
              placement: 'top',
              content: option.account + ' - ' + option.name,
            },
          },
          [title]
        )
      },
    }
  },
  watch: {},
  async mounted() {
    // 查询全部用户
    this.initUserList()

    window.onresize = () => {
      return (() => {
        this.width = this.initWidth()
      })()
    }
    this.setUserRole()
  },
  methods: {
    initUserRole() {
      return {
        roleId: '',
        userIdList: [],
      }
    },
    initWidth() {
      this.screenWidth = document.body.clientWidth
      if (this.screenWidth < 991) {
        return '90%'
      } else if (this.screenWidth < 1400) {
        return '45%'
      } else {
        return '800px'
      }
    },
    // 查询所有用户信息
    initUserList() {
      const params = { orgId: this.role.orgId }
      getAllUsersInfo(params).then((res) => {
        if (res.isSuccess) {
          this.userList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 查询当前角色已关联用户
    setUserRole() {
      this.userRole.roleId = this.role.id
      if (this.role.id) {
        getRoleUsers(this.role.id).then((res) => {
          if (res.isSuccess) {
            this.roleUserData = res.data
            this.userRole.userIdList = res.data.idList
          }
        })
      }
    },
    close() {
      this.$emit('update:dialogVisible', false)
      this.$emit('close')
    },
    reset() {
      // 先清除校验，再清除表单，不然有奇怪的bug
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
      this.userRole = this.initUserRole()
      this.disabled = false
      this.$emit('update:dialogVisible', false)
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.editSubmit()
        } else {
          return false
        }
      })
    },
    // 修改用户角色
    editSubmit() {
      this.disabled = true
      saveRoleForUser(this.userRole).then((res) => {
        if (res.isSuccess) {
          this.$message.success('修改成功')
          this.reset()
          this.$emit('success')
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    filterMethod(query, item) {
      return item.name.indexOf(query) > -1 || item.account.indexOf(query) > -1
    },
  },
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-dialog__body {
    padding: 16px;
  }
  .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-transfer-panel__body {
    height: 346px;
  }
  .el-transfer-panel__list {
    height: 294px;
  }
  .el-transfer__buttons .el-button {
    min-width: 42px;
  }
}
</style>
