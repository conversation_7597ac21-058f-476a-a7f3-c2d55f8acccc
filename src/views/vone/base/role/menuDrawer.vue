<template>
  <vone-drawer
    size="600px"
    :close-on-click-modal="false"
    title="配置菜单资源"
    :visible="menuVisible"
    top="50px"
    @close="onClose"
  >
    <div class="mainContainer">
      <div class="leftBox">
        <ul class="moduleContainer">
          <li
            v-for="(item, i) in moduleList"
            :key="item.id"
            class="itemBox"
            @click="chooseModule(item, i)"
          >
            <el-checkbox
              v-model="item.checked"
              :label="item.id"
              @change="(val) => checkedAll(val, false)"
            />
            <span class="label">{{ item.name }}</span>
          </li>
        </ul>
      </div>
      <div class="rightBox">
        <div class="treeActions">
          <el-input
            v-model="functionName"
            style="width: 100%"
            placeholder="输入关键字过滤"
          />
          <el-checkbox
            v-model="checkedMenu"
            class="all"
            :indeterminate="isIndeterminate"
            @change="(val) => checkedAll(val, true)"
            >全选/反选</el-checkbox
          >
        </div>
        <section class="treeBox">
          <el-tree
            ref="menuTree"
            check-strictly
            :data="selectedModule"
            :default-checked-keys="defaultIdList"
            :expand-on-click-node="false"
            default-expand-all
            highlight-current
            :icon="el-icon-setting"
            node-key="id"
            show-checkbox
            :props="defaultProps"
            :filter-node-method="filterNode"
            @check="checkMenu"
          >
            <template v-slot="{ node, data }">
              <span class="custom-node">
                <el-tag
                  v-if="data.type.code === 'MENU'"
                  size="mini"
                  class="menuTag"
                  >菜单</el-tag
                >
                <el-tag v-else size="mini" class="funcTag">功能</el-tag>
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </section>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submitForm"
        >确定</el-button
      >
    </div>
  </vone-drawer>
</template>

<script>
import {
  searchRoleResIds,
  saveRolePermission,
  getCurrentMenuTree,
} from '@/api/vone/base/role'
import { setPermission } from '@/utils/auth'
import { getUserPermission } from '@/api/user'
import { concat } from 'lodash'
const getParentNode = (list, item) => {
  list.forEach((e) => {
    e.parentList = item ? concat(item?.parentList, item.id) : []
    if (item) {
      e.parentList = item ? concat(item?.parentList, item.id) : []
    }
    if (e.children && e.children.length) {
      getParentNode(e.children, e)
    }
  })
  return list
}
// 设置角色默认数据结构
const initRoleAuthority = () => {
  return {
    roleId: '',
    menuIdList: [],
    functionIdList: [],
  }
}
// 获取当前菜单下所有菜单和功能id
const getMenuIdList = (menuList, type) => {
  let moduleIds = {
    menuIdList: [],
    functionIdList: [],
    allIdList: [],
  }
  menuList?.forEach((item) => {
    const name = item?.type?.code === 'MENU' ? 'menuIdList' : 'functionIdList'
    moduleIds[name]?.push(item.id)
    moduleIds.allIdList.push(item.id)
    if (item?.children?.length > 0) {
      const childIds = getMenuIdList(item.children, type) ?? {}
      moduleIds = {
        menuIdList: [...moduleIds.menuIdList, ...childIds.menuIdList],
        functionIdList: [
          ...moduleIds.functionIdList,
          ...childIds.functionIdList,
        ],
        allIdList: [...moduleIds.allIdList, ...childIds.allIdList],
      }
    }
  })
  return moduleIds
}
// 切换id选中
const triggerCheckId = (list, v, flag) => {
  let index = list.indexOf(v)
  // 全选且默认菜单不包括当前项
  if (flag && index === -1) {
    list.push(v)
  } else if (!flag) {
    // 取消全选且默认菜单包括当前项
    while (index !== -1) {
      list.splice(index, 1)
      index = list.indexOf(v)
    }
  }
}
export default {
  name: 'RoleAuthorityEdit',
  props: {
    menuVisible: {
      type: Boolean,
      default: false,
    },
    // 选中的角色数据
    role: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      functionName: '', // 筛选文本
      roleAuthority: initRoleAuthority(),
      curModule: {},
      selectedModule: [], // 选择的右侧树数据
      moduleList: [], // 所有的模块数据
      defaultIdList: [], // 默认选中数据id
      isIndeterminate: false,
      checkedMenu: false, // 是否全选
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: (data) => data?.type?.code === 'FUNCTION',
      },
      saveLoading: false,
      timer: null,
    }
  },
  computed: {
    // 获取右侧所有菜单和模块id
    curModuleIds() {
      return getMenuIdList(this.selectedModule)
    },
  },
  watch: {
    functionName(val) {
      // 过滤节点树数据
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$refs.menuTree.filter(val)
      }, 1000)
    },
  },
  async created() {
    await this.getAllMenuFuncTree()
    await this.setRoleAuthority()
  },
  methods: {
    getResource() {
      getUserPermission().then(async (response) => {
        const { data, isSuccess } = response
        if (!isSuccess) {
          return this.$message('获取用户权限失败，请重新登录')
        }

        this.$store.commit('user/set_permission', data.functionList)

        setPermission(data.functionList)
      })
    },
    // 模块点击
    chooseModule(item, i) {
      const actived = document.querySelector('.isActive')
      actived?.classList.remove('isActive')
      const itemActive = document.querySelectorAll('.itemBox')[i]
      itemActive?.classList.add('isActive')
      // 当前模块id
      this.curModule = item
      this.selectedModule = getParentNode(item?.children ?? [], null)
      // this.selectedModule = item?.children ?? []
      // 选中的所有菜单和功能id
      const idsList = [
        ...this.roleAuthority.menuIdList,
        ...this.roleAuthority.functionIdList,
      ]
      // 设置默认选中的id
      // this.defaultIdList = this.allIdList.filter(v => idsList.includes(v))
      this.$refs.menuTree.setCheckedKeys(idsList)
      // 设置是否全选
      this.checkAllMenu()
    },
    // 过滤节点方法
    filterNode(value, data) {
      if (!value) return true
      return data.name?.indexOf(value) !== -1
    },
    // 查询所有菜单和功能树
    async getAllMenuFuncTree() {
      try {
        const res = await getCurrentMenuTree()
        if (res.isSuccess) {
          this.moduleList = res.data
        }
      } catch (e) {
        return
      }
    },
    // 查询角色选中的菜单和功能数据
    setRoleAuthority() {
      this.roleAuthority.roleId = this.role.id
      // 回显
      searchRoleResIds({
        roleId: this.role.id,
      }).then((res) => {
        const data = res.data
        this.roleAuthority.menuIdList = data.menuIdList
        this.roleAuthority.functionIdList = data.functionIdList
        // 设置模块默认选中
        this.moduleList.forEach((item) => {
          this.$set(item, 'checked', data.menuIdList.includes(item.id))
        })
        this.curModule = this.moduleList[0]
        // 设置左侧模块默认选中样式
        this.$nextTick(() => {
          this.chooseModule(this.curModule, 0)
        })
      })
    },
    onClose() {
      this.$emit('update:menuVisible', false)
      this.reset()
    },
    reset() {
      this.roleAuthority = initRoleAuthority()
      this.$refs.menuTree.setCheckedKeys([])
    },
    // 保存选择的模块和功能数据
    submitForm() {
      this.saveLoading = true
      const params = {
        roleId: this.roleAuthority.roleId,
        menuIdList: [...new Set(this.roleAuthority.menuIdList)],
        functionIdList: [...new Set(this.roleAuthority.functionIdList)],
      }
      saveRolePermission(params)
        .then((res) => {
          if (res.isSuccess) {
            this.onClose()
            this.$message.success('保存成功')
            this.$emit('success')
            this.getResource()
          }
          this.saveLoading = false
        })
        .catch(() => {
          this.saveLoading = false
        })
    },
    // 默认是否全选
    checkAllMenu() {
      // 选中的所有菜单和功能id
      const idsList = [
        ...this.roleAuthority.menuIdList,
        ...this.roleAuthority.functionIdList,
      ]
      const allIdList = this.curModuleIds.allIdList ?? []
      // 当前模块下的菜单和功能是否都选中
      const checkedLen = allIdList.filter((v) => idsList.includes(v)).length
      // 是否全部选中
      this.checkedMenu = checkedLen === allIdList.length
      // 是否部分选中
      this.isIndeterminate = checkedLen > 0 && checkedLen < allIdList.length
    },
    // 切换右侧选中状态，左侧模块是否选中
    triggerLeftModuleCheck(flag) {
      // 左侧模块是否选中
      triggerCheckId(this.roleAuthority.menuIdList, this.curModule.id, flag)
      // 设置左侧当前模块是否选中样式
      this.moduleList.forEach((item) => {
        if (item.id === this.curModule.id) {
          this.$set(item, 'checked', flag)
        }
      })
    },
    // 切换全选
    checkedAll(val, right) {
      // 切换全选数据
      const list = val ? this.curModuleIds.allIdList : []
      // 设置选中
      this.$refs.menuTree.setCheckedKeys(list)
      // 部分选中
      this.isIndeterminate = false
      // 当前模块所有id
      this.curModuleIds.menuIdList?.forEach((v) => {
        triggerCheckId(this.roleAuthority.menuIdList, v, val)
        if (!val) triggerCheckId(this.roleAuthority.functionIdList, v, val)
      })
      this.curModuleIds.functionIdList?.forEach((v) => {
        triggerCheckId(this.roleAuthority.functionIdList, v, val)
        if (!val) triggerCheckId(this.roleAuthority.menuIdList, v, val)
      })

      // 左侧父级模块是否选中
      if (right) {
        // 点击右侧全选设置左侧模块选中状态
        this.triggerLeftModuleCheck(val)
      } else {
        this.checkedMenu = val
        // 设置左侧模块是否选中
        triggerCheckId(this.roleAuthority.menuIdList, this.curModule.id, val)
      }
    },
    // 节点树切换选择
    checkMenu(data, node) {
      // 当前节点是否被选中
      const selected = node.checkedKeys.indexOf(data.id) > -1 // -1未选中
      const parents = data.parentList || []
      if (selected) {
        parents.map((e) => {
          this.$refs.menuTree.setChecked(e, true)
          triggerCheckId(this.roleAuthority.menuIdList, e, true)
        })
      }
      // 获取当前选中的id数据
      let checkedList = [...node.checkedKeys]
      // 存在子节点
      if (data.children?.length > 0) {
        // 子节点所有id
        const idList = getMenuIdList(data.children).allIdList ?? []
        if (selected) {
          checkedList = [...new Set(checkedList.concat(idList))]
        } else {
          // 删除子节点id
          idList.forEach((v) => {
            const index = checkedList.indexOf(v)
            if (index > -1) {
              checkedList.splice(index, 1)
            }
          })
        }
      }
      if (checkedList.length === 0) {
        // 取消全选
        this.checkedMenu = false
        this.isIndeterminate = false
      } else if (checkedList.length === this.curModuleIds.allIdList?.length) {
        // 全选
        this.checkedMenu = true
        this.isIndeterminate = false
      } else {
        // 部分选中
        this.checkedMenu = false
        this.isIndeterminate = true
      }
      // 左侧父级模块是否选中
      this.triggerLeftModuleCheck(checkedList.length > 0)
      //  处理当前节点子节点选中状态
      this.uniteChildSame(data, selected)
    },
    // 统一处理子节点为相同的勾选状态
    uniteChildSame(data, isSelected) {
      // 设置当前节点选中
      this.$refs.menuTree.setChecked(data.id, isSelected)
      let list = []
      // 类型为菜单
      if (data.type?.code === 'MENU') {
        list = this.roleAuthority.menuIdList
      } else if (data.type?.code === 'FUNCTION') {
        // 类型为功能
        list = this.roleAuthority.functionIdList
      }
      // 设置节点id选中状态
      if (isSelected) {
        triggerCheckId(list, data.id, true)
      } else {
        // 清除选中时，菜单和功能都删除
        triggerCheckId(this.roleAuthority.menuIdList, data.id, false)
        triggerCheckId(this.roleAuthority.functionIdList, data.id, false)
      }
      // 是否存在子数据
      if (data.children?.length > 0) {
        for (let i = 0; i < data.children.length; i++) {
          this.uniteChildSame(data.children[i], isSelected)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.mainContainer {
  display: flex;
  height: 100%;
}
.leftBox {
  height: calc(100vh - 110px);
  width: 212px;
  overflow-y: auto;
  padding: 16px;
  border-right: 1px solid var(--disabled-bg-color);
}
.rightBox {
  width: calc(100% - 212px);
  padding: 16px;
  .treeBox {
    height: calc(100vh - 218px);
    overflow-y: auto;
  }
}
.moduleContainer {
  display: flex;
  flex-direction: column;

  .itemBox {
    padding-left: 18px;
    margin: 0 0 12px 0;
    height: 54px;
    line-height: 54px;
    color: var(--main-font-color);
    border: 1px solid var(--disabled-bg-color);
    border-radius: 4px;
    cursor: pointer;
    :deep(.el-checkbox) {
      .el-checkbox__label {
        display: none;
      }
    }
    .label {
      margin-left: 6px;
    }
    &:last-child {
      margin-bottom: 0;
    }
    &:hover {
      border-color: var(--btn-hover-color);
      .el-checkbox {
        color: var(--btn-hover-color);
      }
    }
  }
  // 选中模块的样式
  .isActive {
    border-color: var(--sub-btn-color);
    .el-checkbox {
      color: var(--sub-btn-color);
    }
  }
}

.treeActions {
  padding-left: 8px;
  .all {
    margin: 16px 0;
  }
}
.custom-node {
  .menuTag {
    margin-right: 8px;
    color: #bd7ffa;
    border-color: #bd7ffa;
    background-color: var(--main-bg-color, #fff);
  }
  .funcTag {
    margin-right: 8px;
    color: #ffbf47;
    border-color: #ffbf47;
    background-color: var(--main-bg-color, #fff);
  }
}
</style>
