<template>
  <div>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="baseProjectRoleTable"
        class="vone-vxe-table"
        height="auto"
        border
        resizable
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="名称" field="name" min-width="150">
          <template #default="{ row }">
            <a @click="showInfo(row)">
              {{ row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column title="角色编号" field="code" min-width="150" />
        <vxe-column title="角色类型" field="type" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.type" effect="dark">公有</el-tag>
            <el-tag v-else type="warning" effect="dark">私有</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="项目类型" field="typeCode" width="100">
          <template #default="{ row }">
            {{ projectTypeName(row) }}
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_role_edit') || !row.type
                  "
                  :icon="ElIconIconfont elIconApplicationEdit icon_click"
                  @click="editRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly ||
                    !$permission('base_role_delete') ||
                    !row.type
                  "
                  :icon="ElIconIconfont elIconApplicationDelete icon_click"
                  @click="delRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="配置权限" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('base_role_auth') || !row.type"
                  :icon="ElIconIconfont elIconApplicationSetting"
                  @click="checkClickRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getRoleList"
    />
    <!-- 新增编辑弹窗 -->
    <add-or-edit
      v-if="dialogVisible"
      ref="editDialog"
      :now-row="nowRow"
      :type="'projectRole'"
      v-model:dialog-visible="dialogVisible"
      :action-type="actionType"
      @success="getRoleList"
    />
    <!-- 设置人员菜单权限 -->
    <menuSetting
      v-if="groupParam.visible"
      v-bind="groupParam"
      v-model="groupParam.visible"
    />

    <!-- 角色详情 -->
    <roleInfoDrawer
      v-if="roleDrawerParam.visible"
      v-bind="roleDrawerParam"
      :type="'projectRole'"
      v-model="roleDrawerParam.visible"
      :table-list="tableList"
    />
  </div>
</template>

<script>
import addOrEdit from '../addOrEdit.vue'
import {
  apiAlmProjectRole,
  apiAlmProjectRoleInfo,
  apiAlmProjectRoleDel,
} from '@/api/vone/project/setting'
import menuSetting from './project-menu-drawer.vue'
import roleInfoDrawer from '../function/role-info-drawer.vue'
import { checkRole } from '@/api/vone/base/role'
export default {
  components: {
    addOrEdit,
    menuSetting,
    roleInfoDrawer,
  },
  props: {
    formData: {
      type: Object,
      default: null,
    },
    extraData: {
      type: Object,
      default: null,
    },
    tableHeight: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      groupParam: { visible: false },
      tableData: {},
      dialogVisible: false, // 新增和编辑弹窗
      userVisible: false, // 分配人员弹窗
      actionType: 'add',
      tableLoading: false,
      nowRow: {},
      roleDrawerParam: { visible: false },
      tableList: [],
    }
  },
  computed: {
    projectTypeName() {
      return function (row) {
        return row.echoMap?.typeCode?.name || ''
      }
    },
  },
  mounted() {},
  methods: {
    // 详情
    showInfo(row) {
      this.roleDrawerParam = { visible: true, id: row.id, row: row }
    },
    // 查询列表
    async getRoleList(ref) {
      let params = {}
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      // const search = this.$refs.searchForm

      params = {
        ...pageObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData, state: true, type: true },
      }
      const res = await apiAlmProjectRole(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableList = res.data.records
    },
    // 修改角色
    editRow(row) {
      // 查询当前行角色的信息
      apiAlmProjectRoleInfo(row.id).then((res) => {
        if (res.isSuccess) {
          this.nowRow = res.data
          this.dialogVisible = true
          this.actionType = 'edit'
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 删除角色
    delRow(row) {
      checkRole(row.id).then((res) => {
        if (res.isSuccess) {
          if (res.data) {
            this.$message.warning('该角色已被使用，不能删除')
          } else {
            this.$confirm(`是否删除角色【${row.name}】`, '删除', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              customClass: 'delConfirm',
              showClose: false,
              type: 'warning',
            }).then(() => {
              apiAlmProjectRoleDel(Array(row.id)).then((res) => {
                if (res.isSuccess) {
                  this.$message.success('删除角色成功')
                  this.getRoleList()
                } else {
                  this.$message.warning(res.msg)
                }
              })
            })
          }
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('baseRoleTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
          type: 'warning',
        })
        this.tableLoading = true
        const selectId = selectData.map((r) => r.id)
        const res = await apiAlmProjectRoleDel(selectId)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getRoleList()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 配置资源
    checkClickRow(row) {
      this.groupParam = {
        visible: true,
        id: row.id,
        title: '配置菜单资源',
        typeCode: row.typeCode,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.switchStyle :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
}

.testTabs {
  width: 100%;
  justify-content: center;
  align-items: center;
  display: flex;
  :deep() {
    .el-radio-button {
      width: 94%;
      display: flex;
      border: 4px solid var(--tab-bg-color);
      background-color: var(--tab-bg-color);
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      color: #4287ff;
      background-color: #fff;
    }
    .el-radio-button__inner {
      color: var(--tab-font-color);
      border: none;
      border-radius: 2px;
      background-color: var(--tab-bg-color);
    }
  }
}
.switchStyle :deep(.el-switch__label--left) {
  z-index: 9;
  left: 18px;
}
.switchStyle :deep(.el-switch__label--right) {
  z-index: 9;
  left: -5px;
}
.switchStyle :deep(.el-switch__label.is-active) {
  display: block;
}
.switchStyle :deep(.el-switch__core) {
  width: 54px !important;
}
.switchStyle :deep(.el-switch__label) {
  width: 54px !important;
}
</style>
