<template>
  <page-wrapper>
    <el-form ref="fileForm" :model="fileForm">
      <el-form-item prop="test">
        <vone-select-tag
          v-model="fileForm.test"
          :option-list="optionList"
          :form-value="fileForm.test"
        />
      </el-form-item>
    </el-form>
  </page-wrapper>
</template>

<script>
export default {
  data() {
    return {
      fileForm: {
        test: '1',
      },
      optionList: [
        {
          id: '1',
          name: '张三',
        },
        {
          id: '2',
          name: '李四',
        },
        {
          id: '3',
          name: '王五',
        },
      ],
    }
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.el-form {
  width: 50%;
}
</style>
