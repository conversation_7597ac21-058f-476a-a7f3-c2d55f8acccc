<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          :model="formData"
          table-search-key="fileManageTable"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              :command="allLoad"
              :disabled="!$permission('base_file_load')"
            >
              批量下载
            </el-dropdown-item>
            <el-dropdown-item
              :command="deleteAll"
              :disabled="!$permission('base_file_del')"
            >
              批量删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="file-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" align="center" fixed="left" />
        <vxe-column title="原始文件名" field="originalFileName">
          <template #default="{ row }">
            <!-- <a v-if="showOffice(row)" @click="getOffice(row)">{{ row.originalFileName }}</a> -->
            <span>{{ row.originalFileName }}</span>
          </template>
        </vxe-column>
        <!-- <vxe-column title="桶" field="bucket" /> -->
        <vxe-column title="业务类型" field="bizType" width="150">
          <template v-slot="{ row }">
            <span v-if="row.bizType">{{ row.bizType.desc }}</span>
            <span v-else>{{ row.bizType }}</span>
          </template>
        </vxe-column>

        <vxe-column title="文件类型" field="fileType" width="120">
          <template v-slot="{ row }">
            <span v-if="row.fileType">{{ row.fileType.desc }}</span>
            <span v-else>{{ row.fileType }}</span>
          </template>
        </vxe-column>
        <vxe-column title="大小" field="size" width="120" />
        <vxe-column title="上传时间" field="createTime" width="180" />
        <vxe-column title="操作" fixed="right" align="left" width="80">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="下载" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('base_file_load')"
                  :icon="ElIconIconfont elIconEditDownload"
                  @click="downLoad(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('base_file_del')"
                  :icon="ElIconIconfont elIconApplicationDelete"
                  @click="del(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />
    <v-only-office
      ref="office"
      v-model="visible"
      :option="option"
      show-del
      @delFile="del"
    />
  </page-wrapper>
</template>

<script>
import VOnlyOffice from '@/components/OnlyOffice'
import {
  apiBaseFileManage,
  apiBaseFileDel,
  apiBaseFileLoadById,
} from '@/api/vone/base/file'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import { download, fileFormatSize, downloadZipFile } from '@/utils'
export default {
  components: {
    VOnlyOffice,
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'fileType',
          name: '文件类型',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择文件类型',
        },
      ],
      formData: {},
      pageLoading: false,
      allLoading: false,
      selecteTableData: [],
      data: {},
      tableOptions: {
        isSelection: true, // 表格有多选时设置
        isOperation: true, // 表格有操作列时设置
        isIndex: false, // 列表序号
        operation: {
          isFixed: true, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '100', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '下载', // 功能名称
              icon: 'el-icon-edit-download', // icon class
              handler: this.downLoad, // 操作事件
              disabled: !this.$permission('base_file_load'),
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.del, // 操作事件
              disabled: !this.$permission('base_file_del'),
            },
          ],
          // 更多操作按钮,如果按钮超过3个，从第三个开始需要添加在moreData中
          moreData: [],
        },
      },
      // 参考vabOnlyOffice组件参数配置
      option: {
        url: '',
        isEdit: '',
        fileType: '',
        title: '',
        lang: '',
        isPrint: '',
        user: { id: null, name: '' },
      },
      visible: false,
      typeList: [],
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  mounted() {
    this.getTableData()
    this.getTypeList()
  },
  methods: {
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },
    async getTypeList() {
      const res = await apiBaseDictEnumList(['FileType'])

      if (!res.isSuccess) {
        return
      }
      res.data.FileType.forEach((element) => {
        element.code = element.value
        element.id = element.value
        element.name = element.label
      })

      this.setData(this.defaultFileds, 'fileType', res.data.FileType)
    },
    async getTableData() {
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      const res = await apiBaseFileManage(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach((element) => {
        element.size = fileFormatSize(element.size)
      })
      this.data = res.data
    },
    async del(row) {
      await this.$confirm(
        `确定删除【${row.originalFileName || row.title}】吗？`,
        '删除',
        {
          type: 'warning',
          closeOnClickModal: false,
          customClass: 'delConfirm',
          showClose: false,
        }
      )
        .then(async (actions) => {
          const res = await apiBaseFileDel([row.id])
          this.pageLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.$message.success('删除成功')
          this.getTableData()
        })
        .catch(() => {})
    },
    // 批量删除
    async deleteAll() {
      const dateList = this.getVxeTableSelectData('file-table')
      if (!dateList.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${dateList.length} 条数据?`, '批量删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false,
        })
        this.pageLoading = true
        const selectId = this.getVxeTableSelectData('file-table').map(
          (r) => r.id
        )
        const res = await apiBaseFileDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    // 单个下载
    async downLoad(row) {
      download(row.originalFileName, await apiBaseFileLoadById([row.id]))
    },
    // 批量下载
    async allLoad() {
      if (!this.getVxeTableSelectData('file-table').length) {
        this.$message.warning('请选择要下载的文件')
        return
      }
      this.allLoading = true
      const fileId = this.getVxeTableSelectData('file-table').map((r) => r.id)
      downloadZipFile(`${Date.now()}.zip`, await apiBaseFileLoadById(fileId))
      this.allLoading = false
    },
    showOffice(row) {
      const type =
        row.originalFileName.split('.')[
          row.originalFileName.split('.').length - 1
        ]
      if (this.$refs.office.getFileType(type) !== '') {
        return true
      }
    },
    async getOffice(row) {
      const type =
        row.originalFileName.split('.')[
          row.originalFileName.split('.').length - 1
        ]
      if (this.$refs.office.getFileType(type) !== '') {
        this.visible = true
        await this.$nextTick(() => {
          this.option = {
            id: row.id,
            title: row.originalFileName,
            fileType: type,
          }
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
// :deep(.el-message-box__status) {
//   top: 25% !important;
// }
</style>
