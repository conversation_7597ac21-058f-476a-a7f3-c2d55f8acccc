<template>
  <page-wrapper>
    <div>
      <el-tabs
        v-model="tabActive"
        style="height: calc(100vh - 100px); margin: -16px -16px 16px -16px"
        class="vone-tab-line"
        @tab-click="tabClick"
      >
        <el-tab-pane
          v-for="item in types"
          :key="item.type"
          :label="item.name"
          :name="item.type"
        />
        <template>
          <component :is="tabActive" v-if="tabActive" />
        </template>
      </el-tabs>
    </div>
  </page-wrapper>
</template>

<script>
const context = import.meta.glob('./message/*.vue')
const allComponents = []
Object.keys(context).forEach((key) => {
  const component = context[key].default
  if (component) {
    allComponents[component.name] = component
  }
})

export default {
  components: {
    ...allComponents,
  },
  data() {
    return {
      types: [
        {
          type: 'engine',
          name: '消息引擎',
        },
        {
          type: 'templete',
          name: '消息模版',
        },
        {
          type: 'reciver',
          name: '接收规则',
        },
        {
          type: 'messageConfig',
          name: '方案配置',
        },
        {
          type: 'publicManagement',
          name: '公告管理',
        },
        {
          type: 'history',
          name: '发送历史',
        },
      ],
      tabActive: 'engine',
    }
  },
  mounted() {},
  methods: {
    tabClick(tab) {
      this.activeName = tab.name
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 16px;
}
</style>
