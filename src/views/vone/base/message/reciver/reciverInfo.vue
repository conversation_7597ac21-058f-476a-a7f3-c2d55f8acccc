<template>
  <vone-drawer
    size="md"
    title="规则详情"
    v-model="visible"
    :before-close="onClose"
    :wrapper-closable="false"
    width="60%"
    :close-on-click-modal="false"
  >
    <vone-desc v-loading="loading" :column="1" class="page">
      <vone-desc-item label="名称">
        {{ addForm.name }}
      </vone-desc-item>

      <vone-desc-item label="业务通知对象">
        {{ addForm.eventNoticyBizs }}
      </vone-desc-item>
      <vone-desc-item label="通用通知角色">
        {{ addForm.eventNotifyRole }}
      </vone-desc-item>
      <vone-desc-item label="通用通知人员">
        <span v-for="(item, index) in addForm.eventNotifyUser" :key="index">
          {{ userMap[item] }}
        </span>
      </vone-desc-item>
      <vone-desc-item label="创建时间">
        {{ addForm.createTime }}
      </vone-desc-item>
      <vone-desc-item label="创建人">
        <span
          v-if="
            addForm.createdBy && addForm.echoMap && addForm.echoMap.createdBy
          "
        >
          <vone-user-avatar
            :avatar-path="addForm.echoMap.createdBy.avatarPath"
            :name="addForm.echoMap.createdBy.name"
          />
        </span>
      </vone-desc-item>

      <vone-desc-item label="描述">
        {{ addForm.description }}
      </vone-desc-item>
    </vone-desc>

    <el-row slot="footer">
      <el-button @click="onClose">取消</el-button>
    </el-row>
  </vone-drawer>
</template>

<script>
import { apiMsgReceiveInfo } from '@/api/vone/base/message'
import { batchQuery } from '@/api/vone/base/role'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'

export default {
  props: {
    id: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      default: null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      addForm: {},
      eventUserList: [],
      roleMap: {},
      userMap: {},
      eventUserMap: {},
    }
  },
  mounted() {
    this.getUserDetail()
  },
  created() {
    this.getOptions()
  },
  methods: {
    getOptions() {
      this.getRoleList()
      this.getEventUser()
      this.getUserList()
    },
    async getUserList() {
      const res = await apiBaseAllUserNoPage()
      if (!res.isSuccess) {
        return
      }

      this.userMap = res.data.reduce((r, v) => (r[v.id] = v.name) && r, {})
    },
    async getRoleList() {
      const res = await batchQuery()
      if (!res.isSuccess) {
        return
      }

      this.roleMap = res.data.reduce((r, v) => (r[v.id] = v.name) && r, {})
    },
    async getEventUser() {
      const res = await apiBaseDictEnumList(['MsgReceiveType'])
      if (!res.isSuccess) {
        return
      }
      this.eventUserList = res.data.MsgReceiveType
      this.eventUserMap = res.data.MsgReceiveType.reduce(
        (r, v) => (r[v.value] = v.label) && r,
        {}
      )
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    async getUserDetail() {
      this.loading = true
      const res = await apiMsgReceiveInfo(this.id)
      this.loading = false

      if (!res.isSuccess) {
        return
      }

      this.addForm = res.data

      const notifyBiz = res.data?.ruleList
        ?.filter((r) => r.ruleType.code == 'BIZ')
        ?.map((j) => j.bizId)
        ?.map((i) => ({
          name: this.eventUserMap[i],
        }))
        ?.map((k) => k.name)
        ?.join(' , ')

      const notifyRole = res.data?.ruleList
        ?.filter((r) => r.ruleType.code == 'ROLE')
        ?.map((j) => j.bizId)
        ?.map((i) => ({
          id: i,
          name: this.roleMap[i],
        }))
        ?.map((k) => k.name)
        ?.join(' , ')

      const notifyUser = res.data?.ruleList
        ?.filter((r) => r.ruleType.code == 'USER')
        .map((r) => r.bizId)

      this.$set(this.addForm, 'eventNoticyBizs', notifyBiz)
      this.$set(this.addForm, 'eventNotifyRole', notifyRole)
      this.$set(this.addForm, 'eventNotifyUser', notifyUser)
    },
  },
}
</script>

<style lang="scss" scoped>
.page {
  padding: 20px;
}
:deep(.vone-el-drawer__layout) {
  padding: 20px;
}
</style>
