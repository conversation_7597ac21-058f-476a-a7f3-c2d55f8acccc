<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseReciverTable"
          :model="formData"
          :table-ref="$refs['baseReciverTable']"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <!-- :disabled="!$permission('base_msg_engine_add')"  -->
        <el-button
          type="primary"
          :icon="el-icon-setting"
          size="small"
          @click="clickAddRow"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><ElIconSetting /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 230px)">
      <vxe-table
        ref="baseReciverTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="规则名称" field="name" min-width="150">
          <template #default="{ row }">
            <a @click="showInfo(row)">{{ row.name }}</a>
          </template>
        </vxe-column>
        <!-- <vxe-column title="适用事件" field="eventType" min-width="150">
          </vxe-column> -->
        <vxe-column title="创建人" field="createdBy" min-width="150">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="getUserInfo(row) ? getUserInfo(row).avatarPath : ''"
              :name="getUserInfo(row) ? getUserInfo(row).name : ''"
            />
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" min-width="150" />
        <!-- <vxe-column title="状态" field="status" min-width="100">
            <template #default="{ row }">
              <el-switch
                :value="row.status"
                :active-value="true"
                :inactive-value="false"
                active-color="#13ce66"
                inactive-color="#B9B9B9"
                @click.native="
                  monitorSwitch( row, row.id)"
              />
            </template>
          </vxe-column> -->
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly"
                  :icon=""el-icon-edit" icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly"
                  :icon=""el-icon-delete" icon_click"
                  @click="deleteRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />
    <!-- 新增编辑 -->
    <reciverAdd
      v-if="reciverParam.visible"
      v-bind="reciverParam"
      v-model="reciverParam.visible"
      @success="getTableData"
    />
    <!-- 详情 -->
    <reciverInfo
      v-if="reciverInfoParam.visible"
      v-bind="reciverInfoParam"
      v-model="reciverInfoParam.visible"
    />
  </div>
</template>

<script>
import { apiMsgReceivePage, apiMsgReceiveHandle } from '@/api/vone/base/message'
import reciverAdd from './reciverAdd.vue'
import reciverInfo from './reciverInfo.vue'

export default {
  name: 'Reciver',
  components: {
    reciverAdd,
    reciverInfo,
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
      ],
      formData: {
        name: '',
        account: '',
        orgId: null,
      },
      pageLoading: false,
      data: {},
      reciverParam: {
        visible: false,
      },
      reciverInfoParam: {
        visible: false,
      },
      actions: [
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete icon_click',
          disabled: !this.$permission('base_msg_templete_del'),
          fn: () => {
            this.deleteRowBatch()
          },
        },
      ],
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.createdBy && row?.echoMap?.createdBy
      }
    },
  },
  mounted() {},
  methods: {
    monitorSwitch() {},
    // 获取表格数据
    getTableData() {
      let params = {}
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      apiMsgReceivePage(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        this.data = res.data
      })
    },
    clickAddRow() {
      this.reciverParam = { visible: true, title: '新增' }
    },
    editClickRow(row) {
      this.reciverParam = { visible: true, title: '编辑', id: row.id }
    },
    deleteRow(row) {
      this.$confirm('是否删除当前数据?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        apiMsgReceiveHandle([row.id], 'DELETE').then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 批量删除
    async deleteRowBatch() {
      const selectData = this.getVxeTableSelectData('baseReciverTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning',
        })
        this.pageLoading = true
        const selectId = selectData.map((r) => r.id)
        const res = await apiMsgReceiveHandle(selectId, 'DELETE')
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    showInfo(row) {
      this.reciverInfoParam = { visible: true, id: row.id }
    },
  },
}
</script>

<style lang="scss" scoped></style>
