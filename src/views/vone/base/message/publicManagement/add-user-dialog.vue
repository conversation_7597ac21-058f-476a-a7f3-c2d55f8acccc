<template>
  <el-dialog
    title="发布设置"
    v-model="visible"
    :width="'500px'"
    :close-on-click-modal="false"
    append-to-body
    :before-close="closePublish"
  >
    <el-form ref="publishForm" :model="publishForm" :rules="rules">
      <el-row>
        <el-col :span="24">
          <el-form-item label="引擎类型" prop="engineType">
            <el-select
              v-model="publishForm.engineType"
              placeholder="请选择引擎类型"
              filterable
            >
              <el-option
                v-for="item in engineTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="消息接收人规则" prop="receiveId">
            <el-select
              v-model="publishForm.receiveId"
              placeholder="请选择消息接收人"
              filterable
            >
              <el-option
                v-for="item in reciveList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer">
      <el-button @click="closePublish">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="confirmPublish"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import { apiMsgReceiveQuery } from '@/api/vone/base/message'

import { editState } from '@/api/vone/base/notice'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => {},
    },
    data: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      publishForm: {},
      rules: {
        engineType: [{ required: true, message: '请选择引擎类型' }],
        receiveId: [{ required: true, message: '请选择消息接收人规则' }],
      },
      saveLoading: false,
      engineTypes: [],
      reciveList: [],
    }
  },
  mounted() {
    this.getEngineTypes()
    this.getReciveData()
  },
  methods: {
    async getEngineTypes() {
      const res = await apiBaseDictEnumList(['MsgEngineType'])
      if (!res.isSuccess) {
        return
      }
      this.engineTypes = res.data.MsgEngineType
    },
    async getReciveData() {
      const res = await apiMsgReceiveQuery()
      if (!res.isSuccess) {
        return
      }
      this.reciveList = res.data
    },
    closePublish() {
      this.orgId = null
      this.filter = ''
      this.$emit('update:visible', false)
    },
    async confirmPublish() {
      try {
        await this.$refs.publishForm.validate()
      } catch (error) {
        return
      }
      this.saveLoading = true
      const res = await editState({
        engineType: this.publishForm.engineType,
        ids: this.type == 'batch' ? this.data.map((r) => r.id) : [this.row.id],
        receiveId: this.publishForm.receiveId,
        state: 1,
      }).catch(() => {
        this.saveLoading = false
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('操作成功')
      this.$emit('success')
      this.closePublish()
    },
  },
}
</script>

<style lang="scss" scoped></style>
