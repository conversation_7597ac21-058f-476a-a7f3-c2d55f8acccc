<template>
  <div>
    <el-dialog
      :title="title"
      width="800px"
      v-model="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="formRule" :disabled="!isEdit">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="索引号" prop="code">
              <el-input v-model="form.code" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主题分类" prop="noticeType">
              <el-select v-model="form.noticeType">
                <el-option
                  v-for="item in notifyTypes"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布日期" prop="updateTime">
              <el-date-picker
                v-model="form.updateTime"
                disabled
                style="width: 100%"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布人" prop="updatedBy">
              <vone-remote-user
                v-model="form.updatedBy"
                disabled
                placeholder="请选择负责人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="liaisoner">
              <vone-remote-user
                v-model="form.liaisoner"
                placeholder="请选择负责人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="公告内容" prop="body">
              <el-input
                v-model="form.body"
                type="textarea"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件" prop="file">
              <vone-upload
                ref="noticeUploadFile"
                :class="['notice-upload', !isEdit ? 'upload-disabled' : '']"
                file-title="上传文件"
                multiple
                drag
                biz-type="NOTICE_FILE_UPLOAD"
                :is-upload="false"
                :file-list="fileList"
                :files-data="fileList"
                @onSuccess="onProgress"
                @remove="handleRemove"
              >
                <div class="el-upload__text">
                  <el-icon><el-icon-upload /></el-icon
                  >将文件拖到此处，或<em>点击上传</em>文件
                </div>
              </vone-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="saveInfo"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Upload as ElIconUpload } from '@element-plus/icons-vue'
import { addNotice, editNotice, getNotice } from '@/api/vone/base/notice'
import storage from 'store'
import { apiBaseDictPage } from '@/api/vone/base/dict'
export default {
  components: {
    ElIconUpload,
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '新增公告',
    },
    clickRow: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      form: {},
      formRule: {
        name: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        code: [{ required: true, message: '请输入索引号', trigger: 'blur' }],
        noticeType: [
          { required: true, message: '请选择主题分类', trigger: 'change' },
        ],
        updateTime: [{ required: true, message: '请选择发布日期' }],
        updatedBy: [{ required: true, message: '请选择发布人' }],
        liaisoner: [{ required: true, message: '请选择联系人' }],
        phone: [
          { required: true, message: '请输入联系电话' },
          {
            pattern: '^1[3,4,5,6,7,8,9][0-9]{9}$',
            message: '请输入正确的手机号',
            trigger: 'change',
          },
        ],
      },
      saveLoading: false,
      notifyTypes: [],
      fileList: [],
    }
  },
  mounted() {
    this.getNotificTypes()
    if (!this.clickRow.id) {
      const loginUser = storage.get('user') || {}
      this.form.updateTime = new Date()
      this.form.updatedBy = loginUser.id
    } else {
      this.getInfo()
    }
  },
  methods: {
    // 查询公告类型
    async getNotificTypes() {
      const res = await apiBaseDictPage({
        size: 999,
        current: 1,
        extra: {},
        model: {
          type: 'NOTICE_TYPE',
        },
      })
      if (res.isSuccess) {
        this.notifyTypes = res.data.records
      }
    },
    getInfo() {
      getNotice(this.clickRow.id).then((res) => {
        if (res.isSuccess) {
          this.form = res.data
          this.fileList = res.data.files
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    onClose() {
      this.$emit('update:visible', false)
      this.form = {}
    },
    onProgress(e) {
      // this.fileStatus = file.status
      this.fileList = e
    },
    // 删除文件
    handleRemove(file) {
      this.fileList = this.fileList.filter((v) => v.uid != file?.raw?.uid)
    },
    async saveInfo() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      this.saveLoading = true
      if (this.clickRow.id) {
        this.form.files = [...this.fileList]
        editNotice(this.form)
          .then((res) => {
            this.saveLoading = false
            if (res.isSuccess) {
              this.$message.success('修改成功')
              this.$emit('success')
              this.onClose()
            } else {
              this.$message.warning(res.msg)
            }
          })
          .catch(() => {
            this.saveLoading = false
          })
      } else {
        this.form.state = 0
        this.form.files = [...this.fileList]
        addNotice(this.form)
          .then((res) => {
            this.saveLoading = false
            if (res.isSuccess) {
              this.$message.success('新增成功')
              this.$emit('success')
              this.onClose()
            } else {
              this.$message.warning(res.msg)
            }
          })
          .catch(() => {
            this.saveLoading = false
          })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.notice-upload {
  .el-icon-upload {
    font-size: 22px;
    margin-right: 4px;
  }
  :deep(.el-upload) {
    width: 100%;
  }
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 70px;
  }
  :deep(.el-upload-dragger .el-icon-upload) {
    margin: 0px 0px;
    line-height: 70px;
  }
  :deep(.el-upload-dragger .el-upload__text) {
    color: var(--font-disabled-color);
    em {
      color: var(--main-theme-color);
    }
  }
}
.upload-disabled {
  :deep(.el-upload) {
    display: none;
  }
  :deep(.el-upload-list) {
    margin-top: 30px;
  }
}
</style>
