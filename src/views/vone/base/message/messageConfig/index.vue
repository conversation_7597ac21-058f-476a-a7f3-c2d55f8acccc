<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseMsgConfigTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['baseMsgConfigTable']"
          @getTableData="getTableData"
        >
          <!-- <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="名称" prop="name">
                  <el-input v-model="formData.name" clearable placeholder="名称" />
                </el-form-item>
              </el-col>
            </el-row> -->
        </vone-search-dynamic>
      </template>
      <template slot="actions">
        <!-- :disabled="!$permission('base_msg_config_add')"  -->
        <el-button
          type="primary"
          :icon="el-icon-setting"
          size="small"
          @click="clickAddRow"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><ElIconSetting /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 230px)">
      <vxe-table
        ref="baseMsgConfigTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="名称" field="name" min-width="250" />
        <vxe-column title="引擎类型" field="msgEngineType" width="100">
          <template #default="{ row }">
            {{ row.msgEngineType.desc }}
          </template>
        </vxe-column>
        <vxe-column title="通知方式" field="scheduled" width="90">
          <template #default="{ row }">
            {{ row.scheduled ? '定时' : '即时' }}
          </template>
        </vxe-column>
        <vxe-column title="消息模板" field="templateId">
          <template #default="{ row }">
            {{ templeteMap[row.templateId] }}
          </template>
        </vxe-column>
        <vxe-column title="通知事件" field="receiveId">
          <template #default="{ row }">
            {{ receiveMap[row.receiveId] }}
          </template>
        </vxe-column>
        <!-- <vxe-column title="创建人" field="url" min-width="150">
            <template #default="{ row }">
              <vone-user-avatar :avatar-path="row.avatarPath" :avatar-type="row.avatarType" :name="row.name" />
            </template>
          </vxe-column> -->

        <vxe-column title="描述" field="description" />
        <vxe-column title="操作" fixed="right" align="left" width="100">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_msg_config_edit')
                  "
                  :icon=""el-icon-edit" icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_msg_config_del')
                  "
                  :icon=""el-icon-delete" icon_click"
                  @click="deleteRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />

    <configAdd
      v-if="configParam.visible"
      v-bind="configParam"
      v-model="configParam.visible"
      @success="getTableData"
    />
  </div>
</template>

<script>
import {
  apiMsgSchemePage,
  apiMsgSchemeHandle,
  apiBaseMsgTemplateQuery,
  apiMsgReceiveQuery,
} from '@/api/vone/base/message'

import configAdd from './configAdd.vue'

export default {
  name: 'MessageConfig',
  components: {
    configAdd,
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
      ],
      formData: {
        name: '',
        account: '',
        orgId: null,
      },
      pageLoading: false,
      data: {
        // records: [
        //   {
        //     id: '1',
        //     name: '方案一',
        //     type: {
        //       code: '11',
        //       desc: '飞书'
        //     },
        //     time: '定时'
        //   }
        // ]
      },
      configParam: {},
      templeteList: [],
      templeteMap: {},
      receiveMap: {},
      actions: [
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete icon_click',
          disabled: !this.$permission('base_msg_config_del'),
          fn: () => {
            this.deleteRowBatch()
          },
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.getTemplateData()
    this.getReciveData()
  },
  methods: {
    async getTemplateData() {
      const res = await apiBaseMsgTemplateQuery()
      if (!res.isSuccess) {
        return
      }
      this.templeteList = res.data
      this.templeteMap = res.data.reduce((r, v) => (r[v.id] = v.name) && r, {})
    },
    async getReciveData() {
      const res = await apiMsgReceiveQuery()
      if (!res.isSuccess) {
        return
      }
      this.reciveList = res.data
      this.receiveMap = res.data.reduce((r, v) => (r[v.id] = v.name) && r, {})
    },
    // 获取表格数据
    getTableData() {
      let params = {}
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      apiMsgSchemePage(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        this.data = res.data
      })
    },
    clickAddRow() {
      this.configParam = {
        visible: true,
        title: '新增',
        templeteList: this.templeteList,
        reciveList: this.reciveList,
      }
    },
    editClickRow(row) {
      this.configParam = {
        visible: true,
        title: '编辑',
        id: row.id,
        templeteList: this.templeteList,
        reciveList: this.reciveList,
      }
    },
    deleteRow(row) {
      this.$confirm('是否删除当前数据?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        apiMsgSchemeHandle([row.id], 'DELETE').then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 批量删除
    async deleteRowBatch() {
      const selectData = this.getVxeTableSelectData('baseMessageTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning',
        })
        this.pageLoading = true
        const selectId = selectData.map((r) => r.id)
        const res = await apiBaseMessageEngineHandle(selectId, 'DELETE')
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
