<template>
  <el-dialog
    size="md"
    :title="title"
    v-model="visible"
    :before-close="onClose"
    :wrapper-closable="false"
    width="50%"
    :close-on-click-modal="false"
  >
    <el-form
      ref="addForm"
      v-loading="loading"
      :model="addForm"
      :rules="rules"
      label-position="top"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="适用引擎类型" prop="typeList">
            <el-select
              v-model="addForm.typeList"
              placeholder="请选择适用引擎类型"
              filterable
              :disabled="id ? true : false"
              multiple
            >
              <el-option
                v-for="item in engineTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="适用事件" prop="eventList">
            <el-select
              v-model="addForm.eventList"
              placeholder="请选择适用事件"
              filterable
              :disabled="id ? true : false"
              multiple
              @change="changeEvent"
            >
              <el-option
                v-for="item in eventList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="showEvent" :span="24">
          <el-form-item prop="event">
            <el-table
              v-if="tableData.length"
              ref="baseMsgTempleteTable"
              class="vone-vxe-table"
              border
              auto-resize
              height="auto"
              :data="tableData"
              row-id="id"
            >
              <el-table-column label="变量名称" prop="name" min-width="150">
                <template #default="{ row }">
                  <strong> ${ {{ row.key }} } </strong>
                </template>
              </el-table-column>

              <el-table-column label="描述" prop="description" min-width="150">
                <template #default="{ row }">
                  {{ row.name }}
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模板标题" prop="title">
            <el-input
              v-model.trim="addForm.title"
              type="textarea"
              :rows="2"
              placeholder="请输入模板标题"
              maxlength="500"
              show-word-limit
              :autosize="{ minRows: 2 }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模板内容" prop="body">
            <el-input
              v-model.trim="addForm.body"
              type="textarea"
              :rows="3"
              placeholder="请输入模板内容"
              maxlength="5000"
              show-word-limit
              :autosize="{ minRows: 3 }"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="模板描述" prop="description">
            <el-input
              v-model.trim="addForm.description"
              type="textarea"
              :rows="2"
              placeholder="请输入模板描述"
              maxlength="500"
              show-word-limit
              :autosize="{ minRows: 2 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="24" class="urlBox">
          <el-col :span="24">
            <el-form-item label="URL" prop="url">
              <el-input v-model="addForm.url" placeholder="POST地址">
                <template slot="prepend">POST</template>
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>

        <el-form-item label="请求头JSON" prop="headers">

          <el-row v-for="(item,index) in jsonList" :key="index" :gutter="12" class="mb-10">
            <el-col :span="4">
              <el-input v-model="item.key" placeholder="请输入KEY" />
            </el-col>
            <el-col :span="15">
              <el-input v-model="item.value" placeholder="请输入VALUE" />
            </el-col>
            <el-col :span="2">
              <el-button icon="el-icon-minus" :disabled="jsonList.length == 1 ? true :false" @click="delRow(index,item)" />
            </el-col>
            <el-col :span="2" style="margin-left:10px">
              <el-button icon="el-icon-plus" @click="addRow(index,item)" />
            </el-col>
          </el-row>
        </el-form-item> -->
    </el-form>
    <el-row slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveUser"
        >确定</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script>
import {
  apiBaseMsgTemplateHandle,
  apiBaseMsgEvent,
  apiBaseMsgTemplateInfo,
  apiMsgEventVariable,
} from '@/api/vone/base/message'

import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import { avatarList } from '@/assets/avatar/avatar'
export default {
  props: {
    id: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      default: null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [
        {
          id: '1',
          name: '${aaa}',
        },
      ],
      showEvent: false,
      avatarList,
      loading: false,
      saveLoading: false,
      save: '保存并添加下一个',
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
          {
            max: 100,
            message: '请输入长度不超过100个字符的名称',
            trigger: 'change',
          },
        ],
        typeList: [
          {
            required: true,
            message: '请选择适用引擎类型',
            trigger: 'change',
            type: 'array',
          },
        ],
        eventList: [
          {
            required: true,
            message: '请选择适用事件',
            trigger: 'change',
            type: 'array',
          },
        ],
        title: [
          {
            required: true,
            message: '请输入模版标题',
            trigger: 'change',
          },
          {
            max: 500,
            message: '请输入长度不超过500个字符的模版标题',
            trigger: 'change',
          },
        ],
        body: [
          {
            required: true,
            message: '请输入模版标题',
            trigger: 'change',
          },
          {
            max: 5000,
            message: '请输入长度不超过5000个字符的模版标题',
            trigger: 'change',
          },
        ],
        description: [
          {
            required: false,
            max: 500,
            message: '请输入长度不超过500个字符的描述',
            trigger: 'change',
          },
        ],
      },
      addForm: {
        name: '',
        state: true,
        description: '',
        typeList: ['FEI_SHU'],
        readonly: false,
      },
      engineTypes: [],
      eventList: [],
    }
  },
  watch: {
    'addForm.event': {
      handler(value) {
        if (!value) {
          this.showEvent = false
          return
        }
        this.showEvent = true
        this.changeEvent(value)
      },
      immediate: true,
    },
  },
  mounted() {
    this.getEngineTypes()
    this.getEventList()
    if (this.id) {
      this.getUserDetail()
    }
    this.$nextTick(() => {
      this.$refs.addForm.clearValidate()
    })
  },
  methods: {
    async changeEvent(val) {
      const res = await apiMsgEventVariable(val)
      if (!res.isSuccess) {
        return
      }
      this.tableData = res.data
    },
    async getEngineTypes() {
      const res = await apiBaseDictEnumList(['MsgEngineType'])
      if (!res.isSuccess) {
        return
      }
      this.engineTypes = res.data.MsgEngineType
    },
    async getEventList() {
      const res = await apiBaseMsgEvent()
      if (!res.isSuccess) {
        return
      }
      this.eventList = res.data
    },

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.addForm.resetFields()
    },

    async getUserDetail() {
      this.loading = true
      const res = await apiBaseMsgTemplateInfo(this.id)
      this.loading = false

      if (!res.isSuccess) {
        return
      }

      this.$set(
        res.data,
        'typeList',
        res.data.typeList.map((r) => r.code)
      )

      this.$set(
        res.data,
        'eventList',
        res.data.eventList.map((r) => r.id)
      )

      this.addForm = res.data
    },

    async saveUser() {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }
      try {
        this.saveLoading = true

        const list = this.addForm.eventList.map((r) => ({
          id: r,
        }))
        this.$set(this.addForm, 'eventList', list)

        const res = await apiBaseMsgTemplateHandle(
          this.addForm,
          this.id ? 'put' : 'post'
        )

        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.saveLoading = false
        this.$message.success(this.id ? '修改成功' : '保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
        return
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.urlBox {
  :deep(.el-input-group__prepend) {
    background-color: #49cc90;
    color: var(--main-bg-color);
  }
}
.mb-10 {
  margin-bottom: 10px;
}
:deep(.el-dialog__body) {
  max-height: 550px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
