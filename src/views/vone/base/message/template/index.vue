<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseMsgTempleteTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['baseMsgTempleteTable']"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <!-- :disabled="!$permission('base_msg_templete_add')"  -->
        <el-button
          type="primary"
          :icon="ElIconIconfont elIconTipsPlusCircle"
          size="small"
          @click="clickAddRow"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 230px)">
      <vxe-table
        ref="baseMsgTempleteTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="模板名称" field="name" min-width="150" />
        <vxe-column title="模板标题" field="title" />
        <vxe-column title="引擎类型" field="type" width="90">
          <template #default="{ row }">
            {{ row.allType }}
          </template>
        </vxe-column>

        <vxe-column title="模板内容" field="body" />

        <vxe-column title="创建人" field="createdBy" width="100">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="getUserInfo(row) ? getUserInfo(row).avatarPath : ''"
              :name="getUserInfo(row) ? getUserInfo(row).name : ''"
            />
          </template>
        </vxe-column>

        <vxe-column title="创建时间" field="createTime" width="170" />
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_msg_templete_edit')
                  "
                  :icon="ElIconIconfont elIconApplicationEdit icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_msg_templete_del')
                  "
                  :icon="ElIconIconfont elIconApplicationDelete icon_click"
                  @click="deleteRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />

    <templeteAdd
      v-if="templeteParam.visible"
      v-bind="templeteParam"
      v-model="templeteParam.visible"
      @success="getTableData"
    />
  </div>
</template>

<script>
import {
  apiBaseMsgTemplatePage,
  apiBaseMsgTemplateHandle,
} from '@/api/vone/base/message'
import templeteAdd from './templeteAdd.vue'

export default {
  name: 'Templete',
  components: {
    templeteAdd,
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
      ],
      formData: {
        name: '',
      },
      pageLoading: false,
      data: {},
      actions: [
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete icon_click',
          disabled: !this.$permission('base_msg_templete_del'),
          fn: () => {
            this.deleteRowBatch()
          },
        },
      ],
      templeteParam: { visible: false },
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.createdBy && row?.echoMap?.createdBy
      }
    },
  },
  mounted() {},
  methods: {
    // 获取表格数据
    getTableData() {
      let params = {}
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      apiBaseMsgTemplatePage(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        res.data.records.forEach((element) => {
          element.allType =
            element.typeList && element.typeList.length
              ? element.typeList.map((r) => r.desc).join('、')
              : ''
        })

        this.data = res.data
      })
    },
    clickAddRow() {
      this.templeteParam = { visible: true, title: '新增' }
    },
    editClickRow(row) {
      this.templeteParam = { visible: true, title: '编辑', id: row.id }
    },
    deleteRow(row) {
      this.$confirm('是否删除当前数据?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        apiBaseMsgTemplateHandle([row.id], 'DELETE').then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 批量删除
    async deleteRowBatch() {
      const selectData = this.getVxeTableSelectData('baseMsgTempleteTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning',
        })
        this.pageLoading = true
        const selectId = selectData.map((r) => r.id)
        const res = await apiBaseMsgTemplateHandle(selectId, 'DELETE')
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
