<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseMessageTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['baseMessageTable']"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <!-- :disabled="!$permission('base_msg_engine_add')"  -->
        <el-button
          type="primary"
          :icon="el-icon-setting"
          size="small"
          @click="clickAddRow"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><ElIconSetting /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 230px)">
      <vxe-table
        ref="baseMessageTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="引擎名称" field="name" min-width="150" />
        <vxe-column title="引擎类型" field="type" width="100">
          <template #default="{ row }">
            <span v-if="row.type">
              {{ row.type.desc }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="连接状态" field="state" width="100">
          <template #default="{ row }">
            <el-button v-if="row.state" type="text" class="safe">
              <el-icon><ElIconSetting /></el-icon>正常
            </el-button>
            <el-button v-else class="danger" type="text">
              <el-icon><ElIconSetting /></el-icon>异常
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="启用状态" field="status" width="100">
          <template #default="{ row }">
            <el-switch
              :model-value="row.status"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              inactive-color="#B9B9B9"
              @click.native="monitorSwitch(row, row.id)"
            />
          </template>
        </vxe-column>

        <vxe-column title="创建人" field="createdBy" width="130">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="getUserInfo(row) ? getUserInfo(row).avatarPath : ''"
              :name="getUserInfo(row) ? getUserInfo(row).name : ''"
            />
          </template>
        </vxe-column>
        <vxe-column
          title="引擎维护人"
          field="engineMaintainers"
          min-width="150"
        >
          <template #default="{ row }">
            <span v-if="row.echoMap.maintainers.length > 1">
              <el-tooltip
                :content="row.echoMap.maintainers.map((r) => r.name).join('、')"
                placement="top"
              >
                <div>
                  {{ row.echoMap.maintainers.map((r) => r.name).join('、') }}
                </div>
              </el-tooltip>
            </span>
            <span v-else-if="row.echoMap.maintainers.length">
              <span v-for="item in row.echoMap.maintainers" :key="item.id">
                <span>
                  {{ row.echoMap.maintainers.map((r) => r.name).join('、') }}
                </span>
              </span>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="150" />
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_msg_engine_edit')
                  "
                  :icon=""el-icon-edit" icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('base_msg_engine_del')
                  "
                  :icon=""el-icon-delete" icon_click"
                  @click="deleteRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />

    <engineAdd
      v-if="engineParam.visible"
      v-bind="engineParam"
      v-model="engineParam.visible"
      :engine-types="engineTypes"
      @success="getTableData"
    />
  </div>
</template>

<script>
import {
  apiBaseMessageEngine,
  apiBaseMessageEngineHandle,
  apiBaseMsgUpdateStatus,
} from '@/api/vone/base/message'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import engineAdd from './engineAdd.vue'

export default {
  name: 'Engine',
  components: {
    engineAdd,
  },
  data() {
    return {
      formData: {
        name: '',
      },
      pageLoading: false,
      data: {},
      actions: [
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete icon_click',
          disabled: !this.$permission('base_msg_engine_del'),
          fn: () => {
            this.deleteRowBatch()
          },
        },
      ],
      engineParam: { visible: false },
      engineTypes: [],
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
        {
          key: 'state',
          name: '状态',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择状态',
          optionList: [
            {
              code: true,
              name: '正常',
              id: '1',
            },
            {
              code: false,
              name: '异常',
              id: '2',
            },
          ],
        },
        {
          key: 'type',
          name: '引擎类型',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择引擎类型',
        },
      ],
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.createdBy && row?.echoMap?.createdBy
      }
    },
  },
  mounted() {
    this.getEngineTypes()
  },
  methods: {
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },
    async getEngineTypes() {
      const res = await apiBaseDictEnumList(['MsgEngineType'])
      if (!res.isSuccess) {
        return
      }
      this.engineTypes = res.data.MsgEngineType

      res.data.MsgEngineType.forEach((element) => {
        element.code = element.value
        element.name = element.label
        element.id = element.value
      })
      this.setData(this.defaultFileds, 'type', res.data.MsgEngineType)
    },
    // 获取表格数据
    getTableData() {
      let params = {}
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      apiBaseMessageEngine(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        this.data = res.data
      })
    },
    clickAddRow() {
      this.engineParam = { visible: true, title: '新增' }
    },
    editClickRow(row) {
      this.engineParam = { visible: true, title: '编辑', id: row.id }
    },
    deleteRow(row) {
      this.$confirm('是否删除当前数据?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        apiBaseMessageEngineHandle([row.id], 'DELETE').then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 批量删除
    async deleteRowBatch() {
      const selectData = this.getVxeTableSelectData('baseMessageTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning',
        })
        this.pageLoading = true
        const selectId = selectData.map((r) => r.id)
        const res = await apiBaseMessageEngineHandle(selectId, 'DELETE')
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    async monitorSwitch(row, id) {
      // if (!this.$permission('pipeline_releaseWindow_update')) return
      const { isSuccess, msg } = await apiBaseMsgUpdateStatus({
        status: !row.status,
        ids: [id],
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }

      this.$message.success('操作成功')
      this.getTableData()
    },
  },
}
</script>

<style lang="scss" scoped>
.danger {
  color: red !important;
}
.safe {
  color: #67c23a !important;
}
</style>
