<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseMsgHistoryTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['baseMsgHistoryTable']"
          @getTableData="getTableData"
        />
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 230px)">
      <vxe-table
        ref="baseMsgHistoryTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="名称" field="title" min-width="150" />
        <vxe-column title="引擎类型" field="msgEngineType" min-width="150">
          <template #default="{ row }">
            <span v-if="row.msgEngineType">
              {{ row.msgEngineType.desc }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="类型" field="msgEventType" min-width="150">
          <template #default="{ row }">
            <span v-if="row.msgEventType">
              {{ row.msgEventType.desc }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="发送结果" field="result" min-width="150">
          <template #default="{ row }">
            <el-button v-if="row.result" class="danger" type="text">
              <el-icon><el-icon-warning /></el-icon> 失败
            </el-button>
            <el-button v-else type="text" class="safe">
              <el-icon><el-icon-success /></el-icon> 成功
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="发送人" field="createdBy" min-width="150">
          <template #default="{ row }">
            <span v-if="row.createdBy && row.echoMap && row.echoMap.createdBy">
              <vone-user-avatar
                :avatar-path="row.echoMap.createdBy.avatarPath"
                :avatar-type="row.echoMap.createdBy.avatarType"
                :name="row.echoMap.createdBy.name"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="发送时间" field="createTime" min-width="150" />
        <!-- <vxe-column title="描述" field="desc" min-width="150" /> -->
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />
  </div>
</template>

<script>
import {
  Warning as ElIconWarning,
  Success as ElIconSuccess,
} from '@element-plus/icons'
import { apiMsgHistoryPage } from '@/api/vone/base/message'

export default {
  components: {
    ElIconWarning,
    ElIconSuccess,
  },
  name: 'History',
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
      ],
      formData: {
        name: '',
        account: '',
        orgId: null,
      },
      id: '',
      title: '',
      exportLoading: false,
      pageLoading: false,

      data: {
        records: [
          {
            id: '1',
            name: '1212',
          },
        ],
      },
      addVisible: false,
      editVisible: false, // 编辑弹窗显示
      passWordDialogParam: { visible: false },
      userDrawerParam: { visible: false },
      addParam: { visible: false },
      importParam: { visible: false }, // 用户导入
      editRow: {}, // 选中的行
      orgData: [],
    }
  },
  computed: {
    userTypeName() {
      return function (row) {
        return row.echoMap?.type?.name || ''
      }
    },
  },
  mounted() {},
  methods: {
    // 获取表格数据
    getTableData() {
      let params = {}
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      apiMsgHistoryPage(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        this.data = res.data
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.danger {
  color: red !important;
}
.safe {
  color: #67c23a !important;
}
</style>
