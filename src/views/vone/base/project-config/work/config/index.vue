<template>
  <page-wrapper>
    <el-row type="flex" class="rowLine">
      <el-col :span="12" class="header">
        <span class="back" @click="back">
          <el-icon class="iconfont"><el-icon-direction-back /></el-icon
        ></span>
        <strong>
          {{
            this.$route.query.type == 'details'
              ? `查看【${this.$route.query.name}】`
              : `配置【${this.$route.query.name}】`
          }}</strong
        >
      </el-col>
      <el-col :span="12" style="text-align: right">
        <el-button
          v-if="this.$route.query.type != 'details'"
          size="small"
          type="primary"
          :icon="ElIconSave"
          :loading="saveLoading"
          @click="apiAlmWorkflowAdd"
          >保存</el-button
        >
      </el-col>
    </el-row>

    <vone-work-flow
      ref="vone-g"
      :xml="xml"
      hide-text-annotation
      :palette-data="nodeTypeList"
      single
      :node-style="nodeStyle"
      :properties-props="{ width: 250 }"
      :preview="this.$route.query.type == 'details'"
      @selectLine="selectLine"
    >
      <!-- <div slot="toolbar-actions">
          <el-button v-if="this.$route.params.type != 'details'" size="small" type="primary" icon="save" @click="apiAlmWorkflowAdd">保存</el-button>

        </div> -->
      <div slot="properties" slot-scope="{ element }">
        <el-form-item label="进度">
          <el-input-number
            :model-value="element.data.progress"
            :step="5"
            :min="0"
            :max="100"
            :disabled="$route.query.type == 'details'"
            controls-position="right"
            style="width: 180px"
            @input="(v) => progressChange(element, v)"
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            key="nodeType"
            v-model="element.data.nodeType"
            style="width: 180px"
            clearable
            placeholder="请选择类型"
            :disabled="$route.query.type == 'details'"
            @change="(v) => typeChange(element, v)"
          >
            <el-option label="开始节点" value="START_NODE" />
            <el-option label="中间节点" value="INTERMEDIATE_NODE" />
            <el-option label="结束节点" value="END_NODE" />
          </el-select>
        </el-form-item>
      </div>
      <div slot="connect" slot-scope="{ element }">
        <el-form-item label="权限">
          <el-select
            key="permission"
            v-model="element.data.permission"
            style="width: 180px"
            multiple
            placeholder="请选择权限"
            clearable
            :disabled="$route.query.type == 'details'"
            @change="(v) => userChange(element, v)"
          >
            <el-option label="处理人" value="HANDLER" />
            <el-option label="负责人" value="RESPONSIBLE" />
            <el-option label="创建人/提出人" value="CREATOR" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            key="role"
            v-model="element.data.role"
            clearable
            style="width: 180px"
            multiple
            placeholder="请选择角色"
            :disabled="$route.query.type == 'details'"
            @change="(v) => roleChange(element, v)"
          >
            <el-option
              v-for="item in roles"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
      </div>
    </vone-work-flow>
  </page-wrapper>
</template>

<script>
import {
  DirectionBack as ElIconDirectionBack,
  Save as ElIconSave,
} from '@element-plus/icons'
import {
  apiAlmStateNoPage,
  apiAlmWorkflowAdd,
  getFlow,
  apiAlmStageNoPage,
} from '@/api/vone/base/work-flow'
import { queryProjectRole } from '@/api/vone/base/role'
import _ from 'lodash'
import { jsonToXml } from './xmlUtils'
export default {
  data() {
    return {
      nodeTypeList: [],
      nodeAppConfig: {
        progress: 0,
        nodeType: '',
      },
      edgeAppConfig: {
        permission: [],
      },
      graphData: {
        nodes: [],
        edges: [],
      },
      workflowId: '',
      nodeStyle: {
        width: 100,
        height: 36,
      },
      xml: undefined,
      paletteData: undefined,
      saveLoading: false,
      roles: [],
      ElIconSave,
    }
  },
  components: {
    ElIconDirectionBack,
  },
  mounted() {
    this.getFlow()
    this.getRoles()
    this.getNodeTypeList()
  },
  methods: {
    // 查询角色
    getRoles() {
      queryProjectRole({ state: true, type: true }).then((res) => {
        for (const item of res.data) {
          item.label = item.name + `(${item.echoMap?.typeCode?.name})`
        }
        this.roles = res.data
      })
    },
    //  默认为处理人和负责人
    selectLine(val, ele) {
      if (typeof val.permission == 'object' || typeof val.role == 'object')
        return
      const permission = val.permission
        ? val.permission.split(',')
        : ['HANDLER', 'RESPONSIBLE', 'CREATOR']
      const role =
        val.role == '' || !val?.role?.length ? [] : val.role.split(',')
      this.$set(val, 'permission', permission)
      this.$set(val, 'role', role)
      ele.el.businessObject.set('permission', permission)
      ele.el.businessObject.set('role', role)
    },
    progressChange(element, v) {
      if (v) {
        element.el.businessObject.set('progress', v)
      } else {
        if (element.data.stageCode == 'IN_PROGRESS') {
          element.el.businessObject.set('progress', 50)
        } else if (element.data.stageCode == 'TODO_STAGE') {
          element.el.businessObject.set('progress', 0)
        } else if (element.data.stageCode == 'HANG_UP_STAGE') {
          element.el.businessObject.set('progress', 100)
        } else if (element.data.stageCode == 'COMPLETE_STAGE') {
          element.el.businessObject.set('progress', 100)
        }
      }
    },
    typeChange(element, v) {
      element.el.businessObject.set('nodeType', v)
    },
    userChange(element, v) {
      element.el.businessObject.set('permission', v)
    },
    roleChange(element, v) {
      element.el.businessObject.set('role', v)
    },
    async getFlow() {
      let nodes = []
      let edges = []
      await getFlow(this.$route.params.id).then((res) => {
        if (res.isSuccess) {
          if (
            res.data.workflowNodes.length > 0 &&
            res.data.workflowTransitions.length > 0
          ) {
            this.workflowId = res.data.workflowNodes[0].workflowId
            nodes = _.cloneDeep(res.data.workflowNodes)
            edges = _.cloneDeep(res.data.workflowTransitions)
            nodes.map((item) => {
              item.nodeType = item.nodeType.code
              item.color = item.echoMap?.stateCode?.color
              item.onlyId = item.id
              item.id = item.code
              delete item.shape
              delete item.size
              delete item.echoMap
            })
            edges.map((item) => {
              item.onlyId = item.id
              item.id = item.code
              item.source = item.sourceAnchor
              item.target = item.targetAnchor
              const permissions = []
              const roles = []
              res.data.workflowAuthorities.map((itm) => {
                // if (itm.transitionId == item.onlyId) {
                //   permissions.push(itm.type.code)
                // }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'RESPONSIBLE'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'HANDLER'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'CREATOR'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'ROLE'
                ) {
                  roles.push(itm.userOrRole)
                }
              })
              item.permission = permissions
              item.role = roles
            })
            this.xml = jsonToXml({ nodes, edges })
          }
        }
      })
    },
    async getNodeTypeList() {
      // 获取所有工作流阶段
      const res1 = await apiAlmStageNoPage()
      if (!res1.isSuccess) {
        return
      }
      // 获取所有工作流状态
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }

      // 取出所有状态包含的阶段
      const allHasStage = res.data.map((r) => r.stageCode)
      // 取出平台所有阶段
      const allStage = res1.data.map((r) => r.code)
      // 对比数据是否一致，如果存在平台上有的阶段，但是没有挂上状态的，拼接在一起
      const diffCode = _.difference(allStage, allHasStage)
      const hJson = res1.data.filter((i, j) => diffCode.indexOf(i.code) !== -1)
      const data = hJson.map((item) => {
        return {
          // name: '',
          // key: '',
          group: item.name,
        }
      })

      this.nodeTypeList = res.data
        .map((item) => {
          return {
            name: item.name,
            key: item.code,
            group: item.echoMap?.stageCode?.name,
            data: {
              stateCode: item.code,
              color: item.color,
              stageCode: item.echoMap?.stageCode?.code,
            },
            di: {
              stroke: item.color,
            },
          }
        })
        .concat(data)
    },
    apiAlmWorkflowAdd(data) {
      const { nodes, edges } = this.$refs['vone-g'].getJSON()
      const workflowNodes = []
      const workflowAuthorities = []
      const workflowTransitions = []

      let editIds = []
      // 提示是否将参数配置完整

      const noProgress = nodes.filter(
        (item) => !item.data.progress && item.data.progress != 0
      )
      const noNodetype = nodes.filter((item) => !item.data.nodeType)
      edges.forEach((item) => {
        if (!item.data.permission || item.data.permission.length == 0) {
          item.data.permission = ['HANDLER', 'RESPONSIBLE']
        }
      })
      if (noProgress.length > 0) {
        this.$message.warning(`请配置${noProgress[0].name}节点的进度`)
        return
      }
      if (noNodetype.length > 0) {
        this.$message.warning(`请配置${noNodetype[0].name}节点的类型`)
        return
      }
      const noPermission = edges.filter(
        (item) => !item.data.permission || item.data.permission.length == 0
      )

      if (noPermission.length > 0) {
        this.$message.warning(`请配置线的流转权限`)
        return
      }
      const startType = nodes.filter(
        (item) => item.data.nodeType == 'START_NODE'
      )
      const endType = nodes.filter((item) => item.data.nodeType == 'END_NODE')
      if (startType.length > 1) {
        this.$message.warning(`开始节点类型只能存在一个`)
        return
      }
      if (endType.length > 1) {
        this.$message.warning(`结束节点类型只能存在一个`)
        return
      }
      // 判断开始结束类型是否重复
      if (nodes.length > 0) {
        nodes.map((item) => {
          // 临时方案 将bpmn唯一id存入description中 因为bpmn 不能使用纯数字id，为非法
          workflowNodes.push({
            id: item.data.onlyId,
            code: item.id,
            name: item.name,
            nodeType: item.data.nodeType ? item.data.nodeType : '',
            progress: item.data.progress ? item.data.progress : 0,
            shape: item.type,
            size: '',
            description: '',
            stageCode: item.data.stageCode,
            stateCode: item.data.stateCode,
            workflowId: this.workflowId,
            x: item.x,
            y: item.y,
          })
        })
      }
      if (edges.length > 0) {
        edges.map((item) => {
          const source = workflowNodes.find((itm) => itm.code == item.source).id
          const target = workflowNodes.find((itm) => itm.code == item.target).id
          editIds.push(source)
          editIds.push(target)
          // 临时方案 将bpmn唯一id存入code中 因为bpmn 不能使用纯数字id，为非法,sourceAnchor存储source ...
          workflowTransitions.push({
            id: item.data.onlyId,
            code: item.id,
            source: source,
            name: item.name || '',
            sourceAnchor: item.source,
            target: target,
            targetAnchor: item.target,
            workflowId: this.workflowId,
            waypoints: JSON.stringify(item.waypoints),
          })
          if (item.data.permission && item.data.permission.length > 0) {
            item.data.permission.map((auth) => {
              workflowAuthorities.push({
                transitionId: item.data.onlyId,
                type: auth,
                userOrRole: '',
                workflowId: this.workflowId,
              })
            })
          }
          if (item?.data?.role?.length) {
            item.data.role.map((role) => {
              workflowAuthorities.push({
                transitionId: item.data.onlyId,
                type: 'ROLE',
                userOrRole: role,
                workflowId: this.workflowId,
              })
            })
          }
        })
      }
      let flag = false // 判断工作流配置是否完整
      editIds = Array.from(new Set(editIds))
      for (let i = 0; i < workflowNodes.length; i++) {
        if (editIds.indexOf(workflowNodes[i].id) == -1) {
          flag = false
          break
        } else {
          flag = true
        }
      }
      if (!flag) {
        this.$message.warning('请配置完整的任务流')
        return
      }
      this.saveLoading = true
      apiAlmWorkflowAdd({
        workflowTransitions,
        workflowAuthorities,
        workflowNodes,
        name: this.$route.query.name,
        id: this.$route.params.id,
      }).then((res) => {
        if (res.isSuccess) {
          this.saveLoading = false
          this.$message.success('保存成功')
          this.back()
        } else {
          this.saveLoading = false
          this.$message.success(res.msg)
        }
      })
    },
    back() {
      this.$router.push({
        path: '/base/setting/view',
        query: {
          activeMenu: 'flow',
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.rowLine {
  border-bottom: 1px solid var(--el-divider);
  margin: 0 -16px 16px;
  padding: 0 16px 16px;
}
.header {
  display: flex;
  align-items: center;
  gap: 0 18px;
  .back {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 30px;
    height: 30px;
    color: var(--main-theme-color);
    box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
    border-radius: 16px;
  }
}
</style>
