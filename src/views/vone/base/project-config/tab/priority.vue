<template>
  <div>
    <vone-search-wrapper style="margin-bottom: 16px">
      <template slot="custom">
        <span class="strong">优先级</span>
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          :icon="ElIconIconfont elIconTipsPlusCircle"
          :disabled="!$permission('work_priority_add')"
          @click="addPriority"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="priority-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ checkMethod: selectable }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" align="center" fixed="left" />
        <vxe-column title="名称" field="name" fixed="left" />
        <vxe-column title="图标" field="icon">
          <template #default="scope">
            <span v-if="scope.row.icon && scope.row.color">
              <i
                :class="`iconfont ${scope.row.icon}`"
                :style="{
                  color: `${scope.row.color ? scope.row.color : '#ccc'}`,
                }"
              />
            </span>
            <span v-else>
              {{ scope.row.icon }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="编码" field="code" />
        <vxe-column title="描述" field="description" />
        <vxe-column title="操作" fixed="right" align="left" width="80">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly || !$permission('work_priority_edit')"
                  :icon="ElIconIconfont elIconApplicationEdit icon_click"
                  @click="editRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="
                    row.readonly || !$permission('work_priority_delete')
                  "
                  :icon="ElIconIconfont elIconApplicationDelete icon_click"
                  @click="delRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTableData"
    />
    <!-- 新增优先级 -->
    <el-dialog
      :title="title"
      width="30%"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
    >
      <el-form ref="priorityForm" :rules="rules" :model="priorityForm">
        <el-form-item label="名称" prop="name">
          <el-input v-model="priorityForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="编号" prop="code">
          <el-input
            v-model="priorityForm.code"
            placeholder="请输入编号"
            :disabled="title == '编辑优先级'"
          />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="priorityForm.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-input
            v-model="priorityForm.color"
            type="color"
            placeholder="请选择颜色"
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="priorityForm.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="sureAdd"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiAlmPriorityAdd,
  apiAlmPriorityPage,
  getAlmGetPriorityId,
} from '@/api/vone/alm/index'
import { apiAlmPriorityDel } from '@/api/vone/base/work-flow'

export default {
  props: {
    id: {
      type: String,
      default: undefined,
    },
    typeCode: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change',
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
            message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称',
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change',
          },
        ],
        code: [
          {
            required: true,
            message: '请输入长度不超过20个字符的标识',
            trigger: 'change',
          },
          {
            pattern: '^\\w{1,20}$',
            message: '请输入不超过20位由字母、数字或者下划线组成的标识',
          },
        ],
        icon: [
          {
            max: 64,
            message: '图标最大长度不能超过64个字符',
            trigger: 'change',
          },
        ],
        color: [
          {
            required: true,
            message: '请选择颜色',
            trigger: 'blur',
          },
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change',
          },
        ],
      },
      tableLoading: false,
      tableOptions: {
        isOperation: true, // 表格有操作列时设置
        operation: {
          isFixed: true, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.editRow, // 操作事件
              disabled: this.getStatus,
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.delRow, // 操作事件
              disabled: this.getDel,
            },
          ],
          // 更多操作按钮,如果按钮超过3个，从第三个开始需要添加在moreData中
          moreData: [],
        },
      },
      title: '',
      tableData: {},
      selecteTableData: [],
      dialogFormVisible: false,
      priorityForm: {
        code: '',
        color: '#000',
        description: '',
        icon: 'el-icon-yibiaoban-shuxingyanse',
        typeCode: this.typeCode,
        readonly: false,
      },
      saveLoading: false,
      formData: {
        typeCode: this.typeCode,
      },
      actions: [
        {
          name: '批量删除',
          disabled: !this.$permission('work_priority_delete'),
          fn: this.deleteAll,
        },
      ],
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  watch: {
    typeCode: {
      handler(v) {
        if (v) {
          this.getTableData()
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    selectable({ row }) {
      if (row.readonly) {
        return false
      } else {
        return true
      }
    },
    onClose() {
      this.$refs.priorityForm.resetFields()
      this.dialogFormVisible = false
    },

    getStatus(index, row) {
      return (
        this.tableData.records[index].readonly ||
        !this.$permission('work_priority_edit')
      )
    },
    getDel(index, row) {
      return (
        this.tableData.records[index].readonly ||
        !this.$permission('work_priority_delete')
      )
    },
    async getTableData() {
      // this.$set(this.formData, 'typeCode', this.typeCode)
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      }
      const res = await apiAlmPriorityPage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    addPriority() {
      this.title = '新增优先级'
      this.dialogFormVisible = true
    },
    editRow(row) {
      this.title = '编辑优先级'
      this.getInfo(row)
      this.dialogFormVisible = true
    },
    async getInfo(row) {
      this.formLoading = true
      const res = await getAlmGetPriorityId(row.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.priorityForm = res.data
    },
    async sureAdd() {
      try {
        await this.$refs.priorityForm.validate()
      } catch (e) {
        return
      }

      try {
        this.$set(this.priorityForm, 'typeCode', this.typeCode)
        this.saveLoading = true
        const res = await apiAlmPriorityAdd(this.priorityForm)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.onClose()
        this.getTableData()
      } catch (e) {
        this.saveLoading = false
      }
    },

    delRow(item) {
      this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false,
      }).then(async () => {
        const res = await apiAlmPriorityDel([item.id])
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('删除成功')
        this.getTableData()
      })
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('priority-table')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(
          `是否删除 ${selectData.length} 条数据?`,
          '批量删除',
          {
            type: 'warning',
            customClass: 'delConfirm',
            showClose: false,
          }
        )
        this.pageLoading = true
        const selectId = this.getVxeTableSelectData('priority-table').map(
          (r) => r.id
        )
        const res = await apiAlmPriorityDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-button--default) {
  border: 1px solid #ced1d9;
  color: #6b7385;
}
.strong {
  font-size: 16px;
  font-weight: 500;
  color: #2c2e36;
}
</style>
