<template>
  <el-row :gutter="12">
    <el-col :span="6">
      <div class="leftCard">
        <div v-loading="pageLoading">
          <template v-if="cardData.length">
            <a
              v-for="item in cardData"
              :key="item.value"
              class="cardBox"
              @click="toDict(item)"
            >
              <el-card
                shadow="hover"
                :class="{ dict_active: flagN === item.value }"
                style="margin-bottom: 10px"
              >
                <el-row :gutter="20">
                  <el-col :span="4">
                    <el-icon class="iconBox"><ElIconSetting /></el-icon>
                  </el-col>
                  <el-col :span="20">
                    <div class="labelBox">
                      {{ item.label }}
                      <!-- <el-button type="text" icon="iconfont el-icon-application-delete" /> -->
                      <!-- <a class="delButton" @click="delItem(item)"> <i class="iconfont el-icon-application-delete" /></a> -->
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </a>
          </template>
          <vone-empty v-else />
        </div>
      </div>
    </el-col>
    <el-col :span="18">
      <source-table
        v-if="typeClassify"
        :id="id"
        :type-classify="typeClassify"
      />
      <!-- <right-side :id="id" :type-classify="typeClassify" class="rightTable" /> -->
    </el-col>
  </el-row>
</template>

<script>
import { SData as ElIconSData } from '@element-plus/icons-vue'

import { apiAlmTypeAdd, apiAlmTypeDel } from '@/api/vone/alm/index'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import sourceTable from './common/source-table.vue'
export default {
  components: {
    sourceTable,
    ElIconSData,
  },
  props: {
    id: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      cardData: [],
      pageLoading: false,
      dialogFormVisible: false,
      typeForm: {},
      types: [],
      saveLoading: false,
      flagN: undefined,
      typeClassify: undefined,
    }
  },
  mounted() {
    this.getTypeList()
  },
  methods: {
    toDict(row) {
      this.flagN = row.value
      this.typeClassify = row.value
    },
    async getTypeList() {
      this.pageLoading = true
      const res = await apiBaseDictEnumList(['TypeClassify'])
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.cardData = res.data.TypeClassify
      this.flagN = res.data.TypeClassify[0].value
      this.typeClassify = res.data.TypeClassify[0].value
    },
    addType() {
      this.dialogFormVisible = true
      this.getDIcPage()
    },
    async getDIcPage() {
      this.pageLoading = true
      const res = await apiBaseDictEnumList(['TypeClassify'])
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.types = res.data.TypeClassify
      this.$set(this.typeForm, 'classify', this.id)
    },
    async sureAdd() {
      this.saveLoading = true
      const res = await apiAlmTypeAdd(this.typeForm)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('新增成功')
      this.dialogFormVisible = false
      this.getTypeList()
    },
    async delItem(item) {
      await this.$confirm(`确定删除${item.name}吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false,
      })
      const res = await apiAlmTypeDel([item.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getTypeList()
    },
  },
}
</script>

<style lang="scss" scoped>
.leftCard {
  height: calc(100vh - 200px);
  overflow-y: auto;
}
.btn_active {
  border-color: var(--main-theme-color, #3e7bfa);
  .labelBox a > i {
    display: block;
  }
}
.rightTable {
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
  padding: 0 10px;
}
.delButton {
  float: right;
}

.cardBox .labelBox i {
  display: none;
}

//  .cardBox  .labelBox:hover a>i{
//   display: block;
// }
</style>
