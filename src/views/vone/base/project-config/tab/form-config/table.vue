<template>
  <div>
    <header class="header">
      <!-- 查询条件 -->
      <el-input
        v-model="search"
        class="search"
        placeholder="请输入关键字"
        :prefix-icon="ElIconIconfont elIconApplicationSearch"
        style="width: 220px"
        @input="filterTable"
      />
      <div class="operation">
        <el-row type="flex" justify="space-between">
          <el-button
            class="ml-16"
            type="primary"
            :icon="ElIconIconfont elIconTipsPlusCircle"
            @click="addTableData"
            >新增</el-button
          >
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"
              ><el-icon class="iconfont"><el-icon-application-more /></el-icon
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-row>
        <!-- <el-button icon="iconfont el-icon-application-delete" @click="deleteTableData">批量删除</el-button> -->
      </div>
    </header>
    <main v-loading="colLoading" class="table_wrap">
      <el-table
        ref="base-setting-table"
        border
        :loading="tableLoading"
        class="vone-table sourceTable"
        table-key="base-setting-table"
        :table-options="tableOptions"
        row-key="Id"
        node-key="Id"
        :data="tableData"
        height="calc(100vh - 156px)"
        @selection-change="(value) => (selectedData = value)"
      >
        <el-table-column prop="shift" class-name="shift" width="46">
          <template>
            <el-icon
              class="iconfont handle"
              style="color: var(--main-theme-color)"
              ><el-icon-application-drag
            /></el-icon>
          </template>
        </el-table-column>
        <el-table-column type="selection" align="center" />
        <el-table-column
          v-if="colConfig.length == 0"
          prop="name"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot="header">
            <el-icon class="iconfont" style="color: #6b7385"
              ><el-icon-application-lockFill
            /></el-icon>
            <el-icon class="iconfont"
              ><el-icon-application-duohangwenben /></el-icon
            >名称
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(col, i) in colConfig"
          :key="col.name"
          :label="col.name"
          :prop="col.field"
          :min-width="col.type == 'date' ? 180 : 150"
        >
          <template v-slot:header="scope">
            <el-popover
              v-if="col"
              :ref="'editPopper_' + scope.$index"
              placement="bottom-start"
              width="250"
              trigger="click"
            >
              <div style="padding: 11px 0; position: relative">
                <el-form
                  :ref="'editForm_' + scope.$index"
                  :model="editForm"
                  :rules="rules"
                >
                  <el-form-item prop="name">
                    <el-input
                      v-model="editForm.name"
                      placeholder="请输入标题"
                    />
                  </el-form-item>
                  <el-form-item label="字段类型">
                    <el-col class="codeType">
                      <el-dropdown
                        ref="dropdown"
                        trigger="click"
                        placement="left"
                        @command="typeChange"
                      >
                        <div class="field_link">
                          <span class="field">
                            <i
                              :class="
                                checkType.icon ||
                                'iconfont el-icon-application-duohangwenben'
                              "
                            />
                            {{ checkType.label || '多行文本' }}
                          </span>
                          <el-icon class="icontype iconfont"
                            ><el-icon-direction-right
                          /></el-icon>
                        </div>
                        <el-dropdown-menu
                          slot="dropdown"
                          class="menuItem"
                          :append-to-body="false"
                        >
                          <el-dropdown-item
                            v-for="item in type"
                            :key="item.id"
                            :command="item"
                            ><i :class="item.icon" />{{
                              item.label
                            }}</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </el-dropdown>
                    </el-col>
                  </el-form-item>
                  <div class="footer">
                    <el-button
                      type="danger"
                      plain
                      @click="closeEditColumn(scope)"
                      >删除</el-button
                    >
                    <el-button
                      type="primary"
                      :loading="saveLoading"
                      @click="saveEditColumn(scope)"
                      >确定</el-button
                    >
                  </div>
                </el-form>
              </div>
              <div
                slot="reference"
                class="col_header text-over"
                :title="col.name"
                @click="editColumn(scope.$index)"
              >
                <el-icon class="iconfont" style="color: #6b7385"
                  ><el-icon-application-lockFill
                /></el-icon>
                <i :class="col.icon" style="color: #6b7385" />
                <div>{{ col.name }}</div>
              </div>
            </el-popover>
          </template>
          <template v-slot="{ row }">
            <template v-if="row.edit[col.id]">
              <span v-if="col.type == 'user'">
                <vone-remote-user
                  :ref="'user_' + col.id"
                  v-model="row[col.id]"
                  placeholder="请选择人员"
                  @blur="setUser(row, col, row[col.id])"
                />
              </span>
              <span v-else-if="col.type == 'date'">
                <el-date-picker
                  :ref="'date_' + col.id"
                  v-model="row[col.id]"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择日期"
                  style="width: 100%"
                  @blur="editRowVal(row, col)"
                />
              </span>
              <span v-else>
                <el-input
                  :ref="'input_' + col.id"
                  v-model="row[col.id]"
                  placeholder="请修改"
                  @blur="editRowVal(row, col)"
                />
              </span>
            </template>
            <template v-else>
              <span
                v-if="col.type == 'user' && userMap[row[col.id]]"
                class="col_cell"
                @click="focusRow(row, col)"
              >
                <vone-user-avatar
                  :avatar-path="userMap[row[col.id]].avatarPath"
                  :avatar-type="true"
                  height="22px"
                  width="22px"
                  :name="userMap[row[col.id]].name"
                  :show-name="true"
                />
              </span>
              <span v-else class="col_cell" @click="focusRow(row, col)">{{
                row[col.id]
              }}</span>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="plus_wrap">
        <el-popover
          :ref="'addPopper'"
          placement="bottom-start"
          width="250"
          trigger="click"
          append-to-body
        >
          <div style="padding: 11px 0; position: relative">
            <el-form ref="addForm" :model="addForm" :rules="rules">
              <el-form-item prop="name">
                <el-input v-model="addForm.name" placeholder="请输入标题" />
              </el-form-item>
              <el-form-item label="字段类型">
                <el-row class="codeType">
                  <el-dropdown
                    ref="dropdown"
                    trigger="click"
                    placement="left"
                    @command="typeChange"
                  >
                    <div class="field_link">
                      <span class="field">
                        <i
                          :class="
                            checkType.icon ||
                            'iconfont el-icon-application-duohangwenben'
                          "
                        />
                        {{ checkType.label || '多行文本' }}
                      </span>
                      <el-icon class="icontype iconfont"
                        ><el-icon-direction-right
                      /></el-icon>
                    </div>
                    <el-dropdown-menu
                      slot="dropdown"
                      class="menuItem"
                      :append-to-body="false"
                    >
                      <el-dropdown-item
                        v-for="item in type"
                        :key="item.id"
                        :command="item"
                        ><i :class="item.icon" />{{
                          item.label
                        }}</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </el-row>
              </el-form-item>
              <div class="footer">
                <el-button type="danger" plain @click="onClose">删除</el-button>
                <el-button
                  type="primary"
                  :loading="saveLoading"
                  @click="addColumn"
                  >确定</el-button
                >
              </div>
            </el-form>
          </div>
          <div slot="reference" class="col_header">
            <el-icon class="iconfont"><el-icon-tips-plus /></el-icon>
          </div>
        </el-popover>
        <div class="plus_body" />
      </div>
    </main>
    <!-- 导入数据 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      v-model="importParam.visible"
      @success="getSourceTableList(currentNodekey)"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import storage from 'store'
import { debounce } from 'lodash'
import { download } from '@/utils'
import {
  addSourceFromData,
  deleteSourceFormData,
  editSourceData,
  editSourceFormValue,
  getSourceById,
  getSourceFormData,
  sortSourceFormData,
} from '@/api/vone/base/source'
import { dataExport } from '@/api/vone/base/file'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import Sortable from 'sortablejs'

const scrollTbodyRight = () => {
  const tbody = document.querySelector('.sourceTable .el-table__body-wrapper')
  tbody.scrollTo({
    behavior: 'smooth',
    left: tbody.scrollWidth,
  })
}

export default {
  props: {
    currentNodekey: {
      // id
      type: [Number, String],
      default: undefined,
    },
  },
  data() {
    return {
      type: [
        {
          label: '多行文本',
          type: 'text',
          icon: 'iconfont el-icon-application-duohangwenben',
        },
        {
          label: '人员',
          type: 'user',
          icon: 'iconfont el-icon-application-member',
        },
        {
          label: '日期',
          type: 'date',
          icon: 'iconfont el-icon-application-date',
        },
      ],
      saveLoading: false,
      actions: [
        {
          name: '批量删除',
          fn: this.deleteTableData,
          disabled: !this.$permission('source_data_delete'),
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('source_data_Import'),
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          disabled: !this.$permission('source_data_export'),
          fn: this.exportFlie,
        },
      ],
      colLoading: false,
      tableLoading: false,
      tableOptions: {
        isGrouping: false,
        isOperation: false,
      },
      tableMap: {},
      tableData: [],
      colConfig: [],
      sourceConfig: {},
      checkType: {
        label: '多行文本',
        type: 'text',
        icon: 'iconfont el-icon-application-duohangwenben',
      },
      addForm: {
        name: '',
      },
      editForm: {
        name: '',
        type: '',
      },
      search: '',
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      },
      userMap: {},
      allUserList: [],
      selectedData: [],
      importParam: { visible: false },
      newIndexList: [],
    }
  },

  mounted() {
    this.getSourceTableList(this.currentNodekey)
    this.getUserList()
  },
  methods: {
    rowDrop() {
      const tbody = document.querySelector(
        '.sourceTable .el-table__body-wrapper tbody'
      )
      Sortable.create(tbody, {
        group: 'list',
        handle: '.handle',
        animation: 50,
        avoidImplicitDeselect: true, // 外部点击不能取消选中
        removeCloneOnHide: true,
        dragClass: 'draggingRow', // 拖动中的dom类名
        scroll: true,
        // 拖拽结束回调，处理添加到左侧分组业务逻辑
        onEnd: (evt) => {
          const { newIndex, oldIndex } = evt
          if (newIndex === oldIndex) return
          // const moved = this.tableData.splice(oldIndex, 1)[0]
          const currRow = this.newIndexList.splice(oldIndex, 1)[0]
          this.newIndexList.splice(newIndex, 0, currRow)
          // this.tableData.splice(newIndex, 0, moved)
          // const ids = this.tableData.map(v => v.id)
          sortSourceFormData(this.currentNodekey, this.newIndexList).then(
            (res) => {
              if (res.isSuccess) {
                this.$message.success('修改成功')
              } else {
                this.$message.error('修改失败')
              }
            }
          )
        },
      })
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '数据表',
        url: `/api/tm-base/formData/excel/downloadImportTemplate?dataId=${this.currentNodekey}`,
        importUrl: `/api/tm-base/formData/excel/import`,
        data: { dataId: this.currentNodekey },
      }
    },
    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true

        download(
          `数据表信息.xls`,
          await dataExport(
            `/api/tm-base/formData/excel/${this.currentNodekey}/export`,
            { search: this.search }
          )
        )

        this.tableLoading = false
      } catch (e) {
        this.tableLoading = false
        return
      }
    },
    handleCommand(e) {
      e && e()
    },
    // 删除列
    async closeEditColumn(scope) {
      if (scope.$index < 2) {
        this.$message.warning('当前列不可删除')
        return
      }
      const index = scope.$index - 2
      const fields = JSON.parse(JSON.stringify(this.sourceConfig.fields)) || []
      fields.splice(index, 1)
      const params = {
        ...this.sourceConfig,
        fields: fields,
      }
      const res = await editSourceData(params)
      if (res.isSuccess) {
        this.$message.success('删除成功')
        this.checkType = {
          label: '多行文本',
          type: 'text',
          icon: 'iconfont el-icon-application-duohangwenben',
        }
        this.refreshTable(this.currentNodekey)
        this.$refs['editPopper_' + scope.$index]?.[0]?.doClose()
      } else {
        this.$message.error('删除失败')
      }
    },
    onClose() {
      this.addForm = { name: '' }
      this.checkType = {
        label: '多行文本',
        type: 'text',
        icon: 'iconfont el-icon-application-duohangwenben',
      }
    },
    typeChange(val) {
      this.$set(this, 'checkType', val)
    },
    editColumn(index) {
      if (index < 2) return
      const current = this.colConfig[index - 2] || {}
      const config = JSON.parse(current.config) || {}
      this.editForm =
        {
          name: current.name,
          type: config.type,
        } || {}
      this.checkType = {
        label: config.label,
        type: config.type,
        icon: config.icon,
      }
    },
    // 修改列数据
    async saveEditColumn(scope) {
      if (scope.$index < 1) return

      try {
        await this.$refs['editForm_' + scope.$index]?.[0].validate()
      } catch (e) {
        return
      }
      const index = scope.$index - 2
      const fields = JSON.parse(JSON.stringify(this.sourceConfig.fields)) || []
      const checkNameLen = fields.filter(
        (v, i) => v.name === this.editForm.name && i != index
      ).length
      if (checkNameLen >= 1) {
        this.$message.error('存在重复列名，请修改')
        return
      }
      const result = {
        ...fields[index],
        name: this.editForm.name,
        config: JSON.stringify(this.checkType),
      }
      fields.splice(index, 1, result)

      const params = {
        ...this.sourceConfig,
        fields: fields,
      }
      const res = await editSourceData(params)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.refreshTable(this.currentNodekey)
        this.$refs['editPopper_' + scope.$index]?.[0]?.doClose()
      } else {
        this.$message.error('修改失败')
      }
    },
    // 新增列数据
    async addColumn() {
      try {
        await this.$refs.addForm.validate()
      } catch (error) {
        return
      }
      const fields = JSON.parse(JSON.stringify(this.sourceConfig.fields)) || []
      const checkNameLen = fields.filter(
        (v) => v.name === this.addForm.name
      ).length
      if (checkNameLen >= 1) {
        this.$message.error('存在重复列名，请修改')
        return
      }

      const result = {
        primary: false,
        dataId: this.currentNodekey,
        name: this.addForm.name,
        config: JSON.stringify(this.checkType),
      }
      fields.push(result)
      const params = {
        ...this.sourceConfig,
        fields: fields,
      }
      const res = await editSourceData(params)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.addForm = { name: '' }
        this.refreshTable(this.currentNodekey)
        this.$refs['addPopper']?.doClose()
      } else {
        this.$message.error('修改失败')
      }
      setTimeout(() => {
        scrollTbodyRight()
      }, 200)
    },
    filterTable: debounce(function () {
      this.getSourceTableList(this.currentNodekey)
    }, 1000),
    async refreshTable(id) {
      this.search = ''
      await this.getTableConfig(id)
      this.getSourceTableList(id)
    },
    async getTableConfig(id) {
      if (!id) return
      this.colLoading = true
      const res = await getSourceById(id)
      if (res.isSuccess) {
        this.sourceConfig = res.data
        this.colConfig = res.data.fields.map((v) => {
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj,
          }
        })
        this.colLoading = false
      }
    },
    // 查询当前数据源下数据
    async getSourceTableList(id) {
      if (!id) return
      this.tableData = []
      this.tableLoading = true
      this.newIndexList = []
      const res = await getSourceFormData(id, this.search)
      if (res.isSuccess) {
        var objmap = new Map()
        this.tableData = res.data.map((e) => {
          objmap.set(e.id, e.columns || {})
          const obj = {
            id: e.id,
            edit: {},
            ...e.columns,
          }
          if (e.columns) {
            Object.keys(e.columns).map((v) => {
              obj.edit[v] = false
            })
          }
          return obj
        })
        this.tableMap = Array.from(objmap).reduce(
          (obj, [key, value]) => Object.assign(obj, { [key]: value }),
          {}
        )
        this.newIndexList = this.tableData.map((e) => e.id)
        // for (const v in res.data) {
        //   const obj = { edit: {}}
        //   Object.keys(res.data[v]).map(v => { obj.edit[v] = false })
        //   list.push({ id: v, ...res.data[v], ...obj })
        // }
        // this.tableData = list.sort((a, b) => b.id - a.id)
        // this.tableData = list
        this.$nextTick(() => {
          this.rowDrop()
        })
        this.tableLoading = false
      }
    },
    setUser(row, col) {
      setTimeout(() => {
        this.editRowVal(row, col)
      }, 200)
    },
    async getUserList() {
      const res = await apiBaseAllUserNoPage()

      if (!res.isSuccess) {
        return
      }
      this.userMap = res.data.reduce((acc, cur) => {
        acc[cur.id] = cur
        return acc
      }, {})
      this.allUserList = res.data
    },
    focusRow(row, col) {
      this.$set(row.edit, col.id, true)
      // 校验当前列数据格式是否正确
      if (col.type === 'date' && !dayjs(row[col.id]).isValid()) {
        this.$set(row, col.id, '')
      } else if (col.type === 'user') {
        isNaN(Number(row[col.id])) && (row[col.id] = '')
      }
      this.$nextTick(() => {
        const refMap = {
          text: 'input_',
          date: 'date_',
        }
        if (col.type === 'user') {
          this.$refs['user_' + col.id]?.[0].$children?.[0].focus()
        } else {
          const ref = refMap[col.type] || 'input_'
          this.$refs[ref + col.id]?.[0]?.focus()
        }
      })
    },
    // 修改列数据
    async editRowVal(row, col) {
      this.$set(row.edit, col.id, false)
      // 未修改
      if (this.tableMap[row.id][col.id] === row[col.id]) return
      const params = {
        dataId: this.currentNodekey,
        rowId: row.id,
        fieldId: col.id,
        value: encodeURIComponent(row[col.id]),
      }
      const res = await editSourceFormValue(params)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.tableMap[row.id][col.id] = row[col.id]
      } else {
        this.$message('修改失败')
      }
    },
    async addTableData() {
      const loginUser = storage.get('user')
      const fields = this.sourceConfig.fields
      const params = fields.reduce((acc, cur) => {
        const config = JSON.parse(cur.config)
        const type = config.type

        acc[cur.id] =
          type === 'date'
            ? dayjs().format('YYYY-MM-DD')
            : type === 'user'
            ? loginUser.id
            : cur.name || '名称'
        return acc
      }, {})
      const res = await addSourceFromData(this.currentNodekey, params)
      if (res.isSuccess) {
        this.$message.success('添加成功')
        this.getSourceTableList(this.currentNodekey)
      } else {
        this.$message.error('添加失败')
      }
    },
    //  删除表格数据
    async deleteTableData() {
      if (this.selectedData.length == 0) {
        this.$message.error('请选择数据')
        return
      }
      try {
        await this.$confirm(
          `确定 ${this.selectedData.length} 个数据吗?`,
          '批量删除',
          {
            type: 'warning',
            customClass: 'delConfirm',
            showClose: false,
          }
        )
      } catch (e) {
        return
      }
      const ids = this.selectedData.map((v) => v.id)

      const res = await deleteSourceFormData(this.currentNodekey, ids)

      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getSourceTableList(this.currentNodekey)
    },
  },
}
</script>

<style lang="scss" scoped>
header.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 16px;
  border-bottom: 1px solid var(--el-divider);
}

.search {
  :deep() {
    .el-input__prefix {
      color: #bcc2cb;
    }
  }
}
.operation {
  display: flex;
}
.table_wrap {
  position: relative;
  padding: 16px;
}
.plus_wrap {
  position: absolute;
  right: 16px;
  top: 16px;
  width: 48px;
  height: calc(100% - 32rem);
  .col_header {
    justify-content: center;
    &:hover {
      cursor: pointer;
    }
  }
}

.sourceTable {
  width: calc(100% - 48px);
  :deep(th.gutter) {
    background-color: var(--bottom-bg-color);
  }
}
:deep(.el-form-item--small.el-form-item) {
  margin-bottom: 16px;
}
:deep(.el-popover) {
  padding: 16px 12px;
}
:deep(th.el-table__cell) {
  padding: 0;
  &:hover {
    cursor: pointer;
    background-color: #f2f3f5;
  }
}
:deep(td.el-table__cell) {
  padding: 0;
}
.col_header {
  display: flex;
  align-items: center;
  gap: 2px;
}

.col_cell {
  display: inline-block;
  width: 100%;
  cursor: pointer;
}
.field_link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.codeType {
  :deep(.el-dropdown) {
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .icontype {
    margin-right: 10px;
  }
  .menuItem {
    position: absolute;
    left: -166px !important;
    background: #fff;
    border: 1px solid #f2f3f5;
    border-radius: 4px;
    padding: 11px 16px;
  }
}
.footer {
  text-align: right;
  :deep(.is-plain) {
    background-color: #fff;
    color: #ed3e3e;
    border: 1px solid currentColor;
  }
}
.handle {
  display: inline-block;
  color: #6b7385;
  cursor: grab;
  margin-right: 4px;
  &:hover {
    color: var(--main-theme-color);
  }
}
</style>
