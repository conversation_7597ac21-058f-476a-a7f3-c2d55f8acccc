<template>
  <!-- 新增自定义属性 -->
  <el-dialog
    title="添加属性"
    width="40%"
    v-model="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
  >
    <el-form ref="addForm" :model="addForm" label-position="top" :rules="rules">
      <el-tabs v-model="addForm.isBuilt" type="card" class="flowTab">
        <el-tab-pane label="自定义" name="false">
          <div v-if="addForm.isBuilt == 'false'">
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="属性名称" prop="name">
                  <el-input
                    v-model.trim="addForm.name"
                    placeholder="请输入属性名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="属性类型" prop="type">
                  <el-select v-model="addForm.type" placeholder="属性类型">
                    <el-option
                      v-for="i in typeOptions"
                      :key="i.key"
                      :label="i.label"
                      :value="i.key"
                      :disabled="i.chooseLength == 0"
                    >
                      <span style="float: left">{{ i.label }}</span>
                      <span
                        style="float: right; color: #8492a6; font-size: 13px"
                        >可添加{{ i.chooseLength }}个</span
                      >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col v-if="addForm.type == 'LINKED'" :span="12">
                <el-form-item label="关联数据表" prop="relationShipsheet">
                  <el-select
                    v-model="addForm.relationShipsheet"
                    placeholder="属性类型"
                  >
                    <el-option
                      v-for="i in sheetOptions"
                      :key="i.id"
                      :label="i.name"
                      :value="i.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  v-if="
                    addForm.type == 'SELECT' ||
                    addForm.type == 'USER' ||
                    addForm.type == 'PROJECTUSER' ||
                    addForm.type == 'ORG' ||
                    addForm.type == 'FILE'
                  "
                  label="多选"
                  prop="multiple"
                  :rules="[
                    {
                      required: true,
                      message: '请选择是否多选',
                      trigger: 'change',
                    },
                  ]"
                >
                  <el-radio-group v-model="addForm.multiple">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="必填项" prop="isRequired">
                  <el-radio-group v-model="addForm.isRequired">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item
              v-if="addForm.type == 'INPUT' || addForm.type == 'TEXTAREA'"
              label="校验规则"
              prop="validator"
            >
              <el-input
                v-model.trim="addForm.validator"
                placeholder="请输入校验规则 ( 正则 )"
              />
            </el-form-item>
            <el-form-item
              v-if="addForm.isRequired"
              label="提示信息"
              prop="message"
            >
              <el-input
                v-model.trim="addForm.message"
                placeholder="请输入校验规则提示信息"
              />
            </el-form-item>

            <el-form-item
              v-if="addForm.type == 'input' || addForm.type == 'textarea'"
              label="校验规则"
              prop="validator"
            >
              <el-input
                v-model.trim="addForm.validator"
                placeholder="请输入校验规则 ( 正则 )"
              />
            </el-form-item>
            <div v-if="addForm.type == 'QUOTE'">
              <el-divider />
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="引用的字段" prop="relationShipsheet">
                    <el-select
                      v-model="addForm.relationShipsheet"
                      placeholder="关联表"
                      @change="sheetChange"
                    >
                      <el-option
                        v-for="i in sheetOptions"
                        :key="i.id"
                        :label="i.name"
                        :value="i.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item class="formlable" label="" prop="relationField">
                    <label slot="label">&nbsp;</label>
                    <el-select
                      v-model="addForm.relationField"
                      placeholder="关联字段"
                    >
                      <el-option
                        v-for="i in fieldOptions"
                        :key="i.id"
                        :label="i.name"
                        :value="i.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="addForm.type == 'QUOTE'" :gutter="12">
                <el-col :span="10" style="padding-right: 2px">
                  <el-form-item label="查找条件" prop="queryCriteriaA">
                    <el-select
                      v-model="addForm.queryCriteriaA"
                      placeholder="属性类型"
                    >
                      <el-option
                        v-for="i in fieldOptions"
                        :key="i.id"
                        :label="i.name"
                        :value="i.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" style="padding-right: 2px; padding-left: 2px">
                  <el-form-item
                    class="formlable"
                    label=""
                    prop="queryCriteriaB"
                  >
                    <label slot="label">&nbsp;</label>
                    <el-select v-model="addForm.queryCriteriaB" placeholder="">
                      <el-option label="等于" value="equal" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10" style="padding-left: 2px">
                  <el-form-item
                    class="formlable"
                    label=""
                    prop="queryCriteriaC"
                  >
                    <label slot="label">&nbsp;</label>
                    <el-select
                      v-model="addForm.queryCriteriaC"
                      placeholder="属性类型"
                    >
                      <el-option
                        v-for="i in customListKeys"
                        :key="i.key"
                        :label="i.name"
                        :value="i.key"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="内置" name="true">
          <el-form-item
            v-if="addForm.isBuilt == 'true'"
            label="属性"
            prop="property"
            :rules="[
              {
                required: true,
                message: '请选择属性',
              },
            ]"
          >
            <el-select v-model="addForm.property" placeholder="请选择">
              <el-option
                v-for="i in options"
                :key="i.key"
                :label="i.name"
                :value="i.key"
              />
            </el-select>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveInfo"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import { getSourceById, getSourceList } from '@/api/vone/base/source'

const typeOptions = [
  {
    key: 'INPUT',
    label: '输入框',
    length: 20,
  },
  // 文本域长度原22，现19
  {
    key: 'TEXTAREA',
    label: '文本域',
    length: 15,
  },
  {
    key: 'SELECT',
    label: '下拉框',
    length: 15,
  },
  {
    key: 'DATE',
    label: '日期选择器',
    length: 10,
  },
  {
    key: 'USER',
    label: '平台人员',
    length: 3,
  },
  {
    key: 'PROJECTUSER',
    label: '项目人员',
    length: 3,
  },
  {
    key: 'ORG',
    label: '组织机构',
    length: 3,
  },
  {
    key: 'FILE',
    label: '文件',
    length: 3,
  },
  {
    key: 'INT',
    label: '数字',
    length: 5,
  },
  {
    key: 'LINKED',
    label: '关联类型',
    length: 10,
  },
  {
    key: 'QUOTE',
    label: '引用类型',
    length: 40,
  },
  {
    key: 'LINK',
    label: '超链接',
    length: 7,
  },
]
const c40To55 = Array.from({ length: 16 }, (_, i) => `c${40 + i}`) // 40-55
const c56To69 = Array.from({ length: 13 }, (_, i) => `c${56 + i}`) // 56-69
const c76To84 = Array.from({ length: 9 }, (_, i) => `c${76 + i}`) // 76-84

const typeMap = {
  USER: ['c1', 'c2', 'c3'],
  PROJECTUSER: ['c4', 'c5', 'c6'],
  ORG: ['c7', 'c8', 'c9'],
  FILE: ['c10', 'c11', 'c12'],
  SELECT: ['c13', 'c14', 'c15', 'c16', 'c17', 'c18'].concat(c76To84),
  INPUT: ['c19', 'c20', 'c21', 'c22'].concat(c40To55),
  INT: ['c23', 'c24', 'c32', 'c33', 'c34', 'c35', 'c36', 'c37', 'c38', 'c39'],
  TEXTAREA: ['c25', 'c26'].concat(c56To69),
  DATE: ['c27', 'c28', 'c29', 'c30', 'c31', 'c85', 'c86', 'c87', 'c88', 'c89'],
  LINKED: [
    'c13',
    'c14',
    'c15',
    'c16',
    'c17',
    'c18',
    'c76',
    'c77',
    'c78',
    'c79',
  ],
  QUOTE: ['c19', 'c20', 'c21', 'c22'].concat(c40To55),
  LINK: ['c69', 'c70', 'c71', 'c72', 'c73', 'c74', 'c75'],
}

import { pick } from 'lodash'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    typeClassify: {
      type: String,
      default: undefined,
    },
    formId: {
      type: String,
      default: undefined,
    },
    customKey: {
      type: String,
      default: undefined,
    },
    customList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'change' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        relationShipsheet: [
          { required: true, message: '请选择关联表', trigger: 'change' },
        ],
        relationField: [
          { required: true, message: '请选择字段', trigger: 'change' },
        ],
        queryCriteriaA: [
          {
            required: true,
            message: '请选择筛选条件',
            trigger: 'change',
            hideRequiredAsterisk: true,
          },
        ],
        queryCriteriaB: [
          { required: true, message: '请选择筛选条件', trigger: 'change' },
        ],
        queryCriteriaC: [
          { required: true, message: '请选择筛选条件', trigger: 'change' },
        ],
      },
      addForm: {
        isBuilt: 'false', // 该字段控制是内置属性还是用户自定义属性
        customFormId: this.formId,
        key: this.customKey,
        isRequired: false,
        multiple: false,
        // sort: 0,
        state: true,
        isFilterShow: false,
        width: 100,
        sortable: false,
        queryCriteriaB: 'equal',
      },
      saveLoading: false,

      typeOptions,

      choosekeys: [], // 每个类型可以选择的key
      typeMap,
      fieldOptions: [],
      sheetOptions: [],
      customListKeys: [],
    }
  },
  watch: {
    visible: {
      handler(v) {
        if (v) {
          // 处理各种自定义字段还剩下几个可以添加
          this.typeOptions.forEach((element) => {
            const hasLength = this.customList.filter(
              (r) => r.type == element.key && !r.isBuilt
            ).length
            element.chooseLength = element.length - hasLength
          })
        }

        // 处理内置属性
        this.options = this.customList.filter(
          (r) =>
            r.isBuilt && !r.state && r.key != 'rateProgress' && r.key != 'delay'
        )
      },
      immediate: true,
    },
    'addForm.type': {
      handler(value) {
        if (value) {
          const input = ['INPUT', 'TEXTAREA']
          this.$set(
            this.addForm,
            'message',
            input.includes(this.addForm.type) ? '请输入' : '请选择'
          )

          if (value == 'LINKED' || value == 'QUOTE') {
            this.getSheetOptions()
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.customListKeys = this.customList.filter((e) => {
      return e.type == 'LINKED'
    })

    // const c76To84 = Array.from({ length: 9 }, (_, i) => `c${76 + (i)}`) // 40-75
  },
  methods: {
    async getSheetOptions() {
      const res = await getSourceList()
      if (res.isSuccess) {
        this.sheetOptions = res.data
      }
    },
    sheetChange(e) {
      this.getFieldOptions(e)
    },
    async getFieldOptions(id) {
      this.$set(this.addForm, 'relationField', null)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        this.fieldOptions = res.data.fields
      }
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.addForm.resetFields()
    },

    // 保存
    async saveInfo() {
      await this.$refs.addForm.validate()
      let formData = {}
      if (this.addForm.isBuilt == 'true') {
        formData = this.options.find((r) => r.key == this.addForm.property)
        var arry = [
          'productVersionId',
          'productModuleFunctionId',
          'productRepairVersionId',
        ]
        if (arry.indexOf(this.addForm.property) > -1) {
          formData.disableSearch = true
        }
      } else {
        // 新增扩展字段,总共描述40个，下拉框10个 日期5个 数字10个。

        this.choosekeys = this.typeMap[this.addForm.type]

        const hasKey = this.customList.map((r) => r.key)
        // 取出还可以选的c...
        const cKey = _.difference(this.choosekeys, hasKey)
        console.log(this.addForm.type, this.choosekeys, hasKey)
        if (!cKey.length) {
          this.$message.warning(`当前类型的自定义字段已达上限,请联系管理员`)
          return
        }
        // 添加新属性的时候的key
        const customKey = _.head(cKey)
        this.$set(this.addForm, 'key', customKey)
        this.$set(this.addForm, 'isBuilt', this.addForm.isBuilt == 'true')
        this.$set(
          this.addForm,
          'defaultTime',
          this.addForm.type == 'DATE' ? '09:00' : null
        )
        // 默认排序
        const max = Math.max.apply(
          Math,
          this.customList.map(function (o) {
            return o.sort
          })
        )
        this.$set(this.addForm, 'sort', max + 1)
        // 默认赋值placeHolder
        const input = ['INPUT', 'TEXTAREA', 'LINK']
        this.$set(
          this.addForm,
          'placeholder',
          input.includes(this.addForm.type) ? '请输入' : '请选择'
        )
        const arry = [
          'placeholder',
          'multiple',
          'validator',
          'message',
          'defaultTime',
          'relationShipsheet',
          'relationField',
          'queryCriteriaA',
          'queryCriteriaB',
          'queryCriteriaC',
        ]
        const otherProperty = { ...pick(this.addForm, arry) }
        this.$set(this.addForm, 'config', JSON.stringify(otherProperty))
        formData = {
          ...pick(this.addForm, [
            'isBuilt',
            'name',
            'type',
            'isRequired',
            'sort',
            'customFormId',
            'key',
            'config',
            'state',
          ]),
        }
      }
      this.onClose()
      this.$emit('success', formData)
    },
  },
}
</script>

<style lang="scss" scoped>
header {
  height: 56px;
  line-height: 56px;
}
.flowTab {
  padding: 12px 16px;
}
.text {
  font-size: 12px;
  color: var(--auxiliary-font-color);
}
a {
  color: var(--main-theme-color);
}
:deep(.el-dialog__body) {
  padding: 0;
}
:deep(.el-form-item__label) {
  height: 32px;
}
.formlable {
  :deep(.el-form-item__label) {
    &::before {
      display: none;
    }
  }
}
</style>
