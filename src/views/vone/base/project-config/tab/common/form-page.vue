<template>
  <div>
    <el-row type="flex" class="rowCol">
      <el-col :span="12" class="header">
        <span class="back" @click="back">
          <el-icon class="iconfont"><ElIconArrowLeft /></el-icon
        ></span>
        <strong>
          {{ !viewType && !$route.query.viewType ? '配置' : '查看' }}
          {{ `【${typeName || $route.query.typeName}】` }}</strong
        >
      </el-col>
      <el-col :span="12" style="text-align: right">
        <el-dropdown
          trigger="click"
          style="margin-right: 16px"
          @command="handleCommand"
        >
          <el-button class="optionBtn">
            预览表单<el-icon class="iconfont el-icon--right"
              ><ElIconDirectionDown
            /></el-icon>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="add">预览新增表单</el-dropdown-item>
            <el-dropdown-item command="edit">预览编辑表单</el-dropdown-item>
            <!-- <el-dropdown-item command="info">预览详情表单</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <el-button>取消</el-button> -->
        <el-button
          v-if="!viewType && !$route.query.viewType"
          type="primary"
          :loading="saveLoading"
          @click="saveForm"
          >确定</el-button
        >
      </el-col>
    </el-row>
    <div class="rowLine" />

    <div v-loading="pageLoading">
      <!-- <div> -->
      <div class="rowLine">
        <header class="headerRow">
          <span>
            <span class="headerTitile">
              关键属性
              <!-- :title="''" :text="'关键属性会显示在表单头部'"  -->
            </span>
            <span class="headerIin"> 关键属性会显示在表单头部 </span>
          </span>
          <span>
            <i
              :class="[
                'iconfont',
                !hideDiv ? 'el-icon-direction-down' : 'el-icon-direction-right',
              ]"
              @click="hideDiv = !hideDiv"
            />
          </span>
        </header>
        <!-- <div slot="actions">
            <i :class="['iconfont' , !hideDiv ? 'el-icon-direction-down' :'el-icon-direction-right']" @click="hideDiv = !hideDiv" />
          </div> -->

        <el-form v-if="!hideDiv" ref="fixedForm" :model="fixedForm">
          <el-row class="basicHeader" type="flex" justify="space-between">
            <el-col
              v-for="item in fixedProperty"
              :key="item.id"
              class="el-col-6"
            >
              <div class="fixedItem">
                <!-- 图标 -->

                <svg
                  v-if="item.key == 'stateCode'"
                  class="icon"
                  aria-hidden="true"
                  style="font-size: 42px"
                >
                  <use xlink:href="#el-icon-icon-fill-zhuangtai" />
                </svg>

                <span v-else>
                  <svg class="icon" aria-hidden="true" style="font-size: 42px">
                    <use :xlink:href="`#${iconMap[item.key]}`" />
                  </svg>
                </span>

                <el-form-item :label="item.name" :prop="item.key">
                  <!-- 人员组件 -->
                  <el-select
                    v-if="item.type == 'USER'"
                    v-model="fixedForm[item.key]"
                    disabled
                  >
                    <el-option
                      v-for="i in userOptions"
                      :key="i.id"
                      :label="i.name"
                      :value="i.id"
                    >
                      <span>
                        {{ i.name }}
                      </span>
                    </el-option>
                  </el-select>

                  <!-- 下拉框 -->
                  <span v-else-if="item.type == 'SELECT'">
                    <el-select
                      v-if="item.key == 'tagId'"
                      v-model="fixedForm[item.key]"
                      disabled
                    >
                      <el-option
                        v-for="i in userOptions"
                        :key="i.id"
                        :label="i.name"
                        :value="i.id"
                      >
                        <span>
                          {{ i.name }}
                        </span>
                      </el-option>
                    </el-select>

                    <el-select
                      v-else
                      v-model="fixedForm[item.key]"
                      :placeholder="item.placeholder"
                      disabled
                    >
                      <el-option
                        v-for="i in item.options"
                        :key="i.id"
                        :label="i.name"
                        :value="item.key == 'planIds' ? i.id : i.code"
                      >
                        <span v-if="item.key == 'typeCode'">
                          <i
                            :class="['iconfont', `${i.icon}`]"
                            :style="{ color: i.color }"
                          />
                          {{ i.name }}
                        </span>
                        <span v-else-if="item.key == 'stateCode'">
                          <svg-icon
                            icon-class="vone-state"
                            :style="{ color: i.color }"
                          />
                          {{ i.name }}
                        </span>
                      </el-option>
                    </el-select>
                  </span>

                  <!-- 日期组件 -->
                  <el-date-picker
                    v-else-if="item.type == 'DATE'"
                    v-model="fixedForm[item.key]"
                    class="dataPicker"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :placeholder="item.placeholder"
                    disabled
                  />

                  <!-- 输入框 -->
                  <el-input
                    v-else
                    v-model="fixedForm[item.key]"
                    :placeholder="item.placeholder || '请输入'"
                    disabled
                  />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <vone-div-wrapper
        :title="'自定义属性'"
        :text="'字段配置修改后,保存才会生效'"
      >
        <div slot="header">
          <el-button
            v-if="!viewType && !$route.query.viewType"
            :icon="el-icon-setting"
            type="primary"
            @click.stop="newProperty"
            >新增</el-button
          >
        </div>

        <div
          v-loading="customLoading"
          class="contentBox"
          :class="{ hideKey: hideDiv }"
        >
          <section class="customBox">
            <el-form
              v-if="customList.length"
              ref="form"
              :model="form"
              label-position="top"
            >
              <div>
                <draggable
                  v-model="sortList"
                  tag="el-timeline"
                  class="customBorder"
                  @change="stepMove"
                >
                  <div
                    v-for="(item, index) in sortList"
                    :key="index"
                    :name="item.key"
                    class="colBox"
                    :class="{
                      selected: currentNodekey === item.key,
                    }"
                    @click.stop="editItem(item, index)"
                  >
                    <!-- 内置字段只能开启关闭,自定义字段可以删除 -->
                    <span @click.stop>
                      <el-switch
                        v-if="item.isBuilt"
                        :model-value="item.state"
                        active-color="#13ce66"
                        class="delButton"
                        :active-value="true"
                        :inactive-value="false"
                        @change="changeState(item, index)"
                      />
                    </span>
                    <el-icon class="iconfont delButton"
                      ><ElIconDelete
                    /></el-icon>
                    <el-form-item
                      :label="item.name"
                      :prop="item.key"
                      :rules="{
                        required: item.isRequired,
                        message: item.message,
                        trigger: 'blur',
                        pattern: item.validator,
                      }"
                    >
                      <!-- 平台人员组件 -->
                      <vone-remote-user
                        v-if="item.type == 'USER'"
                        v-model="form[item.key]"
                        :multiple="item.multiple"
                      />

                      <!-- 项目人员组件 -->
                      <vone-remote-user
                        v-else-if="item.type == 'PROJECTUSER'"
                        v-model="form[item.key]"
                        :multiple="item.multiple"
                      />

                      <!-- 整数 -->
                      <el-input-number
                        v-else-if="item.type == 'INT'"
                        v-model="form[item.key]"
                        :min="0.1"
                        :max="1000"
                        controls-position="right"
                        style="width: 100%"
                        :placeholder="item.placeholder"
                        :precision="item.precision"
                      />

                      <!-- 日期组件 -->
                      <el-date-picker
                        :default-time="
                          `${item.defaultTime}:00`.map((d) =>
                            dayjs(d, 'hh:mm:ss').toDate()
                          )
                        "
                        v-else-if="item.type == 'DATE'"
                        v-model="form[item.key]"
                        :prefix-icon="ElIconDate"
                        type="datetime"
                        format="yyyy-MM-dd HH:mm"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :placeholder="item.placeholder"
                      ></el-date-picker>

                      <!-- 输入框 -->
                      <el-input
                        v-else-if="item.type == 'INPUT'"
                        v-model="form[item.key]"
                        :placeholder="item.placeholder"
                        maxlength="200"
                        show-word-limit
                      />
                      <!-- 超链接 -->
                      <el-input
                        v-else-if="item.type == 'LINK'"
                        v-model="form[item.key]"
                        :placeholder="item.placeholder"
                      />
                      <!-- 输入文本框 -->
                      <el-input
                        v-else-if="item.type == 'TEXTAREA'"
                        v-model="form[item.key]"
                        autosize
                        type="textarea"
                        :placeholder="item.placeholder"
                      />
                      <!-- 组织机构 -->
                      <vone-tree-select
                        v-else-if="item.type == 'ORG'"
                        v-model="form[item.key]"
                        search-nested
                        :tree-data="orgData"
                        placeholder="请选择机构"
                        :multiple="item.multiple"
                      />

                      <!-- 标签 -->
                      <tagSelect
                        v-else-if="item.type == 'SELECT' && item.key == 'tagId'"
                        v-model="form[item.key]"
                      />

                      <vone-upload
                        v-else-if="item.type == 'FILE'"
                        ref="uploadFile"
                        :files-data="[]"
                      />

                      <!-- 下拉单选框 -->
                      <el-select
                        v-else-if="item.type == 'SELECT' && !item.multiple"
                        v-model="form[item.key]"
                        :placeholder="item.placeholder"
                        clearable
                        filterable
                        :disabled="item.disabled"
                        @focus="setOptionWidth"
                      >
                        <!-- 只有来源,分类,优先级传参得时候保存的是code,其它都是id -->
                        <el-option
                          v-for="i in item.options"
                          :key="i.id"
                          :label="i.name"
                          :value="
                            item.key == 'sourceCode' ||
                            item.key == 'typeCode' ||
                            item.key == 'priorityCode'
                              ? i.code
                              : i.id
                          "
                          :style="{ width: selectOptionWidth }"
                        >
                          <span v-if="item.key == 'requirementId'">{{
                            `${i.code}  ${i.name}`
                          }}</span>
                        </el-option>
                      </el-select>
                      <!-- 下拉多选框 -->
                      <el-select
                        v-else-if="item.type == 'SELECT' && item.multiple"
                        v-model="form[item.key]"
                        :placeholder="item.placeholder"
                        multiple
                        clearable
                        filterable
                        @focus="setOptionWidth"
                      >
                        <el-option
                          v-for="i in item.options"
                          :key="i.id"
                          :label="i.name"
                          :value="i.id"
                          :style="{ width: selectOptionWidth }"
                        />
                      </el-select>
                      <!-- 下拉多选框 -->
                      <el-select
                        v-else-if="item.type == 'LINKED'"
                        v-model="form[item.key]"
                        :placeholder="item.placeholder"
                        clearable
                        filterable
                        @focus="setOptionWidth"
                      >
                        <el-option
                          v-for="i in item.options"
                          :key="i.id"
                          :label="i.name"
                          :value="i.id"
                          :style="{ width: selectOptionWidth }"
                        />
                      </el-select>
                      <el-input
                        v-else-if="item.type == 'QUOTE'"
                        v-model="form[item.key]"
                        disabled
                        :placeholder="item.placeholder"
                        maxlength="200"
                        show-word-limit
                      />
                    </el-form-item>
                  </div>
                </draggable>
              </div>
            </el-form>

            <vone-empty v-else />
          </section>

          <section class="rightContent">
            <property
              :type-classify="typeClassify"
              :custom-list="customList"
              :item-info="itemInfo"
              :show-item="showItem"
              :form-id="formId"
              @success="attributeChange"
              @changeLink="changeLink"
            />
          </section>
        </div>
      </vone-div-wrapper>
    </div>

    <!-- 新增 -->
    <addDialog
      v-if="addParam.visible"
      v-bind="addParam"
      v-model="addParam.visible"
      :custom-list="customList"
      @success="getFactory"
    />

    <!-- 预览新增 -->
    <customAdd
      v-if="addFormParam.visible"
      :key="addFormParam.key"
      :type-code="typeClassify"
      v-model="addFormParam.visible"
      v-bind="addFormParam"
      :title="'新增'"
      :form-id="formId || $route.query.formId"
      preview
    />

    <!-- 预览编辑 -->
    <customEdit
      v-if="editFormParam.visible"
      :key="editFormParam.key"
      v-model="editFormParam.visible"
      v-bind="editFormParam"
      :type-code="typeClassify"
      :left-tabs="leftTabs"
      hide-prv
      :right-tabs="rightTabs"
      :form-id="formId || $route.query.formId"
      preview
    />
  </div>
</template>

<script>
import * as dayjs from 'dayjs'

const iconMap = {
  handleBy: 'el-icon-icon-dark-avatar',
  tagId: 'el-icon-icon-fill-biaoqian',
  planStime: 'el-icon-icon-fill-wanchengshijian',
  startTime: 'el-icon-icon-fill-wanchengshijian',
  planEtime: 'el-icon-icon-fill-wanchengshijian',
  endTime: 'el-icon-icon-fill-wanchengshijian',
  expectedTime: 'el-icon-icon-fill-wanchengshijian',
  typeCode: 'el-icon-icon-yixiang',
}

const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD',
}

import { apiBaseFormProperty } from '@/api/vone/base/index'

import { getAlmGetTypeNoPage } from '@/api/vone/alm/index'
import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiVaBaseCustomFormPut } from '@/api/vone/base/customForm'
import { ideaListByCondition } from '@/api/vone/reqmcenter/idea'
import { queryListByCondition } from '@/api/vone/project/index'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import { getSourceById, getSourceFormData } from '@/api/vone/base/source'

import tagSelect from '@/components/CustomEdit/components/tag-select'

import { gainTreeList } from '@/utils'
import { orgList } from '@/api/vone/base/org'

import _ from 'lodash'
import storage from 'store'

import property from '../form-config/property.vue'
import addDialog from '../form-config/add-dialog.vue'
import draggable from 'vuedraggable'
import customEdit from './form-view/index.vue'

import customAdd from './form-view/customAdd.vue'

export default {
  data() {
    return {
      hideDiv: false,
      fileMap,
      form: {},
      userOptions: [
        {
          id: '-1',
          name: '未设置',
        },
      ],
      saveLoading: false,
      fixedForm: {},
      // 固定属性
      fixedProperty: [],
      // 自定义属性
      customList: [],
      addParam: {
        visible: false,
      },
      itemInfo: {},
      showItem: false,
      pageLoading: false,
      // 固定属性图标
      iconMap,
      // formId: undefined,
      currentNodekey: '',
      customFormRules: {},
      sourceList: [],
      // 优先级,
      prioritList: [],
      // 组织机构
      orgData: [],
      addFormParam: { visible: false },
      editFormParam: { visible: false },
      infoFormParam: { visible: false },
      leftTabs: [],
      rightTabs: [
        {
          label: '活动',
          name: 'active',
        },
      ],
      originForm: {},
      selectOptionWidth: '',
      // 所有的固定属性
      allFixed: [],
      customLoading: false,
      sortList: [],
      envList: [],
      dayjs,
    }
  },
  components: {
    property,
    addDialog,
    draggable,
    customEdit,
    tagSelect,
    customAdd,
  },
  props: {
    typeClassify: {
      type: String,
      default: undefined,
    },
    formId: {
      type: String,
      default: undefined,
    },
    typeName: {
      type: String,
      default: undefined,
    },
    viewType: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    typeClassify: {
      handler(v) {
        if (v) {
          this.getFormProperty()
        }
      },
      immediate: true,
    },
    customList: {
      handler(v) {
        if (v) {
          this.sortList = this.customList.filter((r) => r.state)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  async mounted() {
    await this.getFormProperty()
    await this.getOption()
  },
  methods: {
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
      })
      this.$forceUpdate()
    },
    getOption(val) {
      const keys = this.customList.map((r) => r.key)
      this.getOrgList()
      this.getSourceList()
      this.getPrioritList()
      this.getIssueType()
      this.getStateList()
      this.getProjectList()

      if (keys.includes('envCode')) {
        this.getEnvList()
      }

      this.typeClassify == 'ISSUE' ? this.getIdeaList() : null
    },
    back() {
      this.$emit('back')
    },
    handleCommand(command) {
      if (command == 'add') {
        this.addFormParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
        }
      } else if (command == 'edit') {
        this.editFormParam = {
          visible: true,
          title: '编辑',
          id: '',
          key: Date.now(),
          infoDisabled: false,
        }
      } else {
        // this.infoFormParam = {
        //   visible: true, title: '详情',
        //   id: '',
        //   key: Date.now(),
        //   infoDisabled: true
        // }
      }
    },
    attributeChange(e) {
      const data = this.customList.find((r) => r.key == e.key)
      const newData = { ...data, ...e }

      this.customList = this.customList.map(
        (obj) => [newData].find((o) => o.key === obj.key) || obj
      )

      // this.$set(this.customList, this.formItemIndex, obj)
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }
      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    // 归属项目
    async getProjectList() {
      const res = await queryListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'projectId', res.data)
    },
    getFactory(e) {
      var obj = _.clone(e)

      const customMap = {
        sourceCode: this.sourceList, // 来源
        priorityCode: this.prioritList, // 优先级
        envCode: this.envList,
      }
      // if (!obj.isBuilt) {
      //   obj.state = true
      // }
      // obj.isBuilt = obj.isBuilt
      obj.state = true
      obj.isBasic = false
      obj.isExport = false
      obj.isImport = false
      obj.isSearch = false
      // if (!obj.type == 'user') {
      // obj.max = JSON.parse(e.config)?.max

      obj.placeholder = JSON.parse(e.config)?.placeholder
      obj.multiple = JSON.parse(e.config)?.multiple
      obj.message = JSON.parse(e.config)?.message
      obj.validator = JSON.parse(e.config)?.validator
      obj.defaultTime = JSON.parse(e.config)?.defaultTime
      obj.options =
        e.isBuilt && customMap[e.key]
          ? customMap[e.key]
          : JSON.parse(e.config)?.options
      obj.precision = JSON.parse(e.config)?.precision
      obj.defaultValue = JSON.parse(e.config)?.defaultValue
      obj.relationShipsheet = JSON.parse(e.config)?.relationShipsheet
      obj.relationField = JSON.parse(e.config)?.relationField
      obj.queryCriteriaA = JSON.parse(e.config)?.queryCriteriaA
      obj.queryCriteriaB = JSON.parse(e.config)?.queryCriteriaB
      obj.queryCriteriaC = JSON.parse(e.config)?.queryCriteriaC

      if (obj.type == 'LINKED') {
        this.getLinkedSource(obj)
      }

      if (obj.isBuilt) {
        this.customList.forEach((element) => {
          if (element.key == obj.key) {
            this.$set(element, 'state', true)
          }
        })
      } else {
        this.customList.push(obj)
      }
    },
    async getFormProperty() {
      this.pageLoading = true

      try {
        this.pageLoading = true
        this.customList = []
        this.customFormRules = {}
        const res = await apiBaseFormProperty(
          this.formId || this.$route.query.formId
        )
        this.pageLoading = false
        if (!res.isSuccess) {
          return
        }

        if (!res.data) return
        this.originForm = res.data

        if (!res.data || res.data.customFormFields.length == 0) {
          return
        }
        res.data.customFormFields.forEach((element) => {
          element.placeholder = JSON.parse(element.config)?.placeholder
          element.multiple = JSON.parse(element.config)?.multiple
          element.message = JSON.parse(element.config)?.message
          element.validator = JSON.parse(element.config)?.validator
          element.defaultTime = JSON.parse(element.config)?.defaultTime
          element.relationShipsheet = JSON.parse(
            element.config
          )?.relationShipsheet
          element.relationField = JSON.parse(element.config)?.relationField
          element.queryCriteriaA = JSON.parse(element.config)?.queryCriteriaA
          element.queryCriteriaB = JSON.parse(element.config)?.queryCriteriaB
          element.queryCriteriaC = JSON.parse(element.config)?.queryCriteriaC
          if (!element.isBuilt) {
            element.options = JSON.parse(element.config)?.options || []
          }
          element.type = element.type.code
          element.precision = JSON.parse(element.config)?.precision
          element.defaultValue = JSON.parse(element.config)?.defaultValue
          if (
            element.type == 'SELECT' ||
            element.type == 'LINKED' ||
            element.type == 'INT'
          ) {
            this.$set(
              this.form,
              element.key,
              JSON.parse(element.config)?.defaultValue
            )
          }
          if (element.type == 'LINKED') {
            this.getLinkedSource(element)
          }
          var arry = [
            'productVersionId',
            'productModuleFunctionId',
            'productRepairVersionId',
          ]
          if (arry.indexOf(element.key) > -1) {
            element.disableSearch = true
          }
        })

        // 固定属性
        const fixed =
          res.data.customFormFields.filter((r) => r.isBasic && r.state) || []

        this.allFixed = fixed
        // 显示列不展示附件，描述，编码,标题等字段
        const hideItem = ['files', 'description', 'name']
        const filterList = fixed.filter((item) => {
          return hideItem.indexOf(item.key) == -1
        })

        this.fixedProperty = filterList.sort(function (a, b) {
          return a.sort - b.sort
        })
        // 自定义属性---------------------------------------------------------------------
        const custom = res.data.customFormFields.filter((r) => !r.isBasic) || []
        // 排序
        this.customList = custom.filter(
          (r) => r.key != 'delay' && r.key != 'rateProgress'
        )
        // sort(function(a, b) {
        //   return a.sort - b.sort
        // })

        // 如果自定义属性为空，右侧配置面板也为空
        if (!this.customList.length) {
          this.showItem = false
        }
        // 自定义属性的校验规则
        const advanceRoule = this.customList.map((r) => ({
          required: r.isRequired,
          message: r.message,
          max: r.max,
          pattern: r.validator,
          key: r.key,
          trigger: 'blur',
        }))

        advanceRoule.forEach((item) => {
          if (!this.customFormRules[item.key]) {
            this.customFormRules[item.key] = [item]
          } else {
            this.customFormRules[item.key].push(item)
          }
        })

        // 默认赋值当前登录人
        const userInfo = storage.get('user')
        this.$set(this.fixedForm, 'handleBy', '-1')
        this.$set(this.fixedForm, 'tagId', '-1')
        this.$set(this.form, 'putBy', userInfo.id)
        this.$set(this.form, 'leadingBy', userInfo.id)

        this.$set(this.fixedForm, 'planEtime', new Date())
        this.$set(this.fixedForm, 'endTime', new Date())
        this.$set(this.fixedForm, 'expectedTime', new Date())
      } catch (e) {
        this.pageLoading = false
      }
    },
    // 查询需求分类
    async getIssueType() {
      const res = await getAlmGetTypeNoPage(this.typeClassify)
      if (!res.isSuccess) {
        return
      }
      this.setData(this.fixedProperty, 'typeCode', res.data)
      this.$set(
        this.fixedForm,
        'typeCode',
        res.data.length ? res.data[1].code : null
      ) // 默认类型
    },
    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.setData(this.fixedProperty, 'stateCode', res.data)
      this.$set(
        this.fixedForm,
        'stateCode',
        res.data.length ? res.data[0].code : null
      ) // 默认状态
    },
    async getEnvList() {
      const res = await apiBaseDictNoPage({
        type: 'ENVIRONMENT',
      })
      if (!res.isSuccess) {
        return
      }
      this.envList = res.data
      this.setData(this.customList, 'envCode', res.data)
    },
    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeClassify,
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
      this.setData(this.customList, 'sourceCode', res.data)
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()

      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.customList, 'priorityCode', res.data)
    },
    // 关联用户需求
    async getIdeaList() {
      const res = await ideaListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'ideaId', res.data)
    },
    changeLink(val) {
      this.getLinkedSource(val)
    },
    // 查询关联类型字段的数据源值
    async getLinkedSource(val, type) {
      if (!val.relationShipsheet) {
        return
      }
      const res = await getSourceById(val.relationShipsheet)
      if (!res.isSuccess) {
        return
      }

      const res1 = await getSourceFormData(val.relationShipsheet)
      if (!res1.isSuccess) {
        return
      }
      const filedId = res.data.fields.find((r) => r.primary).id
      var newArry = []
      newArry = res1.data.map((e) => {
        var obj = {}
        obj.id = e.id
        obj.name = e.columns[filedId]
        return obj
      })

      this.setData(this.customList, val.key, newArry)
    },

    // 新增
    newProperty() {
      this.addParam = {
        visible: true,
        typeClassify: this.typeClassify,
        formId: this.formId,
        customList: this.customList,
      }
    },
    // 编辑
    editItem(item, index) {
      this.currentNodekey = item.key
      this.showItem = true
      this.itemInfo = item
    },
    // 删除
    async delItem(row, index) {
      await this.$confirm(
        `当前数据为自定义属性,删除后不可恢复,是否删除当前数据?`,
        '提示',
        {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false,
        }
      )

      this.sortList.splice(
        this.sortList.findIndex((i) => i.key === row.key),
        1
      )

      this.saveForm()
    },
    async changeState(row, index) {
      this.$confirm(
        `【${row.name}】系统内置属性,禁用后需重新启用,确定禁用吗?`,
        '提示',
        {
          confirmButtonText: '确认',
          type: 'warning',
          closeOnClickModal: false,
        }
      )
        .then(async () => {
          this.saveForm(row.key)
          this.showItem = false
        })
        .catch((e) => {
          return
        })
    },
    async stepMove(evt) {
      // try {
      //   const list = this.sortList.map(r => r.id)
      //   const res = await apiVaBaseFormSortPut(list)
      //   this.customLoading = false
      //   if (!res.isSuccess) {
      //     return
      //   }
      //   this.$message.success('排序更新成功')
      //   await this.getFormProperty()
      //   await this.getOption()
      // } catch (e) {
      //   this.customLoading = false
      // }
    },
    async saveForm(val) {
      this.customLoading = true
      if (val) {
        this.customList.forEach((element) => {
          element.state = element.key == val ? false : element.state
        })
      }
      try {
        this.saveLoading = true
        this.customLoading = true
        const list = this.customList.filter((r) => !r.state)
        this.originForm.customFormFields = [
          ...this.allFixed,
          ...this.sortList,
          ...list,
        ]

        const res = await apiVaBaseCustomFormPut(this.originForm)
        this.saveLoading = false
        this.customLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('操作成功')
        this.showItem = false
        await this.getFormProperty()
        await this.getOption()
      } catch (e) {
        this.saveLoading = false
        this.customLoading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.border {
  border-left: 4px solid var(--main-theme-color);
  padding-left: 10px;
  height: 28px;
  line-height: 28px;
}
:deep(.el-button--default) {
  border: 1px solid #ced1d9;
  color: #6b7385;
}
.rowBox {
  margin: 20px;
  padding: 20px;
}
.icon {
  font-size: 22px;
}
:deep(.el-col-18) {
  border: 1px solid var(--el-divider);
  border-radius: 5px;
}
:deep(.el-col-6) {
  .el-form-item__label {
    padding-left: 12px;
  }
}

:deep(.el-input .el-input--small .el-input--suffix .is-focus) {
  border: none !important;
}
:deep(.el-date-editor.el-input) {
  width: 100%;
}

.el-col-6 + .el-col-6 {
  margin-bottom: 20px;
}

.basicHeader {
  height: 100px;
  padding: 12px 20px 24px 20px;

  margin: 0 !important;

  .el-col-6 {
    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;
      i,
      .svg-icon {
        font-size: 42px;
        // margin-right: 10px;
      }
      .dataPicker {
        :deep(.el-input__prefix) {
          display: none;
        }
        :deep(.el-input__inner) {
          padding-left: 12px;
        }
      }
    }
    :deep(.el-input--small .el-input__inner) {
      border: none;
      background: none;
    }
    :deep(.el-input__suffix) {
      display: none;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;
    }
    :deep(.el-form-item__label) {
      color: var(--auxiliary-font-color);
      // margin-left: 10px;
    }
    :deep(.el-input--small) {
      font-size: 14px;
    }

    :deep(.el-input.is-disabled .el-input__inner) {
      color: #000 !important;
      font-size: 14px;
    }
  }
}

:deep(.el-divider--horizontal) {
  margin: 10px 0;
}

.header {
  display: flex;
  align-items: center;
  .back {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 30px;
    height: 30px;
    color: var(--main-theme-color);
    box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
    border-radius: 16px;
    margin-right: 10px;
  }
}

.rowLine {
  border-bottom: 1px solid var(--el-divider);
  margin: 0 -16px 16px;
  // padding: 0 16px 16px;
}
.rowCol {
  height: 36px;
  line-height: 36px;
  margin-bottom: 10px;
}
.contentBox {
  display: flex;
  height: calc(100vh - 415px);
  &.hideKey {
    height: calc(100vh - 315px);
  }
  // 左侧
  .customBox {
    padding: 16px 0 16px 16px;
    overflow-y: auto;
    border: 1px solid var(--el-divider);
    border-radius: 5px;
    flex: 1;
    .customBorder {
      display: flex;
      // align-items: stretch;
      // align-content: flex-start;
      justify-content: center;
      flex-wrap: wrap;
      .colBox {
        flex: 1;
        // width: 49%;
        margin-bottom: 16px;
        position: relative;
        margin-right: 16px;
        min-width: calc((100% - 48px) / 2);
        border: 1px dashed var(--col-form-border);
        :deep(.el-form-item) {
          padding: 12px;
          height: 100%;
          border-radius: 2px;
          & :hover {
            cursor: pointer;

            // background-color: #fff;
            // border: none;
          }
        }
        .dataPicker {
          :deep(.el-input__prefix) {
            display: none;
          }
          :deep(.el-input__inner) {
            padding-left: 12px;
          }
        }
        :deep(.el-form-item__content) {
          border: none !important;
        }
        :deep(.el-form-item__label) {
          border: none !important;
          pointer-events: none;
        }
        .delButton {
          position: absolute;
          right: 5%;
          top: 10%;
          display: none;
          color: var(--main-theme-color) !important;
        }
      }
      .colBox:hover {
        cursor: pointer;
        // border: 1px dashed var(--main-theme-color) !important;
        background-color: var(--col-form-hover);
        border: 1px dashed #3e7bfa;
        border-radius: 2px;
      }
      .colBox:selected {
        // border: 1px dashed var(--main-theme-color, #3e7bfa);
        // background-color: var(--col-form-hover);
        // border-radius: 2px;
        :deep(.el-form-item) {
          border: none;
        }
        &:hover {
          border: 1px dashed #3e7bfa;
        }
      }
      .colBox:hover .delButton {
        display: block;
        border: none !important;
      }
    }
  }
  .rightContent {
    width: 265px;
  }
}
.headerRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
.headerTitile {
  font-weight: 500;
}
.headerIin {
  font-weight: 400;
  color: var(--auxiliary-font-color);
  font-size: 12px;
}
</style>
