<template>
  <div class="pageBox">
    <el-row type="flex" class="rowLine">
      <el-col :span="12" class="header">
        <span class="back" @click="back">
          <el-icon class="iconfont"><el-icon-direction-back /></el-icon
        ></span>
        <strong>配置{{ `【${typeName}】` }}表单</strong>
      </el-col>
      <el-col :span="12" style="text-align: right">
        <!-- <el-button :disabled="!workflowNodeId ? true : false">取消</el-button> -->
        <el-button
          type="primary"
          :loading="saveLoading"
          :disabled="!workflowNodeId && !selectLineId ? true : false"
          @click="saveInfo"
          >确定</el-button
        >
      </el-col>
    </el-row>

    <vone-work-flow
      ref="vone-g"
      :xml="xml"
      hide-text-annotation
      single
      :properties-props="{ width: 300 }"
      preview
      @changeTab="changeTab"
      @selectLine="selectLine"
    >
      <div slot="properties">
        <el-row>
          <el-form-item v-loading="tableLoading" label="字段权限">
            <span slot="label" class="label-name">
              <span>字段权限</span>
              <el-dropdown trigger="click" @command="statusCommand">
                <el-button class="copy-btn" @click="copybtnFn('statusdropdown')"
                  >复用</el-button
                >
                <el-dropdown-menu
                  ref="statusdropdown"
                  slot="dropdown"
                  class="dropdownPop"
                >
                  <el-dropdown-item
                    v-for="e in statusList"
                    :key="e.onlyId"
                    :command="e"
                  >
                    <span :style="{ color: e.color }">{{ e.name }} </span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
            <el-table :data="formItems" style="width: 100%" class="vone-table">
              <el-table-column
                prop="name"
                label="字段"
                width="140"
                align="center"
                show-overflow-tooltip
              />
              <el-table-column
                prop="isShow"
                width="70"
                label="可见"
                align="center"
              >
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isShow"
                    :disabled="scope.row.isUpdate"
                    @change="checkhandle(scope.$index, scope.row, 'isShow')"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="isUpdate"
                label="可编辑"
                width="90"
                align="center"
              >
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isUpdate"
                    @change="checkhandle(scope.$index, scope.row, 'isUpdate')"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
      </div>

      <div slot="connect">
        <el-form-item label="必填项">
          <span slot="label" class="label-name">
            <span>必填项</span>
            <el-dropdown trigger="click" @command="lineCommand">
              <el-button class="copy-btn" @click="copybtnFn('linedropdown')"
                >复用</el-button
              >
              <el-dropdown-menu
                ref="linedropdown"
                slot="dropdown"
                class="dropdownPop"
              >
                <el-dropdown-item
                  v-for="(e, i) in lineList"
                  :key="i"
                  :command="e"
                >
                  <span :style="{ color: e.start.color }"
                    >{{ e.start.name }}
                  </span>
                  <span :style="{ color: e.end.color }">{{ e.end.name }} </span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
          <requireList
            ref="component"
            type="2"
            :form-fields="formFields"
            :line-id="selectLineId"
            :type-classify="typeClassify"
            :type-code="typeCode"
            :active-name="'fieldAuth'"
          />

          <!-- <component :is="activeName" v-if="activeName" ref="component" :form-fields="formFields" :line-id="selectLineId" :type-classify="typeClassify" :type-code="typeCode" :active-name="activeName" /> -->

          <!-- <el-table :key="Number(new Date())" v-loading="fieldsLoading" :data="formFields" style="width: 100%" class="vone-table">
              <el-table-column prop="name" label="字段" width="140" align="right" show-overflow-tooltip />
              <el-table-column prop="flowRequired" label="必填" align="center">

                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.flowRequired" @change="checkhandle(scope.$index,scope.row,'flowRequired')" />
                </template>

              </el-table-column>

            </el-table> -->
        </el-form-item>
      </div>
    </vone-work-flow>
  </div>
</template>

<script>
// import { DirectionBack as ElIconDirectionBack } from '@element-plus/icons-vue'

import {
  getFlow,
  saveProjectWorkflowNodeCustomFormField,
} from '@/api/vone/base/work-flow'
import { apiBaseFormProperty } from '@/api/vone/base/index'
import { apiVaBaseCustomFormField } from '@/api/vone/base/customForm'
import { getRequiredFields, saveRequiredFields } from '@/api/vone/alm'
import requireList from '@/views/vone/project/setting/tab/config-info/form-flow/tab/fieldAuth.vue'

import _ from 'lodash'
import { jsonToXml } from './xmlUtils'
export default {
  components: {
    requireList,
    // ElIconDirectionBack,
  },
  props: {
    flowId: {
      type: String,
      default: undefined,
    },
    allcustom: {
      type: Object,
      default: () => {},
    },
    customFormId: {
      type: String,
      default: undefined,
    },
    typeClassify: {
      type: String,
      default: undefined,
    },
    typeCode: {
      type: String,
      default: undefined,
    },
    typeName: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      isNode: false,
      fieldsLoading: false,
      formFields: [],
      allNode: [],
      workflowId: '',
      nodeStyle: {
        width: 200,
        height: 36,
      },
      saveLoading: false,
      stateCode: '',
      workflowNodeId: '',
      xml: undefined,
      formItems: [],
      isIndeterminate: true,
      address: null,
      tableLoading: false,
      selectLineId: null,
      statusList: [],
      lineList: [],
    }
  },
  mounted() {
    this.getFlow()
    this.getFormItems()
  },
  methods: {
    copybtnFn(e) {
      this.$refs[e].showPopper = !this.$refs[e].showPopper
    },
    statusCommand(e) {
      this.getFormAuth(e.stateCode)
    },
    lineCommand(e) {
      this.getLineRequired(e.onlyId)
    },
    async changeTab(val) {
      this.isNode = true
      this.workflowNodeId = val.onlyId
      this.stateCode = val.stateCode
      this.getFormAuth()
    },
    // 获取线上的必填项
    selectLine(val) {
      this.isNode = false

      this.selectLineId = val.onlyId
      this.getLineRequired(val.onlyId)
    },
    async getLineRequired(id) {
      try {
        this.fieldsLoading = true
        const { data, isSuccess, msg } = await getRequiredFields({
          projectId: this.$route.params.id,
          rule: 'NOT_NULL',
          transitionId: id,
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
        })
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }

        if (data.length) {
          const checkId = data.map((r) => r.field)

          this.formFields.forEach((element) => {
            element.flowRequired =
              element.id == 0 && checkId.length == this.formFields.length - 1
                ? true
                : checkId.indexOf(element.key) !== -1

            // element.flowRequired = !!checkId.includes(element.key)
          })
        } else {
          this.formFields.forEach((element) => {
            element.flowRequired = false
          })
        }
        this.fieldsLoading = false
      } catch (e) {
        this.fieldsLoading = false
      }
    },
    // 保存连线上的必填字段
    async saveLineRequiredFields() {
      try {
        this.saveLoading = true
        const list = this.$refs.component.fieldData.filter(
          (v) => v.name !== '全选'
        )
        const checkList = list.filter((r) => r.flowRequired)
        const checkLists = checkList.map((r) => ({
          field: r.key,
          fieldId: r.id,
          projectId: this.$route.params.id || null,
          rule: 'NOT_NULL',
          transitionId: this.selectLineId,
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
        }))
        const params = {
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
          checkList: checkLists,
        }
        const { isSuccess, msg } = await saveRequiredFields(
          this.selectLineId,
          params
        )
        this.saveLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }

        this.$message.success('保存成功')
        this.$refs.component.getLineRequired(this.selectLineId)
      } catch (e) {
        this.saveLoading = false
      }
    },

    saveInfo() {
      if (this.isNode) {
        this.saveConfig()
      } else {
        this.saveLineRequiredFields()
      }
    },
    // 保存节点上的字段权限
    async saveConfig() {
      try {
        this.saveLoading = true

        const customFormList = this.formItems.filter((v) => v.name !== '全选')

        const workflowNodeCustomFormFieldConfigs = customFormList.map((v) => {
          return Object.assign(
            {},
            {
              customFormFieldId: v.id,
              isShow: v.isShow,
              isUpdate: v.isUpdate,
              stateCode: this.stateCode,
              workflowNodeId: this.workflowNodeId,
            }
          )
        })
        const params = {
          customFormId: this.customFormId,
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
          workflowId: this.workflowId,
          workflowNodeCustomFormFieldConfigs: [
            ...workflowNodeCustomFormFieldConfigs,
          ],
        }
        const res = await saveProjectWorkflowNodeCustomFormField(params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('配置成功')
      } catch (e) {
        this.saveLoading = false
      }
    },
    // 全选
    checkhandle(index, val, key) {
      if (val.isUpdate) {
        this.formItems[index].isShow = true
      }

      if (index == 0) {
        // 全选

        if (val[key]) {
          if (key == 'isUpdate') {
            this.formItems.forEach((element) => {
              element.isShow = true
              element.isUpdate = true
            })
            return
          }
          this.formItems.forEach((element) => {
            element[key] = true
          })
        } else {
          this.formItems.forEach((element) => {
            element[key] = false
          })
        }
      } else {
        // 节点字段配置table
        const customFormList = this.formItems.filter((v) => v.name !== '全选')
        const allCheck = this.formItems.find((v) => v.name == '全选')
        const allRequire = this.formFields.find((v) => v.name == '全选')
        this.$set(
          allCheck,
          'isShow',
          customFormList.every((j) => j.isShow)
        )
        this.$set(
          allCheck,
          'isUpdate',
          customFormList.every((j) => j.isUpdate)
        )
        // 连线上必填字段table
        const reqireFields = this.formFields.filter((v) => v.name !== '全选')
        this.$set(
          allRequire,
          'flowRequired',
          reqireFields.every((j) => j.flowRequired)
        )
      }
    },
    back() {
      this.$emit('back')
    },
    async getFormItems() {
      const res = await apiBaseFormProperty(this.customFormId)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      if (res.data && res.data.customFormFields.length) {
        res.data.customFormFields.forEach((element) => {
          element.isShow = !element.isShow ? false : element.isShow
          element.isUpdate = !element.isUpdate ? false : element.isUpdate
          element.flowRequired = false
          element.type = element.type?.code
        })

        const allCheckItem = [
          {
            id: '0',
            name: '全选',
            isShow: false,
            isUpdate: false,
          },
        ]

        const requireCheck = [
          {
            id: '0',
            name: '全选',
            flowRequired: false,
          },
        ]

        const filed = res.data.customFormFields.filter((r) => r.state)
        this.formItems = allCheckItem.concat(filed)
        this.formFields = requireCheck.concat(filed)
      } else {
        this.formItems = []
        this.formFields = []
      }
    },
    async getFormAuth(state) {
      this.tableLoading = true
      const params = {
        typeCode: this.typeCode,
        stateCode: state || this.stateCode,
      }
      const res = await apiVaBaseCustomFormField(this.typeClassify, params)
      this.tableLoading = false
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      // 获取状态为true的数据
      const data = res.data.filter((r) => r.state)
      const checkIsShowData = data.filter((r) => r.isShow).map((r) => r.id)
      const checkIsupdateData = data.filter((r) => r.isUpdate).map((r) => r.id)

      this.formItems.forEach((element) => {
        element.isShow =
          element.id == 0 && checkIsShowData.length == this.formItems.length - 1
            ? true
            : checkIsShowData.indexOf(element.id) !== -1
        element.isUpdate =
          element.id == 0 &&
          checkIsupdateData.length == this.formItems.length - 1
            ? true
            : checkIsupdateData.indexOf(element.id) !== -1
        element.flowRequired = false
      })
    },
    progressChange(element, v) {
      element.el.businessObject.set('progress', v)
    },
    typeChange(element, v) {
      element.el.businessObject.set('nodeType', v)
    },
    userChange(element, v) {
      element.el.businessObject.set('permission', v)
    },
    async getFlow() {
      let nodes = []
      let edges = []
      await getFlow(this.flowId).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
        } else {
          if (
            res.data.workflowNodes.length > 0 &&
            res.data.workflowTransitions.length > 0
          ) {
            this.workflowId = res.data.workflowNodes[0].workflowId
            nodes = _.cloneDeep(res.data.workflowNodes)
            edges = _.cloneDeep(res.data.workflowTransitions)
            nodes.map((item) => {
              item.nodeType = item.nodeType.code
              item.color = item.echoMap?.stateCode?.color
              item.onlyId = item.id
              item.id = item.code
              delete item.shape
              delete item.size
              delete item.echoMap
            })
            this.statusList = nodes
            edges.map((item) => {
              item.onlyId = item.id
              item.id = item.code
              item.source = item.sourceAnchor
              item.target = item.targetAnchor
              const permissions = []
              res.data.workflowAuthorities.map((itm) => {
                // if (itm.transitionId == item.onlyId) {
                //   permissions.push(itm.type.code)
                // }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'RESPONSIBLE'
                ) {
                  permissions.push(itm.type.code)
                }
                if (
                  itm.transitionId == item.onlyId &&
                  itm.type.code == 'HANDLER'
                ) {
                  permissions.push(itm.type.code)
                }
              })
              item.permission = permissions
            })
            this.lineList = edges.map((e) => {
              e.start = nodes.find((i) => i.id == e.source)
              e.end = nodes.find((i) => i.id == e.target)
              return e
            })
            this.xml = jsonToXml({ nodes, edges })
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dropdownPop {
  // z-index: 3000 !important;
  max-height: 350px;
  overflow: hidden !important;
  overflow-y: auto !important;
}
.label-name {
  width: 280px;
  padding-right: 20px;
  display: inline-block;
  display: flex;
  display: flex;
  justify-content: space-between;
  .copy-btn {
    margin-left: 100;
    margin-bottom: 10px;
  }
}

:deep(.bpmn) {
  height: calc(100vh - 130px);
}

.pageBox {
  height: calc(100vh - 130px);
  :deep(.el-card__body) {
    padding: 20px 0;
  }
  :deep(.el-form--label-top .el-form-item__label) {
    margin-left: 20px;
    font-weight: bold;
  }
  .rowLine {
    border-bottom: 1px solid var(--el-divider);
    min-height: 32px;
    line-height: 32px;
    margin: 0 -16px 16px;
    padding: 0 16px 16px;
  }
  .header {
    display: flex;
    align-items: center;
    gap: 0 18px;
    .back {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      width: 30px;
      height: 30px;
      color: var(--main-theme-color);
      box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
      border-radius: 16px;
    }
  }
}

:deep(.el-table__body-wrapper) {
  order: 1;
}
:deep(.el-table th.el-table__cell.is-leaf) {
  color: var(--auxiliary-font-color);
}

:deep(.el-table) {
  border: 0;
  th,
  tr,
  td {
    border: 0;
    background-color: #fff;
  }
  &::before {
    height: 0px;
  }
  &::after {
    width: 0;
  }
  .el-table__fixed:before {
    height: 0;
  }
}
</style>
