<template>
  <vone-drawer v-model="visible" size="lg" :before-close="onClose">
    <div slot="title" class="drawer-title">
      <div class="drawer-title-text">
        {{ `引擎【${basicData.name}】详情` }}
        <el-icon class="iconfont nextBtn"
          ><el-icon-yibiaopan-shangyi
        /></el-icon>

        <el-icon class="iconfont nextBtn"><el-icon-yibiaopan-xiayi /></el-icon>
      </div>
    </div>
    <div v-loading="pageLoading" class="pageBox">
      <div class="title">
        <strong> 基本信息 </strong>
      </div>
      <div class="formBox">
        <vone-desc :column="2">
          <vone-desc-item label="名称">
            {{ basicData.name }}
          </vone-desc-item>
          <vone-desc-item label="分类">
            <span
              v-if="
                basicData.classify &&
                basicData.echoMap &&
                basicData.echoMap.classify
              "
              >{{ basicData.echoMap.classify.name }}</span
            >
            <span v-else>{{ basicData.classify }}</span>
          </vone-desc-item>
          <vone-desc-item label="引擎类型">
            <span
              v-if="
                basicData.instance &&
                basicData.echoMap &&
                basicData.echoMap.instance
              "
              >{{ basicData.echoMap.instance.name }}</span
            >
            <span v-else>{{ basicData.instance }}</span>
          </vone-desc-item>
          <vone-desc-item v-if="basicData.engineLoginName" label="用户名">
            {{ basicData.engineLoginName }}
          </vone-desc-item>
          <vone-desc-item label="巡检策略">
            {{ basicData.patrolStrategy }} 分钟
          </vone-desc-item>
          <vone-desc-item label="创建人">
            <span
              v-if="
                basicData.createdBy &&
                basicData.echoMap &&
                basicData.echoMap.createdBy
              "
            >
              <vone-user-avatar
                :avatar-path="basicData.echoMap.createdBy.avatarPath"
                :name="basicData.echoMap.createdBy.name"
              />
            </span>
          </vone-desc-item>
          <vone-desc-item label="引擎访问url">
            {{ basicData.engineUrl }}
          </vone-desc-item>
          <vone-desc-item label="描述">
            {{ basicData.description }}
          </vone-desc-item>
        </vone-desc>
      </div>
      <div class="title">
        <strong> 关联信息 </strong>
      </div>

      <div class="formBox">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="维护人" name="owner">
            <vxe-table
              ref="engine-info-table"
              class="vone-vxe-table"
              border
              resizable
              show-overflow="tooltip"
              :empty-render="{ name: 'empty' }"
              :data="ownerData"
              :column-config="{
                minWidth: '120px',
              }"
            >
              <vxe-column
                field="userId"
                show-overflow-tooltip
                title="维护人名称"
              >
                <template #default="scope">
                  <span
                    v-if="
                      scope.row.userId &&
                      scope.row.echoMap &&
                      scope.row.echoMap.userId
                    "
                  >
                    <vone-user-avatar
                      :avatar-path="scope.row.echoMap.userId.avatarPath"
                      :name="scope.row.echoMap.userId.name"
                    />
                  </span>
                </template>
              </vxe-column>
              <vxe-column
                field="accendantId"
                show-overflow-tooltip
                title="维护人帐号"
              >
                <template #default="scope">
                  <span
                    v-if="
                      scope.row.userId &&
                      scope.row.echoMap &&
                      scope.row.echoMap.userId
                    "
                  >
                    {{ scope.row.echoMap.userId.account }}
                  </span>
                </template>
              </vxe-column>
              <vxe-column
                field="orgName"
                show-overflow-tooltip
                title="所属机构"
              >
                <template #default="scope">
                  <span
                    v-if="
                      scope.row.userId &&
                      scope.row.echoMap &&
                      scope.row.echoMap.userId &&
                      orgMap[scope.row.echoMap.userId.orgId]
                    "
                  >
                    {{ orgMap[scope.row.echoMap.userId.orgId].name }}
                  </span>
                </template>
              </vxe-column>
            </vxe-table>
          </el-tab-pane>
          <el-tab-pane
            v-if="advanceTypes.indexOf(basicData.instance) == -1"
            label="高级属性"
            name="advance"
          >
            <el-table :data="advanceData" class="vone-table">
              <template>
                <!-- <el-table-column prop="attributesName" label="名称" /> -->
                <el-table-column prop="key" label="名称" />
                <el-table-column prop="value" label="值" />
              </template>
            </el-table>
          </el-tab-pane>
          <el-tab-pane
            v-if="
              basicData.instance && basicData.instance.code == 'JENKINS_MASTER'
            "
            label="节点信息"
            name="engineIp"
          >
            <vxe-table
              ref="engine-info-table"
              class="vone-vxe-table"
              border
              resizable
              show-overflow="tooltip"
              :empty-render="{ name: 'empty' }"
              :data="engineIpData"
              :column-config="{
                minWidth: '120px',
              }"
            >
              <vxe-column field="engineIp" show-overflow-tooltip title="节点IP">
                <template #default="scope">
                  {{ scope.row.engineIp }}
                </template>
              </vxe-column>
              <vxe-column field="instance" show-overflow-tooltip title="类型">
                <template #default="scope">
                  <span v-if="scope.row.instance">
                    {{ scope.row.instance.desc }}
                  </span>
                </template>
              </vxe-column>
              <vxe-column
                field="hostId"
                show-overflow-tooltip
                title="所属服务器"
              >
                <template #default="scope">
                  <span v-if="scope.row.hostId && hostMap[scope.row.hostId]">
                    {{ hostMap[scope.row.hostId] }}
                  </span>
                </template>
              </vxe-column>
              <vxe-column
                field="engineLoginName"
                show-overflow-tooltip
                title="用户名"
              />
              <vxe-column
                field="description"
                show-overflow-tooltip
                title="描述"
              />
            </vxe-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
    </div>
  </vone-drawer>
</template>

<script>
import {
  YibiaopanShangyi as ElIconYibiaopanShangyi,
  YibiaopanXiayi as ElIconYibiaopanXiayi,
} from '@element-plus/icons-vue'

import {
  apiBaseEngineInfoGetById,
  apiBaseEngineNoPage,
} from '@/api/vone/base/engine'
import { apiCmdbHostNoPage } from '@/api/vone/cmdb/host'

import { apiBaseOrgNoPage } from '@/api/vone/base/org'

export default {
  components: {
    ElIconYibiaopanShangyi,
    ElIconYibiaopanXiayi,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: null,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentIndex: undefined, // 当前数据的索引
      pageLoading: false,
      advanceData: [],
      engineIpData: [],
      ownerData: [],
      basicData: {},
      activeName: 'owner',
      tags: [],
      advanceTypes: [
        'TEM-Vcenter',
        'GitLab',
        'ScriptLibrary',
        'DBExecutor',
        'LDAP',
        'ScmAgent',
      ],
      orgMap: {},
      hostMap: {},
    }
  },
  watch: {
    visible(v) {
      if (!v) {
        this.activeName = 'owner'
        return
      }
    },
    activeName(val) {
      val == 'engineIp' ? this.getHostMap() : null
      val == 'engineIp' ? this.getJenkinsMaster() : null
    },
  },
  mounted() {
    this.currentIndex = this.tableList.findIndex((item) => item.id === this.id)
    this.activeName = 'owner'
    this.getBasicInfo()
    this.getOrg()
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    async getOrg() {
      const { data, isSuccess, msg } = await apiBaseOrgNoPage()
      if (!isSuccess) {
        return this.$message.error(msg)
      }
      this.orgMap = data.reduce((r, v) => (r[v.id] = v) && r, {})
    },
    // 查询节点信息
    async getJenkinsMaster() {
      const res = await apiBaseEngineNoPage({
        parentId: this.id,
        queryJenkinsSlave: true,
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.engineIpData = res.data
    },
    async getHostMap() {
      const res = await apiCmdbHostNoPage()
      if (!res.isSuccess) {
        return
      }
      this.hostMap = res.data.reduce((r, v) => (r[v.id] = v.ip) && r, {})
    },
    handleClick() {},
    // 基本信息
    async getBasicInfo(val) {
      try {
        this.pageLoading = true
        const { data, isSuccess, msg } = await apiBaseEngineInfoGetById(
          val || this.id
        )
        this.pageLoading = false
        if (!isSuccess) {
          return this.$message.error(msg)
        }
        this.basicData = data

        this.advanceData = data.engineExtendeds
        this.ownerData = data.engineMaintainers
      } catch (e) {
        this.pageLoading = false
      }
    },

    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getBasicInfo(this.tableList[this.currentIndex].id)
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++
      this.getBasicInfo(this.tableList[this.currentIndex].id)
    },
  },
}
</script>

<style lang="scss" scoped>
.pageBox {
  padding: 20px;
  .title {
    border-left: 3px solid var(--main-theme-color);
    margin-bottom: 10px;
    padding-left: 10px;
  }
  .formBox {
    padding: 10px;
  }
  :deep(.el-form .el-form-item__label) {
    color: var(--auxiliary-font-color);
  }
  :deep(.el-form-item__content) {
    display: flex;
  }
}
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>
