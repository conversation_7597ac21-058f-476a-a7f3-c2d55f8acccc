<template>
  <el-form
    ref="basicForm"
    :model="basicForm"
    label-position="top"
    :rules="basicFormRules"
  >
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item prop="name" label="名称">
          <el-input
            v-model="basicForm.name"
            placeholder="请输入长度不超过32个字符的名称"
          />
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item prop="classify" label="分类">
          <el-select
            v-model="basicForm.classify"
            :disabled="isEdit"
            filterable
            clearable
            placeholder="请选择分类"
            @change="onTypeChange"
          >
            <el-option
              v-for="item in engineTypeDatas"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item prop="instance" label="引擎类型">
          <el-select
            v-model="basicForm.instance"
            filterable
            :disabled="isEdit"
            clearable
            placeholder="请选择类型"
            @change="onInstanceChange"
          >
            <el-option
              v-for="item in engineCategoryDatas"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item prop="engineMaintainers" label="引擎维护人">
          <vone-remote-user v-model="basicForm.engineMaintainers" multiple />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item prop="engineUrl" label="引擎访问url">
          <el-input
            v-model="basicForm.engineUrl"
            :maxlength="500"
            placeholder="请输入引擎访问url"
            onkeyup="this.value=this.value.replace(/[, ]/g,'')"
          />
          <!-- onkeyup="this.value=this.value.replace(/[, ]/g,'')" -->
        </el-form-item>
      </el-col>

      <el-col v-if="basicForm.instance != 'SALT_STACK'" :span="8">
        <el-form-item prop="hostId" label="服务器">
          <el-select
            v-model="basicForm.hostId"
            placeholder="请选择服务器"
            filterable
          >
            <el-option
              v-for="item in hostDatas"
              :key="item.id"
              :label="item.ip"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row
      v-if="basicForm.instance == 'GIT_LAB' || basicForm.instance == 'GIT_EE'"
      :gutter="24"
    >
      <el-col :span="8">
        <el-form-item label="登录方式">
          <el-radio-group
            v-model="login.way"
            :disabled="basicForm.instance == 'GIT_EE'"
            @change="changeLoginWay"
          >
            <el-radio-button label="账号" />
            <el-radio-button label="令牌" />
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          label="引擎token"
          prop="engineToken"
          :rules="[
            {
              required: login.way != '账号',
              message: '请输入引擎token',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="basicForm.engineToken"
            type="password"
            show-password
            :maxlength="250"
            placeholder="请输入引擎token"
            :disabled="login.way == '账号'"
          />
        </el-form-item>
      </el-col>
      <!-- <el-col v-if="isGitLab" :span="8">
          <el-form-item prop="engineToken" label="token认证">
            <el-input v-model="basicForm.engineToken" show-password type="password" placeholder="请输入用户认证的token" />
          </el-form-item>
        </el-col> -->
      <el-col v-if="isSubversion" :span="8">
        <el-form-item prop="synDepot" label="同步代码仓库">
          <el-switch
            v-model="basicForm.synDepot"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item
          prop="engineLoginName"
          label="用户名"
          :rules="[
            {
              required: login.way != '令牌',
              message: '请输入用户名',
              trigger: 'change',
            },
          ]"
        >
          <el-input
            v-model="basicForm.engineLoginName"
            :disabled="login.way == '令牌'"
            placeholder="请输入引擎登录用户名"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          prop="enginePassword"
          label="密码"
          :rules="[
            {
              required: login.way != '令牌',
              message: '请输入密码',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="basicForm.enginePassword"
            type="password"
            placeholder="请输入引擎登录密码"
            :disabled="login.way == '令牌'"
            @focus="clearPassword"
            @blur="getPassword"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="showTag" :span="8">
        <el-form-item prop="engineTagDatas" label="标签">
          <el-select
            v-model="basicForm.engineTagDatas"
            multiple
            filterable
            clearable
            placeholder="请选择标签"
          >
            <el-option
              v-for="(item, index) in categoryDictTag"
              :key="index"
              :label="item.name"
              :value="item.dictKey"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item prop="description" label="描述">
          <el-input
            v-model="basicForm.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
// import { apiBaseSystemDictSelectByParentKey } from '@/api/base/system/dict/dict'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import { getAllUsersInfo } from '@/api/vone/base/role'

// import cloneDeep from 'lodash/cloneDeep'

export const CATEGORY_DICT_TAG = {
  FTP: 'base_system_tag_product',
  SaltStack: 'base_system_tag_host',
  DBExecutor: 'base_system_tag_db',
}
export default {
  props: {
    engineData: {
      type: Object,
      default: null,
    },
    hostDatas: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      basicForm: {
        // parentId: 0,
        state: true,
        // engineToken: undefined,
        engineMaintainers: [],
      },
      login: {
        way: '账号',
      },
      basicFormRules: {
        engineMaintainers: [
          {
            required: true,
            message: '请选择引擎维护人',
            trigger: 'change',
          },
        ],
        name: [
          {
            required: true,
            message: '请输入长度不超过32个字符的名称',
            trigger: 'change',
            max: 32,
          },
        ],
        classify: [
          {
            required: true,
            message: '请选择引擎分类',
            trigger: 'change',
          },
        ],
        instance: [
          {
            required: true,
            message: '请选择引擎引擎类型',
            trigger: 'change',
          },
        ],
        engineUrl: [
          {
            required: true,
            message: '请输入URL地址',
          },
          {
            pattern:
              '^((https|svn|http|ftp|rtsp|mms)?://)?(([0-9a-z_!~*().&=+$%-]+: )?[0-9a-z_!~*().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-z_!~*()-]+.)*([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].[a-z]{2,6})(:[0-9]{1,4})?((/?)|(/[0-9a-zA-Z_!~*().;?:@&=+$,%#-]+)+/?).*[^/]$',
            message: 'URL不允许以/结尾。必须由访问协议，域名IP+端口组成',
          },
        ],
        description: [
          {
            required: false,
            max: 250,
            message: '请输入不超过250个字符的描述',
          },
        ],
      },
      engineTypeDatas: [],
      engineCategoryDatas: [],
      categoryDictTag: [],
      isEdit: false,
      userData: [],
      hasAdvanced: false,
      advanceRules: {},
      advanceForm: {},
      hostList: [], // 服务器
    }
  },
  computed: {
    showTag() {
      const showTag =
        ['FTP', 'SaltStack', 'DBExecutor'].indexOf(
          this.basicForm.engineCategoryId
        ) !== -1
      if (!showTag) {
        Object.assign(this.basicForm, { engineTagDatas: undefined })
      }
      // this.selectDictTag()
      return showTag
    },
    showHost() {
      // const showHost = this.basicForm.engineCategoryId === "JenkinsMaster";
      const showHost =
        this.basicForm.engineCategoryId === 'JenkinsMaster' ||
        this.basicForm.engineCategoryId === 'FTP'
      if (!showHost) {
        Object.assign(this.basicForm, { hostId: undefined })
      }
      return showHost
    },
    isGitLab() {
      const isGitLab = this.basicForm?.echoMap?.instance?.code === 'GIT_LAB'
      if (!isGitLab) {
        Object.assign(this.basicForm, { engineToken: undefined })
      }
      return isGitLab
    },
    isSubversion() {
      const isSubversion =
        this.basicForm?.echoMap?.instance?.code === 'SUB_VERSION'
      if (!isSubversion) {
        Object.assign(this.basicForm, { synDepot: undefined })
      }
      return isSubversion
    },
  },
  watch: {
    engineData() {
      if (!this.engineData) return
      this.isEdit = true
      const user = this.engineData.engineMaintainers
        ? this.engineData.engineMaintainers.map((r) => r.userId)
        : []

      this.basicForm = this.engineData

      this.$set(this.basicForm, 'engineMaintainers', user)
      this.$set(
        this.basicForm,
        'instance',
        this.engineData.instance ? this.engineData.instance.code : null
      )

      if (this.basicForm.engineToken) {
        this.$set(this.login, 'way', '令牌')
      } else {
        this.$set(
          this.basicForm,
          'enginePassword',
          this.engineData.enginePassword || '******'
        )
      }

      this.getInstanceList()
      // this.basicForm.patrolStrategy = `${this.basicForm.patrolStrategy}`

      // if (this.basicForm.engineTagDatas) {
      //   this.basicForm.engineTagDatas = this.basicForm.engineTagDatas.map(
      //     (t) => t.dictKey
      //   )
      // }

      // if (
      //   ['FTP', 'SaltStack', 'DBExecutor'].indexOf(
      //     this.basicForm.engineCategoryId
      //   ) !== -1
      // ) {
      //   this.selectDictTag()
      // }
    },
    'this.basicForm.typeId': {
      handler: function () {
        this.basicForm.engineCategoryId = undefined
      },
    },
  },
  mounted() {
    this.getUserList()
    this.loadEngineType()
  },
  methods: {
    changeLoginWay(val) {
      this.$nextTick(() => {
        this.$refs['basicForm'].clearValidate()
      })
      this.$set(this.login, 'way', val)
      if (val == '令牌') {
        this.$set(this.basicForm, 'engineLoginName', undefined)
        this.$set(this.basicForm, 'enginePassword', undefined)
      } else if (val == '账号') {
        this.$set(this.basicForm, 'engineToken', undefined)
      }
    },
    // 引擎引擎类型下拉框change事件
    onInstanceChange(val) {
      this.$emit('onCategoryChange', val)
      if (val == 'GIT_EE') {
        // this.$set(this.basicForm, 'enginePassword', undefined)
        this.login.way = '令牌'
      }
    },

    // 查询用户
    async getUserList() {
      const res = await getAllUsersInfo()
      if (!res.isSuccess) {
        return
      }
      this.userData = res.data
    },
    clearPassword() {
      if (this.basicForm.enginePassword === '******') {
        this.basicForm.enginePassword = null
      }
    },
    getPassword() {
      if (!this.basicForm.enginePassword) {
        this.basicForm.enginePassword = '******'
      }
    },
    /**
     * 获取引擎分类下拉框数据
     */
    async loadEngineType() {
      const res = await apiBaseDictNoPage({
        type: 'ENGINE_CLASSIFY',
      })
      if (!res.isSuccess) {
        return
      }
      this.engineTypeDatas = res.data
    },
    /**
     * 引擎分类下拉框change事件
     */
    async onTypeChange(val) {
      this.$emit('classfiyChange', val)

      const ID = this.engineTypeDatas.find((r) => r.code == val).id

      this.$set(this.basicForm, 'instance', '')
      const res = await apiBaseDictNoPage({
        parentId: ID,
      })
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.engineCategoryDatas = res.data
    },
    // 回显时默认查询引擎类型下拉框数据
    async getInstanceList() {
      await this.loadEngineType()
      const ID = this.engineTypeDatas.find(
        (r) => r.code == this.basicForm.classify
      ).id

      const res = await apiBaseDictNoPage({ parentId: ID })
      if (!res.isSuccess) {
        return
      }
      this.engineCategoryDatas = res.data
    },
    /**
     * 类型change事件
     */
    async onCategoryChange(v) {
      this.$emit('onCategoryChange', v)
      if (v === 'SaltStack') {
        this.selectDictTag()
        this.basicForm.engineTagDatas = undefined
      }
      if (v === 'DBExecutor') {
        this.selectDictTag()
        this.basicForm.engineTagDatas = undefined
      }
      if (v === 'JenkinsMaster') {
        // this.$emit("onSelectHost");
      }
    },
    /**
     * 查询服务器标签
     */
    // async selectDictTag() {
    //   const {
    //     data,
    //     success,
    //     message
    //   } = await apiBaseSystemDictSelectByParentKey(
    //     CATEGORY_DICT_TAG[this.basicForm.engineCategoryId]
    //   )
    //   if (!success) {
    //     return this.$message.error(message)
    //   }
    //   this.categoryDictTag = data
    // },
    async getFormData() {
      await this.$refs.basicForm.validate()
      const formData = { ...this.basicForm }
      if (this.engineData !== null && formData.enginePassword === '******') {
        delete formData.enginePassword
      }
      // if (formData.engineTagDatas) {
      //   const map = {}
      //   this.categoryDictTag.forEach((t) => (map[t.dictKey] = t))
      //   formData.engineTagDatas = formData.engineTagDatas.map((key) => {
      //     return {
      //       dictKey: map[key].dictKey,
      //       name: map[key].name,
      //       parentKey: CATEGORY_DICT_TAG[formData.engineCategoryId]
      //     }
      //   })
      // }
      // if (formData.engineTagDatas.length) {
      formData.engineMaintainers = formData.engineMaintainers.map((r) => ({
        userId: r,
      }))
      // }
      return formData
    },
  },
}
</script>
