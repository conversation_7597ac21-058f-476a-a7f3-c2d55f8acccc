<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          table-search-key="noticeTable"
          :model="formData"
          :table-ref="$refs['noticeTable']"
          @getTableData="getTableList"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="输入名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </vone-search-dynamic>
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          :icon="el-icon-setting"
          @click="addNotice"
        >
          新增
        </el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><ElIconSetting /></el-icon
          ></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div :style="{ height: $tableHeight }">
      <vxe-table
        ref="noticeTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
      >
        <vxe-column type="checkbox" width="37" fixed="left" />
        <vxe-column title="索引号" field="code" min-width="150" />
        <vxe-column title="标题" field="title" min-width="80" />
        <vxe-column title="主题分类" field="noticeType" min-width="120" />
        <vxe-column title="发布状态" field="state" min-width="120">
          <template #default="{ row }">
            <el-tag
              v-if="row.state == 2"
              style="
                color: var(--Red-10);
                border-color: var(--Red--50);
                background: var(--Red--50);
              "
              >已撤销</el-tag
            >
            <el-tag
              v-if="row.state == 1"
              style="
                color: var(--Green-10);
                border-color: var(--Green--50);
                background: var(--Green--50);
              "
              >已发布</el-tag
            >
            <el-tag
              v-if="row.state == 0"
              style="color: #8c8c8c; border-color: #f5f5f5; background: #f5f5f5"
            >
              暂存</el-tag
            >
          </template>
        </vxe-column>
        <vxe-column title="发布日期" field="createTime" min-width="180" />
        <vxe-column title="发布人" field="updatedBy" min-width="100">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="
                row.echoMap &&
                row.echoMap.updatedBy &&
                row.echoMap.updatedBy.avatarPath
              "
              :name="
                row.echoMap &&
                row.echoMap.updatedBy &&
                row.echoMap.updatedBy.name
              "
            />
          </template>
        </vxe-column>
        <vxe-column title="联系人" field="liaisoner" min-width="80">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="
                row.echoMap &&
                row.echoMap.liaisoner &&
                row.echoMap.liaisoner.avatarPath
              "
              :name="
                row.echoMap &&
                row.echoMap.liaisoner &&
                row.echoMap.liaisoner.name
              "
            />
          </template>
        </vxe-column>
        <vxe-column field="phone" title="联系电话" min-width="150" />
        <vxe-column field="body" title="发布内容" />
        <vxe-column title="操作" fixed="right" align="center" width="120">
          <template #default="{ row }">
            <el-tooltip
              v-if="row.state !== 1"
              class="item"
              content="发布"
              placement="top"
            >
              <el-button
                type="text"
                :icon="el-icon-setting"
                @click="(e) => allBackout(row, 1)"
              />
            </el-tooltip>
            <el-tooltip v-else class="item" content="撤销" placement="top">
              <el-button
                type="text"
                :icon="el-icon-setting"
                @click="(e) => allBackout(row, 2)"
              />
            </el-tooltip>
            <el-divider v-if="row.state !== 1" direction="vertical" />
            <el-tooltip
              v-if="row.state !== 1"
              class="item"
              content="编辑"
              placement="top"
            >
              <el-button
                type="text"
                :icon="el-icon-setting"
                @click="(e) => editClickRow(row)"
              />
            </el-tooltip>
            <el-divider v-if="row.state !== 1" direction="vertical" />
            <el-tooltip
              v-if="row.state !== 1"
              class="item"
              content="删除"
              placement="top"
            >
              <el-button
                type="text"
                :icon="el-icon-setting"
                @click="(e) => deleteRow(row)"
              />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTableList"
    />
    <addEditDialog
      v-if="addEditDialog"
      v-model="addEditDialog"
      :title="title"
      :click-row="clickRow"
      @success="getTableList"
    />
  </page-wrapper>
</template>

<script>
import { getNoticeList, delNotice, editState } from '@/api/vone/base/notice'
import addEditDialog from './add-edit-dialog.vue'

export default {
  components: {
    addEditDialog,
  },
  data() {
    return {
      formData: {},
      tableData: {
        records: [],
      },
      tableLoading: false,
      addEditDialog: false,
      title: '新增公告',
      clickRow: {},
      actions: [
        {
          name: '批量发布',
          fn: (e) => this.allBackout('', 1),
        },
        {
          name: '批量撤销',
          fn: (e) => this.allBackout('', 2),
        },
        {
          name: '批量删除',
          fn: (e) => this.deleteRow(),
        },
      ],
    }
  },
  methods: {
    async getTableList() {
      this.tableLoading = true
      const pageObj = this.$refs.pagination.exportPages()
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      }
      const res = await getNoticeList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    addNotice() {
      this.addEditDialog = true
      this.title = '新增公告'
      this.clickRow = {}
    },
    // 编辑引擎
    editClickRow(row) {
      this.clickRow = row
      this.addEditDialog = true
      this.title = '编辑公告'
    },
    async deleteRow(row) {
      let Ids = []
      if (row && row.id) {
        Ids = [row.id]
      } else {
        Ids = this.getVxeTableSelectData('noticeTable').map((r) => r.id)
      }
      if (Ids.length == 0) {
        this.$message.warning('请选择数据')
        return
      }

      await this.$confirm(`确定删除吗?`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false,
      })
      const res = await delNotice(Ids)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableList()
    },
    async allBackout(row, type) {
      let Ids = []
      if (row.id) {
        Ids = [row.id]
      } else {
        Ids = this.getVxeTableSelectData('noticeTable').map((r) => r.id)
      }
      if (Ids.length == 0) {
        this.$message.warning('请选择数据')
        return
      }
      let Flag = true
      if (type == 1) {
        this.getVxeTableSelectData('noticeTable').map((item) => {
          if (item.state == 1) {
            Flag = false
            this.$message.warning('已发布的数据不能再次发布')
            return
          }
        })
      }
      if (type == 2) {
        this.getVxeTableSelectData('noticeTable').map((item) => {
          if (item.state == 2) {
            Flag = false
            this.$message.warning('已撤销的数据不能再次撤销')
            return
          }
        })
      }
      if (Flag) {
        await this.$confirm(`确定${type == 1 ? '发布' : '撤销'}吗?`, '删除', {
          type: 'warning',
          showClose: false,
        })
        editState({
          ids: Ids,
          state: type,
        }).then((res) => {
          if (res.isSuccess) {
            this.$message.success('修改成功')
            this.getTableList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
