<template>
  <el-dialog
    class="dialogContainer"
    title="复制筛选器"
    v-model="visible"
    width="456px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form
      ref="copyForm"
      :model="copyForm"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="copyForm.name" placeholder="请输入名称" />
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="copyFilter"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { copyFilterApi } from '@/api/vone/dashboard/filter'
export default {
  props: {
    visible: <PERSON><PERSON><PERSON>,
    rowData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      copyForm: {
        name: '',
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'change' }],
      },
      loading: false,
    }
  },
  mounted() {},
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$emit('update:rowData', null)
    },
    async copyFilter() {
      try {
        await this.$refs.copyForm.validate()
      } catch (e) {
        return
      }
      this.loading = true
      const res = await copyFilterApi(this.rowData.id, this.copyForm.name)
      this.loading = false
      if (res.isSuccess) {
        this.$message.success('复制成功')
        this.close()
      } else {
        this.$message.warning(res.msg)
      }
    },
  },
}
</script>
