<template>
  <div>
    <div
      v-for="(item, idx) in conditionGroup.conditions"
      :key="item.key"
      class="custom-form"
    >
      <el-divider v-if="idx != 0" class="group-divider">
        <span style="color: #2c2e36; font-weight: 400">或</span>
      </el-divider>
      <el-row :gutter="32">
        <!-- 动态筛选 -->
        <template v-if="formFields.length > 0">
          <el-col
            v-for="(condition, index) in item.conditions"
            :key="`${condition.field}` + `${index}`"
            :span="12"
            class="form-col"
          >
            <el-select
              v-model="condition.field"
              placeholder="请选择"
              class="form-field"
              filterable
              @change="(val) => fieldChange(idx, val, index, condition)"
            >
              <el-option
                v-for="item in formFieldsData"
                :key="item.id"
                :label="item.name"
                :value="getProperty(item)"
                :disabled="disabledSelectItem(item, idx)"
              />
            </el-select>
            <el-select
              v-model="condition.operator"
              placeholder="请选择"
              class="form-operator"
              @change="(val) => operatorChange(val, condition)"
            >
              <el-option
                v-for="item in operatorData(condition)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <!-- 状态 -->
            <el-select
              v-if="condition.field == 'stateCode'"
              v-model="condition.value"
              placeholder="请选择"
              class="form-item"
              filterable
              multiple
              collapse-tags
            >
              <el-option
                v-for="item in stateList"
                :key="item.value"
                :label="item.name"
                :value="item.code"
              />
            </el-select>

            <el-select
              v-else-if="
                elementType(condition) == 'SELECT' ||
                elementType(condition) == 'LINKED'
              "
              v-model="condition.value"
              placeholder="请选择"
              multiple
              filterable
              collapse-tags
              class="form-item"
              @change="(val) => changeTypeCode(val, idx)"
            >
              <el-option
                v-for="valueItem in condition.selectData"
                :key="valueItem[condition.selectKey]"
                :label="valueItem.name"
                :value="valueItem[condition.selectKey]"
              />
            </el-select>
            <el-input
              v-else-if="
                elementType(condition) == 'INPUT' ||
                elementType(condition) == 'EDITOR'
              "
              v-model="condition.value"
              type="primary"
              class="form-item"
              placeholder="请输入"
            />
            <el-input
              v-else-if="elementType(condition) == 'NUMBER'"
              v-model="condition.value"
              type="number"
              class="form-item"
              placeholder="请输入数字"
            />
            <el-radio-group
              v-else-if="elementType(condition) == 'ICON'"
              v-model="condition.value"
              class="form-item"
            >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
            <template v-else-if="elementType(condition) == 'DATE'">
              <el-date-picker
                v-if="
                  condition.operator != 'BEFORE' &&
                  condition.operator != 'BETWEEN'
                "
                :key="condition.operator"
                v-model="condition.value"
                class="form-item"
                type="datetime"
                placeholder="请选择日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
              <el-date-picker
                :default-time="
                  ['00:00:00', '23:59:59'].map((d) =>
                    dayjs(d, 'hh:mm:ss').toDate()
                  )
                "
                v-if="condition.operator == 'BETWEEN'"
                :key="condition.operator"
                v-model="condition.value"
                class="form-item"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
              <div v-if="condition.operator == 'BEFORE'" class="form-item">
                <el-input-number
                  v-model="condition.value"
                  :min="1"
                  :max="90"
                  controls-position="right"
                  placeholder="请输入"
                  style="width: calc(100% - 30px)"
                />
                <el-tooltip placement="top">
                  <div slot="content">x天内：最多可筛选90天</div>
                  <el-icon
                    class="iconfont"
                    style="font-size: 14px; margin-left: 8px"
                    ><el-icon-tips-info-circle
                  /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <vone-remote-user
              v-else-if="elementType(condition) == 'USER'"
              v-model="condition.value"
              :no-name="false"
              multiple
              class="form-item"
            />
            <el-input
              v-else
              v-model="condition.value"
              type="primary"
              placeholder="请输入"
              class="form-item"
            />
            <span
              v-if="showBtn"
              class="del-btn"
              @click="removeCondition(idx, index)"
            >
              <el-icon class="iconfont"><el-icon-application-delete /></el-icon>
            </span>
          </el-col>
        </template>
      </el-row>
      <div v-if="formFields.length > 0 && showBtn" class="add-btn">
        <span @click="addCondition(idx)">
          <el-icon><el-icon-plus /></el-icon>
          新增筛选条件
        </span>
      </div>
    </div>
    <div
      v-if="formFields.length > 0 && showBtn && type != 'WORK_ITEM'"
      class="add-group"
    >
      <span @click="addGroup">
        <el-icon><el-icon-plus /></el-icon>
        添加条件组
      </span>
    </div>
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle,ApplicationDelete as ElIconApplicationDelete,Plus as ElIconPlus } from '@element-plus/icons-vue'import * as dayjs from 'dayjs';
import { operator } from './filter-utils'
import { queryFilterConditions, querySelectData, apFilterFindState } from '@/api/vone/dashboard/filter'
import { getSourceFormData, getSourceById } from '@/api/vone/base/source'
export default {
  components: {

    ElIconTipsInfoCircle,ElIconApplicationDelete,ElIconPlus,
  },
  data() {

return {
  formFields: [],
conditions: [],
showBtn: this.$route.query?.edit,
stateList: [],
typeCodeData: [],
conditionGroup: {
conditions: [{
conditions: [],
linkOperator: 'OR',
key: new Date().valueOf()
}]
},
  dayjs,
}
},
props: {
rules: {
type: Array,
default: () => []
},
filterData: {
type: Array,
default: () => []
},
type: {
type: String,
default: null
},
chooseProject: {
type: Array,
default: () => []
}
// detail: {
//   type: Object,
//   default: () => {}
// }
},
computed: {
elementType() {
return function(condition) {
  return this.formFields.find(item => item?.echoMap?.tableField?.property == condition.field)?.type?.code || null
}
},
disabledSelectItem() {
return function(row, idx) {
  const selectItems = this.conditionGroup.conditions[idx]?.conditions.filter(item => item.field == row?.echoMap?.tableField?.property)
  return selectItems.length > 0
}
},
operatorData() {
const operatorData = operator
return function(condition) {
  const type = this.elementType(condition)
  return operatorData.filter(item => item.types.includes(type))
}
},
formFieldsData() {
return this.formFields.filter(item => !this.rules.includes(item?.echoMap?.tableField?.property))
},
getProperty() {
return function(item) {
  return item?.echoMap?.tableField?.property
}
}
},
watch: {
filterData: {
handler: function(val) {
  // 数据回显逻辑处
  if (val.length) {
    if (this.type == 'WORK_ITEM') {
      this.conditionGroup.conditions[0].conditions = val
    } else {
      this.conditionGroup.conditions = val
    }
    this.$nextTick(() => {
      this.conditionGroup.conditions.forEach((itm, idx) => {
        itm.conditions.map(async(item, index) => {
          if (item.tableName && item.selectKey) {
            const selectData = await this.getAttrSelectData(item.tableName, item.dependentField, idx)

            this.$set(this.conditionGroup.conditions[idx]?.conditions[index], 'selectData', selectData)
          }
          if (item?.relationShipsheet) {
            const selectData = await this.getLinkedData(item.relationShipsheet)
            this.$set(this.conditionGroup.conditions[idx]?.conditions[index], 'selectData', selectData)
          }
        })
      })
    })
  }
},
immediate: true
},
chooseProject: {
immediate: true,
handler: function handler(val) {
  this.getStateCodeList(val)
},
deep: true
}
},
mounted() {
if (!this.type) return
this.getFilterConditions(this.type)
},
methods: {
getFilterConditions(type) {
queryFilterConditions(type).then(res => {
  if (res.isSuccess) {
    const data = res.data.filter(item => item.echoMap?.tableField?.tableName && item?.type?.code != 'FILE' && item?.type?.code != 'QUOTE')
    this.formFields = data
  }
})
},
addGroup() {
this.conditionGroup.conditions.push({
  conditions: [],
  linkOperator: 'OR',
  key: new Date().valueOf()
})
this.$nextTick(() => {
  this.addCondition(this.conditionGroup.conditions.length - 1)
})
},
addCondition(idx) {
const selectField = this.conditionGroup.conditions[idx].conditions.map(item => item.field)
const initField = this.formFieldsData.filter(item => !selectField.includes(item?.echoMap?.tableField?.property))
this.conditionGroup.conditions[idx]?.conditions.push({
  field: initField[0]?.echoMap?.tableField?.property,
  operator: '',
  value: '',
  selectData: [],
  selectKey: '',
  tableName: ''
})
// if (initField[0].key == 'stateCode') {
//   this.getState()
//   return
// }
// 触发字段的change事件
this.fieldChange(
  idx,
  initField[0]?.echoMap?.tableField?.property,
  this.conditionGroup.conditions[idx].conditions.length - 1,
  this.conditionGroup.conditions[idx].conditions[this.conditionGroup.conditions[idx].conditions.length - 1]
)
},
async fieldChange(groupIdx = 0, val, index, condition) {
if (val == 'stateCode') {
  this.getStateCodeList(this.chooseProject, groupIdx)
}
const conditions = this.conditionGroup.conditions[groupIdx].conditions
for (const i in condition) {
  if (i != 'field') {
    this.$set(conditions[index], i, i == 'selectData' ? [] : '')
  }
}
const operatorData = this.operatorData(condition)
operatorData && this.$set(conditions[index], 'operator', operatorData[0]?.value)
const selectField = this.formFields.find(item => val == item?.echoMap?.tableField?.property)
// 存储相关关键字段 用于数据回显使用
const tableName = selectField.echoMap?.tableField?.selectTableName
const selectKey = selectField.echoMap?.tableField?.selectKey || 'id'
const dependentField = selectField?.echoMap?.tableField?.dependentField
this.$set(conditions[index], 'tableName', tableName)
this.$set(conditions[index], 'selectKey', selectKey)
this.$set(conditions[index], 'dependentField', dependentField)
if (selectField?.type?.code == 'SELECT') {
  const configData = JSON.parse(selectField?.config)
  if (tableName && selectKey) {
    const selectData = await this.getAttrSelectData(tableName, dependentField, groupIdx)
    this.$set(conditions[index], 'selectData', selectData)
  } else {
    if (configData?.options) {
      this.$set(conditions[index], 'selectData', configData?.options)
    }
  }
}
if (selectField?.type?.code == 'LINKED') {
  const id = JSON.parse(selectField.config)?.relationShipsheet
  this.$set(conditions[index], 'relationShipsheet', id)
  const selectData = await this.getLinkedData(id)
  this.$set(conditions[index], 'selectData', selectData)
}
},
// 获取自定义属性下拉框数据
async getAttrSelectData(tableName, dependentField, groupIdx) {
// 数据联动
let linkage = {}
if (dependentField) {
  const dependenetData = this.conditionGroup.conditions[groupIdx].conditions.filter(item => item.field == dependentField)
  linkage = {
    'field': dependentField != 'classify' ? dependentField : 'classify',
    'operator': 'IN',
    'value': dependentField != 'classify' ? dependenetData?.[0]?.value : this.type
  }
}
const params = {
  'enableEcho': false,
  'table': tableName,
  'where': dependentField ? [linkage] : []
}
let selectData = []
await querySelectData(params).then(res => {
  selectData = res.data
})
return selectData || []
},
// 获取关联字段下拉框数据
async getLinkedData(id) {
const res = await getSourceById(id)
const selectData = []
if (res.isSuccess) {
  const fixedKey = res.data?.fields?.find(e => e.primary)?.id
  // 关联字段下拉数据
  const fieldRes = await getSourceFormData(id)
  const columns = fieldRes?.data || []
  for (const item of columns) {
    item.columns[fixedKey] && selectData.push({
      name: item.columns[fixedKey],
      id: item.id
    })
  }
}
return selectData
},
removeCondition(groupIdx, index) {
this.conditionGroup.conditions[groupIdx].conditions.splice(index, 1)
this.$nextTick(() => {
  // 如果删除的是最后一个条件 则删除条件组
  if (this.conditionGroup.conditions[groupIdx].conditions.length == 0) {
    this.conditionGroup.conditions.splice(groupIdx, 1)
  }
})
},
// 处理运算符切换日期选择窗报错问题
operatorChange(val, condition) {
const elementType = this.elementType(condition)
if (elementType == 'DATE') {
  condition.value = null
  condition.dateVal && delete condition.dateVal
}
},
changeTypeCode(val, idx) {
this.typeCodeData = val
this.getStateCodeList(this.chooseProject, idx)
},
async getStateCodeList(val, groupIdx) {
this.stateList = []
const projects = val
const typeClassifies = this.type == 'WORK_ITEM' ? [] : [this.type]
const typeCodes = this.conditionGroup.conditions?.[groupIdx]?.conditions?.find(r => r.field == 'typeCode')?.value || []

const res = await apFilterFindState(
  projects,
  typeClassifies,
  typeCodes || this.typeCodeData
)
if (!res.isSuccess) {
  this.$message.warning(res.msg)
  return
}
this.stateList = res.data
}

}
}
</script>

<style lang="scss" scoped>
.custom-form {
  .form-col {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    .form-field {
      width: 130px;
      margin-right: 4px;
    }
    .form-operator {
      width: 90px;
      margin-right: 4px;
    }
    .form-item {
      width: calc(100% - 260px);
      margin-right: 12px;
      :deep(.el-select__tags + .el-input) {
        .el-input__inner {
          height: 32px !important;
        }
      }
    }
    .del-btn {
      cursor: pointer;
      color: var(--font-second-color);
      &:hover {
        color: #db2c3a;
      }
    }
  }

  .add-btn {
    padding-bottom: 8px;
    span {
      color: var(--main-theme-color);
      font-size: 14px;
      cursor: pointer;
    }
  }
  :deep(.group-divider) {
    margin: 4px 0 12px 0;
  }
}
.add-group {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  span {
    color: var(--main-theme-color);
    font-size: 14px;
    cursor: pointer;
  }
  &:hover {
    background: #f5f6f7;
  }
}
:deep() {
  .el-form .el-form-item {
    margin-bottom: 12px;
  }
}
</style>
