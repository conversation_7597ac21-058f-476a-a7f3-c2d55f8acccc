<template>
  <page-wrapper>
    <template>
      <vone-search-wrapper>
        <template slot="search">
          <el-tabs
            v-model="type"
            class="vone-tabs"
            style="margin-right: 12px"
            @tab-click="tabChange"
          >
            <el-tab-pane label="我的" name="my" />
            <el-tab-pane label="收藏夹" name="favorites" />
            <el-tab-pane label="公共" name="common" />
            <el-tab-pane label="分享" name="shared" />
          </el-tabs>
          <el-input
            v-model="searchName"
            style="width: 260px"
            placeholder="请输入筛选器名称查询"
            @change="getFilterData"
          >
            <template slot="prefix">
              <el-icon class="el-input__icon"><el-icon-search /></el-icon>
            </template>
          </el-input>
        </template>
        <template v-if="type == 'my'" slot="actions">
          <el-button
            type="primary"
            :icon="ElIconIconfont elIconIconSystemAdd"
            @click="addFilter"
          >
            新增
          </el-button>
          <el-dropdown trigger="click" @command="(e) => e && e()">
            <el-button class="btnMore"
              ><el-icon class="iconfont"><el-icon-application-more /></el-icon
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="(e) => batchDel()"
                >批量删除</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </vone-search-wrapper>
      <div :style="{ height: $tableHeight }">
        <vxe-table
          ref="filterTable"
          :key="tableKey"
          class="vone-vxe-table"
          height="auto"
          border
          show-overflow="tooltip"
          :loading="loading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ minWidth: '150px', resizable: true }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column
            v-if="type == 'my'"
            type="checkbox"
            width="40"
            fixed="left"
            align="center"
          />
          <vxe-column title="名称" field="name" min-width="150">
            <template #default="{ row }">
              <svg
                v-if="row.collected"
                class="icon svg-icon collect"
                aria-hidden="true"
                @click="collect(row)"
              >
                <use xlink:href="#el-icon-icon-shoucang-on" />
              </svg>
              <svg
                v-else
                class="icon svg-icon collect"
                aria-hidden="true"
                @click="collect(row)"
              >
                <use xlink:href="#el-icon-icon-shoucang-off" />
              </svg>
              <a @click="configFilter(row)">
                {{ row.name }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="权限" field="scope" width="90">
            <template #default="{ row }">
              <el-tag
                v-if="row.scope == 0"
                style="
                  color: var(--Blue-10);
                  background: var(--Blue--50);
                  border-color: var(--Blue--50);
                "
                effect="dark"
                >公有</el-tag
              >
              <el-tag
                v-else
                style="
                  color: var(--Orange-10);
                  background: var(--Orange--50);
                  border-color: var(--Orange--50);
                "
                type="warning"
                effect="dark"
                >私有</el-tag
              >
            </template>
          </vxe-column>
          <vxe-column title="所有者" field="user">
            <template #default="{ row }">
              <vone-user-avatar
                :avatar-path="ownerUser(row).avatarPath"
                :avatar-type="ownerUser(row).avatarType"
                :name="ownerUser(row).name"
              />
            </template>
          </vxe-column>
          <vxe-column title="描述" field="description" />
          <vxe-column
            v-if="type != 'favorites'"
            title="操作"
            fixed="right"
            align="left"
            :width="type != 'my' ? 120 : 160"
          >
            <template #default="{ row }">
              <template v-if="type == 'my'">
                <el-tooltip class="item" content="编辑" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconIconSystemEdit icon_click"
                    @click="editFilter(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="配置" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconApplicationStepSetting"
                    @click="configFilter(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="分享" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconIconLineFenxiang"
                    @click="shareFilter(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="删除" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconIconSystemDelete"
                    @click="delFilter(row)"
                  />
                </el-tooltip>
              </template>
              <template v-else-if="type == 'common'">
                <el-tooltip class="item" content="复制" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconIconFuzhi"
                    @click="copyFilter(row)"
                  />
                </el-tooltip>
              </template>
              <template v-else-if="type == 'shared'">
                <el-tooltip class="item" content="复制" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconIconFuzhi"
                    @click="copyFilter(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="删除" placement="top">
                  <el-button
                    type="text"
                    :icon="ElIconIconfont elIconIconSystemDelete"
                    @click="deleteSharedFilter(row)"
                  />
                </el-tooltip>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </div>

      <vone-pagination
        ref="pagination"
        :total="tableData.total"
        @update="getFilterData"
      />
      <edit-filter
        v-if="visible"
        v-model="visible"
        :row-data="rowData"
        @success="getFilterData"
      />
      <shared-filter
        v-if="shareVisible"
        v-model="shareVisible"
        :row-data="rowData"
        @success="getFilterData"
      />
      <copy-filter
        v-if="copyVisible"
        v-model="copyVisible"
        :row-data="rowData"
        @success="getFilterData"
      />
    </template>
  </page-wrapper>
</template>

<script>
import EditFilter from './edit-filter.vue'
import SharedFilter from './shared-filter.vue'
import CopyFilter from './copy-filter.vue'
// import Config from './config.vue'
import {
  getFilterPage,
  getCollectFilterPage,
  getMyFilterPage,
  collectFilter,
  getSharedFilterPage,
  delFilter,
  delsSaredFilter,
} from '@/api/vone/dashboard/filter'
export default {
  components: {
    EditFilter,
    SharedFilter,
    CopyFilter,
    // Config
  },
  data() {
    return {
      searchName: '',
      type: 'my',
      loading: false,
      tableKey: 'my',
      tableData: {
        records: [],
        total: null,
      },
      visible: false,
      rowData: null,
      shareVisible: false,
      copyVisible: false,
      showConfig: false,
    }
  },
  computed: {
    ownerUser() {
      return function (row) {
        const avatarPath = row.echoMap?.createdBy?.avatarPath || ''
        const avatarType = row.echoMap?.createdBy?.avatarType || ''
        const name = row.echoMap?.createdBy?.name || ''
        return { avatarPath, avatarType, name }
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.getFilterData()
    })
  },
  methods: {
    async getFilterData() {
      this.loading = true
      const pageObj = this.$refs.pagination?.pageObj
      const params = {
        ...pageObj,
        extra: {},
        model: {
          scope: this.type == 'common' ? 0 : '',
          name: this.searchName,
        },
      }

      let res = null
      switch (this.type) {
        case 'favorites':
          res = await getCollectFilterPage(params)
          break
        case 'my':
          res = await getMyFilterPage({
            ...params,
            sort: 'sort',
            order: 'ascending',
          })
          break
        case 'common':
          res = await getFilterPage(params)
          break
        case 'shared':
          res = await getSharedFilterPage(params)
      }
      this.loading = false
      if (res.isSuccess) {
        this.tableData = res.data
      } else {
        this.$message.warning(res.msg)
      }
    },
    tabChange() {
      this.tableData = {}
      this.tableKey = this.type
      this.$refs.filterTable.refreshColumn()
      this.getFilterData()
    },
    collect(row) {
      collectFilter(row.id, !row.collected).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success(!row.collected ? '收藏成功' : '取消收藏成功')
        this.getFilterData()
      })
    },
    addFilter() {
      this.visible = true
    },
    batchDel() {
      const selectData = this.getVxeTableSelectData('filterTable')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.$confirm('是否删除所选筛选器?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        const data = selectData.map((item) => item.id)
        delFilter(data).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getFilterData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    editFilter(row) {
      this.rowData = row
      this.visible = true
    },
    configFilter(row) {
      const path =
        this.type == 'my'
          ? `/filter/config/${row.id}?edit=1&type=${this.type}`
          : `/filter/config/${row.id}?type=${this.type}`
      this.$router.push(path)
    },
    shareFilter(row) {
      this.rowData = row
      this.shareVisible = true
    },
    deleteSharedFilter(row) {
      const params = {
        userIds: [this.$store.state.user.user.id],
        viewIds: [row.id],
      }
      this.$confirm('是否删除当前被分享的筛选器?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        delsSaredFilter(params).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getFilterData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    delFilter(row) {
      this.$confirm('是否删除当前筛选器?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning',
      }).then(() => {
        delFilter([row.id]).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getFilterData()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    copyFilter(row) {
      this.rowData = row
      this.copyVisible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.page-wrapper {
  min-height: calc(100vh - 20px);
}
.collect {
  font-size: 16px;
  cursor: pointer;
}

.el-tag.el-tag--small {
  height: 24px;
  line-height: 22px;
  padding: 0px 8px;
}
</style>
