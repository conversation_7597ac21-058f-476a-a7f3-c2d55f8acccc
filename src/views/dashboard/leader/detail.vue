<template>
  <div id="dashboard-main" class="dashboard-main">
    <div class="dashboard-header">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div class="dashboard-content">
      <div class="title1">
        <div style="flex: 1">
          <span>{{ projectName }}</span>
          <vone-icon-select
            v-model="handleBy"
            select-type="user"
            filterable
            remote
            :loading="remoteLoading"
            :data="userList"
            class="remoteuser"
            style="width: 150px"
            @change="getTableData"
          >
            <el-option
              v-for="i in userList"
              :key="i.id"
              :label="i.name"
              :value="i.id"
              style="width: 100%; display: flex"
            >
              <span>
                <vone-user-avatar
                  :avatar-path="i.avatarPath"
                  :avatar-type="i.avatarType"
                  :show-name="false"
                  height="22px"
                  width="22px"
                />
              </span>
              {{ i.name }}
            </el-option>
          </vone-icon-select>
        </div>
        <div class="buttonbox">
          <el-button
            :class="active == 'list' ? 'active' : ''"
            @click="changeActive('list')"
            >列表</el-button
          >
          <el-button
            :class="active == 'card' ? 'active' : ''"
            @click="changeActive('card')"
            >甘特图</el-button
          >
        </div>
      </div>
      <div v-if="active == 'list'" style="height: calc(100vh - 180px)">
        <vxe-table
          ref="baseUserTable"
          class="vone-vxe-table"
          border
          auto-resize
          height="auto"
          show-overflow="tooltip"
          :loading="pageLoading"
          :empty-render="{ name: 'empty' }"
          :data="data.records"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column title="标题" field="name" fixed="left" min-width="220">
            <template #default="{ row }">
              <a @click="jumpProject(row)">{{ row.name }}</a>
            </template>
          </vxe-column>
          <vxe-column title="工作项类型" width="120">
            <template #default="{ row }">
              <el-icon class="iconfont" style="color: rgb(135, 145, 250)"
                ><el-icon-icon-xuqiu
              /></el-icon>
              <el-icon class="iconfont" style="color: rgb(250, 107, 87)"
                ><el-icon-icon-quexian
              /></el-icon>
              <el-icon class="iconfont" style="color: rgb(62, 123, 250)"
                ><el-icon-icon-renwu
              /></el-icon>
              <span>
                {{ row.sourceType.desc || '' }}
              </span>
            </template>
          </vxe-column>
          <vxe-column title="状态" field="stateCode" width="150">
            <template #default="{ row }">
              <taskStatus v-if="row" :key="Date.now()" :workitem="row" />
            </template>
          </vxe-column>
          <vxe-column field="leadingBy" title="负责人" width="120">
            <template #default="{ row }">
              <vone-user-avatar
                :avatar-path="leadingUser(row).avatarPath"
                :name="leadingUser(row).name"
              />
            </template>
          </vxe-column>
          <vxe-column title="处理人" field="handleBy" width="120">
            <template #default="{ row }">
              <vone-user-avatar
                :avatar-path="handleUser(row).avatarPath"
                :name="handleUser(row).name"
              />
            </template>
          </vxe-column>
          <vxe-column field="putBy" title="提出人" width="120">
            <template #default="{ row }">
              <vone-user-avatar
                :avatar-path="putUser(row).avatarPath"
                :name="putUser(row).name"
              />
            </template>
          </vxe-column>
          <vxe-column title="计划开始时间" field="planStime" width="180">
            <template #default="{ row }">
              <span v-if="row.planStime">
                {{ dayjs(row.planStime).format('YYYY-MM-DD HH:mm') }}
              </span>
              <span v-else>{{ row.planStime }}</span>
            </template>
          </vxe-column>
          <vxe-column title="计划完成时间" field="planEtime" width="180">
            <template #default="{ row }">
              <span v-if="row.planEtime">
                {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
              </span>
              <span v-else>{{ row.planEtime }}</span>
            </template>
          </vxe-column>
          <vxe-column title="创建时间" field="createTime" width="180">
            <template #default="{ row }">
              <span v-if="row.createTime">
                {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
              </span>
              <span v-else>{{ row.createTime }}</span>
            </template>
          </vxe-column>
          <vxe-column title="优先级" field="priorityCode" width="150">
            <template #default="{ row }">
              <vone-icon-select
                v-model="row.priorityCode"
                :data="prioritList"
                filterable
                clearable
                style="width: 100%"
                class="prioritList"
              >
                <el-option
                  v-for="item in prioritList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.code"
                >
                  <i
                    :class="`iconfont ${item.icon}`"
                    :style="{
                      color: item.color,
                      fontSize: '16px',
                      paddingRight: '6px',
                    }"
                  />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
            </template>
          </vxe-column>
        </vxe-table>
        <vone-pagination
          ref="pagination"
          :total="data.total"
          @update="getTableData"
        />
      </div>
      <div v-else>
        <div class="monthBox">
          <div class="yearsbox">
            <el-button
              :icon="el-icon-setting"
              @click="changeYear('down')"
            />
            {{ nowYear + '年' + nowMonth + '月' }}
            <el-button
              :icon="el-icon-setting"
              @click="changeYear('add')"
            />
          </div>
          <el-calendar
            v-model="value"
            :first-day-of-week="7"
            style="margin-top: 16px"
          >
            <template slot="dateCell" slot-scope="{ date, data }">
              <div class="cellInfo">
                <div class="cellInfoBox">
                  <p>{{ data.day.split('-')[2] }}</p>
                </div>
                <div v-for="(item, i) in monthList" :key="i">
                  <div v-if="data.day == item.date">
                    <div
                      v-for="(v, index) in item.items.slice(0, 2)"
                      :key="index"
                      :class="[
                        'planTitle',
                        v.sourceType.code == 'TASK'
                          ? 'task'
                          : v.sourceType.code == 'BUG'
                          ? 'bug'
                          : 'issue',
                      ]"
                    >
                      <span
                        v-if="
                          v.sourceType.code !== null &&
                          statusColor[v.sourceType.code]
                        "
                        class="monthStatus"
                        :style="{
                          background: statusColor[v.sourceType.code].color,
                        }"
                      />
                      <el-tooltip
                        effect="dark"
                        :tabindex="999"
                        :content="v.name"
                        placement="top"
                      >
                        <span>{{ v.name }}</span>
                      </el-tooltip>
                    </div>
                    <el-popover
                      placement="bottom"
                      width="400"
                      trigger="click"
                      popper-class="popper"
                    >
                      <div v-for="(v, index) in item.items" :key="index">
                        <div style="border-bottom: 1px solid #ebeef5">
                          <div
                            :class="[
                              'popTitle',
                              v.sourceType.code == 'TASK'
                                ? 'task'
                                : v.sourceType.code == 'BUG'
                                ? 'bug'
                                : 'issue',
                            ]"
                          >
                            <div class="title">{{ v.code + v.name }}</div>
                          </div>
                        </div>
                      </div>
                      <div
                        v-if="item.items.length > 2"
                        slot="reference"
                        class="moreTitle"
                      >
                        +{{ item.items.length - 2 }}
                      </div>
                    </el-popover>
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getWorkList, getWorkListNoPage } from '@/api/vone/dashboard/leader'
import taskStatus from '@/views/vone/project/common/change-status/index.vue'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { getUserDetail, apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { cloneDeep } from 'lodash'
export default {
  components: { taskStatus },
  data() {
    return {
      data: {},
      pageLoading: false,
      active: 'list',
      cardList: [],
      prioritList: [],
      nowYear: '',
      nowMonth: '',
      monthList: [],
      value: '',
      statusColor: {
        TASK: '#3E7BFA',
        BUG: '#ED5B56',
        ISSUE: '#8474ED',
      },
      userList: [],
      remoteLoading: false,
      handleBy: '',
      projectName: '',
    }
  },
  computed: {
    handleUser() {
      return function (row) {
        return row?.echoMap?.handleBy
      }
    },
    leadingUser() {
      return function (row) {
        return row?.echoMap?.leadingBy
      }
    },
    putUser() {
      return function (row) {
        return row?.echoMap?.putBy
      }
    },
  },
  mounted() {
    this.handleBy = this.$route.query.userId
    this.projectName = this.$route.query.projectName
    this.getTableData()
    this.getPrioritList()
    const data = new Date()
    this.nowYear = data.getFullYear()
    this.nowMonth = data.getMonth() + 1
    if (this.$route.query.latitude == 'org') {
      this.getUserList()
    } else {
      this.getApiProjectUserNoPage()
    }
  },
  methods: {
    async getUserList() {
      const res = await apiBaseAllUserNoPage({ orgId: this.$route.query.orgId })
      if (!res.isSuccess) {
        return
      }
      const userData = cloneDeep(res.data)
      const hasCurrentUser = userData.filter(
        (item) => item.id == this.$route.query.userId
      )
      if (!hasCurrentUser?.length) {
        const userItem = await getUserDetail(this.$route.query.userId)
        if (userItem.isSuccess) {
          userData.unshift(userItem.data)
        }
      }
      this.userList = userData
    },
    async getApiProjectUserNoPage() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.query.projectId,
      })
      if (!res.isSuccess) {
        return
      }
      this.userList = res.data
    },
    getTableData() {
      if (this.active == 'list') {
        const pageObj = this.$refs.pagination?.pageObj || {
          current: 1,
          size: 20,
        }
        const { endDate, startDate, projectId, orgId, latitude } =
          this.$route.query
        const params = {
          ...pageObj,
          extra: {
            ...this.extraData,
          },
          model: {
            handleBy: this.handleBy,
            endDate: endDate,
            startDate: startDate,
            projectIds: latitude == 'project' ? [projectId] : [],
            orgIds: latitude == 'org' ? [orgId] : [],
          },
        }
        getWorkList(params).then((res) => {
          if (res.isSuccess) {
            this.data = res.data
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        const { endDate, startDate, projectId, orgId, latitude } =
          this.$route.query
        getWorkListNoPage({
          endDate: endDate,
          startDate: startDate,
          projectIds: latitude == 'project' ? projectId.split(',') : [],
          orgIds: latitude == 'org' ? orgId.split(',') : [],
          userIds: this.handleBy ? [this.handleBy] : [],
        }).then((res) => {
          if (res.isSuccess) {
            this.monthList = res.data
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
    async jumpProject(row) {
      const workItemType = row.sourceType.code
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr =
        workItemType == 'ISSUE'
          ? '/project/issue'
          : workItemType == 'TASK'
          ? '/project/task'
          : workItemType == 'BUG'
          ? '/project/defect'
          : '/project/issue'
      const newpage = await this.$router.resolve({
        path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`,
        query: {
          showDialog: true,
          queryId: row?.id,
          rowTypeCode: row?.typeCode,
          stateCode: row?.stateCode,
          projectId: projectId,
        },
      })
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    changeYear(type) {
      if (type == 'add') {
        if (this.nowMonth == 12) {
          this.nowYear = this.nowYear + 1
          this.nowMonth = 1
        } else {
          this.nowMonth = this.nowMonth + 1
        }
      } else {
        if (this.nowMonth == 1) {
          this.nowYear = this.nowYear - 1
          this.nowMonth = 12
        } else {
          this.nowMonth = this.nowMonth - 1
        }
      }
      this.value = this.nowYear + '-' + this.nowMonth
      this.getTableData()
    },
    changeActive(type) {
      this.active = type
      this.getTableData()
    },
    goBack() {
      this.$router.push({
        path: '/leaderwork/view',
        query: { type: 1 },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.remoteuser {
  margin-left: 15px;
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
}
.dashboard {
  &-main {
    touch-action: none;
    background: var(--content-bg-color);
  }
  &-header {
    height: 48px;
    line-height: 48px;
    background: var(--main-bg-color);
    padding: 0px 16px;
    margin: -10px -10px 0px -10px;
    box-shadow: var(--nav-top-shadow);
    border: 1px solid var(--solid-border-color);
    display: flex;
    align-items: center;
    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
    :deep(.vone-tabs .el-tabs__nav) {
      border: none;
    }
  }
  &-content {
    background: #fff;
    height: calc(100vh - 68px);
    margin-top: 10px;
    padding: 16px;
    padding-bottom: 10px;
  }
}
:deep(.pagination) {
  position: static;
}
:deep(.el-row) {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
:deep(.el-col) {
  padding-left: 0px !important;
  padding-right: 0px !important;
  // margin-top: 10px;
}
:deep(.el-form .el-form-item) {
  margin-bottom: unset;
}
:deep(.el-form .el-form-item__label) {
  line-height: 32px;
}
:deep(.el-form) {
  display: flex;
}
.bg {
  background: #fff;
  margin: 10px 0px;
  padding: 20px;
}
.btnbox {
  height: 26px;
  width: 26px;
  border: 1px solid var(--input-border-color);
  padding: 0px 4px;
  text-align: center;
  line-height: 26px;
  margin: 70px 20px;
  border-radius: 50%;
}
.childBox {
  max-height: 462px;
  overflow-y: auto;
}
.small-bg {
  background: #f7f7f7;
  border-radius: 4px;
  padding: 10px 12px;
  margin-top: 10px;
}
.title1 {
  padding-bottom: 16px;
  line-height: 24px;
  display: flex;
}
// .buttonbox {
// 	.el-button {
// 		height: 18px;
// 		line-height: 18px;
// 		font-size: 12px
// 	}
// }
.active {
  border: 1px solid var(--main-theme-color);
  color: var(--main-theme-color);
}
// 年
.yearsbox {
  width: 265px;
  height: 32px;
  line-height: 32px;
  display: flex;
  justify-content: space-around;
  margin: 0 auto;
  .el-button {
    padding: 0px 8px;
    min-width: unset;
  }
  .el-button--default {
    border: 1px solid #ced1d9;
    color: #6b7385;
  }
  .el-button + .el-button {
    margin-left: 0px;
  }
}
.monthBox {
  border: 1px solid #ebeef5;
  .datebox {
    padding: 0px 22px;
    margin-top: 10px;
    height: calc(100vh - 190px);
  }
  .el-row {
    height: 100%;
  }
  .el-col {
    padding: 0px 6px !important;
    margin-bottom: 12px;
    height: 31%;
    overflow: hidden;
    cursor: pointer;
    .month {
      height: 100%;
      border: 1px solid #ebeef5;
      .topbox {
        text-align: center;
        font-size: 18px;
        font-weight: 500;
        line-height: 76px;
      }
      .planbox {
        height: 65%;
      }
      .planOut,
      .planIn {
        width: 50%;
        display: inline-block;
        padding: 30px 0px 0px 50px;
        height: 100%;
        // height: calc(23vh - 81px);
        .count {
          font-weight: 500;
          font-size: 24px;
          color: #1d2129;
          line-height: 34px;
          margin-top: 8px;
        }
      }
      .planOut {
        background-color: #f0f7ff;
        border-top: 2px solid #2d6ef7;
        span {
          color: #2d6ef7;
        }
      }
      .planIn {
        background: #f0fff1;
        border-top: 2px solid #2cc750;
        span {
          color: #2cc750;
        }
      }
    }
  }
}
// 隐藏日历上月下月的日期
:deep(.el-calendar-table:not(.is-range) td.next) {
  pointer-events: none;
  // .cellInfo {
  //   opacity: 0;
  // }
}
:deep(.el-calendar-table:not(.is-range) td.prev) {
  pointer-events: none;
  // .cellInfo {
  //   opacity: 0;
  // }
}
:deep(.el-calendar__body) {
  padding: 0px;
}
:deep(.el-calendar-table thead th) {
  padding: 10px 0px;
  background: #fafafa;
  color: #6b7385;
  border-right: 1px solid #eaecf0;
}
:deep(.el-calendar-table thead th:last-child) {
  border-right: none;
}
:deep(.el-calendar-table tr td:first-child) {
  border-left: none;
}
:deep(.el-calendar-table td:last-child) {
  border-right: none;
}

:deep(.el-calendar__header) {
  display: none;
}
:deep(.el-calendar-table .el-calendar-day:hover) {
  background: #fff;
}
:deep(.el-calendar-table td.is-selected) {
  background-color: unset;
}
.popTitle {
  cursor: pointer;
  height: 30px;
  line-height: 30px;
  margin: 12px;
  padding: 0px 8px;
  display: flex;
  justify-content: space-between;
  .title {
    width: 285px;
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .popTags {
    padding: 0px 6px;
    font-size: 12px;
    border: 1px solid;
    display: inline-block;
    height: 22px;
    line-height: 22px;
    margin-top: 4px;
  }
}
.task {
  background-color: #e3f0ff;
  margin: 4px 0px;
}
.bug {
  background-color: #ffe1e0;
  margin: 4px 0px;
}
.issue {
  background-color: #e4e0ff;
  margin: 4px 0px;
}
.planTitle {
  height: 30px;
  line-height: 30px;
  margin-top: 4px;
  padding: 0px 8px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.el-calendar-table .el-calendar-day) {
  height: 145px;
}
.moreTitle {
  background: #f2f3f5;
  color: #838a99;
  line-height: 30px;
  text-align: center;
  margin-top: 4px;
}
:deep(.prioritList) {
  .el-input__inner {
    padding: 0;
    border: 0;
    padding-left: 25px;
  }
  .el-input__icon {
    display: none;
  }
}
</style>
