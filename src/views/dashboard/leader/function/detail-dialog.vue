<template>
  <el-dialog
    :close-on-click-modal="false"
    title="数据详情"
    v-model="visible"
    :before-close="close"
  >
    <div>
      <vxe-table
        ref="issue-table"
        class="vone-vxe-table"
        border
        resizable
        min-height="200"
        max-height="300"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="标题"
          field="name"
          min-width="240"
          fixed="left"
          class-name="name_col custom-title-style"
          show-overflow="ellipsis"
          tree-node
        >
          <template #default="{ row }">
            <el-tooltip
              v-if="!row.groupType"
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="goto(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${
                      row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'
                    }`,
                  }"
                />
                <span class="custom-title-style-text">{{
                  row.code + ' ' + row.name
                }}</span>
              </span>
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="状态"
          field="stateCode"
          width="220"
        >
          <template #default="{ row }">
            <issueStatus
              v-if="row && !row.groupType && row.echoMap.stateCode"
              :key="Date.now()"
              :workitem="row"
              :no-permission="!$permission('project_issue_flow')"
            />
            <span
              v-else
              class="tagItem"
              :style="{
                border: `1px solid ${stateCodesMap[row.stateCode].color}`,
                color: `${stateCodesMap[row.stateCode].color}`,
              }"
            >
              {{ stateCodesMap[row.stateCode].name }}
            </span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          field="leadingBy"
          title="负责人"
          width="120"
        >
          <template #default="{ row }">
            <vone-user-avatar
              v-if="leadingUser(row).avatarPath"
              :avatar-path="leadingUser(row).avatarPath"
              :name="leadingUser(row).name"
            />
            <vone-remote-user
              v-else
              v-model="row.leadingBy"
              class="remoteuser"
              disabled
            />
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="处理人"
          field="handleBy"
          width="120"
        >
          <template #default="{ row }">
            <vone-user-avatar
              v-if="handleUser(row).avatarPath"
              :avatar-path="handleUser(row).avatarPath"
              :name="handleUser(row).name"
            />
            <vone-remote-user
              v-else
              v-model="row.handleBy"
              class="remoteuser"
              disabled
            />
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          field="putBy"
          title="提出人"
          width="120"
        >
          <template #default="{ row }">
            <vone-user-avatar
              v-if="putUser(row).avatarPath"
              :avatar-path="putUser(row).avatarPath || ''"
              :name="putUser(row).name || ''"
            />
            <vone-remote-user
              v-else
              v-model="row.putBy"
              class="remoteuser"
              disabled
            />
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="计划开始时间"
          field="planStime"
          width="180"
        >
          <template #default="{ row }">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="计划完成时间"
          field="planEtime"
          width="180"
        >
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="创建时间"
          field="createTime"
          width="180"
        >
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="workItemTypes.includes(type)"
          title="优先级"
          field="priorityCode"
          width="150"
        >
          <template #default="{ row }">
            <span v-if="prioritData(row)">
              <i
                :class="`iconfont ${prioritData(row).icon}`"
                :style="{
                  color: prioritData(row).color,
                  fontSize: '16px',
                  paddingRight: '6px',
                }"
              />
              {{ prioritData(row).name }}
            </span>
            <span v-else>""</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="['checkHour', 'completeHour'].includes(type)"
          title="工时日期"
          field="fillingTime"
          width="130"
        >
          <template #default="{ row }">
            <span v-if="!row.groupType">{{
              dayjs(row.fillingTime).format('YYYY-MM-DD')
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="hourTypes.includes(type)"
          title="工作项名称"
          field="name"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <el-icon class="iconfont" style="color: rgb(135, 145, 250)"
                  ><el-icon-icon-xuqiu
                /></el-icon>
                <el-icon class="iconfont" style="color: rgb(250, 107, 87)"
                  ><el-icon-icon-quexian
                /></el-icon>
                <el-icon class="iconfont" style="color: rgb(62, 123, 250)"
                  ><el-icon-icon-renwu
                /></el-icon>
                <span>
                  {{ row.name || workItemName(row) }}
                </span>
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column
          v-if="hourTypes.includes(type)"
          :title="type != 'estimateHour' ? '填报工时' : '预估工时'"
          width="90"
        >
          <template #default="{ row }">
            <span v-if="type != 'estimateHour'">{{
              row.duration ? row.duration + 'h' : ''
            }}</span>
            <span v-else>{{
              row.estimateHour ? row.estimateHour + 'h' : ''
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          v-if="hourTypes.includes(type)"
          title="填报日期"
          field="createTime"
          width="180"
        >
          <template #default="scope">
            {{ scope.row.createTime }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      style="position: static"
      @update="getInitTableData"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  IconXuqiu as ElIconIconXuqiu,
  IconQuexian as ElIconIconQuexian,
  IconRenwu as ElIconIconRenwu,
} from '@element-plus/icons'
const stateCodesMap = {
  UAT_TEST_COM: {
    name: 'UAT测试_已完成',
    color: '#df6311',
  },
  UAT_TEST_DO: {
    name: 'UAT测试_进行中',
    color: '#72eedf',
  },
  UAT_TEST_NS: {
    name: 'UAT测试_未开始',
    color: '#8a8585',
  },
  TEST_COM: {
    name: '软件测试_已完成',
    color: '#0c1910',
  },
  TEST_DOING: {
    name: '软件测试_进行中',
    color: '#8cc733',
  },
  TEST_NS: {
    name: '软件测试_未开始',
    color: '#877d7d',
  },
  CodeDev_COM: {
    name: '代码开发_已完成',
    color: '#841cd9',
  },
  CodeDev_Do: {
    name: '代码开发_进行中',
    color: '#b86f6f',
  },
  CodeDev_NS: {
    name: '代码开发_未开始',
    color: '#5f5959',
  },
  UIDesign_Com: {
    name: 'UI设计_已完成',
    color: '#13d5d8',
  },
  UIDesign_DO: {
    name: 'UI设计_进行中',
    color: '#c6a9c0',
  },
  UIDesign_NO: {
    name: 'UI设计_未开始',
    color: '#60549c',
  },
  Prototype_Design_Com: {
    name: '需求分析丨原型设计_已完成',
    color: '#db10ea',
  },
  Prototype_Design_Do: {
    name: '需求分析丨原型设计_进行中',
    color: '#0cd4bd',
  },
  Prototype_Design_No: {
    name: '需求分析丨原型设计_未启动',
    color: '#787279',
  },
  DEMAND_POOL: {
    name: '需求池',
    color: '#ADB0B8',
  },
  HANG_UP: {
    name: '已挂起',
    color: '#BD7FFA',
  },
  ANALYSIS_DO: {
    name: '分析中',
    color: '#FFBF47',
  },
  DEVELOP_DO: {
    name: '开发中',
    color: '#37CDDE',
  },
  TEST_DO: {
    name: '测试中',
    color: '#FC9772',
  },
  DONE: {
    name: '已完成',
    color: '#3CB540',
  },
  TODO: {
    name: '未开始',
    color: '#ADB0B8',
  },
  PROCESS_DO: {
    name: '处理中',
    color: '#37CDDE',
  },
  BUG_DONE: {
    name: '已修复',
    color: '#4BCCBB',
  },
  CHECK_TODO: {
    name: '待验证',
    color: '#F5B4B2',
  },
  OPEN_AGAIN: {
    name: '再打开',
    color: '#37CDDE',
  },
  REFUSE_DONE: {
    name: '已拒绝',
    color: '#FA6B57',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  Verificationfailed: {
    name: '验证不通过',
    color: '#f41010',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  CLOSE: {
    name: '已关闭',
    color: '#3CB540',
  },
  PROCESS_TODO: {
    name: '待处理',
    color: '#ADB0B8',
  },
}
import {
  getDemandDetail,
  getTaskData,
  getBugDetail,
  getHourDetail,
  getCheckHourDetail,
  getCompleteHour,
} from '@/api/vone/dashboard/index'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import {
  apiAlmPriorityNoPage,
  unfinishedRequirement,
  unfinishedRisk,
} from '@/api/vone/alm/index'
import dayjs from 'dayjs'
export default {
  components: {
    issueStatus,
    ElIconIconXuqiu,
    ElIconIconQuexian,
    ElIconIconRenwu,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    queryType: {
      type: [String, Number],
      default: null,
    },
    layout: {
      type: Object,
      default: () => {},
    },
    projectId: {
      type: String,
      default: '',
    },
    orgId: {
      type: String,
      default: '',
    },
    userId: {
      type: String,
      default: '',
    },
    chartType: {
      type: String,
      default: '',
    },
    code: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      stateCodesMap,
      tableData: {},
      prioritList: [],
      tableLoading: false,
      workItemTypes: ['issue', 'task', 'bug', 'risk'],
      hourTypes: ['estimateHour', 'checkHour', 'completeHour'],
    }
  },
  computed: {
    workItemName() {
      return function (row) {
        return row?.echoMap?.biz?.name
      }
    },
    handleUser() {
      return function (row) {
        return row?.echoMap?.handleBy || { handleBy: row.handleBy }
      }
    },
    leadingUser() {
      return function (row) {
        return row?.echoMap?.leadingBy || { leadingBy: row.leadingBy }
      }
    },
    putUser() {
      return function (row) {
        return row?.echoMap?.putBy || { putBy: row.putBy }
      }
    },
    prioritData() {
      return function (row) {
        return (
          this.prioritList.find((item) => item.code == row.priorityCode) || null
        )
      }
    },
  },
  mounted() {
    this.getPrioritList()
    this.getInitTableData(this.layout)
  },
  methods: {
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    // 初始化进入页面列表
    async getInitTableData(data) {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      }
      let params = {
        ...tableAttr,
        extra: {},
        model: {
          projectIds:
            this.layout?.latitude == 'project'
              ? this.projectId?.split(',') || []
              : [],
          orgIds:
            this.layout?.latitude == 'org' ? this.orgId?.split(',') || [] : [],
          productsetIds: data?.productId || [],
          queryType: this.queryType,
        },
      }
      const types = ['estimateHour', 'checkHour', 'completeHour', 'task']
      if (types.includes(this.type)) {
        params.model.startDate = dayjs(data?.time && data.time[0]).format(
          'YYYY-MM-DD'
        )
        params.model.endDate = dayjs(data?.time && data.time[1]).format(
          'YYYY-MM-DD'
        )
      } else {
        params.model.date = {
          start: dayjs(data?.time && data.time[0]).format('YYYY-MM-DD'),
          end: dayjs(data?.time && data.time[1]).format('YYYY-MM-DD'),
        }
      }
      if (this.chartType == 'userChart') {
        params.model.userIds = this.userId?.split(',') || []
      }
      this.tableLoading = true
      let res = null
      if (this.code == 'all') {
        params.model = { projectId: this.projectId }
      }
      if (this.code == 'all' && this.type == 'risk') {
        res = await unfinishedRisk(params)
      } else if (this.code == 'all' && this.type == 'issue') {
        res = await unfinishedRequirement(params)
      } else {
        switch (this.type) {
          case 'issue':
            res = await getDemandDetail(params)
            break
          case 'task':
            res = await getTaskData(params)
            break
          case 'bug':
            res = await getBugDetail(params)
            break
          case 'estimateHour':
            res = await getHourDetail(params)
            break
          case 'checkHour':
            res = await getCheckHourDetail(params)
            break
          case 'completeHour':
            res = await getCompleteHour(params)
            break
          default:
            break
        }
      }
      this.tableLoading = false
      if (!res?.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    close() {
      this.$emit('update:visible', false)
    },
    async goto(row) {
      const projectKey =
        row.echoMap?.projectInfo?.code ||
        row.echoMap?.productId?.code ||
        row.code
      const projectTypeCode =
        row.echoMap?.projectInfo?.typeCode ||
        row.echoMap?.productId?.typeCode ||
        row.typeCode
      const projectId =
        row.echoMap?.projectInfo?.id ||
        row.echoMap?.productId?.id ||
        row.projectId
      let path = ''

      const query = {
        showDialog: true,
        queryId: row?.id,
        rowTypeCode: row?.typeCode,
        stateCode: row?.stateCode,
        projectId: projectId,
      }

      switch (this.type) {
        case 'issue':
          path = `/project/issue/${projectKey}/${projectTypeCode}/${projectId}`
          break
        case 'task':
          path = `/project/task/${projectKey}/${projectTypeCode}/${projectId}`
          break
        case 'bug':
          path = `/project/defect/${projectKey}/${projectTypeCode}/${projectId}`
          break
        case 'risk':
          path = `/project/risk/${projectKey}/${projectTypeCode}/${projectId}`
          delete query.rowTypeCode
          delete query.stateCode
          delete query.projectId
          break
        case 'estimateHour':
          break
        case 'checkHour':
          break
        case 'completeHour':
          break
        default:
          break
      }
      const newpage = await this.$router.resolve({
        path,
        query,
      })
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
  },
}
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: none;
  }
  :deep(.el-input__suffix) {
    display: none;
  }
  :deep(.el-input.is-disabled .el-input__inner) {
    background-color: #fff;
    color: var(--font-main-color);
    cursor: text;
  }
}
.tagItem {
  text-align: center;
  border-radius: 3px;
  padding: 4px 10px;
  min-width: 80px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
