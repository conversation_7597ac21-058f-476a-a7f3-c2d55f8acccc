<template>
  <div class="box" style="background: #fff">
    <div class="title">
      <vone-user-avatar
        :avatar-path="data.avatarPath"
        :avatar-type="data.avatarType"
        :name="data.name"
      />
      <div style="flex: 1; text-align: right">
        <a @click="gotoDetail">详情 > </a>
      </div>
    </div>
    <div class="detail">
      <div>角色：</div>
      <span>{{ roleList.join(',') }}</span>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    form: {
      type: Object,
      default: () => {},
    },
    parentData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      roleList: [],
    }
  },
  mounted() {
    this.roleList =
      this.data?.echoMap?.projectRoles?.map((item) => {
        return item.name
      }) || []
  },
  methods: {
    gotoDetail() {
      const data = {}
      data.startDate = dayjs(this.form.time && this.form.time[0]).format(
        'YYYY-MM-DD'
      )
      data.endDate = dayjs(this.form.time && this.form.time[1]).format(
        'YYYY-MM-DD'
      )
      data.projectId = this.parentData.id
      data.projectName = this.parentData.name
      data.userId = this.data.id
      data.orgId = this.parentData.id
      data.latitude = this.form.latitude
      localStorage.setItem('leaderwork', JSON.stringify(this.form))
      this.$router.push({
        path: '/leaderwork/detail/' + this.data.id,
        query: data,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.box {
  border-radius: 4px;
  padding: 10px;
  height: 124px;
}
.title {
  display: flex;
  margin-bottom: 6px;
  a {
    color: #777f8e;
    text-align: right;
  }
}
.detail {
  color: #777f8e;
  line-height: 22px;
  margin-bottom: 4px;
  span {
    color: #2c2e36;
  }
}
</style>
