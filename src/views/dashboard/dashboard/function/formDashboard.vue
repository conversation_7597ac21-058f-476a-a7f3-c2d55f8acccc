<template>
  <FormRender ref="formRender" :model="form" v-model:list="list" />
</template>

<script>
import FormRender from '@/components/FormRender'
export default {
  components: { FormRender },
  data() {
    return {
      form: {},
      list: [],
    }
  },
  created() {
    const data = [
      // 输入框
      {
        type: 'input',
        key: 'name',
        label: '名称',
        placeholder: '请输入',
        initData: '',
        config: {
          props: {
            disabled: true,
          },
        },
        rules: [{ required: true, message: '名称不能为空' }],
      },
      {
        type: 'number',
        key: 'age',
        label: '年龄',
        placeholder: '请输入',
        initData: 0,
        config: {
          props: {
            disabled: true,
            min: 0,
          },
        },
        rules: [{ required: true, message: '年龄不能为空' }],
      },
      {
        type: 'date',
        key: 'date',
        label: '出生日期',
        placeholder: '请选择',
        initData: null,
        config: {
          props: {
            disabled: true,
          },
        },
        rules: [{ required: true, message: '请选择日期' }],
      },
      {
        type: 'select',
        key: 'sex',
        label: '性别',
        placeholder: '请选择',
        initData: '',
        config: {
          // select 属性
          props: {
            filterable: true,
            clearable: true,
            disabled: false,
          },
          // 设置select option label 及 value绑定的值
          optionConfig: {
            label: 'name',
            value: 'id',
          },
          axios: {
            url: '',
            method: '',
            query: {},
          },
          optionsList: [
            {
              name: '123',
              id: 1,
              isSlot: true,
              icon: 'el-icon-top',
              color: 'red',
            },
          ],
          listeners: {},
        },
      },
    ]
    data.map((item) => {
      this.$set(this.form, item.key, item.initData)
    })
    this.list = data
  },
}
</script>
