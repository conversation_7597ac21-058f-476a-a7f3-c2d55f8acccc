<template>
  <div>
    <div class="dashboard-header">
      <el-row :gutter="12">
        <el-col :span="24">
          <span style="float: right">
            <el-button
              :icon="el-icon-setting"
              @click="addComponents"
              >添加部件</el-button
            >
            <el-divider
              style="margin: -3px 12px 0px 12px"
              direction="vertical"
            />
            <el-button @click="back">取消</el-button>
            <el-button type="primary" @click="saveLayout">保存</el-button>
          </span>
        </el-col>
      </el-row>
    </div>
    <div class="edit-dashboard-content">
      <!-- <div class="widget-list">
          <el-collapse v-model="activeNames" accordion>
            <el-collapse-item v-for="(item,index) in widgetList" :key="index" :title="item.title" :name="item.title">
              <div
                v-for="widgteItem in item.widget"
                :key="widgteItem.key"
                class="widget"
              >
                <img
                  :src="require(`@/assets/dashboard/light/${widgteItem.img}.png`)"
                  draggable="true"
                  unselectable="on"
                  :data-name="widgteItem.name"
                  :data-key="widgteItem.key"
                  :title="widgteItem.title"
                  @dragstart="drastart"
                  @drag="drag"
                  @dragend="dragend"
                >
              </div>
            </el-collapse-item>
          </el-collapse>
        </div> -->
      <div id="layoutWrapper" class="layoutWrapper">
        <grid-layout
          v-if="
            layout != null || !(editId != null) || (editId && layout != null)
          "
          ref="gridlayout"
          v-model:layout="layout"
          :col-num="12"
          :row-height="78"
          :is-draggable="true"
          :is-resizable="true"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[10, 10]"
          :use-css-transforms="false"
        >
          <!-- eslint-disable-next-line vue/valid-v-for -->
          <grid-item
            v-for="(item, index) in layout"
            :key="index"
            class="setting-grid-item"
            :x="item.x"
            :min-h="item.widgetCode == 'project-information' ? 3 : 2"
            :min-w="2"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
          >
            <div class="iconfontbox">
              <el-icon class="iconfont"><el-icon-application-edit /></el-icon>
              <el-icon class="iconfont"><el-icon-application-delete /></el-icon>
            </div>
            <component
              :is="coms[item.widgetCode + item.i]"
              v-if="coms && item.queryParams"
              :form-info="item.queryParams && JSON.parse(item.queryParams)"
              v-model:layout="layout"
            />
            <div class="item-mmodal" />
          </grid-item>
        </grid-layout>
      </div>
    </div>
    <card-setting
      v-if="settingVisible"
      v-model="settingVisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <card-dialog
      v-if="visible"
      v-model="visible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <pipeline-dialog
      v-if="pipelinevisible"
      v-model:pipelinevisible="pipelinevisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <product-dialog
      v-if="productvisible"
      v-model:productvisible="productvisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <package-dialog
      v-if="packagevisible"
      v-model:packagevisible="packagevisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <cmdb-dialog
      v-if="cmdbvisible"
      v-model:cmdbvisible="cmdbvisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <CustomPieDialog
      v-if="custompieVisible"
      v-model="custompieVisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <CustomTrendDialog
      v-if="customtrendVisible"
      v-model="customtrendVisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <CustomListDialog
      v-if="customlistVisible"
      v-model="customlistVisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <ProjectBugRDialog
      v-if="projectBugRVisible"
      v-model="projectBugRVisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <CustomProjectRole
      v-if="projectRoleVisible"
      v-model="projectRoleVisible"
      v-model:form-info="formInfo"
      v-model:is-edit-form="isEditForm"
      v-model:layout="layout"
      @success="update"
    />
    <el-dialog
      class="dialog-main"
      :before-close="close"
      :close-on-click-modal="false"
      width="800px"
      :model-value="addVisible"
      title="添加部件"
    >
      <div style="display: flex">
        <div class="left-menu">
          <div
            v-for="(item, index) in widgetList"
            :key="index"
            :class="index == activeMenu ? 'active' : ''"
            class="list"
            @click="changeMenu(index)"
          >
            {{ item.title }}
            <span>{{ item.widget.length }}</span>
          </div>
        </div>
        <div class="right-content">
          <div
            v-for="(widgteItem, idx) in widgetList[activeMenu].widget"
            :key="idx"
            :class="[
              'widget',
              widgteItem.key == chosecardKey ? 'is-active' : '',
            ]"
            @click="choseCard(widgteItem.key)"
          >
            <div class="imgbox">
              <img
                :src="require(`@/assets/dashboard/light/${widgteItem.img}.png`)"
              />
            </div>
            <div class="title">
              {{ widgteItem.title }}
              <p>{{ widgteItem.desc }}</p>
            </div>
            <el-icon class="iconfont"
              ><el-icon-tips-check-circle-fill
            /></el-icon>
          </div>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { data } from './data'
const mouseXY = { x: null, y: null }
// const DragPos = { 'x': null, 'y': null, 'w': 2, 'h': 2, 'i': null }
import throttle from 'lodash/throttle'
import cloneDeep from 'lodash/cloneDeep'
import VueGridLayout from 'vue-grid-layout'
import { searchDashboard, operationDashboard } from '@/api/vone/dashboard'
import CardDialog from './card-dialog.vue'
import PipelineDialog from './pipeline-dialog.vue'
import ProductDialog from './product-dialog.vue'
import PackageDialog from './package-dialog.vue'
import CmdbDialog from './cmdb-dialog.vue'
import Placeholder from './placeholder.vue'
// import { snowGuid } from '@/utils/index'
import CardSetting from './card-setting.vue'
import { mapGetters } from 'vuex'
import CustomAdd from '@/views/vone/base/project-config/tab/common/form-view/customAdd.vue'
import CustomPieDialog from './custom-pie.vue'
import CustomTrendDialog from './custom-trend.vue'
import CustomListDialog from './custom-list.vue'
import ProjectBugRDialog from './projectbugr-dialog.vue'
import CustomProjectRole from './project-role-dialog.vue'
export default {
  name: 'Dashboard',
  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem,
    CardDialog,
    Placeholder,
    PipelineDialog,
    ProductDialog,
    PackageDialog,
    CmdbDialog,
    CardSetting,
    CustomAdd,
    CustomPieDialog,
    CustomTrendDialog,
    CustomListDialog,
    ProjectBugRDialog,
    CustomProjectRole,
  },
  props: {
    editId: {
      type: [String, Number],
      default: null,
    },
    isEdit: Boolean,
    addName: {
      type: [String, Number],
      default: '',
    },
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      layout: null,
      coms: {},
      elWindow: {
        x: '',
        y: '',
      },
      currentDashboardData: {},
      visible: false,
      pipelinevisible: false,
      productvisible: false,
      packagevisible: false,
      cmdbvisible: false,
      settingVisible: false, // 设置标题弹窗
      formInfo: {},
      changeTheme: '',
      activeNames: data.activeNames,
      widgetList: data.widgetList,
      addVisible: false,
      activeMenu: 0,
      chosecardKey: '',
      custompieVisible: false,
      customtrendVisible: false,
      customlistVisible: false,
      projectBugRVisible: false,
      projectRoleVisible: false,
      isEditForm: false,
    }
  },
  computed: {
    ...mapGetters(['themeCode']),
  },
  created() {
    if (this.editId == null) {
      this.layout = []
    }
  },
  async mounted() {
    // document.addEventListener('dragover', throttle((e) => {}, 50), false) // 临时去掉节流方法
    document.addEventListener(
      'dragover',
      (e) => {
        e.preventDefault()
        e.dataTransfer.dropEffect = 'move'
        mouseXY.x = e.clientX
        mouseXY.y = e.clientY
      },
      false
    )
    document.addEventListener(
      'drag',
      throttle((e) => {}, 100),
      false
    )
    if (this.editId != null) {
      this.searchDashboard()
    }
    if (this.detailData) {
      this.getDetail()
    }
  },
  destroyed() {
    document.removeEventListener('dragover', (e) => {
      mouseXY.x = e.clientX
      mouseXY.y = e.clientY
    })
    document.removeEventListener('drag', (e) => {})
  },
  methods: {
    back() {
      this.$emit('update:editId', null)
      this.$emit('update:isEdit', false)
    },
    getDetail() {
      const layout = cloneDeep(this.detailData.dashboardComponents)
      this.currentDashboardData = this.detailData
      layout.map((item) => {
        item.i = item.id
      })
      this.layout = layout
      const coms = {}
      layout.map((item) => {
        coms[`${item.widgetCode}${item.i}`] = this.requireCom(item.widgetCode)
      })
      this.$nextTick(() => {
        this.coms = coms
      })
    },
    searchDashboard() {
      searchDashboard(this.editId).then((res) => {
        if (res.isSuccess) {
          const layout = cloneDeep(res.data.dashboardComponents)
          this.currentDashboardData = res.data
          layout.map((item) => {
            item.i = item.id
          })
          this.layout = layout
          const coms = {}
          layout.map((item) => {
            coms[`${item.widgetCode}${item.i}`] = this.requireCom(
              item.widgetCode
            )
          })

          this.$nextTick(() => {
            this.coms = coms
          })
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    async saveLayout() {
      const params = {
        dashboardComponents: this.layout,
        name: this.addName,
        description: '',
        sort: 1,
      }
      if (this.editId == null) {
        await operationDashboard(params, 'post').then((res) => {
          if (res.isSuccess) {
            this.$message.success(res.msg)
            this.$emit('searchOwerDashboard', res.data.id)
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        params['name'] = this.currentDashboardData.name
        params['id'] = this.editId
        await operationDashboard(params, 'put').then((res) => {
          if (res.isSuccess) {
            this.$message.success(res.msg)
            this.$emit('searchDashboardDetail', this.editId)
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
      this.back()
    },
    deleteItem(i) {
      const _layout = cloneDeep(this.layout)
      this.layout = this.layout.filter((obj) => obj.i !== i)
      const item = _layout.find((obj) => obj.i == i)
      delete this.coms[`${item.widgetCode}${item.i}`]
    },

    requireCom(path) {
      return (resolve) => require([`@/views/widget/${path}.vue`], resolve)
    },
    changeMenu(index) {
      this.activeMenu = index
    },
    addComponents() {
      this.addVisible = true
    },
    close() {
      this.addVisible = false
    },
    update() {
      this.addVisible = false
      const coms = {}
      this.layout.map((item) => {
        coms[`${item.widgetCode}${item.i}`] = this.requireCom(item.widgetCode)
      })
      this.$nextTick(() => {
        this.coms = coms
      })
    },
    // 添加
    save() {
      this.isEditForm = false
      if (this.chosecardKey == '') {
        return this.$message.warning('请选择卡片')
      }
      // 只显示标题弹窗
      const editTitleList = [
        'code-cards',
        'my-dynamic',
        'workflow-task',
        'user-pageview',
        'my-notice',
      ]
      // 流水线，代码库,我的动态
      if (this.chosecardKey.indexOf('cmdb') !== -1) {
        this.cmdbvisible = true
      } else if (this.chosecardKey.indexOf('product') !== -1) {
        this.productvisible = true
      } else if (this.chosecardKey.indexOf('package') !== -1) {
        this.packagevisible = true
      } else if (this.chosecardKey.indexOf('pipeline') !== -1) {
        this.pipelinevisible = true
      } else if (this.chosecardKey.indexOf('resource') !== -1) {
        this.settingVisible = true
      } else if (editTitleList.includes(this.chosecardKey)) {
        this.settingVisible = true
      } else if (this.chosecardKey === 'project-information') {
        // 个人卡片
        // this.layout.map(item => {
        //   if (item.i == this.formInfo.code) {
        //     item.queryParams = JSON.stringify(this.formInfo)
        //   }
        // })
        this.layout.push({
          x: (this.layout.length * 2) % 12,
          y: this.layout.length + 12, // puts it at the bottom
          w: 12,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo }),
          widgetCode: this.formInfo.key,
        })
        this.update(this.formInfo.key, this.layout.length + 1)
      } else if (this.chosecardKey == 'custom-pie') {
        this.custompieVisible = true
      } else if (
        this.chosecardKey == 'custom-trend' ||
        this.chosecardKey == 'custom-histogram'
      ) {
        // 柱状图和趋势图
        this.customtrendVisible = true
      } else if (this.chosecardKey == 'custom-list') {
        this.customlistVisible = true
      } else if (this.chosecardKey == 'project-bug-repair') {
        this.projectBugRVisible = true
      } else if (this.chosecardKey == 'project-role-task') {
        this.projectRoleVisible = true
      } else {
        this.visible = true
      }
    },
    // 编辑
    editItem(item) {
      this.isEditForm = true

      this.formInfo = JSON.parse(item.queryParams)
      this.$set(this.formInfo, 'id', item.id)
      // 只显示标题弹窗
      const editTitleList = [
        'code-cards',
        'my-dynamic',
        'workflow-task',
        'user-pageview',
        'my-notice',
      ]
      // 流水线，代码库,我的动态
      if (item.widgetCode.indexOf('cmdb') !== -1) {
        this.cmdbvisible = true
      } else if (item.widgetCode.indexOf('product') !== -1) {
        this.productvisible = true
      } else if (item.widgetCode.indexOf('package') !== -1) {
        this.packagevisible = true
      } else if (item.widgetCode.indexOf('pipeline') !== -1) {
        this.pipelinevisible = true
      } else if (item.widgetCode.indexOf('resource') !== -1) {
        this.settingVisible = true
      } else if (editTitleList.includes(item.widgetCode)) {
        this.settingVisible = true
      } else if (item.widgetCode === 'project-information') {
        // 个人卡片
        // this.layout.map(item => {
        //   if (item.i == this.formInfo.code) {
        //     item.queryParams = JSON.stringify(this.formInfo)
        //   }
        // })
        this.layout.push({
          x: (this.layout.length * 2) % 12,
          y: this.layout.length + 12, // puts it at the bottom
          w: 12,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo }),
          widgetCode: this.formInfo.key,
        })
        this.update(this.formInfo.key, this.layout.length + 1)
      } else if (item.widgetCode == 'custom-pie') {
        this.custompieVisible = true
      } else if (
        item.widgetCode == 'custom-trend' ||
        item.widgetCode == 'custom-histogram'
      ) {
        // 柱状图和趋势图
        this.customtrendVisible = true
      } else if (item.widgetCode == 'custom-list') {
        this.customlistVisible = true
      } else if (item.widgetCode == 'project-bug-repair') {
        this.projectBugRVisible = true
      } else if (item.widgetCode == 'project-role-task') {
        this.projectRoleVisible = true
      } else if (this.chosecardKey == 'custom-pie') {
        this.custompieVisible = true
      } else if (
        this.chosecardKey == 'custom-trend' ||
        this.chosecardKey == 'custom-histogram'
      ) {
        // 柱状图和趋势图
        this.customtrendVisible = true
      } else if (this.chosecardKey == 'custom-list') {
        this.customlistVisible = true
      } else if (this.chosecardKey == 'project-bug-repair') {
        this.projectBugRVisible = true
      } else if (this.chosecardKey == 'project-role-task') {
        this.projectRoleVisible = true
      } else {
        this.visible = true
      }
    },
    // 点击卡片
    choseCard(key) {
      this.chosecardKey = key
      this.widgetList[this.activeMenu].widget.map((item) => {
        if (item.key == key) {
          this.formInfo = item
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-header {
    height: 48px;
    line-height: 48px;
    background: var(--main-bg-color, #fff);
    box-shadow: var(--nav-top-shadow);
    padding: 0 16px;
    margin: -10px -10px 10px -10px;
    position: relative;
    z-index: 999;
  }
}
.edit-dashboard-content {
  display: flex;
  .layoutWrapper {
    width: calc(100%);
    // overflow-x: hidden;
    .vue-grid-layout {
      margin: -10px;
      min-height: calc(100vh - 82px) !important;
      background: var(--solid-border-color);
      background-image: linear-gradient(
          0deg,
          var(--content-bg-color) 10px,
          transparent 10px
        ),
        linear-gradient(90deg, var(--content-bg-color) 10px, transparent 10px);
      background-position: 1.5px 10px;
      background-size: calc(8.33333% - 0.1%) 88px;
      background-attachment: local, scroll;
      // overflow-x: hidden;
      // overflow-y: scroll;
    }
  }
}
.setting-grid-item {
  position: relative;
  .iconfontbox {
    position: absolute;
    right: 10px;
    top: 15px;
    z-index: 999;
  }
  .iconfont {
    font-size: 14px;
    color: var(--font-second-color);
    cursor: pointer;
    margin-left: 12px;
    &:hover {
      color: var(--main-theme-color);
    }
  }
  .item-mmodal {
    position: absolute;
    cursor: move;
    width: 100%;
    left: 0;
    top: 0;
    bottom: 16px;
    z-index: 2;
  }
}
.vue-grid-item {
  overflow: hidden;
  // background-color: white;
  box-shadow: 0px 2px 6px 2px rgba(14, 48, 88, 0.04);
}
.dialog-main {
  :deep(.el-dialog__body) {
    padding: 0px;
  }
  .left-menu {
    width: 220px;
    border-right: 1px solid var(--solid-border-color);
    height: 495px;
    .list {
      height: 36px;
      line-height: 36px;
      padding: 0px 16px;
      color: var(--font-main-color);
      cursor: pointer;
      span {
        color: var(--font-second-color);
        float: right;
      }
    }
    .active {
      background-color: var(--main--50);
      color: var(--main-theme-color);
    }
  }
  .right-content {
    width: calc(100% - 220px);
    height: 495px;
    overflow: auto;
    padding: 0px 16px 16px 16px;
    .widget {
      position: relative;
      padding: 0px 16px;
      height: 112px;
      border-radius: 4px;
      background-color: #fff;
      box-shadow: var(--main-bg-shadow);
      border: 1px solid var(--solid-border-color);
      display: flex;
      align-items: center;
      margin-top: 16px;
      .imgbox {
        width: 130px;
        img {
          width: 100%;
          box-shadow: var(--nav-top-shadow);
          border-radius: 4px;
          overflow: hidden;
        }
      }
      .title {
        margin-left: 16px;
        color: var(--font-main-color);
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        p {
          color: var(--font-second-color);
          margin: 0px;
          margin-top: 4px;
          font-weight: normal;
          font-size: 14px;
        }
      }
      .iconfont {
        font-size: 16px;
        position: absolute;
        color: var(--main-theme-color);
        position: absolute;
        right: 10px;
        top: 10px;
      }
    }
    .is-active {
      border: 2px solid var(--main-theme-color);
      background-color: var(--main--50);
      box-sizing: border-box;
    }
  }
}
</style>
