<template>
  <el-dialog
    title="共享仪表板"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
    :before-close="onClose"
  >
    <el-form ref="form" :model="form" label-position="top">
      <el-form-item label="编辑者" prop="previewAdd22">
        <ShareItemConditions
          ref="shareConditionEdit"
          type="WORK_ITEM"
          :filter-data="editData"
          :form="data"
        />
      </el-form-item>
      <el-form-item label="查看者" prop="previewAdd">
        <ShareItemConditions
          ref="shareConditionCheck"
          type="WORK_ITEM"
          :filter-data="previewData"
          :form="data"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveInfo"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  apiInsightShareBoardPut,
  apiInsightShareBoardInfo,
} from '@/api/vone/weidget'
import ShareItemConditions from './share-item-grid.vue'
import _ from 'lodash'

export default {
  components: {
    ShareItemConditions,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      previewData: [],
      editData: [],
      form: {},
      saveLoading: false,
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    async getInfo() {
      const res = await apiInsightShareBoardInfo(this.data.id)
      if (!res.isSuccess) {
        console.warn(res.msg)
        return
      }
      res.data.forEach((element) => {
        element.authAction = element.authAction?.code
        element.authType = element.authType?.code
      })

      this.previewData = res.data.filter((r) => r.authAction == 'VIEW')
      this.editData = res.data.filter((r) => r.authAction == 'EDIT')
    },
    async saveInfo() {
      try {
        this.saveLoading = true
        const viewList = this.$refs.shareConditionCheck.conditions
        const editList = this.$refs.shareConditionEdit.conditions

        viewList.forEach((element) => {
          element.authAction = 'VIEW'
        })
        editList.forEach((element) => {
          element.authAction = 'EDIT'
        })

        const allList = _.concat(viewList, editList)

        const res = await apiInsightShareBoardPut(this.data.id, allList)
        this.saveLoading = false
        if (!res.isSuccess) {
          console.warn(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.$emit('update:visible')
        this.$emit('success')
      } catch (error) {
        this.saveLoading = false
        return
      }
    },
    onClose() {
      this.$emit('update:visible')
    },
  },
}
</script>

<style></style>
