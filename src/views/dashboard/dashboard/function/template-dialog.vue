<template>
  <el-dialog
    :model-value="visible"
    :before-close="close"
    title="新增工作台"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" label-position="top" :rules="rules">
      <el-form-item v-if="addtype" label="模板类型" prop="type">
        <el-select
          v-model="form.type"
          filterable
          value-key="id"
          @change="temChang"
        >
          <el-option
            v-for="item in templateList"
            :key="item.id"
            :label="item.name"
            :value="item"
          />
          <!-- <el-option label="研发度量" value="yanfaduliang">研发度量</el-option> -->
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formItemList.includes('name') && form.type !== 'yanfaduliang'"
        prop="name"
        label="名称"
      >
        <el-input ref="name" v-model.trim="form.name" />
      </el-form-item>
      <el-form-item
        v-if="formItemList.includes('version')"
        label="资源类型"
        prop="version"
      >
        <el-select
          v-model="form.version"
          clearable
          filterable
          value-key="value"
        >
          <el-option
            v-for="item in cmdbTypeList"
            :key="item.value"
            :label="item.label"
            :value="datafilter(item)"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formItemList.includes('project')"
        label="项目"
        prop="project"
      >
        <el-select
          v-model="form.project"
          clearable
          filterable
          value-key="id"
          @change="changeProject"
        >
          <el-option
            v-for="item in projectIdList"
            :key="item.id"
            :label="item.name"
            :value="datafilter(item)"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formItemList.includes('plan')"
        label="迭代"
        prop="plan"
      >
        <el-select v-model="form.plan" clearable filterable value-key="id">
          <el-option
            v-for="item in planIdList"
            :key="item.id"
            :label="item.name"
            :value="datafilter(item)"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="选择模版" prop="customDashboardDatas">
          <el-radio-group v-model="form.customDashboardDatas" clearable @change="changeRadio">
            <el-radio>自定义工作台</el-radio>
            <div v-for="temp in temps" :key="temp.id">
              <el-radio :label="temp.id">{{ temp.name }}</el-radio>
            </div>
          </el-radio-group>
        </el-form-item> -->
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { searchDefaultDashboard, setTemplateWork } from '@/api/vone/dashboard'
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'

import reduce from 'lodash/reduce'

export default {
  name: 'TemplateDialog',
  props: {
    visible: Boolean,
    isEdit: Boolean,
    addtype: {
      type: Boolean,
      default: false,
    },
    addItem: {
      type: Object,
      default: () => {},
    },
    templateList: {
      type: Array,
      default: () => {},
    },
  },
  data() {
    return {
      temps: [],
      tempMap: {},
      form: {
        name: this.addItem.name,
        type: {
          id: '',
        },
        // version: {},
        // project: {},
        // plan: {}
      },
      saveLoading: false,
      cmdbTypeList: [
        {
          value: '主机',
          label: '主机',
        },
        {
          value: '服务应用数量',
          label: '服务应用数量',
        },
        {
          value: '应用组件',
          label: '应用组件',
        },
        {
          value: '数据库组',
          label: '数据库组',
        },
      ],
      projectIdList: [],
      planIdList: [],
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        version: [{ required: true, message: '请选择资源', trigger: 'change' }],
        project: [{ required: true, message: '请选择项目', trigger: 'change' }],
        plan: [{ required: true, message: '请选择迭代', trigger: 'change' }],
        type: [{ required: true, message: '请选择模板', trigger: 'change' }],
      },
      formItemList: [],
    }
  },
  mounted() {
    this.getProjectList()
    this.formItemList = Object.keys(this.addItem.param)
    if (this.addtype) {
      this.form.name = this.templateList[0].name
      this.form.type = this.templateList[0]
      this.formItemList = Object.keys(this.templateList[0].param)
    } else {
      this.form.type.id = this.addItem.id
    }
  },
  methods: {
    temChang(e) {
      this.form.name = e.name
      this.formItemList = Object.keys(e.param)
    },
    datafilter(e) {
      const obj = {
        id: e.id,
        name: e.name,
      }
      return obj
    },
    changeProject(val) {
      this.getPlanList(val.id)
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async getTemps() {
      const { data, isSuccess, msg } = await searchDefaultDashboard()
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.temps = data
      this.tempMap = reduce(data, (r, v) => (r[v.id] = v) && r, {})
    },
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 迭代
    async getPlanList(val) {
      const res = await apiAlmProjectPlanNoPage({ projectId: val })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },
    changeRadio(val) {
      if (val) {
        this.$set(this.form, 'name', this.tempMap[val].name)
      } else {
        this.$set(this.form, 'name', '')
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.type.id === '0') {
            // this.$emit('update:addName', this.form.name)
            this.$emit('submit', this.form, 'add')
            this.close()
          } else {
            setTemplateWork(this.form.type.id, this.form).then((res) => {
              if (res.isSuccess) {
                this.$emit('submit', res.data, 'edit')
                this.close()
                // this.$emit('refreshDashboard')
              }
            })
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-radio {
  margin-top: 8px;
}
</style>
