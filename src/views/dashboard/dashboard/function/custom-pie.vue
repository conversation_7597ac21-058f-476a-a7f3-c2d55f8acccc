<template>
  <el-dialog
    :model-value="visible"
    :before-close="close"
    title="编辑部件"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form ref="form" :model="form" :rules="formRule">
      <el-form-item prop="tableViewId" label="筛选器">
        <el-select v-model="form.tableViewId" filterable>
          <el-option
            v-for="(item, index) in filterList"
            :key="index"
            :label="item.name"
            :value="item.id"
            >{{ item.name }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item prop="name" label="部件名称">
        <el-input ref="name" v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item prop="x" label="统计类型">
        <el-select v-model="form.x" placeholder="请选择" filterable>
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :value="item.key"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getFilterList } from '@/api/vone/dashboard'
import { queryFilterConditions } from '@/api/vone/dashboard/filter'
import _ from 'lodash'
export default {
  props: {
    visible: Boolean,
    isEditForm: Boolean,
    formInfo: {
      type: Object,
      default: () => {},
    },
    layout: {
      type: [Object, Array],
      default: null,
    },
  },
  data() {
    return {
      form: {
        name: '',
        y: 'count(*)', // 必填参数,不可删除
      },
      formRule: {
        name: [{ required: true, message: '请输入名称' }],
        tableViewId: [{ required: true, message: '请选择筛选器' }],
        x: [{ required: true, message: '请选择统计类型' }],
      },
      filterList: [],
      typeList: [],
      saveLoading: false,
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.form = this.formInfo
    }
    this.getList()
    this.getTypeList()
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    getList() {
      getFilterList({
        table: 'work_item_entity',
      }).then((res) => {
        if (res.isSuccess) {
          this.filterList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getTypeList() {
      queryFilterConditions('WORK_ITEM').then((res) => {
        if (res.isSuccess) {
          this.typeList = res.data.filter((r) => r.key != 'tagId')
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      this.$set(
        this.form,
        'xName',
        this.typeList.find((t) => t.key == this.form.x).name
      )
      if (this.isEditForm) {
        layouts.map((item) => {
          if (item.id == this.form.id && item.widgetCode == this.form.key) {
            item.queryParams = JSON.stringify({ ...this.form })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % 12,
          y: this.layout.length + 12, // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.form }),
          widgetCode: this.formInfo.key,
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:visible', false)
      this.$emit('success')
    },
  },
}
</script>
