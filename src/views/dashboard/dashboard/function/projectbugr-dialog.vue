<template>
  <el-dialog
    :model-value="visible"
    :before-close="close"
    title="编辑部件"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form ref="form" :model="form" :rules="formRule">
      <el-form-item prop="name" label="部件名称">
        <el-input ref="name" v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="筛选器" prop="tableViewId">
        <el-select v-model="form.tableViewId" clearable filterable>
          <el-option
            v-for="item in filterList"
            :key="item.key"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-row :gutter="8">
        <el-col :span="12">
          <el-form-item prop="timeCycle" label="X轴">
            <el-select v-model="form.timeCycle" placeholder="请选择" filterable>
              <el-option
                v-for="(item, index) in typeList"
                :key="index"
                :value="item.key"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="days" label="N天之前">
            <el-input v-model="form.days" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="8">
        <el-col :span="12">
          <el-form-item prop="y" label="收集方式">
            <el-select v-model="form.y" placeholder="请选择">
              <el-option
                v-for="(item, index) in wayList"
                :key="index"
                :value="item.key"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="isShowTodo" label="显示未解决问题发展趋势">
            <el-radio-group v-model="form.isShowTodo" style="width: 100%">
              <el-radio :label="0">是</el-radio>
              <el-radio :label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { apiAlmProjectNoPage } from '@/api/vone/project/index'

import filterListMixin from '@/mixin/filter-list'
import _ from 'lodash'
export default {
  mixins: [filterListMixin],
  props: {
    isEditForm: Boolean,
    visible: Boolean,
    isEdit: Boolean,
    formInfo: {
      type: Object,
      default: () => {},
    },
    layout: {
      type: [Object, Array],
      default: null,
    },
  },
  data() {
    return {
      form: {
        name: this.formInfo.title,
        isShowTodo: 0,
        timeCycle: 'DAY',
        y: 'count(*)',
        days: '30',
      },
      formRule: {
        name: [{ required: true, message: '请输入项目名称' }],
        tableViewId: [{ required: true, message: '请选择筛选器' }],
        timeCycle: [{ required: true, message: '请选择X轴' }],
        days: [
          { required: true, message: '请输入N天之前' },
          {
            pattern: '^[0-9]*$',
            message: '请输入数字',
          },
        ],
        y: [{ required: true, message: '请选择收集方式' }],
      },
      saveLoading: false,
      typeList: [
        {
          name: '每日',
          key: 'DAY',
        },
        {
          name: '每周',
          key: 'WEEK',
        },
        {
          name: '每月',
          key: 'MOUTH',
        },
        {
          name: '每季',
          key: 'QUARTER',
        },
        {
          name: '每年',
          key: 'YEAR',
        },
      ],
      projectIdList: [],
      wayList: [
        {
          name: '计数',
          key: 'count(*)',
        },
        {
          name: '累计',
          key: 'sum',
        },
      ],
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.form = this.formInfo
    }
    this.getProjectList()
  },
  methods: {
    // 项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }

      const layouts = _.cloneDeep(this.layout)
      if (this.isEditForm) {
        layouts.map((item) => {
          if (item.id == this.form.id && item.widgetCode == this.form.key) {
            item.queryParams = JSON.stringify({ ...this.form })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % 12,
          y: this.layout.length + 12, // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.form }),
          widgetCode: this.formInfo.key,
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:visible', false)
      this.$emit('success')
    },
  },
}
</script>
