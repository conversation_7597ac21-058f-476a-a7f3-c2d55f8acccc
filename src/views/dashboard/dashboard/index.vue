<template>
  <div id="dashboard-main" class="dashboard-main" :class="[...overflowY]">
    <div v-if="!isEdit && dashboardTab.length > 0">
      <div
        class="dashboard-header"
        :style="{ 'margin-top': isFullFlag ? 0 : '-10px' }"
      >
        <el-row :gutter="12">
          <el-col :span="12">
            <a class="filterLink" @click="tofilter">
              <svg class="icon" aria-hidden="true" style="font-size: 20px">
                <use xlink:href="#el-icon-peizhi-shaixuanqi" />
              </svg>
              筛选器
            </a>

            <el-divider direction="vertical" />

            <el-dropdown
              v-model="tabVisible"
              trigger="click"
              placement="bottom-start"
              width="260px"
              @command="tabChange"
            >
              <span class="el-dropdown-link reference">
                {{ activeName }}
                <span
                  v-if="activeData.authActions && activeData.authActions.length"
                  class="typeIcon colorBlue referenceTag"
                >
                  共享
                </span>
                <span v-else class="typeIcon colorOrange referenceTag">
                  我的
                </span>
                <span
                  v-if="activeData.isDefault"
                  class="typeIcon colorInfo referenceTag"
                >
                  默认
                </span>
                <el-icon class="iconfont el-icon--right"
                  ><el-icon-direction-down
                /></el-icon>
              </span>
              <el-dropdown-menu slot="dropdown" class="dropdownContent">
                <el-input v-model="searchText" placeholder="搜索工作台" />
                <div class="search-main">
                  <el-dropdown-item
                    v-for="item in searchList"
                    :key="item.id"
                    class="mainContent"
                    :style="{
                      color:
                        activeId == item.id ? 'var(--main-theme-color)' : '',
                    }"
                    :command="item.id"
                  >
                    <span class="leftSection">
                      <span class="leftText">
                        {{ item.name }}
                      </span>

                      <span class="leftTag">
                        <span
                          v-if="item.authActions && item.authActions.length"
                          class="typeIcon colorBlue"
                        >
                          共享
                        </span>
                        <span v-else class="typeIcon colorOrange"> 我的 </span>
                        <span v-if="item.isDefault" class="typeIcon colorInfo">
                          默认
                        </span>
                      </span>
                    </span>
                    <span class="operationBtn" @click.stop>
                      <el-dropdown
                        trigger="click"
                        :hide-on-click="true"
                        @command="(e) => e && e(item)"
                      >
                        <el-icon class="iconfont"
                          ><el-icon-application-more
                        /></el-icon>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item
                            :icon="ElIconIconfont elIconApplicationCopyContent"
                            :command="() => setDefault(item)"
                          >
                            <span>设为默认</span>
                          </el-dropdown-item>
                          <el-dropdown-item
                            :icon="ElIconIconfont elIconApplicationDelete"
                            :command="() => deleteDashboard(item)"
                          >
                            <span>删除</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </span>
                  </el-dropdown-item>
                </div>

                <div class="add" @click="addDashboard">
                  <el-icon class="iconfont"><el-icon-tips-plus /></el-icon
                  >新增工作台
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
          <el-col :span="12" class="rightCol">
            <span
              v-if="
                currentDashboardData &&
                currentDashboardData.echoMap &&
                currentDashboardData.echoMap.userId
              "
              class="shareSpan"
            >
              <vone-user-avatar
                :avatar-path="currentDashboardData.echoMap.userId.avatarPath"
                :avatar-type="true"
                :name="currentDashboardData.echoMap.userId.name"
                :show-name="true"
              />
            </span>
            <span class="btnList">
              <el-button
                v-show="isFullFlag"
                :icon="ElIconIconfont elIconDirectionFullscreen"
                @click="fullScreen"
                >取消全屏</el-button
              >
              <el-button
                v-show="!isFullFlag"
                :icon="ElIconIconfont elIconDirectionFullscreen"
                @click="fullScreen"
                >全屏展示</el-button
              >
              <el-button
                v-if="haEditAuth"
                :icon="ElIconIconfont elIconApplicationEdit"
                @click="editDashboard(editGroup)"
                >编辑工作台</el-button
              >
              <el-button
                type="primary"
                :icon="ElIconIconfont elIconTipsPlusCircle"
                @click="addDashboard"
                >新增工作台</el-button
              >
              <el-dropdown
                v-if="haEditAuth"
                trigger="click"
                @command="(e) => e && e()"
                @click.stop
              >
                <el-tooltip effect="dark" content="更多" placement="top">
                  <el-button
                    class="btnMore"
                    :icon="ElIconIconfont elIconApplicationMore"
                  />
                </el-tooltip>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :command="(e) => editName(editGroup)"
                    :icon="ElIconIconfont elIconApplicationRename"
                    >重命名仪表盘</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="(e) => shareCard(editGroup)"
                    :icon="ElIconIconfont elIconApplicationShare"
                    >共享仪表盘</el-dropdown-item
                  >
                  <el-dropdown-item
                    :icon="ElIconIconfont elIconApplicationDelete"
                    :command="(e) => deleteDashboard(editGroup)"
                    >删除仪表盘</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </el-col>
        </el-row>
      </div>
      <div
        class="dashboard-content"
        :style="{ margin: isFullFlag ? '0 0' : '0 -10px' }"
      >
        <grid-layout
          v-model:layout="layout"
          :col-num="12"
          :row-height="78"
          :is-draggable="false"
          :is-resizable="false"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[10, 10]"
          :use-css-transforms="false"
        >
          <grid-item
            v-for="(item, index) in layout"
            :key="index"
            class="setting-grid-item"
            :x="item.x"
            :min-h="5"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.widgetCode"
          >
            <component
              :is="coms[item.widgetCode]"
              v-if="coms"
              :key="index"
              :form-info="JSON.parse(item.queryParams)"
            />
          </grid-item>
        </grid-layout>
      </div>
    </div>
    <div v-if="isshowtemplate && !isEdit" class="guide">
      <div class="title">一键生成工作台</div>
      <div class="template-card">
        <div
          v-for="(item, index) in templateList"
          :key="index"
          :class="['card-item', { active: index === templateid }]"
          @click="check(index, item)"
        >
          <div class="card-header">
            {{ item.name }}
            <span v-show="index !== templateid" class="template-icon" />
            <el-icon class="template-icon-selected"
              ><el-icon-success
            /></el-icon>
          </div>
          <div class="card-content">
            <img
              style="width: 100%; height: 182px; padding: 24px"
              :src="require(`@/assets/dashboard/light/template${item.id}.svg`)"
            />
            <p>{{ item.description }}</p>
          </div>
        </div>
      </div>
      <el-button class="set-work" type="primary" @click="addwork"
        >生成工作台</el-button
      >
    </div>
    <template-dialog
      v-if="templatevisible"
      v-model="templatevisible"
      :add-item="templateItem"
      :addtype="addtype"
      :template-list="templateList"
      @submit="templateSubmit"
    />
    <dashboard-add
      v-if="visible"
      v-model="visible"
      :add-name="addName"
      :is-edit="isEdit"
      @refreshDashboard="searchOwerDashboard"
    />
    <dashboard-edit
      v-if="isEdit"
      :detail-data="detailData"
      :edit-id="editId"
      :add-name="addName"
      :is-edit="isEdit"
      @searchDashboardDetail="searchDashboardDetail"
      @searchOwerDashboard="searchOwerDashboard"
    />
    <el-dialog
      :model-value="editVisible"
      title="重命名仪表盘"
      width="456px"
      :before-close="closeDialog"
    >
      <el-form :model="editGroup" :rules="editGroupRules" label-position="top">
        <el-form-item label="名称" prop="name">
          <el-input v-model.trim="editGroup.name" />
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button :loading="renameLoading" type="primary" @click="rename"
          >确定</el-button
        >
      </template>
    </el-dialog>

    <ShareDialog
      v-if="shareParam.visible"
      v-bind="shareParam"
      v-model="shareParam.visible"
    />
  </div>
</template>

<script>
import VueGridLayout from 'vue-grid-layout'
import DashboardAdd from './function/dashboard-add'
import DashboardEdit from './function/dashboard-edit'
import TemplateDialog from './function/template-dialog'
import ShareDialog from './function/share-dialog'
import measureDashboard from './measure/index'
import {
  searchMyDashboard,
  searchDashboard,
  operationDashboard,
  searchDefaultDashboard,
  setDashboardDefault,
} from '@/api/vone/dashboard'
import { templateList } from './function/data'
import { mapGetters } from 'vuex'
export default {
  name: 'Dashboard',
  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem,
    DashboardAdd,
    DashboardEdit,
    TemplateDialog,
    ShareDialog,
    measureDashboard,
  },
  data() {
    return {
      shareParam: { visible: false },
      dashboardTab: [], // 工作台tab数据
      activeId: '', // 激活的tab
      activeName: '',
      layout: [], // 工作台展示的组件
      editGroup: {
        name: '',
      }, // 表单数据
      editGroupRules: {
        name: [{ required: true, message: '请输入工作台名称' }],
      },
      coms: {}, // 动态引入的组件
      currentDashboardData: {}, // 当前工作台数据
      visible: false,
      isEdit: false,
      editId: null,
      addName: '',
      templateList: templateList,
      templateid: 0,
      templatevisible: false,
      templateItem: null,
      detailData: null,
      addtype: false,
      isshowtemplate: false,
      tabVisible: false,
      searchText: '',
      editVisible: false,
      haEditAuth: false,
      isFullFlag: false,
      isShared: false,
      renameLoading: false,
      activeData: {},
    }
  },
  computed: {
    ...mapGetters(['themeCode']),
    searchList() {
      if (!this.searchText) {
        return this.dashboardTab
      }
      return this.dashboardTab.filter((item) => {
        return item.name.includes(this.searchText)
      })
    },
    overflowY() {
      return this.isFullFlag ? ['overflowY', 'overflowX'] : ''
    },
  },
  watch: {
    layout: {
      handler(value) {
        const coms = {}
        value.map((item) => {
          coms[`${item.widgetCode}`] = this.requireCom(item.widgetCode)
        })
        this.$nextTick((res) => {
          this.coms = coms
        })
      },
      immediate: true,
    },
  },
  async mounted() {
    await this.searchOwerDashboard()
    this.getTemps()
    window.addEventListener('resize', () => {
      if (!this.isFullScreen()) {
        if (!this.isFullFlag) return
        this.isFullFlag = false
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', () => {})
  },
  methods: {
    fullele() {
      return (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement ||
        document.mozFullScreenElement ||
        null
      )
    },
    // 判断是否全屏
    isFullScreen() {
      return !!(document.webkitIsFullScreen || this.fullele())
    },
    templateSubmit(e, type) {
      this.addName = e.name
      this.isEdit = true
      if (type === 'edit') {
        this.detailData = e
      } else {
        this.detailData = null
      }
    },
    async getTemps() {
      const { data, isSuccess, msg } = await searchDefaultDashboard()
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.templateList = data
      this.templateList.unshift({
        id: '0',
        name: '自定义工作台',
        description: '工作台拥有强大的自定义组装配置能力，覆盖各类管理场景。',
        param: {
          name: '',
        },
      })
      // this.templateList.push({
      //   id: '4',
      //   name: '研发度量工作台',
      //   description: '通过可视化仪表盘，从组织、项目、产线不同维度，度量团队需求响应能力、交付质量、团队负荷、持续集成/部署能力等指标，为持续改进打下数据基础。',
      //   param: {
      //     name: ''
      //   }
      // })
      this.templateItem = this.templateList[0]
    },
    addwork() {
      if (this.templateid == '4') {
        this.$router.push({
          path: '/dashboard/measure',
        })
      }
      this.addtype = false
      this.templatevisible = true
    },
    check(index, e) {
      this.templateItem = e
      this.templateid = index
    },
    tabChange(val) {
      this.activeData = this.dashboardTab.find((item) => item.id == val)
      this.activeName = this.dashboardTab.find((item) => item.id == val).name
      this.activeId = val

      this.tabVisible = false
      this.searchDashboardDetail(val)
    },
    rename() {
      const params = {
        ...this.currentDashboardData,
        name: this.editGroup.name,
      }
      this.renameLoading = true
      operationDashboard(params, 'put')
        .then((res) => {
          this.renameLoading = false
          if (res.isSuccess) {
            this.dashboardTab.find((item) => {
              item.id == this.currentDashboardData.id
                ? (item.name = this.editGroup.name)
                : item.name
            })
            this.$message.success('修改成功')
            this.editVisible = false
            this.activeName = this.editGroup.name
          } else {
            this.$message.warning(res.msg)
          }
        })
        .catch(() => {
          this.renameLoading = false
        })
    },
    closeDialog() {
      this.editVisible = false
    },
    editName(item) {
      this.editId = item.id
      this.editVisible = true
    },
    searchOwerDashboard(id) {
      searchMyDashboard().then((res) => {
        if (res.isSuccess) {
          if (res.data.length > 0) {
            this.isshowtemplate = false
            const data = res.data.map((item) => ({
              id: item.id,
              name: item.name,
              authActions: item.authActions,
              isDefault: item.isDefault,
            }))
            this.dashboardTab = data
            if (id) {
              const addId = data.find((item) => item.id == id).id
              this.activeId = addId
              this.activeName = data.find((item) => item.id == id).name
              this.activeData = data.find((item) => item.id == id)
              this.searchDashboardDetail(addId)
            } else {
              const defaultData = data.find((r) => r.isDefault)
              this.activeId = defaultData ? defaultData.id : data[0].id
              this.activeName = defaultData ? defaultData.name : data[0].name
              this.activeData = defaultData || data[0]
              this.searchDashboardDetail(this.activeId)
            }
          } else {
            this.isshowtemplate = true
            this.dashboardTab = []
            this.layout = []
          }
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    searchDashboardDetail(id) {
      searchDashboard(id).then((res) => {
        if (res.isSuccess) {
          this.editGroup = res.data
          this.currentDashboardData = res.data
          res.data.dashboardComponents.map((item) => {
            item.i = item.widgetCode
          })
          this.$nextTick(() => {
            this.layout = res.data.dashboardComponents
            // 如果authActions字段有值,说明是别人分享过来的工作台,则判断当前用户是否有编辑权限

            if (res.data && res.data.authActions.length) {
              this.isShared = !res.data.authActions.find((r) => r.code == 'OWN')
              this.haEditAuth = !!res.data.authActions.filter(
                (r) => r.code == 'EDIT'
              ).length
            } else {
              this.haEditAuth = true
            }
          })
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    addDashboard() {
      this.tabVisible = false
      this.templatevisible = true
      this.addtype = true
    },
    editDashboard(item) {
      this.isEdit = true
      this.editId = item.id
    },
    // 全屏展示
    fullScreen() {
      this.isFullFlag = !this.isFullFlag
      const element = document.getElementById('dashboard-main') // 指定全屏区域元素
      if (this.isFullFlag) {
        element.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    deleteDashboard(item) {
      this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
      }).then(async () => {
        const res = await operationDashboard([item.id], 'delete')
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        this.searchOwerDashboard()
        this.$message.success('删除成功')
      })
    },
    requireCom(path) {
      return (resolve) => require([`@/views/widget/${path}.vue`], resolve)
    },
    shareCard(item) {
      this.shareParam = { visible: true, data: item }
    },
    tofilter() {
      const newpage = this.$router.resolve({
        path: `/filter/view`,
      })
      window.open(newpage.href, '_blank')
    },
    // 设置默认工作台
    async setDefault(item) {
      console.log(item, 'item')
      const res = await setDashboardDefault(item.id)
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.$message.success('操作成功')
      this.searchOwerDashboard()
    },
    clickActive(data) {
      this.activeId = data.id
    },
  },
}
</script>

<style lang="scss" scoped>
.overflowY {
  overflow-y: auto;
}
.overflowX {
  overflow-x: hidden;
}

.dashboard {
  &-main {
    touch-action: none;
    background: var(--content-bg-color);
  }
  &-header {
    height: 48px;
    line-height: 48px;
    background: var(--main-bg-color);
    padding: 0px 16px;
    margin: -10px -10px 0px -10px;
    box-shadow: var(--nav-top-shadow);
    border: 1px solid var(--solid-border-color);
    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
    :deep(.vone-tabs .el-tabs__nav) {
      border: none;
    }
  }
  &-content {
    margin: 0 -10px;
  }
}

.btnList {
  :deep(.el-button + .el-button) {
    margin-left: 12px;
  }
  .moreButton {
    min-width: 30px;
  }
}
.guide {
  text-align: center;
  padding: 50px 64px 0;
  .title {
    color: var(--main-theme-color);
    font-size: 20px;
    font-weight: bold;
  }
  .template-card {
    display: flex;
    justify-content: space-between;
    .card-item {
      margin-top: 36px;
      width: 244px;
      background: var(--main-bg-color, #fff);
      border-radius: 6px;
      border: 2px solid rgb(240, 245, 252);
      .card-header {
        width: 100%;
        height: 96px;
        line-height: 96px;
        background: rgba(243, 247, 253, 0.5);
        position: relative;
        .template-icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          border-radius: 10px;
          border: 1.375px solid #c1c8d6;
          box-sizing: border-box;
          position: absolute;
          right: 17.5px;
          top: 17.35px;
        }
        .template-icon-selected {
          display: inline-block;
          color: var(--main-theme-color, #3e7bfa);
          font-size: 22px;
          position: absolute;
          right: 17.5px;
          top: 17.35px;
        }
      }
      .card-content {
        padding: 0 36px 36px;
        p {
          text-align: left;
          font-size: 14px;
          line-height: 22px;
          font-weight: normal;
          color: #53565c;
        }
      }
    }
    .active {
      border: 2px solid var(--main-theme-color, #3e7bfa);
      box-sizing: border-box;
    }
  }
  .set-work {
    margin-top: 36px;
    width: 220px;
    height: 32px;
  }
}

.rightCol {
  display: flex;
  justify-content: right;
  align-items: center;

  .shareSpan {
    margin-right: 16px;
  }
}
.reference {
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  color: #000;
}
.referenceTag {
  font-weight: 400;
  font-size: 12px;
  border-radius: 2px;
  width: 20px;
  text-align: center;
  margin: 0 3px;
  padding-left: 5px;
}

.filterLink {
  font-size: 16px;
  padding: 3px 5px;
  border-radius: 4px;
  font-weight: 500;
}
.filterLink:hover {
  background-color: #f0f0f0;
}

.colorOrange {
  border: 1px solid #f27900;
  color: #f27900;
}
.colorBlue {
  border: 1px solid #3388ff;
  color: #3388ff;
}
.colorInfo {
  border: 1px solid #99a0ac;
  color: #99a0ac;
}

.operationBtn {
  // display: none;
  margin-left: 16px;
}

.add {
  line-height: 40px;
  padding: 0px 12px;
  border-top: 1px solid var(--solid-border-color);
  cursor: pointer;
  color: var(--font-main-color);
  .iconfont {
    margin-right: 4px;
    color: var(--font-second-color);
  }
}
.mainContent {
  &:hover {
    .operationBtn {
      display: block;
    }
  }
  display: flex;
  line-height: 34px;
  height: 34px;
  justify-content: space-between;
  align-items: center;

  .leftSection {
    display: flex;
    .leftText {
      flex-grow: 1;
      flex-shrink: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 6px;
    }
    .leftTag {
      flex-shrink: 0;
    }
  }
  .typeIcon {
    margin-right: 5px;
    padding: 0px 4px;
    border-radius: 4px;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
  }
}

.el-popper {
  &[x-placement^='bottom'] {
    margin-top: -6px;
  }
}
.dropdownContent {
  :deep(.el-input--small) {
    padding: 10px 10px 5px 10px;
  }
}
</style>
