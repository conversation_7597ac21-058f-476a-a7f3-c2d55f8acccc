<template>
  <div style="width: 100%; height: 320px">
    <vone-echarts-card :title="'需求概览'" class="dashboard_card">
      <span slot="tips" style="height: 24px">
        <el-tooltip placement="right">
          <div slot="content">
            待处理需求数：根据软件需求创建的时间，统计软件需求在当前时间段内的数量。<br />
            1.创建时间大于当前月开始日期并且小于当前月结束日期。<br />
            处理中需求数：根据软件需求创建的时间，统计软件需求在当前时间段内的数量。<br />
            1.创建时间小于当前月开始日期并且（实际完成日期大于当前月开始日期或者未完成的软件需求数量）。<br />
            已完成需求数：统计周期内状态为“已完成”的需求数量，根据软件需求实际完成的时间，统计软件需求在当前时间段内的数量。<br />
            1.实际完成时间大于当前月开始日期并且小于当前月结束日期。<br />
            需求优先级占比：根据软件需求状态为未完成（除已完成以外的其他需求状态），统计软件需求在当前时间段内各个优先级的数量。<br />
            1、需求创建时间在当前时间段结束时间之前。
          </div>
          <el-icon
            class="iconfont"
            style="color: var(--font-disabled-color); margin-left: 4px"
            ><el-icon-tips-info-circle
          /></el-icon>
        </el-tooltip>
      </span>
      <div class="count">
        <div @click="showDetail('0')">
          待处理需求数 <span>{{ countData.todoNum }}</span>
        </div>
        <div @click="showDetail('1')">
          处理中需求数 <span>{{ countData.processingNum }}</span>
        </div>
        <div @click="showDetail('2')">
          已完成需求数 <span>{{ countData.completeNum }}</span>
        </div>
      </div>
      <div class="solid" />
      <span> 需求优先级占比 </span>
      <div style="height: 150px">
        <vone-echarts
          v-if="data.length > 0"
          :options="options"
          @chartClick="chartClick"
        />
        <vone-empty v-else />
      </div>
    </vone-echarts-card>
    <detailDialog
      v-if="detailParams.visible"
      v-bind="detailParams"
      v-model="detailParams.visible"
      :query-type="detailParams.queryType"
      :type="detailParams.type"
      :layout="layout"
      :type-data="detailParams.typeData"
    />
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle } from '@element-plus/icons-vue'
import detailDialog from './demand-dialog.vue'
import { getDemandCount, getDeamndChart } from '@/api/vone/dashboard/index'
import dayjs from 'dayjs'
export default {
  components: {
    detailDialog,
    ElIconTipsInfoCircle,
  },
  props: {
    layout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      data: [],
      options: {
        legend: {
          orient: 'vertical',
          left: 'right',
          textStyle: {
            // 图例文字的样式
            color: '#2C2E36',
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
        },
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '35%'],
            radius: ['35%', '60%'],
            avoidLabelOverlap: false,
            label: {
              position: 'inside',
              formatter: '{c}',
              color: '#2C2E36',
            },
            data: [],
            itemStyle: {
              normal: {
                color: function (params) {
                  // 预定义一个颜色数组
                  var colorList = ['#3E7BFA', '#699DFF', '#91BBFF', '#BAD7FF']
                  // 返回每个饼图扇区的颜色
                  return colorList[params.dataIndex % colorList.length]
                },
              },
            },
          },
        ],
      },
      detailParams: {
        visible: false,
      },
      countData: {},
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (
          val &&
          val.time &&
          ((val.latitude == 'org' && val.orgId?.length > 0) ||
            (val.latitude == 'project' && val.projectId?.length > 0) ||
            (val.latitude == 'product' && val.productId?.length > 0))
        ) {
          this.getData(val)
          this.getChartData(val)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    getData(data) {
      getDemandCount({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        date: {
          end: dayjs(data.time && data.time[1]).format('YYYY-MM-DD'),
          start: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        },
      }).then((res) => {
        if (res.isSuccess) {
          this.countData = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getChartData(data) {
      getDeamndChart({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        date: {
          end: dayjs(data.time && data.time[1]).format('YYYY-MM-DD'),
          start: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        },
      }).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.options.series[0].data = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    chartClick(e) {
      this.detailParams = { visible: true, type: 'chart', typeData: e.data }
    },
    showDetail(type) {
      this.detailParams = {
        visible: true,
        queryType: type,
        type: 'count',
        typeData: {},
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard_card {
  height: 100%;
  :deep(.title) {
    display: flex;
    align-items: center;
    height: 24px;
    i {
      height: 16px !important;
    }
  }
  .count {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 34px;
    div {
      display: inline-flex;
      margin-right: 40px;
    }
    span {
      font-size: 26px;
      font-style: normal;
      font-weight: 700;
      line-height: 34px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .solid {
    width: 100%;
    height: 1px;
    background: #f0f0f0;
    margin: 12px 0px;
  }
}
</style>
