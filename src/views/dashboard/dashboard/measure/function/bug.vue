<template>
  <div style="width: 100%; height: 298px">
    <vone-echarts-card :title="'缺陷概览'" class="dashboard_card">
      <span slot="tips" style="height: 24px">
        <el-tooltip placement="right">
          <div slot="content">
            待修复缺陷数：通过缺陷创建时间与缺陷结束时间来计算是否属于当前统计时间段内缺陷状态为待处理的数量。<br />
            1、创建时间小于当前月开始日期并且（结束时间大于当前月开始日期或者结束时间为空）。<br />
            2、创建时间大于当前月开始日期并且小于当前月结束日期。<br />
            修复中缺陷数：通过缺计划完成时间与结束状态来计算是否属于当前统计时间段内缺陷状态为处理中的数量。<br />
            1、缺陷计划完成时间大于当前月开始日期并且小于当前月结束日；<br />
            2、缺陷结束状态为“已修复”。<br />
            已处理缺陷数：通过缺陷计划完成时间与结束状态来计算是否属于当前统计时间段内缺陷状态为已完成的数量。<br />
            1、缺陷计划完成时间大于当前月开始日期并且小于当前月结束日期；<br />
            2、缺陷结束状态是已关闭。<br />
            缺陷修复率：缺陷修复率=修复数/缺陷总数；<br />
            缺陷总数（分母）：当月开始时间前未完成的缺陷数+缺陷创建时间大于当月开始时间且缺陷创建时间小于当月结束时间；<br />
            修复数（分子）：缺陷状态变为已完成的时间，处于当前选择时间段。<br />
            缺陷类型占比：按照当前时间段缺陷总数计算规则，再按缺陷类型进行分组统计数量。
          </div>
          <el-icon
            class="iconfont"
            style="color: var(--font-disabled-color); margin-left: 4px"
            ><el-icon-tips-info-circle
          /></el-icon>
        </el-tooltip>
      </span>
      <div class="count">
        <div @click="showDetail('0')">
          待修复缺陷数 <span>{{ countData.todoNum }}</span>
        </div>
        <div @click="showDetail('1')">
          修复中缺陷数 <span>{{ countData.processingNum }}</span>
        </div>
        <div @click="showDetail('2')">
          已处理缺陷数 <span>{{ countData.completeNum }}</span>
        </div>
        <div>
          缺陷修复率 <span>{{ countData.repairRate }}%</span>
        </div>
      </div>
      <div class="solid" />
      <span> 缺陷类型占比 </span>
      <div style="height: 200px">
        <vone-echarts
          v-if="data.length > 0"
          :options="options"
          @chartClick="chartClick"
        />
        <vone-empty v-else />
      </div>
    </vone-echarts-card>
    <detailDialog
      v-if="detailParams.visible"
      v-bind="detailParams"
      v-model="detailParams.visible"
      :query-type="detailParams.queryType"
      :type="detailParams.type"
      :layout="layout"
      :type-data="detailParams.typeData"
    />
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle } from '@element-plus/icons-vue'
import detailDialog from './bug-dialog.vue'
import { getBugCount, getBugChart } from '@/api/vone/dashboard/index'
import dayjs from 'dayjs'
export default {
  components: {
    detailDialog,
    ElIconTipsInfoCircle,
  },
  props: {
    layout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      options: {
        legend: {
          orient: 'vertical',
          left: 'right',
          textStyle: {
            // 图例文字的样式
            color: '#2C2E36',
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
        },
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '30%'],
            radius: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              position: 'inside',
              formatter: '{c}',
              color: '#2C2E36',
            },
            data: [],
            itemStyle: {
              normal: {
                color: function (params) {
                  // 预定义一个颜色数组
                  var colorList = ['#ED5B56', '#F27D79', '#F7A19E', '#FDD3D2']
                  // 返回每个饼图扇区的颜色
                  return colorList[params.dataIndex % colorList.length]
                },
              },
            },
          },
        ],
      },
      data: [],
      detailParams: {
        visible: false,
      },
      countData: {},
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (
          val &&
          val.time &&
          ((val.latitude == 'org' && val.orgId?.length > 0) ||
            (val.latitude == 'project' && val.projectId?.length > 0) ||
            (val.latitude == 'product' && val.productId?.length > 0))
        ) {
          this.getData(val)
          this.getChartData(val)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    getData(data) {
      getBugCount({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        date: {
          end: dayjs(data.time && data.time[1]).format('YYYY-MM-DD'),
          start: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        },
      }).then((res) => {
        if (res.isSuccess) {
          this.countData = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getChartData(data) {
      getBugChart({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        date: {
          end: dayjs(data.time && data.time[1]).format('YYYY-MM-DD'),
          start: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        },
      }).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.options.series[0].data = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    chartClick(e) {
      this.detailParams = { visible: true, type: 'chart', typeData: e.data }
    },
    showDetail(type) {
      this.detailParams = { visible: true, queryType: type, type: 'count' }
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard_card {
  height: 100%;
  :deep(.title) {
    display: flex;
    align-items: center;
    height: 24px;
    i {
      height: 16px !important;
    }
  }
  .count {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 34px;
    div {
      display: inline-flex;
      margin-right: 20px;
    }
    span {
      font-size: 26px;
      font-style: normal;
      font-weight: 700;
      line-height: 34px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .solid {
    width: 100%;
    height: 1px;
    background: #f0f0f0;
    margin: 12px 0px;
  }
}
</style>
