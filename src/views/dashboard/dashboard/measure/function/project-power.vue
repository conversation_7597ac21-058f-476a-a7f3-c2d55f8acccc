<template>
  <div style="width: 100%; height: 450px">
    <vone-echarts-card :title="'工程能力'" class="dashboard_card">
      <span slot="tips" style="height: 24px">
        <el-tooltip placement="right">
          <div slot="content">
            构建次数：统计周期内编译构建流水线执行的次数,包含了研发构建、测试构建以及统一构建。<br />
            部署次数：统计周期内部署流水线执行的次数。<br />
            构建成功率：统计周期内编译构建流水线成功次数/编译构建流水线执行总次数。<br />
            部署成功率：统计周期内部署流水线成功次数/部署流水线执行总次数。
          </div>
          <el-icon
            class="iconfont"
            style="color: var(--font-disabled-color); margin-left: 4px"
            ><el-icon-tips-info-circle
          /></el-icon>
        </el-tooltip>
      </span>
      <div style="height: 32px; margin-top: 10px">
        <el-date-picker
          v-model="dateValue"
          type="monthrange"
          format="yyyy-MM"
          value-format="yyyy-MM"
          style="height: 32px"
          :default-value="defaultTime"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          @change="changeDate"
        />
      </div>
      <el-row :gutter="24" style="margin-top: 12px">
        <el-col :span="12">
          <div style="padding-right: 10px" @click="getDetail('0')">
            <div class="successBox bg1">
              构建成功率<span>{{ data.successRate }}%</span>
            </div>
            <div style="margin-top: 10px; padding-left: 16px">构建次数</div>
            <div style="height: 200px">
              <vone-echarts :options="options" />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div style="padding-left: 10px" @click="getDetail('1')">
            <div class="successBox bg2">
              部署成功率<span>{{ data1.successRate }}%</span>
            </div>
            <div style="margin-top: 10px; padding-left: 16px">部署次数</div>
            <div style="height: 200px">
              <vone-echarts :options="options1" />
            </div>
          </div>
        </el-col>
      </el-row>
    </vone-echarts-card>
    <detailDialog
      v-if="detailParams.visible"
      :layout="layout"
      :query-type="detailParams.queryType"
      v-bind="detailParams"
      v-model="detailParams.visible"
      :date-value="dateValue"
    />
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle } from '@element-plus/icons-vue'
import { getPipCount } from '@/api/vone/dashboard/index'
import detailDialog from './project-dialog.vue'
import dayjs from 'dayjs'
export default {
  components: {
    detailDialog,
    ElIconTipsInfoCircle,
  },
  props: {
    layout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dateValue: [],
      defaultTime: [],
      data: {},
      data1: {},
      options: {
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#3E7BFA',
            },
            showSymbol: false,
          },
        ],
      },
      options1: {
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#18BF82',
            },
            showSymbol: false,
          },
        ],
      },
      detailParams: { visible: false },
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (
          val &&
          val.time &&
          ((val.latitude == 'org' && val.orgId?.length > 0) ||
            (val.latitude == 'project' && val.projectId?.length > 0) ||
            (val.latitude == 'product' && val.productId?.length > 0))
        ) {
          this.getData(val) // 构建0
          this.getData1(val) // 部署
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.setDefaultTimeRange()
  },
  methods: {
    setDefaultTimeRange() {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1) // 设置为当前月份的上一个月
      this.defaultTime = [start, end]
      this.dateValue = [...this.defaultTime]
    },
    getData(data) {
      getPipCount({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        startDateStr: dayjs(this.dateValue[0]).format('YYYY-MM'),
        endDateStr: dayjs(this.dateValue[1]).format('YYYY-MM'),
        queryType: 0,
      }).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.options.series[0].data = res.data.ydata
          this.options.xAxis.data = res.data.xdata
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getData1(data) {
      getPipCount({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        startDateStr: dayjs(this.dateValue[0]).format('YYYY-MM'),
        endDateStr: dayjs(this.dateValue[1]).format('YYYY-MM'),
        queryType: 1,
      }).then((res) => {
        if (res.isSuccess) {
          this.data1 = res.data
          this.options1.series[0].data = res.data.ydata
          this.options1.xAxis.data = res.data.xdata
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getDetail(type) {
      this.detailParams = { visible: true, queryType: type }
    },
    changeDate() {
      this.getData(this.layout)
      this.getData1(this.layout)
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard_card {
  height: 100%;
  :deep(.title) {
    display: flex;
    align-items: center;
    height: 24px;
    i {
      height: 16px !important;
    }
  }
  :deep(.el-col) {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
  .successBox {
    padding: 17px 16px;
    background-size: cover;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 34px;
    display: flex;
    span {
      font-style: normal;
      font-weight: 700;
      font-size: 26px;
      line-height: 34px;
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .bg1 {
    background-image: url(@/assets/dashboard/measure/project-1.png);
  }
  .bg2 {
    background-image: url(@/assets/dashboard/measure/project-2.png);
  }
}
:deep(.el-range-editor--small.el-input__inner) {
  height: 32px !important;
}
</style>
