<template>
  <div style="width: 100%; height: 320px">
    <vone-echarts-card :title="'需求趋势'" class="dashboard_card">
      <span slot="tips" style="height: 24px">
        <el-tooltip placement="right">
          <div slot="content">
            需求趋势：统计周期内每日新增、完成以及存量(即尚未完成)需求的数量曲线。
          </div>
          <el-icon
            class="iconfont"
            style="color: var(--font-disabled-color); margin-left: 4px"
            ><el-icon-tips-info-circle
          /></el-icon>
        </el-tooltip>
      </span>
      <div style="height: 220px">
        <vone-echarts :options="options" @chartClick="chartClick" />
      </div>
    </vone-echarts-card>
    <detailDialog
      v-if="detailParams.visible"
      v-bind="detailParams"
      v-model="detailParams.visible"
      :type="detailParams.type"
      :layout="layout"
      :data="data"
      :value-data="valueData"
    />
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle } from '@element-plus/icons'
import detailDialog from './demand-dialog.vue'
import { getDemandTrendChart } from '@/api/vone/dashboard/index'
import dayjs from 'dayjs'
export default {
  components: {
    detailDialog,
    ElIconTipsInfoCircle,
  },
  props: {
    layout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      data: {},
      dateValue: '',
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
          },
        },

        legend: {
          data: ['存量', '新增', '完成'],
          left: 'right',
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisPointer: {
            type: 'shadow',
          },
        },
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: [
          {
            name: '存量',
            type: 'bar',
            stack: 'Ad',
            itemStyle: {
              // 为每个柱子设置颜色
              color: '#F5BF1D',
            },
            data: [],
          },
          {
            name: '新增',
            type: 'bar',
            stack: 'Ad',
            itemStyle: {
              // 为每个柱子设置颜色
              color: '#3E7BFA',
            },
            data: [],
          },
          {
            name: '完成',
            type: 'line',
            itemStyle: {
              // 为每个柱子设置颜色
              color: '#18BF82',
            },
            data: [],
          },
        ],
      },
      detailParams: {
        visible: false,
      },
      valueData: {},
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (
          val &&
          val.time &&
          ((val.latitude == 'org' && val.orgId?.length > 0) ||
            (val.latitude == 'project' && val.projectId?.length > 0) ||
            (val.latitude == 'product' && val.productId?.length > 0))
        ) {
          this.getChartData(val)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    getChartData(data) {
      getDemandTrendChart({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        date: {
          end: dayjs(data.time && data.time[1]).format('YYYY-MM-DD'),
          start: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        },
      }).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
          this.options.xAxis.data = res.data.date
          this.options.series[0].data = res.data.residue
          this.options.series[1].data = res.data.add
          this.options.series[2].data = res.data.complete
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    chartClick(params) {
      if (params.name == '(空)') {
        this.$message.warning('当前数据分组,暂不支持查看')
        return
      }
      const type =
        params.seriesName == '新增'
          ? '0'
          : params.seriesName == '存量'
          ? '1'
          : '2'
      this.detailParams = {
        visible: true,
        type: 'trend',
        queryType: type,
        index: params.dataIndex,
        date: params.name,
      }
      this.valueData = {
        type: type,
        name: params.seriesName,
        value: params.data,
        add: this.options.series[1].data?.[params.dataIndex] || 0,
        residue: this.options.series[0].data?.[params.dataIndex] || 0,
        close: this.options.series[2].data?.[params.dataIndex] || 0,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard_card {
  :deep(.title) {
    display: flex;
    align-items: center;
    height: 24px;
    i {
      height: 16px !important;
    }
  }
  height: 100%;
  .count {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    div {
      display: inline-block;
      margin-right: 40px;
    }
    span {
      font-size: 26px;
      font-style: normal;
      font-weight: 700;
      line-height: 34px;
    }
  }
  .solid {
    width: 100%;
    height: 1px;
    background: #f0f0f0;
    margin: 12px 0px;
  }
}
:deep(.el-range-editor--small.el-input__inner) {
  height: 32px !important;
}
</style>
