<template>
  <div style="width: 100%; height: 100%">
    <vone-echarts-card :title="'资源统计'" class="dashboard_card">
      <span slot="tips" style="height: 24px">
        <el-tooltip placement="right">
          <div slot="content">
            任务总数：通过任务计划开始日期与任务实际完成时间来计算是否属于当前统计时间段内的数量。<br />
            1、计划开始日期小于当前月开始日期并且（实际完成日期大于当前月开始日期或者实际完成日期为空）；<br />
            2、计划开始日期大于当前月开始日期并且小于当前月结束日期。<br />
            新增数：通过任务创建时间时间来计算是否属于当前统计时间段内的数量。<br />
            1、计划开始日期大于当前月开始日期并且小于当前月结束日期。<br />
            完成数：通过任务实际完成时间来计算是否属于当前统计时间段内的数量。<br />
            1、实际完成时间大于当前月开始日期并且小于当前月结束日期.
          </div>
          <el-icon
            class="iconfont"
            style="color: var(--font-disabled-color); margin-left: 4px"
            ><el-icon-tips-info-circle
          /></el-icon>
        </el-tooltip>
      </span>
      <div style="display: flex">
        <div style="display: flex; margin-top: 22px">
          <div class="task task1" @click="showDetail('0')">
            任务总数
            <span>{{ taskData.total }}</span>
          </div>
          <div class="task task2" @click="showDetail('1')">
            新增数
            <span>{{ taskData.addNum }}</span>
          </div>
          <div class="task task3" @click="showDetail('2')">
            完成数
            <span>{{ taskData.completeNum }}</span>
          </div>
        </div>
        <div style="flex: 1; height: 170px">
          <vone-echarts v-if="data.length > 0" :options="options" />
          <vone-empty v-else />
        </div>
      </div>
    </vone-echarts-card>
    <detailDialog
      v-if="detailParams.visible"
      :layout="layout"
      :query-type="detailParams.queryType"
      v-bind="detailParams"
      v-model="detailParams.visible"
    />
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle } from '@element-plus/icons-vue'
import detailDialog from './task-dialog.vue'
import { getTaskCount } from '@/api/vone/dashboard/index'
import dayjs from 'dayjs'
export default {
  components: {
    detailDialog,
    ElIconTipsInfoCircle,
  },
  props: {
    layout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      taskData: {},
      data: [],
      detailParams: { visible: false },
      options: {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['70%', '90%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              formatter: '{num|' + 88 + '}' + '\n\r' + '{name|总数}',
              rich: {
                num: {
                  fontSize: 18,
                  color: '#777F8E',
                },
                name: {
                  fontFamily: '微软雅黑',
                  fontSize: 14,
                  color: '#777F8E',
                  lineHeight: 30,
                },
              },
            },
            data: [
              { value: 20, name: '需求' },
              { value: 18, name: '测试' },
              { value: 10, name: 'UI' },
              { value: 20, name: '前端' },
              { value: 20, name: '后端' },
            ],
            itemStyle: {
              normal: {
                color: function (params) {
                  // 预定义一个颜色数组
                  var colorList = [
                    '#8474ED',
                    '#3E7BFA',
                    '#3DAAF2',
                    '#18BF82',
                    '#F5BF1D',
                  ]
                  // 返回每个饼图扇区的颜色
                  return colorList[params.dataIndex % colorList.length]
                },
              },
            },
          },
        ],
      },
    }
  },
  watch: {
    layout: {
      handler(val) {
        if (
          val &&
          val.time &&
          ((val.latitude == 'org' && val.orgId?.length > 0) ||
            (val.latitude == 'project' && val.projectId?.length > 0) ||
            (val.latitude == 'product' && val.productId?.length > 0))
        ) {
          this.getData(val)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    getData(data) {
      getTaskCount({
        projectIds: data.projectId || [],
        orgIds: data.orgId || [],
        productsetIds: data.productId || [],
        startDate: dayjs(data.time && data.time[0]).format('YYYY-MM-DD'),
        endDate: dayjs(data.time && data.time[1]).format('YYYY-MM-DD'),
      }).then((res) => {
        if (res.isSuccess) {
          this.taskData = res.data
          this.data = res.data.data
          this.options.series[0].data = res.data.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    showDetail(type) {
      this.detailParams = {
        visible: true,
        queryType: type,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard_card {
  height: 100%;
  :deep(.title) {
    display: flex;
    align-items: center;
    height: 24px;
    i {
      height: 16px !important;
    }
  }
  .task {
    background-size: cover;
    width: 186px;
    height: 110px;
    padding: 20px;
    span {
      display: block;
      font-size: 26px;
      font-weight: 700;
      line-height: 34px;
      margin-top: 6px;
      cursor: pointer;
    }
  }
  .task1 {
    background-image: url(@/assets/dashboard/measure/task-1.png);
  }
  .task2 {
    background-image: url(@/assets/dashboard/measure/task-2.png);
    margin-left: 20px;
  }
  .task3 {
    background-image: url(@/assets/dashboard/measure/task-3.png);
    margin-left: 20px;
  }
}
</style>
