<template>
  <div v-left-to-right-drag class="drag-bar" />
</template>

<script>
export default {
  name: 'Left2RightDragBar',
  directives: {
    'left-to-right-drag': {
      inserted(el, binding) {
        el.onmousedown = function (e) {
          const init = e.clientX
          const parent = el.parentNode
          const initWidth = parent.offsetWidth
          document.onmousemove = function (e) {
            const end = e.clientX
            const newWidth = end - init + initWidth
            if (newWidth < initWidth) {
              parent.style.width = 240 + 'px'
            } else if (
              newWidth < document.body.clientWidth / 2 - 100 &&
              newWidth > 10
            ) {
              parent.style.width = newWidth + 'px'
            }
          }
          document.onmouseup = function () {
            document.onmousemove = document.onmouseup = null
          }
        }
      },
    },
  },
}
</script>

<style lang="scss" scoped>
.drag-bar {
  height: 100%;
  width: 1px;
  position: absolute;
  right: 0px;
  top: 0;
  cursor: col-resize;
  background-color: #ebeef5;
  border: 0px;
}

.drag-bar:hover {
  width: 3px;
}
</style>
