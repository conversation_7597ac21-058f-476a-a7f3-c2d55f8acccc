import request from '@/utils/axios-api'

// 登录
export function login(data) {
  return request({
    url: '/api/oauth/oauth/noToken/login',
    method: 'post',
    data,
  })
}
// 获取用户信息
export function getInfo(data) {
  return request({
    url: '/api/oauth/oauth/anno/verify',
    method: 'get',
    data,
  })
}
// 获取用户权限
export function getUserPermission() {
  return request({
    url: '/api/oauth/oauth/resource/visible',
    method: 'get',
  })
}
// 获取动态菜单
export function getRouterMenu() {
  return request({
    url: '/api/oauth/oauth/menu/router',
    method: 'get',
  })
}
// 退出登录
export function logout(userId, token) {
  return request({
    url: `/api/oauth/oauth/noToken/logout?userId=${userId}&token=${token}`,
    method: 'post',
  })
}

// 查询当前登录用户自定义固定列
export function getFixedMenu() {
  return request({
    url: '/api/base/base/personalizedFixedMenu/find',
    method: 'get',
  })
}

// 保存菜单
export function holdMenu(data) {
  return request({
    url: '/api/base/base/personalizedFixedMenu',
    method: 'PUT',
    data,
  })
}
