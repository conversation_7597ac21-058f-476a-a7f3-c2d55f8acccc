import request from '@/utils/axios-api'

// 标签list
export function findALlTestTabs() {
  return request({
    url: '/api/testm/testTab/findALlTestTabs',
    method: 'get',
  })
}

export function editTestTabs(data) {
  return request({
    url: '/api/testm/testTab',
    method: data.id ? 'put' : 'post',
    data,
  })
}
// 标签add
export function delTestTabs(data) {
  return request({
    url: '/api/testm/testTab',
    method: 'delete',
    data,
  })
}
// detail
export function detailsTestTabs(id) {
  return request({
    url: `/api/testm/testTab/${id}`,
    method: 'get',
  })
}

// detail
export function testCaseTreeSwap(data) {
  return request({
    url: `/api/testm/testTab/tabDrag`,
    method: 'put',
    data,
  })
}
// detail
export function queryCaseTree(data) {
  return request({
    url: `/api/testm/testTab/findTabsLikeName`,
    method: 'post',
    data,
  })
}
/**
 * @description 查询或新建标签
 * @param {string} name 标签名称
 */
export function queryCase(data) {
  return request({
    url: `/api/testm/testTab/findTestTabsByNameWhileWriting`,
    method: 'post',
    data,
  })
}
/**
 * @description 根据用例库id查询用例库标签列表
 * @param {string} id 用例库id
 */
export function getCaseLibraryTags(data) {
  return request({
    url: '/api/testm/testTab/getTestTabByLibraryId',
    method: 'post',
    data,
  })
}
// 标签列表分页
export function pageTab(data) {
  return request({
    url: `/api/testm/testTab/pageTab`,
    method: 'post',
    data,
  })
}
