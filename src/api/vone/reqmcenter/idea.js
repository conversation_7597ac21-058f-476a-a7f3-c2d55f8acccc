import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 查询意向列表
export function apiAlmIdeaPage(data) {
  return request({
    url: '/api/alm/alm/idea/page',
    method: 'post',
    data,
  })
}
// 下拉条件查询意向列表无权限
export function ideaListByCondition(data) {
  return request({
    url: '/api/alm/alm/idea/queryListByCondition',
    method: 'post',
    data,
  })
}

// 意向新增/编辑
export function apiAlmIdeaAddOrEdit(data) {
  return request({
    url: '/api/alm/alm/idea',
    method: data.id ? 'put' : 'post',
    data,
  })
}

/**
 * 根据ID删除意向
 * @param id
 */
export async function apiAlmIdeaDel(data) {
  return request({
    url: `/api/alm/alm/idea`,
    method: 'delete',
    data,
  })
}

/**
 * 根据ID查询意向详情
 * @param id
 */
export async function apiAlmIdeaInfo(id) {
  return request({
    url: `/api/alm/alm/idea/${id}`,
    method: 'get',
  })
}

/**
 *查询意向可流转的状态节点
 * @param id
 */
export async function apiAlmIdeaFindNextNode(ideaId) {
  return request({
    url: `/api/alm/alm/idea/findNextNode/${ideaId}`,
    method: 'get',
  })
}

// 意向状态流转
export function apiAlmIdeaFlow(ideaId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/alm/alm/idea/transitionState/${ideaId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put',
  })
}
// 批量查询查询意向列表
export function ideaListQuery(data) {
  return request({
    url: '/api/alm/alm/idea/query',
    method: 'post',
    data,
  })
}
// 查询意向或者需求关联的父子需求
export function ideaParent(data) {
  return request({
    url: '/api/alm/alm/requirement/selectRequirementByIdeaIdOrRequirementId',
    method: 'post',
    data,
  })
}

// 下载导入模板
export function downloadIdeaImportTemplate(params) {
  return request({
    url: `/api/alm/alm/idea/downloadImportTemplate`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
    params,
  })
}

// 导入Excel
export function apiIdeaImport(data) {
  return request({
    url: `/api/alm/alm/idea/importProgram`,
    method: 'post',
    data: requestUtils.fileFormData(data),
  })
}

// 意向关联需求
export function apiAlmPutIdeaAndIssue(data) {
  return request({
    url: `/api/alm/alm/requirement/correlatedIdea`,
    method: 'put',
    data,
  })
}
// 跟踪视图
export async function getTraceViewByIdeaId(ideaId) {
  return request({
    url: `/api/alm/alm/idea/getTraceViewByIdeaId/${ideaId}`,
    method: 'get',
  })
}
// 跟踪视图基础信息
export async function getTraceViewBaseInfoByIdeaId(ideaId) {
  return request({
    url: `/api/alm/alm/idea/getTraceViewBaseInfoByIdeaId/${ideaId}`,
    method: 'get',
  })
}

// 用户需求关联需求，主办产品/辅办产品
export async function issueIdeaProduct(ideaId) {
  return request({
    url: `/api/alm/alm/issueIdeaProduct/productAndRequirement/${ideaId}`,
    method: 'GET',
  })
}

/**
 * 根据产品Id删除用户需求关联的产品
 * @param id
 */
export async function issueIdeaProductDel(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProduct`,
    method: 'delete',
    data,
  })
}

/**
 * 修改用户需求关联的产品
 * @param id
 */
export async function issueIdeaProductPut(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProduct`,
    method: 'PUT',
    data,
  })
}

/**
 * 添加用户需求关联的产品
 * @param id
 */
export async function issueIdeaProductAdd(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProduct/batchSave`,
    method: 'POST',
    data,
  })
}

/**
 * 用户需求批量关联需求
 * @param id
 */
export async function issueIdeaProductRequirement(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProductRequirement/batchSave`,
    method: 'POST',
    data,
  })
}

/**
 * 根据产品Id删除用户需求关联的产品
 * @param id
 */
export async function issueIdeaProductRequirementDel(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProductRequirement/delete`,
    method: 'delete',
    data,
  })
}

/**
 * 需求新增添加关联关系
 * @param id
 */
export async function issueIdeaProductRelation(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProductRequirement`,
    method: 'POST',
    data,
  })
}
