import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

export function apiBaseDictEnumList(data) {
  return request({
    url: '/api/tm-base/common/enum/list',
    method: 'post',
    data,
  })
}

// licenses到期提醒
export function apiOuthLicenses() {
  return request({
    url: `/api/oauth/oauth/verify/licenses/noToken`,
    method: 'GET',
  })
}
// 上传licenses文件
export function apiOuthUploadLicenses(data) {
  return request({
    url: '/api/oauth/oauth/upload/licenses/noToken',
    method: 'post',
    data: requestUtils.fileFormData(data),
  })
}
