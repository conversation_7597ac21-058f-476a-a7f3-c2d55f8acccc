import axiosApi from '@/utils/axios-api'

/**
 * 获取脚本列表
 * @param {typeId} params
 */
export async function apiBaseScriptFiles(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/page`,
    method: 'post',
    data,
  })
}

/**
 * 删除脚本
 * @param { ids: String } params
 */
export async function apiBaseScriptFilesDelete(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript`,
    method: 'DELETE',
    data,
  })
}

/**
 * 脚本管理--权限分配---保存
 */
export async function apiBaseScriptGrant(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScriptOrg/batchAnthorize`,
    method: 'put',
    data: data,
  })
}

/**
 * 脚本管理--权限分配--选中数据
 */
export async function apiBaseScriptDivision(params) {
  return axiosApi({
    url:
      `/api/pipeline/pipeline/pipelineScriptOrg/selectScriptOrgById?sctiptId=` +
      params.sctiptId,
    method: 'get',
    params,
  })
}

// 脚本管理---新增脚本
export async function apiBaseScriptAddFile(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScript/addScriptFile',
    method: 'post',
    data,
  })
}

/**
 * 脚本管理新增获取对话框数据
 */
export async function apiBaseScriptSelectByTypeId(data) {
  return axiosApi({
    url: `/api/base/base/dictionary/query`,
    method: 'post',
    data,
  })
}
/**
 * 脚本管理--批量上传---下载批量上传模板
 */
export async function apiBaseScriptDownload() {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/download`,
    method: 'get',
    responseType: 'blob',
  })
}
/**
 * 脚本管理--批量上传---上传脚本
 */
export async function apiBaseScriptbatchUpload(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/batchUploadScriptFile`,
    method: 'post',
    data,
  })
}

// 批量上传脚本 ---保存描述
export async function apiBaseScriptUpdataDescription(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScript/saveDescription',
    method: 'put',
    data,
  })
}

/**
 * 脚本管理--除了自定义脚本以外的下发脚本持续集成引擎下拉框数据
 */
export async function apiBaseScriptSendByCategoryId(data) {
  return axiosApi({
    url: `/api/tm-base/base/engine/query`,
    method: 'post',
    data,
  })
}

/**
 * 脚本管理----自定义脚本--服务应用下拉框数据
 */
export async function apiBaseScriptSelectBusiness(params) {
  return axiosApi({
    url: `/api-cmdb/api/cmdb/applicationBase/selectHasDeploySystemByBusinessSystemIds`,
    method: 'get',
    params,
  })
}
/**
 * 脚本管理----自定义脚本--应用环境下拉框数据
 */
export async function apiBaseScriptSelectEnvi(params) {
  return axiosApi({
    url: `/api-cmdb/api/cmdb/applicationModuleDeploy/selectHasDeployEnvByBusinessSystemIdsAndSystemIds`,
    method: 'get',
    params,
  })
}
/**
 * 脚本管理--下发脚本--点击下发按钮下发脚本
 */
export async function apiBaseScriptCustomBatchSend(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/batchSendScript`,
    method: 'post',
    data,
  })
}

/**
 * 脚本管理--自定义脚本--下发脚本--点击执行按钮执行脚本
 */
export async function apiBaseScriptCustomExecution(params) {
  return axiosApi({
    url: `/api-cmdb/api/cmdb/script/executionCustomScript`,
    method: 'post',
    params,
  })
}
/**
 * 获取脚本下发历史
 * @param { scriptId: String } params
 */
export async function apiBaseSelectScriptSendHistoryByScriptId(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScriptSendHistory/selectScriptSendHistoryByScriptId`,
    method: 'get',
    data,
  })
}

// 脚本管理---编辑脚本
export async function apiBaseScriptEditScript(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScript/selectContentById',
    method: 'get',
    data,
  })
}

// 编辑脚本 ---保存脚本
export async function apiBaseScriptUpdataFile(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScript/updateScriptFile',
    method: 'put',
    data: data,
  })
}
/**
 * 脚本管理--删除目标机脚本
 */
export async function apiBaseScriptDeleteTarget(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScriptSend/selectScriptSendByScriptId`,
    method: 'get',
    data,
  })
}

/**
 * 脚本管理--删除目标机脚本--选中删除
 */
export async function apiBaseScriptDeleteChecked(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/checkScriptFiles`,
    method: 'post',
    data: data,
  })
}

/**
 * 脚本管理--删除目标机脚本--确认删除
 */
export async function apiBaseScriptDeleteCheckedSure(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/delScriptSend`,
    method: 'post',
    data: data,
  })
}

/**
 * 脚本管理----自定义脚本--下发脚本--查询table表格
 */
export async function apiBaseScriptCustomTable(params) {
  return axiosApi({
    url: `/api-cmdb/api/cmdb/applicationModuleDeploy/selectDeployByParam`,
    method: 'get',
    params,
  })
}

/**
 * 脚本管理--批量查询脚本
 */
export async function apiPiplineScriptQuery(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/query`,
    method: 'post',
    data: data,
  })
}
