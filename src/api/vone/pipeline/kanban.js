import request from '@/utils/request'

// 任务---任务流转
export function apiTaskCheckStatus(taskKey, projectKey) {
  return request({
    url: `/api-alm/api/alm/tm_task_dict_taskflow/idea/status/${taskKey}/${projectKey}`,
    method: 'GET',
  })
}

/**
 * 查询看板列表
 */
export function apiAlmGetProjectKanbanList(params) {
  return request(`/api-alm/projectm/kanban/getKanbanModel`, {
    params,
  })
}

/**
 * 获取看板列 列表
 */
export function apiAlmGetProjectKanbanColumn(params) {
  return request(`/api-alm/projectm/kanban/getKanbanColumnModel`, {
    params,
  })
}

/**
 * 获取项目下需求分类、缺陷分类、任务分类列表
 * @param {string} projectKey projectKey
 */
export function apiAlmGetIssueTypeList(projectKey) {
  return request(`/api-alm/projectm/kanban/issueTypeList/${projectKey}`)
}

/**
 * 创建看板
 */
export function apiAlmPostProjectCreateKanban(data) {
  return request(`/api-alm/projectm/kanban/createKanbanModel`, {
    method: 'post',
    data,
  })
}

/**
 * 查询看板下的实例数据
 */
export function apiAlmGetProjectKanbanIssue(params) {
  return request(`/api-alm/projectm/kanban/getKanbanIssue`, {
    params,
  })
}

/**
 * 获取项目下的未归档迭代列表
 */
export function apiAlmGetProjectSprint(params) {
  return request(`/api-alm/projectSprint`, {
    params,
  })
}

/**
 * 实例状态流转
 */
export function apiAlmPostProjectChangeIssueStatus(data) {
  return request(`/api-alm/projectm/kanban/changeIssueStatus`, {
    method: 'post',
    data,
  })
}

/**
 * 删除看板
 * @param {integer} id id
 */
export function apiAlmDeleteProjectKanban(id) {
  return request(`/api-alm/projectm/kanban/deleteKanbanModel/${id}`, {
    method: 'delete',
  })
}

// 查询里程碑阶段
export async function apiProjectGetMilestoneList(params) {
  return request({
    url: `/api-alm/project-milestone/project-milestone-stage/list`,
    method: 'get',
    params,
  })
}

export async function apiDefectCurStateList(defectKey, projectKey) {
  return request({
    url: `/api-alm/api/alm/df_defecrt_dict_taskflow/idea/status/${defectKey}/${projectKey}`,
    method: 'get',
  })
}

// 需求Id查询需求可流转到的节点信息
export function listWorkflowBoardEdges(params, projectKey) {
  return request({
    url:
      `/api-alm/api/alm/dict_taskflow/issue/status?issueKey=` +
      params +
      '&projectKey=' +
      projectKey,
    method: 'GET',
  })
}

/**
 * 批量保存用户列表自定义列信息
 * @param {string} appKey appKey
 * @param {string} tableKey tableKey
 */
export function apiBasePutCustomColumn(appKey, tableKey, kanbanId, data) {
  return request(
    `/api-base/api/base/system/personalization/custom/column/${appKey}/${tableKey}/${kanbanId}`,
    {
      method: 'put',
      data,
    }
  )
}

/**
 * 查询用户列表自定义列信息
 * @param {string} appKey appKey
 * @param {string} tableKey tableKey
 */
export function apiBaseGetCustomColumn(appKey, tableKey, kanbanId) {
  return request(
    `/api-base/api/base/system/personalization/custom/column/${appKey}/${tableKey}/${kanbanId}`
  )
}

// 视图切换，按需求\人员展示
export async function apiGetKBFlow(params) {
  return request({
    url: `/api-alm/projectm/kanban/getLaneKanbanIssue`,
    method: 'get',
    params,
  })
}
