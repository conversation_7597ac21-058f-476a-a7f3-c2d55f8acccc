// import request from '@/utils/request'
import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 获取应用组件页面服务应用
export function apiBaseGetapplication(data) {
  return request({
    url: '/api/cmdb/cmdb/application/page',
    method: 'POST',
    data,
  })
}

// 业务系统--配置--tab页--服务应用 ----环境下拉框

export function apiBaseDeleteApplilcation(params) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationBase/deleteApplilcation',
    method: 'DELETE',
    params,
  })
}

// 服务应用--新增保存数据

export function apiBaseAddApplicationBaseData(data) {
  return request({
    url: '/api/cmdb/cmdb/application',
    method: 'post',
    data,
  })
}

// 服务应用--删除数据

export function apiBaseDelApplicationBaseData(data) {
  return request({
    url: '/api/cmdb/cmdb/application',
    method: 'DELETE',
    data,
  })
}

// 业务系统--配置--tab页--服务应用--新增保存数据

export function apiBasePutApplicationBaseData(data) {
  return request({
    url: '/api/cmdb/cmdb/application',
    method: 'put',
    data,
  })
}

export function apiBaseBusinessSelectEnvDictById(id) {
  return request({
    url: '/api-cmdb/api/cmdb/businessSystem/selectEnvDictById/' + id,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--获取基本信息

export function apiBaseSelectBaseDatagById(id) {
  return request({
    url: `/api/cmdb/cmdb/application/${id}`,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--获取便签页

export function apiBaseGetApplicationModuleByType(params) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationBase/getApplicationModuleByType',
    method: 'get',
    params,
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--权限分配保存

export function apiBaseUpdateApplicationBaseData(data) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationBase/updateApplicationBaseData',
    method: 'post',
    data: requestUtils.formData(data),
  })
}

// 业务系统--配置--编辑--工程配置

export function apiBuniesSelectWarehouseBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationWarehouse/findByApplicationId/${applicationId}`,
    method: 'GET',
  })
}

// 业务系统--配置--编辑--仓库配置

export function apiBuniesSselectEngineByCategoryId(params) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationEngine/selectEngineListByCategoryId',
    method: 'get',
    params,
  })
}

// 修改服务应用授权信息

export function apiBaseSaveApplicationOrg(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationOrg',
    method: 'put',
    data,
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--权限分配

export function apiBaseSelectOrgBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationOrg/${applicationId}`,
    method: 'GET',
  })
}

// 业务系统--配置--新增服务应用---保存并开始配置

export function apiBaseSelectEngineByTypeId(typeId) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationEngine/selectEngineByTypeId/' + typeId,
    method: 'get',
  })
}

// 业务系统--仓库配置--仓库名称下拉框
export async function apiCmdbSelectCodeRepByCategoryIdAndEngineId(engineId) {
  return request({
    url: `/api/cmdb/cmdb/applicationWarehouse/findGroupsAndUsersByEngineId/${engineId}`,
    method: 'GET',
  })
}

// 业务系统-仓库配置--保存

export function apiCmdbUpdateWarehouseConfig(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationWarehouse/updateApplicationWarehouse',
    method: 'put',
    data,
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--工程配置-保存

export function apiBaseSaveModule(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationModule',
    method: 'put',
    data,
  })
}

/**
 * 分支检测
 * @param {*} params
 */
export async function apiCmdbCheckCodeLinePath(params) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/checkCodeLinePath`,
    params,
  })
}

// 根据服务应用ID查询分支列表
export function apiBuniessSelectCodeLineBySystemId(data) {
  return request({
    url: `/api/cmdb/cmdb/applicationBranch/page`,
    method: 'POST',
    data,
  })
}

// 业务系统--分支配置

export async function apiBuniessSelectSvnCodeLineDetail(params) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/selectSvnCodeLineDetail`,
    method: 'GET',
    params,
  })
}

// 业务系统--配置--分支配置--新增--源分支
export async function apiBuniessDictCodeLineStrategy(id) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/getDictCodeLineByStrategy/${id}`,
    method: 'GET',
  })
}

// 业务系统--配置-分支配置--删除
export async function apiBuniessDeleteCodeLineBySystemId(
  applicationId,
  branchCode
) {
  return request({
    url: `/api/cmdb/cmdb/applicationBranch/findByApplicationId/${applicationId}/${branchCode}`,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--授权信息

export function apiBaseSelectOrgAndUserBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationOrg/${applicationId}`,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--操作历史

export function apiBaseQueryOperationLog(params) {
  return request({
    url: '/api-base/api/base/log/operation',
    method: 'get',
    params,
  })
}

// 业务系统--引擎配置 ==保存
export async function apiBuniesUpdateEngineConfig(data) {
  return request({
    url: `/api/cmdb/cmdb/applicationEngine/updateApplicationEngine`,
    method: 'put',
    data,
  })
}

// 业务系统--引擎配置 =查询引擎信息
export async function apiBuniesSelectEngineBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationEngine/findByApplicationId/${applicationId}`,
    method: 'get',
  })
}

// 业务系统--引擎配置 =查询环境
export async function apiBuniesSelectTagByType(params) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationEngine/getDictDataByParentKey`,
    method: 'get',
    params,
  })
}

export async function apiBaseSelectByTagDictKeyAndTagParentKey({
  dictKey,
  parentKey,
}) {
  return request({
    url: `/api-base/api/base/system/engine/selectByTagDictKeyAndTagParentKey/${dictKey}/${parentKey}`,
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--制品信息

export function apiBaseGetProductBaseBySystemId(params) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationProduct/getProductBySystemId',
    method: 'get',
    params,
  })
}

/**
 * 制品库---下载
 */
export async function apiBuniessProductOperate(params) {
  return request({
    url: `/api-pipeline/productOperate/downLoad`,
    method: 'get',
    params,
    // 下载文件配置
    transformResponse: [requestUtils.download({ fileName: params.name })],
    responseType: 'blob',
  })
}

// 制品库---删除
export function apiBuniessDeleteProductById(id) {
  return request({
    url: `/api-pipeline/productOperate/deleteProduct/${id}`,
    method: 'delete',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--流水线

export function apiBaseGetPipelineBySystemId(params) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationPipeline/getPipelineBySystemId',
    method: 'get',
    params,
  })
}

// 获取流水线--日志
export async function apiBuniessGetBuildLog(id) {
  return request({
    url: `/api-pipeline/publish/pipelineExhibit/getBuildLogByPipelineIdAndBuildId/${id}`,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--部署配置

export function apiBaseSelectEnvDetailBySystemId(params) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationEnv/selectEnvDetailBySystemId',
    method: 'get',
    params,
  })
}

// 业务系统--配置--tab页--DB配置---保存

export function apiBuniessUpdateDbCompentsBySystemId(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationDb/updateApplicationDb',
    method: 'PUT',
    data,
  })
}

// 业务系统--配置--tab页--DB配置---查询

export function apiBuniessSelectDbCompentsBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationDb/findByApplicationId/${applicationId}`,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--DB目录

export function apiBaseSelectDbCatalogBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationDbCatalog/findByApplicationId/${applicationId}`,
    method: 'get',
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--DB目录--保存

export function apiBaseSaveDbCatalog(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationDbCatalog/updateApplicationDbCatalog',
    method: 'put',
    data,
  })
}

// 业务系统--配置--tab页--服务应用 ----编辑--TAB页--部署配置

export function apiBaseSelectNoSystemAppCompentsBySystemId(params) {
  return request({
    url: '/api-cmdb/api/cmdb/app/selectNoSystemAppCompentsBySystemId',
    method: 'get',
    params,
  })
}

// 根据服务应用id查询部署配置

export function apiBaseSelectDeployConfigBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationDeploy/findByApplicationId/${applicationId}`,
    method: 'get',
  })
}

// 业务系统--配置-部署配置--保存
export async function apiBuniesSaveDeployConfig(data) {
  return request({
    url: `/api/cmdb/cmdb/applicationDeploy/updateApplicationDeploy`,
    method: 'put',
    data,
  })
}

/**
 * 部署配置运行版本检测一致性
 * @param {*} params
 */
export function apiCmdbversionChecking(params) {
  return request({
    url: '/api-pipeline/publish/pipelineExhibit/versionChecking',
    method: 'get',
    params,
  })
}

/**
 * 如果不一致更新版本信息
 * @param {*} params
 */
export function apiCmdbupdateDeployInfo(params) {
  return request({
    url: '/api-pipeline/publish/pipelineExhibit/updateDeployInfo',
    method: 'put',
    data: params,
  })
}

// 业务系统--分支配置--检查
export async function apiBuniessModuleCodeLineBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationModuleBranch/findByApplicationId/${applicationId}`,
    method: 'GET',
  })
}

// 业务系统--工程分支配置

export async function apiBuniesSelectGitCodeLineDetail(params) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/selectGitCodeLineDetail`,
    method: 'GET',
    params,
  })
}
// 工程分支--查询列表
export async function apiCmdbModuleBranchNoPage(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationModuleBranch/findByApplicationId/${applicationId}`,
    method: 'get',
  })
}

// 业务系统--工程分支配置--保存

export async function apiBuniesSaveModuleCodeLine(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationModuleCodeLine/saveModuleCodeLine`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}

// 业务系统--仓库配置---新增组

export async function apiBuniesGitCreateGroup(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationEngine/gitCreateGroup`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}

// 业务系统--仓库配置---新增用户

export async function apiBuniesGitCreateUser(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationEngine/gitCreateUser`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}

// 业务系统--工程分支配置--新增仓库地址

export async function apiBuniesGetRepNameListByEngineId(params) {
  return request({
    url: `/api-scm/api/codeManage/getRepNameListByEngineId`,
    // url: `/api-cmdb/api/cmdb/codeManage/getRepNameListByEngineId`,
    method: 'GET',
    params,
  })
}

// 业务系统--配置--编辑--仓库配置--保存

export function apiBuniesScreateSvnRepPath(data) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationEngine/createSvnRepPath',
    method: 'post',
    data: requestUtils.formData(data),
  })
}

/**
 * git添加工程信息
 * @param {*} params
 * @param {String} params.systemId
 * @param {String} params.moduleId
 */
export async function apiCmdbAddGitModule(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationModule/addGitModule`,
    method: 'post',
    data: requestUtils.formData(data),
  })
}

// 业务系统--配置--分支配置--新增--分支名称下拉框
export async function apiBuniessCodeLineName(strategy) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/getDictCodeLineByStrategy/${strategy}`,
    method: 'GET',
  })
}
// 业务系统--配置-分支配置--保存git
export async function apiBuniessImportSvnCodeLine(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/importSvnCodeLine`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}
// 业务系统--配置-分支配置--保存svn
// POST /api/cmdb/applicationCodeLine/importGitCodeLine
export async function apiBuniessImportGitCodeLine(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/importGitCodeLine`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}

// 业务系统--分支配置--检查
export async function apiBuniessCheckGitPath(params) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationModule/checkGitProject`,
    method: 'GET',
    params,
  })
}

/**
 * 复制Git分支
 * @param {*} data
 */
export async function apiCmdbCopyGitCodeLine(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/copyGitCodeLine`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}
/**
 * 复制SVN分支
 * @param {*} data
 */
export async function apiCmdbCopySvnCodeLine(data) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/copySvnCodeLine`,
    method: 'POST',
    data: requestUtils.formData(data),
  })
}

/**
 * 业务系统服务应用-分支-查源分支
 */
export function apiCmdbCodeLineSource(params) {
  return request({
    url: `/api-cmdb/api/cmdb/applicationCodeLine/selectCodeLineBySystemIdAndModuleId`,
    method: 'get',
    params,
  })
}

/**
 * 不分页查询所有服务应用
 * @param {*} params
 */
export function apiCmdbServerNoPage(data) {
  return request({
    url: '/api/cmdb/cmdb/application/query',
    method: 'POST',
    data,
  })
}

/**
 * 根据仓库引擎id查询组或用户名
 */
export function apiCmdbFindGroupsAndUser(engineId) {
  return request({
    url: `/api/cmdb/cmdb/applicationWarehouse/findGroupsAndUsersByEngineId/${engineId}`,
    method: 'get',
  })
}

/**
 * 根据服务应用查询工程信息
 * @param {*} params
 */
export function apiCmdbApplicationModule(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationModule/page',
    method: 'POST',
    data,
  })
}
/**
 * 根据服务应用ID查询工程分支列表
 * @param {*} params
 */
export function apiBuniessProjectBranchBySystemId(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationBranch/page',
    method: 'POST',
    data,
  })
}

/**
 * 根据服务应用id查询分支
 */
export function apiCmdbFindBranch(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationBranch/findBranchToCodeMByApplicationId/${applicationId}`,
    method: 'get',
  })
}

/**
 * 根据服务应用ID查询工程分支列表
 * @param {*} params
 */
export function apiCmdbNewBranch(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationBranch',
    method: 'POST',
    data,
  })
}

// 服务应用部署分页列表查询
export function apiCmdbServerList(data) {
  return request({
    url: '/api/cmdb/cmdb/applicationDeploy/page',
    method: 'POST',
    data,
  })
}
// 服务应用部署分页列表查询
export function apiCmdbServerGraph(applicationId) {
  return request({
    url:
      '/api/cmdb/cmdb/application/findTopologyByApplicationId/' + applicationId,
    method: 'get',
  })
}

/**
 * 查询构建配置列表
 * @param {*} params
 */
export function apiCmdbBuildFilePage(data) {
  return request({
    url: '/api/cmdb/applicationBuildFile/page',
    method: 'POST',
    data,
  })
}

// 构建配置---新增
export function apiCmdbBuildFileImport(data) {
  return request({
    url: `/api/cmdb/applicationBuildFile`,
    method: 'post',
    data,
  })
}

/**
 * 根据Id查询构建参数文件内容
 */
export function apiCmdbBuildFileGetById(id) {
  return request({
    url: `/api/cmdb/applicationBuildFile/${id}`,
    method: 'get',
  })
}

/**
 * 构建参数--修改
 */
export async function apiCmdbBuildFilePut(data) {
  return request({
    url: `/api/cmdb/applicationBuildFile`,
    method: 'put',
    data,
  })
}

// 删除构建参数文件
export function apiCmdbBuildFileDel(data) {
  return request({
    url: '/api/cmdb/applicationBuildFile',
    method: 'DELETE',
    data,
  })
}

// 查询构建参数文件下发历史记录
export function apiCmdbBuildFileSendHistory(data) {
  return request({
    url: `/api/cmdb/applicationBuildFileSendHistory/page`,
    method: 'post',
    data,
  })
}

// 下发构建参数
export function apiCmdbBuildFileSend(data) {
  return request({
    url: `/api/cmdb/applicationBuildFileSend/send`,
    method: 'post',
    data,
  })
}

// 查询目标机参数文件
export function apiCmdbBuildFileSendPage(data) {
  return request({
    url: `/api/cmdb/applicationBuildFileSend/page`,
    method: 'post',
    data,
  })
}

// 制品库---删除
export function apiCmdbBuildFileSendDel(buildFileId, data) {
  return request({
    url: `/api/cmdb/applicationBuildFileSend/${buildFileId}`,
    method: 'delete',
    data,
  })
}

// 服务应用查询tag列表
export function apiCmdbServerTagList(data) {
  return request({
    url: `/api/cmdb/cmdb/applicationTag/page`,
    method: 'post',
    data,
  })
}

// 服务应用查询tag列表
export function apiCmdbServerTagListNoPage(data) {
  return request({
    url: `/api/cmdb/cmdb/applicationTag/query`,
    method: 'post',
    data,
  })
}

// 服务应用删除tag
export function apiCmdbServerTagDel(ids) {
  return request({
    url: `/api/cmdb/cmdb/applicationTag`,
    method: 'delete',
    data: ids,
  })
}

// 根据服务应用id获取版本管理测的tag
export function apiCmdbServerTagBySystemId(id) {
  return request({
    url: `/api/cmdb/cmdb/applicationTag/findTagToCodeMByApplicationId/${id}`,
    method: 'get',
  })
}

// 服务应用新增tag
export function apiCmdbServerTagListAdd(data) {
  return request({
    url: `/api/cmdb/cmdb/applicationTag`,
    method: 'post',
    data,
  })
}
