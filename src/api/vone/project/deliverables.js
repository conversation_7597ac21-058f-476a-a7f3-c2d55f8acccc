import request from '@/utils/axios-api'

// 查询交付物列表
export function getDeliverablesList(data) {
  return request({
    url: '/api/alm/alm/plan/deliverables/query',
    method: 'post',
    data,
  })
}
// 查询交付物列表分页
export function getDeliverablesListPage(data) {
  return request({
    url: '/api/alm/alm/plan/deliverables/page',
    method: 'post',
    data,
  })
}
// 新增交付物
export function addDeliverables(data) {
  return request({
    url: '/api/alm/alm/plan/deliverables',
    method: 'post',
    data,
  })
}
// 删除交付物
export function deleteDeliverables(data) {
  return request({
    url: '/api/alm/alm/plan/deliverables',
    method: 'DELETE',
    data,
  })
}
// 修改交付物
export function editDeliverables(data) {
  return request({
    url: '/api/alm/alm/plan/deliverables',
    method: 'put',
    data,
  })
}
