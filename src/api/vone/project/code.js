import request from '@/utils/axios-api'
// 根据项目id查询看板
export function getRelevanceListByProjectId(projectId) {
  return request({
    url: `/api/codem/codeRepository/getReleasableCodeRepositoty/${projectId}`,
    method: `get`,
  })
}

// 保存关联
export function holdRelevanceListByProjectId(projectId, data) {
  return request({
    url: `/api/codem/codeRepository/codeRepositoryProject/${projectId}`,
    method: `post`,
    data: data,
  })
}

// 取消关联
export function delRelevanceListByProjectId(projectId, data) {
  return request({
    url: `/api/codem/codeRepository/codeRepositoryProject/${projectId}`,
    method: `DELETE`,
    data: data,
  })
}
