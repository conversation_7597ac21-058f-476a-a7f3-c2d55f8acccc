import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 任务列表

export function apiGetTaskList(data) {
  return request({
    url: '/api/alm/alm/task/page',
    method: 'post',
    data,
  })
}

// 任务新增

export function apiAlmTaskAdd(data) {
  return request({
    url: '/api/alm/alm/task',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 任务详情
export function apiAlmTaskInfo(id) {
  return request({
    url: `/api/alm/alm/task/${id}`,
    method: 'get',
  })
}

// 任务删除
export function apiAlmTaskDel(data) {
  return request({
    url: `/api/alm/alm/task`,
    method: 'DELETE',
    data,
  })
}

// 查询任务可流转的状态
export function apiAlmTaskFindNextNode(taskId) {
  return request({
    url: `/api/alm/alm/task/findNextNode/${taskId}`,
    method: 'get',
  })
}

// 任务状态流转
export function apiAlmTaskFlow(taskId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/alm/alm/task/transitionState/${taskId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put',
  })
}

// 查询任务----不分页

export function apiAlmTaskFindNextNoPage(data) {
  return request({
    url: '/api/alm/alm/task/query',
    method: 'post',
    data,
  })
}

// 根据项目id查询未完成且未关联需求的任务
export function apiAlmFindTodoByProjectId(projectId) {
  return request({
    url: `/api/alm/alm/task/findTodoByProjectId/${projectId}`,
    method: 'get',
  })
}

// 需求关联或解除关联任务
export function apiAlmCorrelatedTask(data) {
  return request({
    url: '/api/alm/alm/requirement/correlatedTask',
    method: 'put',
    data,
  })
}

// 下载任务导入模板
export function downloadTaskTemplate(projectId, data) {
  return request({
    url: `/api/alm/alm/task/excel/downloadImportTemplate/${projectId}`,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
  })
}

// 任务--导入
export function apiAlmTaskLoad(data) {
  return request({
    url: `/api/alm/alm/task/excel/import`,
    method: 'post',
    data: requestUtils.fileFormData(data),
  })
}

// 任务关联或解除关联子任务
export function apiAlmCorrelatedTaskDel(data) {
  return request({
    url: '/api/alm/alm/issue_item/relation/correlatedTask',
    method: 'put',
    data,
  })
}
