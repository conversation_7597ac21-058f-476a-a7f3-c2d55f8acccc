import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 获取表格数据
export async function apiAlmGetDefectData(data) {
  return request({
    url: `/api/alm/alm/bug/page`,
    method: 'post',
    data,
  })
}
// 下拉条件查询测试计划列表无权限
export async function testListByCondition(data) {
  return request({
    url: `/api/testm/testPlan/queryListByCondition`,
    method: 'post',
    data,
  })
}
// 下拉条件查询缺陷列表无权限
export async function bugListByCondition(data) {
  return request({
    url: `/api/alm/alm/bug/queryListByCondition`,
    method: 'post',
    data,
  })
}

// 缺陷新增
export async function apiAlmBugAdd(data) {
  return request({
    url: `/api/alm/alm/bug`,
    method: data.id ? 'put' : 'post',
    data,
  })
}
// 缺陷删除
export async function apiAlmBugDel(data) {
  return request({
    url: `/api/alm/alm/bug`,
    method: 'DELETE',
    data,
  })
}

// 缺陷-详情
export async function apiAlmBugInfo(id) {
  return request({
    url: `/api/alm/alm/bug/${id}`,
    method: 'get',
  })
}

// 缺陷--不分页
export async function apiAlmBugNoPage(data) {
  return request({
    url: `/api/alm/alm/bug/query`,
    method: 'post',
    data,
  })
}
// 缺陷--不分页
export async function queryBugList(data) {
  return request({
    url: `/api/alm/alm/bug/queryBugList`,
    method: 'get',
    data,
  })
}

// 测试计划--不分页
export async function apiTestPlanNoPage(data) {
  return request({
    url: `/api/testm/testPlan/query`,
    method: 'post',
    data,
  })
}

// 查询缺陷可流转的状态
export function apiAlmBugFindNextNode(bugId) {
  return request({
    url: `/api/alm/alm/bug/findNextNode/${bugId}`,
    method: 'get',
  })
}

// 缺陷状态流转
export function apiAlmBugFlow(bugId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/alm/alm/bug/transitionState/${bugId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put',
  })
}

// 下载缺陷导入模板
export function downloadBugTemplate(projectId, data) {
  return request({
    url: `/api/alm/alm/bug/excel/downloadImportTemplate/${projectId}`,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
  })
}

// 缺陷--导入
export function apiAlmBugLoad(data) {
  return request({
    url: `/api/alm/alm/bug/excel/import`,
    method: 'post',
    data: requestUtils.fileFormData(data),
  })
}
// 查询缺陷可流转的状态
export function connectBugPlan(caseId, testPlanId, data) {
  return request({
    url: `/api/alm/testPlanCaseBug/connectBugPlan/${caseId}/${testPlanId}`,
    method: 'post',
    data,
  })
}
// 查询缺陷分组
export async function getGroup(data) {
  return request({
    url: `/api/alm/alm/bug/group`,
    method: 'post',
    data,
  })
}
