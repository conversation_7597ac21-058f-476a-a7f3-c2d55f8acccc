import request from '@/utils/axios-api'

// 查询迭代计划--不分页

export function apiAlmProjectPlanNoPage(data) {
  return request({
    url: '/api/alm/alm/projectPlan/query',
    method: 'post',
    data,
  })
}
export function planListByCondition(data) {
  return request({
    url: '/api/alm/alm/projectPlan/queryListByCondition',
    method: 'post',
    data,
  })
}

// 根据条件查询项目计划事项信息 --不分页

export function apiAlmFindItemNoPage(data) {
  return request({
    url: '/api/alm/alm/projectPlan/findItemByQuery',
    method: 'post',
    data,
  })
}

// 迭代计划--新增

export function apiAlmProjectPlanAdd(data) {
  return request({
    url: '/api/alm/alm/projectPlan',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 迭代计划--删除

export function apiAlmProjectPlanDel(data) {
  return request({
    url: '/api/alm/alm/projectPlan',
    method: 'DELETE',
    data,
  })
}

// 迭代计划--详情

export function apiAlmPlanInfo(id) {
  return request({
    url: `/api/alm/alm/projectPlan/${id}`,
    method: 'get',
  })
}

// 关联迭代
export function apiAlmPlanLinkedSprint(data) {
  return request({
    url: `/api/alm/alm/projectPlan/linkedSprint`,
    method: 'PUT',
    data,
  })
}

// 取消关联迭代
export function apiAlmPlanCancleSprint(data) {
  return request({
    url: `/api/alm/alm/projectPlan/notLinkedSprint`,
    method: 'PUT',
    data,
  })
}
/**
 * @desc 分页查询所有的工作项
 * @param {object} data - 查询参数
 */
export function getWorkItemsByPage(data) {
  return request({
    url: '/api/alm/alm/projectPlan/pageItem',
    method: 'post',
    data,
  })
}
// 查询项目集下计划
export function projectProgramPlanTree(data) {
  return request({
    url: '/api/alm/alm/projectPlan/projectProgramPlanTree',
    method: 'get',
    data,
  })
}
// 项目集详情

export function relateProgram(id, data) {
  return request({
    url: `/api/alm/alm/projectProgram/${id}`,
    method: 'post',
    data,
  })
}
// 批量项目集与项目关联关系

export function updatePlanBatch(id, relation, data) {
  return request({
    url: `/api/alm/alm/projectPlan/updatePlanBatch/${id}/${relation}`,
    method: 'put',
    data,
  })
}

// 查询迭代燃尽图
export function findBurnDownByPlanId(planId, data) {
  return request({
    url: `/api/alm/alm/projectPlan/findBurnDownByPlanId/${planId}`,
    method: 'get',
    data,
  })
}

// 迭代--分页接口
export function almProjectProjectPlanPage(data) {
  return request({
    url: `/api/alm/alm/projectPlan/page`,
    method: 'post',
    data,
  })
}

// 批量修改项目计划与产品版本关联关系

export function updateProjectPlan(productVersionId, relation, data) {
  return request({
    url: `/api/alm/alm/projectPlan/updateProjectPlan/${productVersionId}/${relation}`,
    method: 'put',
    data,
  })
}
// 查询归档配置
export function getProjectConf(projectId) {
  return request({
    url: `/api/alm/alm/projectConf/getProjectConf/${projectId}`,
    method: 'get',
  })
}
// 配置归档
export function updateProjectConf(data) {
  return request({
    url: `/api/alm/alm/projectConf/updateProjectConf`,
    method: 'post',
    data,
  })
}
// 计划归档
export function filed(id) {
  return request({
    url: `/api/alm/alm/projectPlan/filed/${id}`,
    method: 'put',
  })
}
// 撤销归档
export function unarchive(id) {
  return request({
    url: `/api/alm/alm/projectPlan/unarchive/${id}`,
    method: 'put',
  })
}
/**
 * @desc 修改需求，缺陷，任务关联计划
 * @param {string} issueId 事项id
 * @param {string} planId 计划id
 * @param {string} typeClassify 事项类型
 * @param {string} typeCode 任务类型
 */
export function updateIssueLinkedPlan(data) {
  return request({
    url: '/api/alm/alm/projectPlan/linkedSprint',
    method: 'put',
    data,
  })
}
/**
 * @desc 取消需求，缺陷，任务关联计划
 * @param {string} issueId 事项id
 * @param {string} planId 计划id
 * @param {string} typeClassify 事项类型
 * @param {string} typeCode 任务类型
 */
export function deleteIssueLinkedPlan(data) {
  return request({
    url: '/api/alm/alm/projectPlan/notLinkedSprint',
    method: 'put',
    data,
  })
}
// 批量修改项目计划与产品版本关联关系
export function saveVersionProjectPlan(data) {
  return request({
    url:
      '/api/alm/alm/projectPlan/updateProjectPlan/' +
      data.productVersionId +
      '/' +
      data.relation,
    method: 'put',
    data: data.projectPlanIds,
  })
}
