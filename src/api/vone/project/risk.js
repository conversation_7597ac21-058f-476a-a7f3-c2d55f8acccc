import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

/**
 * @desc 查询项目集风险列表
 * @param {string} projectGroupKey 项目key
 */
export function getRiskList(params) {
  return request({
    url: '/api-alm/risk/getRiskList',
    method: 'get',
    params,
  })
}
/**
 * @desc 查询项目风险列表
 * @param {string}
 */
export function getProjectRiskList(data) {
  return request({
    url: '/api/alm/alm/risk/page',
    method: 'post',
    data,
  })
}

/**
 * @desc 风险新增
 * @param {string}
 */
export function apiAlmRiskAdd(data) {
  return request({
    url: '/api/alm/alm/risk',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 风险详情
export function apiAlmRiskInfo(id) {
  return request({
    url: `/api/alm/alm/risk/${id}`,
    method: 'get',
  })
}

// 修改风险状态
export function EditStatus(params) {
  return request({
    url: '/api-alm/risk/editStatus',
    method: 'get',
    params,
  })
}
// 删除风险数据
export function delRiskData(data) {
  return request({
    url: '/api/alm/alm/risk',
    method: 'delete',
    data,
  })
}
// 获取当前用户下的项目集
export function apiSelectProjectGroupByUserId(params) {
  return request({
    url: `/api-alm/projectGroup/selectProjectGroupByUserId`,
    method: 'get',
    params,
  })
}
/**
 * @desc 根据项目key查询项目
 * @param {string} projectGroupKey 项目key
 */
export function apiAlmGetProjectsByGroupKey(params) {
  return request({
    url: '/api-alm/projectGroup/getProjectsByGroupKey',
    method: 'get',
    params,
  })
}
/**
 * @desc 新建风险数据
 * @param {}
 */
export function createRiskData(data) {
  return request({
    url: '/api-alm/risk/createRisk',
    method: 'post',
    data,
  })
}
// 查询所属迭代
export function apiPlanprojectSprint(params) {
  return request({
    url: '/api-alm/projectSprint',
    method: 'get',
    params,
  })
}
// 查询里程碑阶段
export function apiProjectGetMilestoneList(params) {
  return request({
    url: `/api-alm/project-milestone/project-milestone-stage/list`,
    method: 'get',
    params,
  })
}
// 通过条件查询项目
export function getAllProject(params) {
  return request({
    url: `/api-alm/project/getAllProject`,
    method: `get`,
    params,
  })
}

// 查询风险可流转的状态
export function apiAlmRiskFindNextNode(riskId, projectType) {
  return request({
    url: `/api/alm/alm/risk/findNextNode/${riskId}/${projectType}`,
    method: 'get',
  })
}

// 风险状态流转
export function apiAlmRiskFlow(
  riskId,
  sourceStateCode,
  targetStateCode,
  projectType
) {
  return request({
    url: `/api/alm/alm/risk/transitionState/${riskId}/${sourceStateCode}/${targetStateCode}/${projectType}`,
    method: 'put',
  })
}

// 项目/项目集导出excel
// 项目下载风险导入模板
export function downloadProjectRiskImportTemplate(params) {
  return request({
    url: `/api/alm/alm/risk/downloadImportTemplate`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
    data: params,
  })
}

// 导入项目风险Excel
export function apiProjectRiskImport(data) {
  return request({
    url: `/api/alm/alm/risk/import`,
    method: 'post',
    data: requestUtils.fileFormData(data),
  })
}

// 项目集下载风险导入模板
export function downloadProjectmRiskImportTemplate(params) {
  return request({
    url: `/api/alm/alm/risk/downloadImportProjectProgramTemplate`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
    params,
  })
}

// 导入项目集风险Excel
export function apiProjectmRiskImport(data) {
  return request({
    url: `/api/alm/alm/risk/importProgram`,
    method: 'post',
    data: requestUtils.fileFormData(data),
  })
}

/**
 * @desc 新建风险关联关系
 * @param {}
 */
export function createRiskItem(data) {
  return request({
    url: '/api/alm/alm/riskItem',
    method: 'post',
    data,
  })
}

// 查询风险可关联工作项信息
export function apiAlmRiskAssociable(riskId, data) {
  return request({
    url: `/api/alm/alm/riskItem/associable/${riskId}`,
    method: 'get',
    data,
  })
}

/**
 * @desc 查询风险关联关系
 * @param {}
 */
export function getRiskItemQuery(data) {
  return request({
    url: '/api/alm/alm/riskItem/query',
    method: 'post',
    data,
  })
}

/**
 * @desc 删除风险关联关系
 * @param {}
 */
export function delRiskItemRelation(data) {
  return request({
    url: '/api/alm/alm/riskItem',
    method: 'delete',
    data,
  })
}
