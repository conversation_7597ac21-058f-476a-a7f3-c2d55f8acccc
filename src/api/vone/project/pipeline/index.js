import request from '@/utils/axios-api'
// 查询当前登录用户可关联的流水线信息
export function getPipelineToProject(projectId, params) {
  return request({
    url: `/api/pipeline/pipeline/pipelineJob/getReleasablePipelineJob/${projectId}`,
    method: `get`,
    data: params,
  })
}

// 项目关联流水线
export function relevancePipelineForProject(projectId, data) {
  return request({
    url: `/api/pipeline/pipeline/pipelineProject/${projectId}`,
    method: 'post',
    data,
  })
}

// 项目取消关联流水线
export function cancelRelevancePipeline(projectId, data) {
  return request({
    url: `/api/pipeline/pipeline/pipelineProject/${projectId}`,
    method: 'delete',
    data,
  })
}
