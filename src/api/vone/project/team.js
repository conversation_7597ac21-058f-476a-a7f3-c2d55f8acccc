import request from '@/utils/axios-api'

// 团队查询
export function teamList(data) {
  return request({
    url: '/api/alm/projectTeam/tree',
    method: 'post',
    data,
  })
}
// 查询项目团队树
export function projectTeamList(id, params) {
  return request({
    url: `/api/alm/projectTeam/tree/${id}`,
    method: 'get',
    data: params,
  })
}
// 导入团队
export function importTeam(data) {
  return request({
    url: '/api/alm/projectTeam/import',
    method: 'post',
    data,
  })
}
// 新增团队
export function addTeam(data) {
  return request({
    url: '/api/alm/projectTeam',
    method: 'post',
    data,
  })
}
// 编辑团队
export function editProjectTeam(projectId, teamId, leaderBy) {
  return request({
    url: `/api/alm/projectTeam/updateLeaderBy/${projectId}/${teamId}/${leaderBy}`,
    method: 'put',
  })
}

// 项目查询可选择父级团队
export function getTeamListByProjectId(data) {
  return request({
    url: `/api/alm/projectTeam/queryTreeByProjectId`,
    method: 'post',
    data,
  })
}
// 项目新增成员
export function addTeamListByProjectId(data) {
  return request({
    url: `/api/alm/projectTeam/user`,
    method: 'post',
    data,
  })
}

// 删除项目成员
export function delUserListById(projectId, teamId, data) {
  return request({
    url: `/api/alm/projectTeam/user/${projectId}/${teamId}`,
    method: 'delete',
    data,
  })
}

// 修改团队人员角色
export function editRoleByUserId(data) {
  return request({
    url: '/api/alm/projectTeam/user',
    method: 'put',
    data,
  })
}
// 查询团队人员
export function getProjectUserList(data) {
  return request({
    url: `/api/alm/projectTeam/user/page`,
    method: 'post',
    data,
  })
}
export function queryAllUser(data) {
  return request({
    url: '/api/alm/projectTeam/user/query',
    method: 'post',
    data,
  })
}
// 删除项目团队
export function delProjectTeam(projectId, data) {
  return request({
    url: `/api/alm/projectTeam/${projectId}`,
    method: 'delete',
    data,
  })
}

// 根据项目id查询项目下角色
export function queryProjectRoleById(data) {
  return request({
    url: '/api/alm/alm/projectRole/queryProject',
    method: 'post',
    data,
  })
}
