import request from '@/utils/axios-api'

// 测试计划树list
export function TestPlanTree() {
  return request({
    url: '/api/testm/testPlanGroupTree/findTestPlanGroupTree',
    method: 'get',
  })
}
// 测试计划树add
export function addTestPlanTree(data) {
  return request({
    url: '/api/testm/testPlanGroupTree',
    method: 'post',
    data,
  })
}
// 测试计划树edit
export function editTestPlanTree(data) {
  return request({
    url: '/api/testm/testPlanGroupTree',
    method: 'put',
    data,
  })
}
// 测试计划树del
export function delTestPlanTree(data) {
  return request({
    url: '/api/testm/testPlanGroupTree',
    method: 'delete',
    data,
  })
}
/**
 * @description 分页查询查询测试计划列表
 * @param {object} data 查询条件
 */
export function getTestPlanCase(data) {
  return request({
    url: `/api/testm/testPlan/findPlanByTreeId`,
    method: 'post',
    data,
  })
}

// 测试计划下的用例数
export function getproductCaseLibrary(data) {
  return request({
    url: `/api/testm/productCaseLibrary/query`,
    method: 'post',
    data,
  })
}
export function getLibrary() {
  return request({
    url: `/api/testm/productCaseLibrary/getLibrary`,
    method: 'get',
  })
}
//   根据执行人查计划树
export function getAllExecPlan(data) {
  return request({
    url: `/api/testm/testPlanCase/getAllExecPlan`,
    method: 'post',
    data,
  })
}
/**
 * @description 根据用例库id查询用例分组树
 * @param {string} id 用例库id
 */
export function findProductCaseTreeOfRepository(id) {
  return request({
    url: `/api/testm/productCaseTree/findProductCaseTreeOfRepository/${id}`,
    method: 'get',
  })
}
// 新增测试计划
export function saveProductPlan(data) {
  return request({
    url: `/api/testm/testPlan/saveProductPlan`,
    method: 'post',
    timeout: '0',
    data,
  })
}
// 用例ku查树
export function testProductCaseList(data) {
  return request({
    url: `/api/testm/testProductCase/query`,
    method: 'post',
    data,
  })
}
// 测试计划回显
export function queryPlanById(id) {
  return request({
    url: `/api/testm/testPlan/queryPlanById/${id}`,
    method: 'get',
  })
}
// 测试计划编辑
export function updateProductPlan(data) {
  return request({
    url: `/api/testm/testPlan/updateProductPlan`,
    method: 'put',
    data,
  })
}
// 删除计划树
export function deleteTreeById(id) {
  return request({
    url: `/api/testm/testPlanGroupTree/deleteTreeById/${id}`,
    method: 'delete',
  })
}
// 删除计划
export function deleteProductPlan(id) {
  return request({
    url: `/api/testm/testPlan/deleteProductPlan/${id}`,
    method: 'delete',
  })
}
// 删除计划
export function deltestPlan(data) {
  return request({
    url: `/api/testm/testPlan`,
    method: 'delete',
    data,
  })
}
// 删除计划
export function removePlan(data) {
  return request({
    url: `/api/testm/testPlan/removePlan`,
    method: 'post',
    data,
  })
}
// 获取全部规划用例信息
export function getAllConnect(id, data) {
  return request({
    url: `/api/testm/testProductCase/getAllConnect/${id}`,
    method: 'post',
    data,
  })
}
// 获取全部规划用例信息
export function getAllConnectByLibraryId(id, data) {
  return request({
    url: `/api/testm/testProductCase/getAllConnectByLibraryId/${id}`,
    method: 'post',
    data,
  })
}
// 计划树拖拽
export function updateById(data) {
  return request({
    url: `/api/testm/testPlanGroupTree/updateById`,
    method: 'put',
    data,
  })
}
/**
 * @description 修改测试计划分组
 * @param {string} treeId 测试计划分组id
 * @param {array} data 测试计划数据
 */
export function updateTestPlanGroup(treeId, data) {
  return request({
    url: `/api/testm/testPlan/updateProductPlanListTree/${treeId}`,
    method: 'put',
    data,
  })
}
/**
 * @description 计划归档
 */
export function planArchive(data) {
  return request({
    url: `/api/testm/testPlanArchive/planArchive`,
    method: 'post',
    data,
  })
}
/**
 * @description 查测试计划全部树
 */
export function findAllTestPlanGroupTree() {
  return request({
    url: `/api/testm/testPlanGroupTree/findAllTestPlanGroupTree`,
    method: 'get',
  })
}
/**
 * @description 根据树id 查计划
 */
export function findPlanArchiveByTreeId(data) {
  return request({
    url: `/api/testm/testPlanArchive/findPlanArchiveByTreeId`,
    method: 'post',
    data,
  })
}
/**
 * @description 还原计划
 */
export function recallPlanArchive(data) {
  return request({
    url: `/api/testm/testPlanArchive/recallPlanArchive`,
    method: 'post',
    data,
  })
}
// 计划详情
export function getOne(id) {
  return request({
    url: `/api/testm/testPlanArchive/getOne/${id}`,
    method: 'get',
  })
}
// 计划树
export function findTestPlanGroupTreeByTreeId(id) {
  return request({
    url: `/api/testm/testPlanGroupTree/findTestPlanGroupTreeByTreeId/${id}`,
    method: 'get',
  })
}
// 归档计划树
export function findTestPlanArchiveGroupTreeByTreeId(id) {
  return request({
    url: `/api/testm/testPlanGroupTree/findTestPlanArchiveGroupTreeByTreeId/${id}`,
    method: 'get',
  })
}
// 测试计划回收
// getRecycle

export function getRecycle(data) {
  return request({
    url: `/api/testm/testPlan/getPlanByGraft`,
    method: 'post',
    data,
  })
}

// 测试计划还原
export function recallRecy(data) {
  return request({
    url: `/api/testm/testPlan/callBack`,
    method: 'post',
    data,
  })
}
// 测试计划为分组
export function getNoGroupCase(data) {
  return request({
    url: `/api/testm/testPlan/getNoGroupCase`,
    method: 'post',
    data,
  })
}
// 测试计划归档分页
export function getArchiveRecord(data) {
  return request({
    url: `/api/testm/testPlanArchive/getArchiveRecord`,
    method: 'post',
    data,
  })
}
// 测试计划这是根据归档记录查归档计划
export function getArchiveCase(updateTime, updateBy) {
  return request({
    url: `/api/testm/testPlanArchive/getArchiveCase/${updateTime}/${updateBy}`,
    method: 'post',
  })
}
// 撤销归档
export function recallArchiveRecord(updateTime, updateBy) {
  return request({
    url: `/api/testm/testPlanArchive/recallArchiveRecord/${updateTime}/${updateBy}`,
    method: 'post',
  })
}
// 根据计划id获取产品下拉框
export function getProductInfoByPlanId(id) {
  return request({
    url: `/api/testm/testPlan/getProductInfoByPlanId/${id}`,
    method: 'get',
  })
}

// 根据计划id获取产品下拉框
export function queryBugPlan(data) {
  return request({
    url: `/api/alm/testPlanCaseBug/queryBugPlan`,
    method: 'post',
    data,
  })
}
// 批量执行测试用例
export function executePlanCase(testPlanId, status, data) {
  return request({
    url: `/api/testm/testPlanCase/executePlanCase/${testPlanId}/${status}`,
    method: 'post',
    data,
  })
}
// 获取测试计划树级下用例数量
export function queryAllCaseNumberOfPlanTree(id) {
  return request({
    url: `/api/testm/productCaseTree/queryAllCaseNumberOfPlanTree/${id}`,
    method: 'get',
  })
}
export function testPlanCaseTree(data) {
  return request({
    url: `/api/testm/testPlanCaseTree/query`,
    method: 'post',
    data,
  })
}
/**
 * @desc 用例迁移保存
 * @param {*} data 参数
 */
export function copyProductCaseToOtherLibrary(data) {
  return request({
    url: `/api/testm/productCaseLibrary/copyProductCaseToOtherLibrary`,
    method: 'post',
    timeout: '0',
    data,
  })
}
