import request from '@/utils/axios-api'
// 分页查询报价
export function getProductQuotationPage(data) {
  return request({
    url: `/api/product/productQuotation/page`,
    method: 'POST',
    data,
  })
}
// POST:新增报价
// PUT:编辑报价
// DELETE:删除报价

export function operationQuotation(data, type) {
  return request({
    url: `/api/product/productQuotation`,
    method: type,
    data,
  })
}
// 查询详情
export function getProductQuotationById(data) {
  return request({
    url: `/api/product/productQuotation/${data}`,
    method: 'get',
  })
}
// 根据产品id返回产品报价趋势图数据
export function getProductQuotationResultByProductId(productId) {
  return request({
    url: `/api/product/productQuotation/getProductQuotationResultByProductId/${productId}`,
    method: 'get',
  })
}
// 根据产品id查询产品立项相关信息
export function getProductApprovalInfo(productId) {
  return request({
    url: `/api/product/productQuotation/queryByProductId/${productId}`,
    method: 'get',
  })
}
// 分页查询产品成本
export function getProductCostData(data) {
  return request({
    url: `/api/product/productCosts/page`,
    method: 'POST',
    data,
  })
}
// 产品成本操作
export function operationCost(data, type) {
  return request({
    url: `/api/product/productCosts`,
    method: type,
    data,
  })
}
// 分页查询维护成本信息
export function getProductMaintenanceCostsPage(data) {
  return request({
    url: `/api/product/productMaintenanceCosts/page`,
    method: 'POST',
    data,
  })
}
// POST:新增
// PUT:编辑
// DELETE:删除
// 维护成本信息
export function productMaintenanceCosts(data, type) {
  return request({
    url: `/api/product/productMaintenanceCosts`,
    method: type,
    data,
  })
}
// 根据产品id查询产品维护成本信息
export function getQueryByProductId(productId) {
  return request({
    url: `/api/product/productMaintenanceCosts/queryByProductId/${productId}`,
    method: 'get',
  })
}
// 分页查询交付项目
export function getDeliverProjectData(data) {
  return request({
    url: `/api/product/productProjectQuotation/page`,
    method: 'POST',
    data,
  })
}
// 交付项目报价-操作
export function operationDeliverProject(data, type) {
  return request({
    url: `/api/product/productProjectQuotation`,
    method: type,
    data,
  })
}
// 分页查询竞品数据
export function getCompetitorData(data) {
  return request({
    url: `/api/product/productCompetitorsQuotation/page`,
    method: 'POST',
    data,
  })
}
// 竞品信息-操作
export function operationCompetitor(data, type) {
  return request({
    url: `/api/product/productCompetitorsQuotation`,
    method: type,
    data,
  })
}
// 查询收益分析
export function getIncomeAnalysis(productId) {
  return request({
    url: `/api/product/productCosts/queryBenefitAnalysis/${productId}`,
    method: 'get',
  })
}
