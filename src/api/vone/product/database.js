import request from '@/utils/axios-api'
// 分页查询资料库
export function getProductDocumentPage(data) {
  return request({
    url: `/api/product/productDocument/page`,
    method: 'POST',
    data,
  })
}
// POST:新增资料库
// PUT:编辑资料库
// DELETE:删除资料库

export function operationDocument(data, type) {
  return request({
    url: `/api/product/productDocument`,
    method: type,
    data,
  })
}
// 查询详情
export function getProductDocumentById(data) {
  return request({
    url: `/api/product/productDocument/${data}`,
    method: 'get',
  })
}
