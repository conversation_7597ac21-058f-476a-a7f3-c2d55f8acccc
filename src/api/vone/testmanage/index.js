import request from '@/utils/axios-api'

/**
 * @desc 查询项目下用例库列表
 * @param {number} projectId 项目id
 */
export function getCaseLibraryByprojectId(data) {
  return request({
    url: '/api/testm/testCaseLibrary/query',
    method: 'post',
    data,
  })
}
export function getLibraryIdByProjectId(data) {
  return request({
    url: '/api/testm/productCaseLibrary/getLibraryIdByProjectId',
    method: 'get',
    data,
  })
}
/**
 * @desc 新增用例库id
 * @param {string} createdBy 创建人
 * @param {string} leadingBy 责任人
 * @param {string} name 标题
 * @param {string} productId 关联产品id
 * @param {string} projectId 关联项目id
 * @param {string} type 用例库类型 product/project
 */
export function createCaseLibrary(data) {
  return request({
    url: '/api/testm/testCaseLibrary',
    method: 'post',
    data,
  })
}
/**
 * @desc 修改用例库名称，关联项目，责任人
 * @param {string} id 用例库id
 * @param {string} leadingBy 负责人id
 * @param {string} name 用例库名称
 * @param {string} projectId 关联项目id
 */
export function editCaseLibrary(data) {
  return request({
    url: '/api/testm/testCaseLibrary',
    method: 'put',
    data,
  })
}
/**
 * @desc 删除用例库
 * @param {arrary} projectId 用例库id数据
 */
export function deleteCaseLibrary(data) {
  return request({
    url: '/api/testm/testCaseLibrary',
    method: 'delete',
    data,
  })
}
/**
 * @desc 查询用例库下的用例树
 * @param {string} libraryId 用例库id
 */
export function getCaseTree(data) {
  return request({
    url: '/api/testm/testCaseTree/findTestCaseTreeOfRepository/' + data,
    method: 'get',
  })
}
/**
 * @desc 用例树新增层级树
 * @param {string} createdBy 创建人
 * @param {string} leadingBy 责任人
 * @param {string} libraryId 用例库id
 * @param {string} name 用例树名称
 * @param {string} parentId 用例树父级id
 */
export function appendCaseTreeNode(data) {
  return request({
    url: '/api/testm/testCaseTree',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询当前层级下树菜单
 * @param {string} parentId 父级id
 */
export function getTreeChildren(data) {
  return request({
    url: '/api/testm/testCaseTree/query',
    method: 'post',
    data,
  })
}
/**
 * @desc 删除当前用例树层级
 * @param {string} id 用例树id
 */
export function deleteCaseTree(data) {
  return request({
    url: '/api/testm/testCaseTree',
    method: 'delete',
    data,
  })
}
/**
 * @desc 修改用例树名称
 * @param {string} id 用例树id
 * @param {string} leadingBy 负责人
 * @param {string} name 名称
 * @param {string} parentId 父级id
 * @param {string} libraryId 用例库id
 */
export function editCaseTreeName(data) {
  return request({
    url: '/api/testm/testCaseTree',
    method: 'put',
    data,
  })
}
export function testCaseTreeSwap(data) {
  return request({
    url: `/api/testm/testCaseTree/updateById`,
    method: 'put',
    data,
  })
}

/**
 * @desc 判断当前用例版本是否为最新版本
 */
export function testProductCaseIsNewVersion(data) {
  return request({
    url: '/api/testm/testProductCase/newVersion',
    method: 'post',
    data,
  })
}
/**
 * @desc 用例升级
 */
export function testProductCaseUpgrade(data) {
  return request({
    url: '/api/testm/testProductCase/caseUpgrade',
    method: 'put',
    data,
  })
}
/**
 * @desc 用例升级
 */
export function planUpgrade(data) {
  return request({
    url: '/api/testm/testPlanCase/planUpgrade',
    method: 'put',
    data,
  })
}

/**
 * @desc 产品用例历史
 */
export function testProductCaseHistory(data) {
  return request({
    url: '/api/testm/testProductCaseHistory/page',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询用例历史版本对比信息
 * @param {array} id 用例id数组
 */
export function getCaseHistoryCompare(data) {
  return request({
    url: '/api/testm/testProductCaseHistory/compareVersion',
    method: 'post',
    data,
  })
}

/**
 * @desc 产品用例库查询用例趋势
 * @param {string} libraryId 用例库id
 */
export function findAllTrendByLibraryId(libraryId) {
  return request({
    url: `/api/testm/testProductCase/findAllTrendByLibraryId/${libraryId}`,
    method: 'get',
  })
}

/**
 * @desc 用例数量人员分布
 * @param {string} libraryId 用例库id
 */
export function findCaseNumberUserd(libraryId) {
  return request({
    url: `/api/testm/testProductCase/findCaseNumberUser/${libraryId}`,
    method: 'get',
  })
}

/**
 * @desc 根据用例id查询该用例执行记录里的计划
 */
export function findPlanByCaseId(caseId) {
  return request({
    url: `/api/testm/testPlanCaseResult/findPlanByCaseId/${caseId}`,
    method: 'put',
  })
}

/**
 * @desc 根据用例id和计划id查询执行记录
 */
export function findRecordByCaseIdAndPlanId(data) {
  return request({
    url: `/api/testm/testPlanCaseResult/findRecordByCaseIdAndPlanId`,
    method: 'put',
    data,
  })
}

/**
 * @desc 查看测试用例
 */
export function getPlanListPage(data) {
  return request({
    // url: '/api/testm/testProductCase/getPlanList',
    url: '/api/testm/testPlanCase/pageCase',
    method: 'post',
    data,
  })
}

/**
 * @desc 分组指派
 */
export function groupForUser(data) {
  return request({
    url: '/api/testm/testPlanCase/groupForUser',
    method: 'post',
    data,
  })
}

/**
 * @desc 测试计划里程碑柱状图
 */
export function queryTestPlanRecords(data) {
  return request({
    url: '/api/testm/testReport/queryTestPlanRecords',
    method: 'POST',
    data,
  })
}

/**
 * @description 复用测试计划
 * @param {string} treeId 测试计划id
 * @param {arrary} caseId 用例id
 */
export function copyCurrentPlan(data) {
  return request({
    url: '/api/testm/testPlan/planCopy',
    method: 'post',
    data,
  })
}

/**
 * @description 概览--用例库总数
 * @param {string} libraryIds 产品id
 */
export function getLibraryNumOnProductId(libraryIds) {
  return request({
    url: `/api/testm/testProductCase/getCaseNumOnLibraryIds?libraryIds=${libraryIds}`,
    method: 'get',
  })
}

/**
 * @description 概览--测试计划
 * @param {string} libraryIds 产品id
 */
export function getPlanNumOnProductId(libraryIds) {
  return request({
    url: `/api/testm/testPlan/getPlanNumOnLibrarieId?librarieIds=${libraryIds}`,
    method: 'get',
  })
}

/**
 * @description 概览--缺陷统计
 * @param {string} libraryIds 产品id
 */
export function summaryBugOnProductId(libraryIds) {
  return request({
    url: `/api/alm/alm/bug/summaryBugOnLibrarieId?librarieIds=${libraryIds}`,
    method: 'get',
  })
}
/**
 * @description 查询全部测试用例库
 * @param {}
 */
export function getAllLibraryInfo() {
  return request({
    url: '/api/testm/productCaseLibrary/getAllProductCaseLibraryInfo',
    method: 'get',
  })
}
/**
 * @description 概览--测试用例分布状态
 * @param {string} productId 产品id
 */
export function testPlanCaseStatus(planIds) {
  return request({
    url: `/api/testm/testPlanCase/status?planIds=${planIds}`,
    method: 'get',
  })
}

/**
 * @description 概览--测试报告新增
 * @param {string} productId 产品id
 */
export function testReportStatus(planIds) {
  return request({
    url: `/api/testm/testReport/status`,
    method: 'get',
  })
}

// 测试计划树list---概览
export function testPlanTreeView() {
  return request({
    url: '/api/testm/testPlanGroupTree/findTestPlanGroupTreeAndPlan',
    method: 'get',
  })
}

/**
 * @description 查询测试概览趋势分析折线图
 * @param {string} type 查询类型
 * @param {string} libraryId 产品库id
 */
export function testTendencyLine(type, libraryId) {
  return request({
    url: `/api/testm/testm/statistics/tendency/linechar?type=${type}&libraryIds=${libraryId}`,
    method: 'get',
  })
}
export function histogram(type, data) {
  return request({
    url: `/api/testm/testm/statistics/tendency/histogram/${type}`,
    method: 'put',
    data,
  })
}
export function linechar(type, data) {
  return request({
    url: `/api/testm/testm/statistics/tendency/linechar/${type}`,
    method: 'put',
    data,
  })
}
/**
 * @description 查询测试概览趋势分析柱状图
 * @param {string} type 类型
 * @param {string} libraryId 产品库id
 */
export function testTendencyBar(type, libraryId) {
  return request({
    url: `/api/testm/testm/statistics/tendency/histogram?type=${type}&libraryIds=${libraryId}`,
    method: 'get',
  })
}
// 查询测试模块概览
export function getOverview() {
  return request({
    url: '/api/testm/testOverview/getOverview',
    method: 'get',
  })
}
// 用例为分组
export function getNoGroupCase(data) {
  return request({
    url: '/api/testm/testProductCase/getNoGroupCase',
    method: 'post',
    data,
  })
}
// 用例树归档
export function treeArchive(treeId) {
  return request({
    url: `/api/testm/testProductCaseArchive/treeArchive/${treeId}`,
    timeout: '0',
    method: 'get',
  })
}
/**
 * @desc 查询测试计划下执行用例树
 * @param {string} execBy 执行人
 * @param {string} planId 计划id
 * @param {string} status 用例执行状态
 */
export function getPlanCaseTree(data) {
  return request({
    url: '/api/testm/productCaseTree/querySubordinateOfPlanTree',
    method: 'put',
    data,
  })
}

// 根据产品Id查询用例库
export function getProductCaseLibrary(productId) {
  return request({
    url: `/api/testm/productCaselibraryConnect/getProductCaseLibrary/${productId}`,
    method: 'get',
  })
}

// 根据用例库Id或者项目id查询用例
export function getCaseByProjectIdOrLibraryId(type, id, data) {
  return request({
    url: `/api/testm/testProductCase/getCaseByProjectIdOrLibraryId/${type}/${id}`,
    method: 'get',
    data,
  })
}
