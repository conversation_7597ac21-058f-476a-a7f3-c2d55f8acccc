import request from '@/utils/axios-api'

/**
 * @description 分页查询测试计划下用例列表
 * @param {string} planId 测试计划id
 * @param {string} treeId 分组树id
 */
export function getPlanCasesList(data) {
  return request({
    url: '/api/testm/testPlanCase/page',
    method: 'post',
    data,
  })
}
/**
 * @description 查询当前计划关联用例的用例分组树
 * @param {string} libraryId 测试计划id
 */
export function getPlanRelaseCaseTree(libraryId) {
  return request({
    url: `/api/testm/productCaseTree/findProductCaseTreeOfRepository/${libraryId}`,
    method: 'get',
  })
}
/**
 * @description 查询当前计划关联用例的用例分组树
 * @param {string} libraryId 测试计划id
 */
export function findProductCaseTreeByLibrary(libraryId) {
  return request({
    url: `/api/testm/productCaseTree/findProductCaseTreeByLibrary/${libraryId}`,
    method: 'get',
  })
}
/**
 * @description 测试计划关联用例保存
 * @param {string} createdBy 用户
 * @param {string} planId 计划id
 * @param {object} map 用例和树节点映射关系
 * @param {Array} testcaseIds 关联的用例
 * @param {Array} testcaseTreeIds 分组树id
 */
export function savePlanReleaseCases(data) {
  return request({
    url: '/api/testm/testPlanCase/saveTestPlanCase',
    method: 'post',
    data,
  })
}
/**
 * @description 查询测试计划未关联的测试用例
 * @param {string} planId 测试计划id
 * @param {string} libraryId 用例库id
 */
export function getPlanUnreleaseCases(data) {
  return request({
    url: '/api/testm/testPlanCaseTree/findPlanCaseNoRelation',
    method: 'get',
    data,
  })
}
/**
 * @description 取消测试计划关联的用例
 * @param {string} planId 测试计划id
 * @param {Array} testCaseIds 取消关联的用例
 */
export function deletePlanReleaseCases(data) {
  return request({
    url: '/api/testm/testPlanCase/deleteTestPlanCase',
    method: 'delete',
    data,
  })
}
/**
 * @description 查询测试计划用例统计数据
 * @param {string} planId 测试计划
 */
export function getPlanExcuteDetail(data) {
  return request({
    url: '/api/testm/testReport/queryTestPlanGroupDetail',
    method: 'get',
    data,
  })
}
/**
 * @description 执行测试计划用例
 * @param {string} execBy 执行人
 * @param {array} caseId 用例id
 * @param {string} planId 计划id
 * @param {string} status 用例状态结果
 * @param {string} result 用例执行结果
 */
export function excutePlanCaseResult(data) {
  return request({
    url: `/api/testm/testPlanCase/executePlanCase`,
    method: 'post',
    data,
  })
}
/**
 * @description 点击用例查询用例详细数据
 * @param {string} id 用例id
 */
export function getPlanCaseDetail(data) {
  return request({
    url: '/api/testm/testPlanCaseTree/findPlanCase',
    method: 'get',
    data,
  })
}
/**
 * @description 创建测试报告
 * @param {string} createdBy 创建人
 * @param {Array} planId 计划id
 * @param {string} product 是否是产品 true
 * @param {string} testReportName 报告名称
 */
export function createTestPlanReport(data) {
  return request({
    url: '/api/testm/testReport',
    method: 'post',
    data,
  })
}
/**
 * @description 查询生成的测试报告
 * @param {*}
 */
export function getReportHistory() {
  return request({
    url: '/api/testm/testReport/getHistory',
    method: 'get',
  })
}

/**
 * @description 查询用例执行结果
 * @param {string} planId 测试计划id
 * @param {string} testcaseId 测试用例id
 */
export function getPlanCaseResult(data) {
  return request({
    url: '/api/testm/testPlanCase/executePlanCaseResult',
    method: 'get',
    data,
  })
}
/**
 * @description 查询测试计划下所有用例
 * @param {string} planId 测试计划id
 */
export function getPlanAllCases(planId) {
  return request({
    url: `/api/testm/testPlan/getTestProductCaseById/${planId}`,
    method: 'get',
  })
}
/**
 * @description 查询测试计划分组树权限信息
 * @param {string} treeId 树id
 */
export function getPlanGroupTreePermission(treeId) {
  return request({
    url: `/api/testm/testPlanGroupTreeOrg/${treeId}`,
    method: 'get',
  })
}
/**
 * @description 修改测试计划分组树权限信息
 * @param {array} orgIds 组织机构id数组
 * @param {array} testPlanGroupTreeId 树id数组
 */
export function updatePlanGroupTreePermission(data) {
  return request({
    url: '/api/testm/testPlanGroupTreeOrg',
    method: 'put',
    data,
  })
}
/**
 * @desc 查询测试计划下用例树全部数据
 * @param {string} planId 测试计划id
 */
export function getTotalTreeByPlanId(planId) {
  return request({
    url: `/api/testm/productCaseTree/getTreeByPlanId/${planId}`,
    method: 'get',
  })
}
/**
 * @desc 查询测试计划下用例树
 * @param {string} planId 测试计划id
 * @param {string} treeId 树级id
 */
export function getTreeByPlanId(planId, treeId) {
  return request({
    url: `/api/testm/productCaseTree/getTreeByPlanId/${planId}/${treeId}`,
    method: 'get',
  })
}
/**
 * @desc 撤销用例执行结果
 * @param {string} planId 测试计划id
 * @param {string} testCaseId 用例id
 */
export function revertTestCaseResult({ planId, testCaseId }) {
  return request({
    url: `/api/testm/testPlanCaseResult/revertExcute/${planId}/${testCaseId}`,
    method: 'put',
  })
}
/**
 * @desc 查询全部用例树级和用例
 * @param {string} libraryId 用例库id
 * @param {string} planId 测试计划id
 * @param {array} tabInfo 标签id数组
 * @param {string} name 用例名称
 * @param {string} caseKey 用例编号
 * @param {string} priority 优先级
 * @param {string} treeId 树级id
 */
export function queryTreeCases(data) {
  return request({
    url: '/api/testm/testProductCase/queryTestCaseLibrary',
    method: 'put',
    data,
    timeput: '0',
  })
}
/**
 * @desc 通过筛选条件查询树级及用例
 * @param {string} libraryId 用例库id
 * @param {string} planId 测试计划id
 * @param {array} tabInfo 标签id数组
 * @param {string} name 用例名称
 * @param {string} caseKey 用例编号
 * @param {string} priority 优先级
 */
export function queryTreeCasesByfilter(data) {
  return request({
    url: '/api/testm/testProductCase/queryTestCaseTreeByCase',
    method: 'put',
    data,
    timeout: '0',
  })
}
export function deleteTestPlanCaseByTreeId(data) {
  return request({
    url: `/api/testm/testPlanCase/deleteTestPlanCaseByTreeId?planId=${data.planId}&treeId=${data.treeId}`,
    method: 'delete',
    timeout: '0',
  })
}
