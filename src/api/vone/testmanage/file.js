import request from '@/utils/axios-api'

/**
 * @description 查询重复的用例树和数量
 * @param {string} libraryId 用例库id
 */
export function getRepeatCaseTree(libraryId) {
  return request({
    url: `/api/testm/testCaseComparison/getTestCaseComparisonCount/${libraryId}`,
    method: 'get',
  })
}
/**
 * @description 根据树id查询冲突用例
 * @param {string} treeId 树id
 */
export function getCasesByTreetId(treeId) {
  return request({
    url: `/api/testm/testCaseComparison/getTestCaseComparisonByTreeId/${treeId}`,
    method: 'get',
  })
}
/**
 * @description 分页查询冲突用例数据
 * @param {object} data 分页参数
 */
export function getCasesByTreeIdPage(data) {
  return request({
    url: `/api/testm/testCaseComparison/getTestCaseComparisonByTreeIdPage`,
    method: 'post',
    data,
  })
}
/**
 * @description 保存修改分组树下冲突用例
 * @param {array} data 冲突用例列表
 */
export function saveProjectRepeatCases(data) {
  return request({
    url: `/api/testm/testCaseComparison/updateById`,
    method: 'post',
    data,
  })
}
/**
 * @description 确认导入冲突用例的xmind文件
 * @param {string} libraryId 用例库id
 */
export function ImportRepeatXmindFile(libraryId) {
  return request({
    url: `/api/testm/testCaseComparison/updateByLibraryId/${libraryId}`,
    method: 'post',
  })
}
/**
 * @description 根据用例id查询用例详情
 * @param {string} caseId 用例id
 */
export function getCaseDetail(caseId) {
  return request({
    url: `/api/testm/testCaseComparison/${caseId}`,
    method: 'get',
  })
}
/**
 * @description 根据用例库id删除xmind文件缓存
 * @param {string} libraryId 用例库id
 */
export function deleteXmindFileCache(libraryId) {
  return request({
    url: `/api/testm/testCaseComparison/findByLibraryId/${libraryId}`,
    method: 'delete',
  })
}
/**
 * @description 查询当前节点路径信息
 * @param {string} treeId
 */
export function getNodePath(treeId) {
  return request({
    url: `/api/testm/productCaseTree/findParentTreePath/${treeId}`,
    method: 'get',
  })
}
/**
 * @description 查询测试管理导入xmind文件冲突用例数量
 * @param {string} libraryId 用例库id
 */
export function getImportRepeatCasesCountIntestm(libraryId) {
  return request({
    url: `/api/testm/testProductCaseComparison/getTestProductCaseComparisonCount/${libraryId}`,
    method: 'get',
  })
}
/**
 * @description 查询测试管理冲突用例数据不分页
 * @param {string} treeId 树id
 */
export function getRepeatCaseListIntestm(treeId) {
  return request({
    url: `/api/testm/testProductCaseComparison/getTestProductCaseComparisonByTreeId/${treeId}`,
    method: 'get',
  })
}
/**
 * @description 分页查询测试管理冲突用例数据
 * @param {}
 */
export function getRepeatCaseListIntestmPage(data) {
  return request({
    url: `/api/testm/testProductCaseComparison/page`,
    method: 'post',
    data,
  })
}
/**
 * @description 保存测试管理修改后的分组树下冲突用例
 * @param {array} data 冲突用例列表
 */
export function saveRepeatCasesIntestm(data) {
  return request({
    url: `/api/testm/testProductCaseComparison/updateById`,
    method: 'post',
    data,
  })
}
/**
 * @description 确认导入测试管理冲突用例的xmind文件
 * @param {string} libraryId 用例库id
 * @param {boolean} accept 批量解决用例冲突方式
 */
export function ImportRepeatXmindFileIntestm(libraryId, accept) {
  return request({
    url: `/api/testm/testProductCaseComparison/updateByLibraryId/${libraryId}/${accept}`,
    method: 'post',
  })
}
/**
 * @description 根据用例库id删除测试管理下xmind文件缓存
 * @param {string} libraryId 用例库id
 */
export function deleteXmindFileCacheIntestm(libraryId) {
  return request({
    url: `/api/testm/testProductCaseComparison/findByLibraryId/${libraryId}`,
    method: 'delete',
  })
}

/**
 * @description 查询导出的文件字段
 * @param {*}
 */
export function getExportFileFields() {
  return request({
    url: '/api/testm/testProductCase/excel/export/fields',
    method: 'get',
  })
}
