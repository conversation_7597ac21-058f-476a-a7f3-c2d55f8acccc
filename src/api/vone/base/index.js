import request from '@/utils/axios-api'

// 引擎查询
export function apiBaseEngineList(data) {
  return request({
    url: '/api/noToken/login',
    method: 'post',
    data,
  })
}

// 菜单查询
export function apiBaseMenuList(data) {
  return request({
    url: '/api/menu/menu/page',
    method: 'post',
    data,
  })
}

// 菜单新增
export function apiBaseMenuAddApp(data) {
  return request({
    url: '/api/menu/menu',
    method: 'POST',
    data,
  })
}
// 根据ID查询菜单
export function apiBaseMenuGetById(id) {
  return request({
    url: `/api/menu/menu/${id}`,
    method: 'GET',
  })
}

// 查询系统下所有菜单
export function apiBaseMenuTree(data) {
  return request({
    url: '/api/menu/menu/tree',
    method: 'POST',
    data,
  })
}

// 菜单修改
export function apiBaseMenuEditApp(data) {
  return request({
    url: '/api/menu/menu',
    method: 'PUT',
    data,
  })
}
// 菜单删除
export function apiBaseMenuDelApp(data) {
  return request({
    url: '/api/menu/menu',
    method: 'DELETE',
    data,
  })
}

// 查询企业信息
export function getCompanyInfo(code) {
  return request({
    url: `/api/base/tenantPlatformConfig/noToken/findByCode/${code}
    `,
    method: 'GET',
  })
}

// 查询机构信息
export function getOrgInfo(params) {
  return request({
    url: `/api/base/base/org/selectOrgById`,
    method: 'GET',
    data: params,
  })
}

// 查询工作项 固定属性+自定义属性
export function apiBaseFormProperty(id) {
  return request({
    url: `/api/tm-base/customForm/${id}`,
    method: 'get',
  })
}

// 查询工作项 保存内置自定义属性
export function apiBaseCustomPropertySave(form_id, data) {
  return request({
    url: `/api/tm-base/base/form/${form_id}/field`,
    method: 'post',
    data,
  })
}

// 删除工作项属性
export function apiBasePropertyDel(form_id, field_id) {
  return request({
    url: `/api/tm-base/base/form/${form_id}/field/${field_id}`,
    method: 'DELETE',
  })
}

// 查询工作项单个属性的详情,回显
export function apiBaseFormPropertyGetInfo(form_id, field_id) {
  return request({
    url: `/api/tm-base/base/form/${form_id}/field/${field_id}`,
    method: 'get',
  })
}
