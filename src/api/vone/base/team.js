import request from '@/utils/axios-api'

// 团队查询
export function teamList(data) {
  return request({
    url: '/api/base/sysTeam/tree',
    method: 'post',
    data,
  })
}

// 新增团队
export function addTeam(data) {
  return request({
    url: '/api/base/sysTeam',
    method: 'post',
    data,
  })
}

// 修改团队
export function editTeam(data) {
  return request({
    url: '/api/base/sysTeam',
    method: 'put',
    data,
  })
}

// 删除团队
export function delTeam(data) {
  return request({
    url: '/api/base/sysTeam',
    method: 'delete',
    data,
  })
}

// 查询人员
export function getUserListById(id, param) {
  return request({
    url: `/api/base/sysTeam/${id}/user/page`,
    method: 'post',
    data: param,
  })
}
// 查询人员无分页
export function getUserListByIdNoPage(id) {
  return request({
    url: `/api/base/sysTeam/${id}/user/query`,
    method: 'get',
  })
}
export function getProjectUserList(data) {
  return request({
    url: `/api/alm/projectTeam/user/page`,
    method: 'post',
    data,
  })
}
// 根据团队id查询团队详情
export function queryDetail(id) {
  return request({
    url: `/api/alm/projectTeam/${id}`,
    method: 'get',
  })
}
// 添加人员
export function addUserListById(id, data) {
  return request({
    url: `/api/base/sysTeam/${id}/user`,
    method: 'post',
    data,
  })
}

// 删除人员
export function delUserListById(id, data) {
  return request({
    url: `/api/base/sysTeam/${id}/user`,
    method: 'delete',
    data: data,
  })
}

// 项目查询可选择父级团队
export function getTeamListByProjectId(data) {
  return request({
    url: `/api/base/sysTeam/queryTreeByProjectId`,
    method: 'post',
    data,
  })
}

// 项目新增成员
export function addTeamListByProjectId(teamId, projectId, roleId, data) {
  return request({
    url: `/api/base/sysTeam/${teamId}/addProjectTeamUserAndRole/${projectId}/${roleId}`,
    method: 'post',
    data,
  })
}

// 查询项目角色
export function getRoleListByIdNoPage(id) {
  return request({
    url: `/api/alm/alm/projectRole/allRoleByProjectId/` + id,
    method: 'get',
  })
}
