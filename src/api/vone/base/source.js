import request from '@/utils/axios-api'

/**
 * @desc 查询数据源列表
 * @param {}
 */
export function getSourceList() {
  return request({
    url: '/api/tm-base/formData/definition/all',
    method: 'get',
  })
}
/**
 * @desc 查询单个数据源
 * @param {string} id 数据源id
 */
export function getSourceById(id) {
  return request({
    url: `/api/tm-base/formData/definition/${id}`,
    method: 'get',
  })
}
/**
 * @desc 新增数据源
 * @param {object} data 数据源数据
 */
export function addSourceData(data) {
  return request({
    url: `/api/tm-base/formData/definition`,
    method: 'post',
    data,
  })
}
/**
 * @desc 编辑数据源数据
 * @param {object} data 数据源数据
 */
export function editSourceData(data) {
  return request({
    url: `/api/tm-base/formData/definition`,
    method: 'put',
    data,
  })
}
/**
 * @desc 删除数据源
 * @param {string} id 数据源id
 */
export function deleteSourceById(id) {
  return request({
    url: `/api/tm-base/formData/definition/${id}`,
    method: 'delete',
  })
}
/**
 * @desc 查询数据源表单数据
 * @param {string} id 数据id
 */
export function getSourceFormData(id, search) {
  return request({
    url: `/api/tm-base/formData/${id}/data${search ? '?search=' + search : ''}`,
    method: 'get',
  })
}
/**
 * @desc 新增数据源表单数据
 * @param {string} id 数据id
 * @param {object} data 新增数据
 */
export function addSourceFromData(id, data) {
  return request({
    url: `/api/tm-base/formData/${id}/data`,
    method: 'post',
    data,
  })
}
/**
 * @desc 编辑数据源表单数据
 * @param {string} dataId 数据id
 * @param {string} rowId 行id
 * @param {object} data 当前行数据
 */
export function editSourceFormData({ dataId, rowId, data }) {
  return request({
    url: `/api/tm-base/formData/${dataId}/data/${rowId}`,
    method: 'put',
    data,
  })
}
/**
 * @desc 修改数据源表单单项数据
 * @param {string} dataId 数据源id
 * @param {string} fieldId 列id
 * @param {string} rowId 行id
 * @param {object} value 修改值
 */
export function editSourceFormValue({ dataId, fieldId, rowId, value }) {
  return request({
    url: `/api/tm-base/formData/${dataId}/data/${fieldId}/${rowId}?value=${value}`,
    method: 'put',
  })
}
/**
 * @desc 删除数据源表单数据
 * @param {string} dataId 数据源id
 * @param {string} data 列表id
 */
export function deleteSourceFormData(dataId, data) {
  return request({
    url: `/api/tm-base/formData/${dataId}/data`,
    method: 'delete',
    data,
  })
}
/**
 * @desc 数据源列表排序
 * @param {string} dataId 数据源id
 * @param {string} data 列表id
 */
export function sortSourceFormData(dataId, data) {
  return request({
    url: `/api/tm-base/formData/${dataId}/sort`,
    method: 'put',
    data,
  })
}
