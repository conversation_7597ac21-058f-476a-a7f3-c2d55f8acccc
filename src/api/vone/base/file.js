import request from '@/utils/axios-api'

// 文件参数
export function apiBaseFileLoad(url, data) {
  return request({
    url: url,
    method: 'post',
    data: data,
    responseType: 'blob',
    timeout: '0',
  })
}

// 文件服务器
export function apiBaseFileManage(data) {
  return request({
    url: '/api/base/base/file/page',
    method: 'post',
    data,
  })
}

// 文件服务器--s删除
export function apiBaseFileDel(data) {
  return request({
    url: '/api/base/base/file',
    method: 'delete',
    data,
  })
}

// 文件下载--根据id
export function apiBaseFileLoadById(data, downloadFun, row) {
  return request({
    url: '/api/base/base/file/download',
    method: 'post',
    data,
    responseType: 'blob',
    onDownloadProgress: (progressEvent) => {
      if (downloadFun && typeof downloadFun == 'function') {
        downloadFun(progressEvent, row)
      }
    },
  })
}

// 文件服务器
export function sliceUpload(data) {
  return request({
    url: '/api/base/base/file/anyone/upload',
    method: 'post',
    data,
  })
}
// 数据表导出
export function dataExport(url, data, type) {
  return request({
    url: url,
    method: type || 'get',
    data: data,
    responseType: 'blob',
    timeout: '0',
  })
}
