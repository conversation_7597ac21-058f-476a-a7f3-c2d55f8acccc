import request from '@/utils/axios-api'

// 工作流阶段 -----------------------------------------------------
// 工作流阶段列表查询
export function apiProjectmWorkflowStage(data) {
  return request({
    url: '/api/alm/alm/stage/page',
    method: 'post',
    data,
  })
}

// 工作流阶段新增
export function apiAlmStageAdd(data) {
  return request({
    url: '/api/alm/alm/stage',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 工作流阶段删除
export function apiAlmStageDel(data) {
  return request({
    url: '/api/alm/alm/stage',
    method: 'DELETE',
    data,
  })
}

// 工作流阶段列表--不分页
export function apiAlmStageNoPage(data) {
  return request({
    url: '/api/alm/alm/stage/query',
    method: 'post',
    data,
  })
}

// 工作流阶段详情
export function apiAlmStageGetById(id) {
  return request({
    url: `/api/alm/alm/stage/${id}`,
    method: 'get',
  })
}

// 状态 -----------------------------------------------------

// 工作流状态列表查询
export function apiProjectmWorkflowStatus(data) {
  return request({
    url: '/api/alm/alm/state/page',
    method: 'post',
    data,
  })
}
// 保存工作流节点关联表单属性权限信息
export function saveProjectWorkflowNodeCustomFormField(data) {
  return request({
    url: '/api/alm/alm/projectWorkflowNodeCustomFormField/saveProjectWorkflowNodeCustomFormField',
    method: 'post',
    data,
  })
}
// 工作流状态新增
export function apiAlmStatusAdd(data, method) {
  return request({
    url: '/api/alm/alm/state',
    method: method,
    data,
  })
}

// 工作流状态删除
export function apiAlmStateDel(data) {
  return request({
    url: '/api/alm/alm/state',
    method: 'DELETE',
    data,
  })
}

// 工作流状态--不分页
export function apiAlmStateNoPage(data) {
  return request({
    url: '/api/alm/alm/state/query',
    method: 'post',
    data,
  })
}

// 工作流状态详情
export function apiAlmStateGetById(id) {
  return request({
    url: `/api/alm/alm/state/${id}`,
    method: 'get',
  })
}

// 任务流-----------------------------
export function apiAlmWorkflowInfoList(data) {
  return request({
    url: '/api/alm/alm/workflowInfo/page',
    method: 'post',
    data,
  })
}

// 任务流新增
export function apiAlmWorkflowAdd(data) {
  return request({
    url: '/api/alm/alm/workflowInfo',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 任务流删除
export function apiAlmWorkflowDel(data) {
  return request({
    url: '/api/alm/alm/workflowInfo',
    method: 'DELETE',
    data,
  })
}

// 工作流详情
export function getFlow(id) {
  return request({
    url: `/api/alm/alm/workflowInfo/${id}`,
    method: 'get',
  })
}

// 任务流批量查询
export function apiAlmWorkflowNoPage(data) {
  return request({
    url: '/api/alm/alm/workflowInfo/query',
    method: 'post',
    data,
  })
}

// 优先级删除
export function apiAlmPriorityDel(data) {
  return request({
    url: '/api/alm/alm/priority',
    method: 'DELETE',
    data,
  })
}

// 来源删除
export function apiAlmSourceDel(data) {
  return request({
    url: '/api/alm/alm/source',
    method: 'DELETE',
    data,
  })
}

// 事项删除
export function apiAlmTypeDel(data) {
  return request({
    url: '/api/alm/alm/type',
    method: 'DELETE',
    data,
  })
}

// 事项类型查询
export function apiAlmTypePage(data) {
  return request({
    url: '/api/alm/alm/type/page',
    method: 'post',
    data,
  })
}

// 任务流基本信息修改
export function apiAlmWorkflowBasicInfoPut(data) {
  return request({
    url: '/api/alm/alm/workflowInfo/updateBase',
    method: 'put',
    data,
  })
}

// 查询所有内置表单
export function apiBaseAllForm(id) {
  return request({
    url: ``,
    method: 'get',
  })
}

// 切换工作流
export function apiAlmWorkflowSwitchWorkflow(data) {
  return request({
    url: '/api/alm/alm/projectWorkflow/switchWorkflow',
    method: 'put',
    data,
  })
}
// 复制工作流
export function workflowInfoCopy(data) {
  return request({
    url: '/api/alm/alm/workflowInfo/copy',
    method: 'post',
    data,
  })
}

// 修改工作项排序
export function sortTypeBoard(data) {
  return request({
    url: '/api/alm/alm/type/order',
    method: 'PUT',
    data,
  })
}
// 查询工作项类型,不分页
export function getListNeglectQuery(data) {
  return request({
    url: '/api/alm/alm/type/getListNeglect',
    method: 'post',
    data,
  })
}

// 修改工作项状态
export function changeTypeEnabled(id, enabled) {
  return request({
    url: `/api/alm/alm/type/enabled?id=${id}&enabled=${enabled}`,
    method: 'PUT',
  })
}
// 同步工作项状态到项目下
export function anyncTypeToProject(id) {
  return request({
    url: `/api/alm/alm/type/${id}/enabledProject`,
    method: 'PUT',
  })
}
