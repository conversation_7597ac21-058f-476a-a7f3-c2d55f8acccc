import request from '@/utils/axios-api'

/**
 * @desc 查询当前代码库成员组和成员
 * @param {string} codeRepositoryId 代码库id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoMemberDetails(data) {
  return request({
    url: '/api/codem/codeRepository/getRepositoryMemDetail',
    method: 'get',
    data,
  })
}

/**
 * @desc 查询当前代码库成员组
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoGroups(data) {
  return request({
    url: '/api/codem/codem/group/getGroups',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询代码库成员组内成员列表
 * @param {string} groupId 组id
 * @param {string} privateToken 令牌
 * @param {stirng} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getGroupMembers(data) {
  return request({
    url: '/api/codem/codeRepository/getMembers',
    method: 'get',
    data,
  })
}
/**
 * @desc 删除代码库成员组成员
 * @param {string} groupId 成员组id
 * @param {stirng} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} userId 用户id
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function deleteGroupMember(data) {
  return request({
    url: '/api/codem/codeRepository/deleteMember',
    method: 'delete',
    data,
  })
}
/**
 * @desc 创建代码库成员组
 * @param {string} accessLevel 权限等级
 * @param {string} description 描述
 * @param {string} groupName 成员组名称
 * @param {string} path 成员组路径
 * @param {stirng} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} visibility 可见性 private public internal
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function createRepoGroup(data) {
  return request({
    url: '/api/codem/codem/group/createGroup',
    method: 'post',
    data,
  })
}
/**
 * @desc 代码库添加成员组
 * @param {string} accessLevelNum 权限等级
 * @param {string} description 描述
 * @param {string} groupName 成员组名称
 * @param {string} id 成员组id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} codeRepositoryId 代码库id
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function addGroupInRepo(data) {
  return request({
    url: '/api/codem/codeRepository/addGroup',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询代码库所有成员
 * @param {string} repositoryType 代码库类型
 * @param {string} privateToken 令牌
 * @param {string} url 地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoAllMembers(data) {
  return request({
    url: '/api/codem/codem/user/getUsers',
    method: 'get',
    data,
  })
}
/**
 * @desc 删除代码库成员组
 * @param {string} codeRepositoryId 代码库id
 * @param {string} id 成员组id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function deleteRepoGroup(data) {
  return request({
    url: '/api/codem/codeRepository/removeGroup',
    method: 'delete',
    data,
  })
}
/**
 * @desc 代码库添加人员
 * @param {number} accessLevelNum 权限等级
 * @param {number} codeRepositoryId 代码库id
 * @param {number} id 用户id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 地址
 * @param {string} date 有效期
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function addMemberInRepo(data) {
  return request({
    url: '/api/codem/codeRepository/addMember',
    method: 'post',
    data,
  })
}
/**
 * @desc 删除代码库成员
 * @param {string} codeRepositoryId 代码库id
 * @param {string} id 成员id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function deleteRepoMember(data) {
  return request({
    url: '/api/codem/codeRepository/deleteMember',
    method: 'delete',
    data,
  })
}
/**
 * @desc 创建新用户
 * @param {boolean} createGroup 是否可创建组
 * @param {boolean} createProject 是否可创建项目
 * @param {string} email 邮箱
 * @param {boolean} isAdmin 是否是管理员
 * @param {string} userName 用户名
 * @param {string} name 账户名称
 * @param {string} privateToken 令牌
 * @param {string} engineId 引擎id
 * @param {string} account 用户
 * @param {number} projectsLimit 项目个数
 * @param {string} repositoryType 代码库类型
 * @param {string} url 地址
 */
export function createNewUser(data) {
  return request({
    url: '/api/codem/codem/user/createUser',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询代码库当前用户信息
 * @param {string} repositoryType 代码库类型
 * @param {string} privateToken 令牌
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoUserInfo(data) {
  return request({
    url: '/api/codem/codem/user/getCurrentUser',
    method: 'get',
    data,
  })
}
/**
 * @desc gitee搜索成员
 * @param {string} privateToken 令牌
 * @param {string} url 代码库地址
 * @param {string} userName	用户名称
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function searchGiteeUsers(data) {
  return request({
    url: '/api/codem/codem/user/giteeGetUser',
    method: 'get',
    data,
  })
}
/**
 * @desc 修改当前用户权限和有效期
 * @param {string} accessLevelNum 权限等级
 * @param {string} codeRepositoryId 代码库id
 * @param {string} date 有效期
 * @param {string} id 角色id
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} account 用户
 * @param {string} privateToken 令牌
 * @param {string} codeEngineId 引擎id
 */
export function updateMemberPermission(data) {
  return request({
    url: '/api/codem/codeRepository/updateProjectMember',
    method: 'put',
    data,
  })
}
/**
 * @desc 修改当前用户组权限和有效期
 * @param {string} accessLevelNum 权限等级
 * @param {string} codeRepositoryId 代码库id
 * @param {string} date 有效期
 * @param {string} id 用户组id
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} account 用户
 * @param {string} privateToken 令牌
 * @param {string} codeEngineId 引擎id
 */
export function updateGroupPermission(data) {
  return request({
    url: '/api/codem/codeRepository/updateProjectGroup',
    method: 'put',
    data,
  })
}
/**
 * @description 查询代码库授权信息
 * @param {string} codeId 代码库id
 */
export function getLibraryAuthorize(data) {
  return request({
    url: '/api/codem/codeAuthorize/selectUserAuthorizeByCodeId',
    method: 'get',
    data,
  })
}
/**
 * @description 查询下一级部门信息
 * @param {string} codeId 代码库id
 * @param {string} orgId 部门id
 */
export function getNextDepartment(data) {
  return request({
    url: '/api/codem/codeAuthorize/selectOrgUserBycodeIdOrgId',
    method: 'get',
    data,
  })
}
/**
 * @description 修改代码库授权信息
 * @param {array} codeIdList 代码库id集合
 * @param {array} infoList 授权信息集合
 */
export function updateLibraryAuthorize(data) {
  return request({
    url: '/api/codem/codeAuthorize/batchInsertListByCodeIdAndOrgAndUserInfo',
    method: 'post',
    data,
  })
}
/**
 * @description 删除代码库授权人员
 * @param {string} codeId 代码库id
 * @param {array} userId 授权人员id集合
 */
export function deleteLibraryAuthorize(codeId, data) {
  return request({
    url: '/api/codem/codeAuthorize/deleteByCodeIdAndUserId?codeId=' + codeId,
    method: 'delete',
    data,
  })
}
/**
 * @description 根据代码库id和用户id查询用户信息
 * @param {string} codeId 代码库id
 * @param {string} name 用户名称
 */
export function getUserInfoByCodeIdAndUserId(data) {
  return request({
    url: '/api/codem/codeAuthorize/selectUserNameAndDeptOne',
    method: 'post',
    data,
  })
}

// 获取组内成员列表查询
export function getMmbersList(data) {
  return request({
    url: '/api/codem/codem/group/getMembers',
    method: 'get',
    data,
  })
}

// 成员组内新增成员
export function addMemberByGroup(data) {
  return request({
    url: '/api/codem/codem/group/addMember',
    method: 'post',
    data,
  })
}
// 分页查询代码库成员或组
export function getMemberOrGroup(data) {
  return request({
    url: '/api/codem/codeRepository/member/page',
    method: 'post',
    data,
  })
}
// 查询代码库可添加组
export function getAddGroup(codeRepositoryId, keyword) {
  return request({
    url: `/api/codem/codeRepository/member/bindable/codeGroup/${codeRepositoryId}`,
    method: 'get',
    data: { keyword },
  })
}
// 查询可添加用户
export function getAddUser(codeRepositoryId, keyword) {
  return request({
    url: `/api/codem/codeRepository/member/bindable/codeUser/${codeRepositoryId}`,
    method: 'get',
    data: { keyword },
  })
}
// 添加代码库成员或组
export function addCodeMemberOrGroup(data) {
  return request({
    url: '/api/codem/codeRepository/member',
    method: 'post',
    data,
  })
}
// 修改代码库成员
export function updateCodeMemberOrGroup(data) {
  return request({
    url: '/api/codem/codeRepository/member',
    method: 'put',
    data,
  })
}
// 删除代码库成员
export function deleteCodeMemberOrGroup(data) {
  return request({
    url: `/api/codem/codeRepository/member`,
    method: 'delete',
    data,
  })
}
/**
 * 同步代码库下用户信息
 */
export function syncUserList(engineId) {
  return request({
    url: `/api/codem/codeRepository/member/sync/${engineId}`,
    method: 'post',
    data: [],
  })
}
