import request from '@/utils/axios-api'

// 获取token列表
export function getTokenList(data) {
  return request({
    url: '/api/codem/codeUserToken/page',
    method: 'post',
    data,
  })
}

// 新增token
export function addToken(data) {
  return request({
    url: '/api/codem/codeUserToken',
    method: 'post',
    data,
  })
}

// 编辑token
export function editToken(data) {
  return request({
    url: '/api/codem/codeUserToken',
    method: 'PUT',
    data,
  })
}

// 删除token
export function delToken(data) {
  return request({
    url: '/api/codem/codeUserToken',
    method: 'DELETE',
    data,
  })
}
