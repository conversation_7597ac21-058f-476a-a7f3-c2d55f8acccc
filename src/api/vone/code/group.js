import request from '@/utils/axios-api'

/**
 * 查询用户组
 */
export function getGroupList(data) {
  return request({
    url: '/api/codem/codem/group/query',
    method: 'post',
    data,
  })
}
/**
 * 新增用户组
 */
export function addGroup(data) {
  return request({
    url: '/api/codem/codem/group',
    method: 'post',
    data,
  })
}
/**
 * 修改用户组
 */
export function updateGroup(data) {
  return request({
    url: '/api/codem/codem/group',
    method: 'put',
    data,
  })
}
/**
 * 修改用户组全部字段
 */
export function updateGroupAll(data) {
  return request({
    url: '/api/codem/codem/group/all',
    method: 'put',
    data,
  })
}
/**
 * 删除用户组
 */
export function deleteGroup(data) {
  return request({
    url: `/api/codem/codem/group`,
    method: 'delete',
    data,
  })
}
/**
 * 查询用户组成员
 */
export function getGroupMemberList(data) {
  return request({
    url: `/api/codem/codem/groupUser/page`,
    method: 'post',
    data,
  })
}
/**
 * 新增用户组成员
 */
export function addGroupMember(data) {
  return request({
    url: '/api/codem/codem/groupUser',
    method: 'post',
    data,
  })
}
/**
 * 修改用户组成员
 */
export function updateGroupMember(data) {
  return request({
    url: '/api/codem/codem/groupUser',
    method: 'put',
    data,
  })
}
/**
 * 修改用户组成员全部数据
 */
export function updateGroupMemberAll(data) {
  return request({
    url: '/api/codem/codem/groupUser/all',
    method: 'put',
    data,
  })
}
/**
 * 删除用户组成员
 */
export function deleteGroupMember(data) {
  return request({
    url: `/api/codem/codem/groupUser`,
    method: 'delete',
    data,
  })
}
/**
 * 同步代码库中的Group信息
 */
export function syncGroupList(engineId) {
  return request({
    url: `/api/codem/codem/group/sync/${engineId}`,
    method: 'put',
  })
}
/**
 * 同步代码库中的Group下用户信息
 */
export function syncUserList(engineId) {
  return request({
    url: `/api/codem/codem/groupUser/sync/${engineId}`,
    method: 'put',
  })
}
// 获取代码库组权限
export function getGroupAuthority(groupId, data) {
  return request({
    url: `/api/codem/codem/group/session/authorize/${groupId}`,
    method: 'get',
    data: data,
  })
}
