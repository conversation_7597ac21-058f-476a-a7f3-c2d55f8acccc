import request from '@/utils/axios-api'

/**
 * @desc 查询纳管代码库
 * @param {string} repositoryType 代码库类型
 * @param {string} userId 当前登录用户id
 */
export function getRelateRepository(data) {
  return request({
    url: '/api/codem/codeRepository/getRepositoryList',
    method: 'post',
    data,
  })
}

/**
 * @desc 查询纳管代码库分页
 * @param {string} repositoryType 代码库类型
 * @param {string} userId 当前登录用户id
 */
export function getRelateRepositoryPage(data) {
  return request({
    url: '/api/codem/codeRepository/page',
    method: 'post',
    data,
  })
}
// 查询代码库无分页
export function getRelateRepositoryNoPage(data) {
  return request({
    url: '/api/codem/codeRepository/query',
    method: 'post',
    data,
  })
}

/**
 * @desc 查询线上代码库
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} page 页数
 * @param {string} perPage 条目数
 * @param {string} codeEngineId 引擎id
 * @param {string} account 用户名
 */
export function queryRepositoryList(data) {
  return request({
    url: '/api/codem/codeRepository/queryRepository',
    method: 'post',
    data,
  })
}
/**
 * @desc 模糊查询线上代码库
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} projectName 模糊搜索值
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function querySearchRepositories(data) {
  return request({
    url: '/api/codem/codeRepository/getProjectSearch',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询指定代码库信息
 * @param {string} codeRepositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 */
export function getRepositoryById(data) {
  return request({
    url: '/api/codem/codeRepository/getRepositoryById',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询指定代码库信息detail
 */
export function getCodeRepositoryByCondition(data) {
  return request({
    url: '/api/codem/codeRepository/getCodeRepositoryByCondition',
    method: 'post',
    data,
  })
}
/**
 * @desc 导入代码库
 * @param {string} codeType 代码库类型
 * @param {string} operateType 操作类型 import create
 * @param {string} privateToken 用户令牌
 * @param {string} repositoryName 代码库名称
 * @param {string} repositoryType 代码库类型
 * @param {string} updatedBy 更新人id
 * @param {string} url 代码库地址
 * @param {string} id gitlab和gitee代码库id
 * @param {string} maintenanceStaffId 代码库维护人id
 * @param {string} maintenanceStaffName 代码维护人名称
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function importRepository(data) {
  return request({
    url: '/api/codem/codeRepository/importRepository',
    method: 'post',
    data,
  })
}
/**
 * @desc 创建代码库
 * @param {string} allPath 全路径
 * @param {string} path 父路径名称
 * @param {string} createUser 创建者id
 * @param {string} codeType 代码库语言种类
 * @param {string} operateType 代码库操作种类
 * @param {string} privateToken 用户令牌
 * @param {string} repositoryName 代码库名称
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} maintenanceStaffId 代码库维护人id
 * @param {string} maintenanceStaffName 代码维护人名称
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function createRepository(data) {
  return request({
    url: '/api/codem/codeRepository/createRepository',
    method: 'post',
    data,
  })
}
/**
 * @desc 删除代码库
 * @param {string} id 代码库id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function delRepository(data) {
  return request({
    url: '/api/codem/codeRepository/delRepository',
    method: 'delete',
    data,
  })
}
/**
 * @desc 删除纳管代码库
 * @param {string} id 代码库id
 * @param {string} repositoryType 代码库类型
 */
export function delManageRepo(data) {
  return request({
    url: '/api/codem/codeRepository/delManageRepository',
    method: 'delete',
    data,
  })
}
/**
 * @desc 查询代码库详细信息
 * @param {string} branchName 分支名称
 * @param {string} codeRepositoryId 代码库id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 仓库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoDetail(data) {
  return request({
    url: '/api/codem/codeRepository/getRepositoryDetails',
    method: 'get',
    data,
  })
}
/**
 * @desc 修改代码库信息
 * @param {string} codeType 代码类型
 * @param {string} maintenanceStaffId 维护人id
 * @param {string} maintenanceStaffName 维护人名称
 * @param {string} description 描述
 * @param 代码库其他数据
 */
export function updateLibraryDetail(data) {
  return request({
    url: '/api/codem/codeRepository/updateCodeRepository',
    method: 'put',
    data,
  })
}
/**
 * @desc 代码库添加收藏
 * @param {string} codeRepositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} userId 用户id
 */
export function createRepositoryCollection(data) {
  return request({
    url: '/api/codem/codeRepositoryCollection/createRepositoryCollection',
    method: 'post',
    data,
  })
}
/**
 * @desc 代码库取消收藏
 * @param {string} codeRepositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} userId 用户id
 */
export function delRepositoryCollection(data) {
  return request({
    url: '/api/codem/codeRepositoryCollection/delRepositoryCollection',
    method: 'delete',
    data,
  })
}

// 代码库库权限
export function codeLibraryIdAuth(repositoryId) {
  return request({
    url: `/api/codem/codem/repository/org/${repositoryId}`,
    method: 'GET',
  })
}

//  代码库权限修改
export function codeLibraryOrgPut(data) {
  return request({
    url: `/api/codem/codem/repository/org`,
    method: 'put',
    data,
  })
}
/**
 * @desc 批量查询已关联的代码库
 * @param {string} requirementId 需求id
 */
export function getReleasedLibByQuery(data) {
  return request({
    url: '/api/alm/alm/issueRequirementCode/query',
    method: 'post',
    data,
  })
}

// 代码库团队列表
export function getTeamPermissionList(id) {
  return request({
    url: `/api/codem/codemTeam/queryTeam/${id}`,
    method: 'GET',
  })
}

// 项目下代码库团队列表
export function getTeamPermissionListByProjectId(data) {
  return request({
    url: `/api/alm/projectTeam/getCodemTeam`,
    method: 'GET',
    data,
  })
}

// 代码库团队删除
export function delTeamPermissionById(codemRepositoryId, teamId) {
  return request({
    url: `/api/codem/codemTeam/deleteTeam/${codemRepositoryId}/${teamId}`,
    method: 'delete',
  })
}

// 代码库团队新增
export function holdTeamPermissionById(codemRepositoryId, data) {
  return request({
    url: `/api/codem/codemTeam/addTeam/${codemRepositoryId}`,
    method: 'post',
    data: data,
  })
}
// 获取代码库权限
export function getCodeRepositoryId(codeRepositoryId, data) {
  return request({
    url: `/api/codem/codeRepository/member/session/authorize/${codeRepositoryId}`,
    method: 'get',
    data: data,
  })
}
