import request from '@/utils/axios-api'

// 新建mergeRequest
export function createMergeRequest({ codeRepositoryId, data }) {
  return request({
    url: `/api/codem/codeMergeRequest/create/${codeRepositoryId}`,
    method: 'post',
    data,
  })
}
// 接受mergeRequest
export function acceptMergeRequest({ codeRepositoryId, mergeRequestId, data }) {
  return request({
    url: `/api/codem/codeMergeRequest/accept/${codeRepositoryId}/${mergeRequestId}`,
    method: 'post',
    data,
  })
}
// 关闭mergeRequest
export function closeMergeRequest({ codeRepositoryId, mergeRequestId }) {
  return request({
    url: `/api/codem/codeMergeRequest/close/${codeRepositoryId}/${mergeRequestId}`,
    method: 'post',
  })
}
// 分页查询mergeRequest
export function getMergeRequestList({ codeRepositoryId, data }) {
  return request({
    url: `/api/codem/codeMergeRequest/page/${codeRepositoryId}`,
    method: 'get',
    data,
  })
}
// 查询mergerequest提交总数
export function getMergeRequestCount({ codeRepositoryId, state }) {
  return request({
    url: `/api/codem/codeMergeRequest/total/${codeRepositoryId}`,
    method: 'get',
    data: { state },
  })
}
// 查询MergerRequest 的 commits信息
export function getCommitList(codeRepositoryId, mergeRequestId) {
  return request({
    url: `/api/codem/codeMergeRequest/commits/${codeRepositoryId}/${mergeRequestId}`,
    method: 'get',
  })
}
// 查询MergerRequest 的 changes信息
export function getChangesList(codeRepositoryId, mergeRequestId) {
  return request({
    url: `/api/codem/codeMergeRequest/changes/${codeRepositoryId}/${mergeRequestId}`,
    method: 'get',
  })
}
