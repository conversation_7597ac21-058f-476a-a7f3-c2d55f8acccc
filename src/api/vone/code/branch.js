import request from '@/utils/axios-api'

/**
 * @desc 查询分支目录结构列表
 * @param {string} branchName	分支名称
 * @param {string} codeRepositoryId 代码仓库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} path 文件路径
 * @param {string} privateToken 令牌
 * @param {string} url 路径
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoFileTree(data) {
  return request({
    url: '/api/codem/codem/fileContent/queryFileTree',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询分支文件内容
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} branchName 分支名称
 * @param {string} path 文件路径（包括文件名）
 * @param {string} privateToken 令牌
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getFileContent(data) {
  return request({
    url: '/api/codem/codem/fileContent/queryFileContent',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询文件最近commit信息
 * @param {string} branchName 分支名称
 * @param {string} codeRepositoryId 代码库id
 * @param {string} filePath 文件路径
 * @param {string} privateToken 令牌
 * @param {string} engineId 引擎id
 * @param {string} account 用户
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 */
export function getFileRecentCommit(data) {
  return request({
    url: '/api/codem/codeBranch/getRecentCommit',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询已纳管代码库分支列表
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 */
export function getRepoBranchList(data) {
  return request({
    url: '/api/codem/codeBranch/getBranchesByRepId',
    method: 'get',
    data,
  })
}
/**
 * @desc 新增代码库分支
 * @param {string} branchName 新建分支名
 * @param {string} privateToken 令牌
 * @param {string} orignBranchName 源分支
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} codeEngineId 引擎id
 * @param {string} url 代码库地址
 * @param {string} description 描述
 * @param {string} title 标题
 * @param {string} createdBy 创建人
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function createNewBranch(data) {
  return request({
    url: '/api/codem/codeBranch/createBranch',
    method: 'post',
    data,
  })
}
/**
 * @desc 导入服务器已有代码库分支
 * @param {string} branchName 新建分支名
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeEngineId 引擎id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} updatedBy 更新人
 */
export function importNewBranch(data) {
  return request({
    url: '/api/codem/codeBranch/importBranch',
    method: 'post',
    data,
  })
}
/**
 * @desc 空代码库创建分支
 * @param {string} privateToken 令牌
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} createdBy 创建人
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function createFirstBranch(data) {
  return request({
    url: '/api/codem/codeBranch/createFirstBranch',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询对应代码库所有分支列表
 * @param {string} privateToken 令牌
 * @param {string} repositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoBranches(data) {
  return request({
    url: '/api/codem/codeBranch/queryBranch',
    method: 'get',
    data,
  })
}
/**
 * @desc 删除代码库服务器分支
 * @param {string} privateToken 令牌
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} branchName 目标分支
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function delRepoBranch(data) {
  return request({
    url: '/api/codem/codeBranch/delBranch',
    method: 'delete',
    data,
  })
}
/**
 * @desc 删除代码库纳管的分支
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} branchName 目标分支
 */
export function delManageRepoBranch(data) {
  return request({
    url: '/api/codem/codeBranch/delManageBranch',
    method: 'delete',
    data,
  })
}
/**
 * @desc 整分支合并
 * @param {string} description 描述
 * @param {string} privateToken 令牌
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} orignBranchName 源分支
 * @param {string} branchName 目标分支
 * @param {string} title 分支合并标题
 * @param {string} url 代码库地址
 * @param {string} maintenanceStaffId 代码库维护人id
 * @param {string} path 代码库路径
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function mergeTargetBranch(data) {
  return request({
    url: '/api/codem/codeBranch/mergeBranch',
    method: 'post',
    data,
  })
}
/**
 * @desc 代码分支同步
 * @param {string} path 代码库路径
 * @param {string} privateToken 令牌
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} updatedBy 更新人
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function syncRepoBranch(data) {
  return request({
    url: 'api/codem/codeBranch/synRemoteBranch',
    method: 'post',
    data,
  })
}
/**
 * @desc 新增tag基线
 * @param {string} orignBranchName 基线基于分支
 * @param {string} privateToken 令牌
 * @param {string} repositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} tagName 基线名称
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 * @param {string} createdBy 创建人
 * @param {string} description 描述
 */
export function createTagLine(data) {
  return request({
    url: '/api/codem/codeBranchTag/createTag',
    method: 'post',
    data,
  })
}
/**
 * @desc 导入服务器tag基线
 * @param {string} repositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} tagName 基线名称
 * @param {string} createdBy 操作人
 * @param {string} description 描述
 */
export function importTagLine(data) {
  return request({
    url: '/api/codem/codeBranchTag/importTag',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询纳管的tag基线列表
 * @param {string} codeRepositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 */
export function getManageTags(data) {
  return request({
    url: '/api/codem/codeBranchTag/queryManageTags',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询当前代码库所有tag基线
 * @param {string} codeRepositoryId 代码库地址
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoAllTags(data) {
  return request({
    url: '/api/codem/codeBranchTag/queryTags',
    method: 'get',
    data,
  })
}
/**
 * @desc 删除服务器代码tag基线
 * @param {number} repositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} privateToken 令牌
 * @param {string} tagName 基线名称
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function delRepoTag(data) {
  return request({
    url: '/api/codem/codeBranchTag/delTag',
    method: 'delete',
    data,
  })
}
/**
 * @desc 删除纳管代码tag基线
 * @param {number} repositoryId 代码库id
 * @param {string} tagName 基线名称
 * @param {string} repositoryType 代码库类型
 */
export function delManageRepoTag(data) {
  return request({
    url: '/api/codem/codeBranchTag/delManageTag',
    method: 'delete',
    data,
  })
}

// 修改分支审核状态
export function updateBranch(data) {
  return request({
    url: '/api/codem/codeBranch/updateCodeCheckStatusByBranchId',
    method: 'PUT',
    data,
  })
}

// 获取操作记录合并描述列表
export function getMergedRequestList(data) {
  return request({
    url: '/api/codem/commit/getMergedRequestDetail',
    method: 'PUT',
    data,
  })
}
