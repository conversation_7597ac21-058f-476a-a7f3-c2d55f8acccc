import request from '@/utils/axios-api'

/**
 * @desc 查询代码库所有用户
 * @param {string} privateToken 令牌
 * @param {stirng} repositoryType 代码库类型
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getRepoUsers(data) {
  return request({
    url: '/api/codem/codem/user/getCurrentUser',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询代码合并撤版操作记录
 * @param {string} repositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 */
export function getHistoryLogs(data) {
  return request({
    url: '/api/codem/codeBranchOperateHistory/getMergeRevokeHistory',
    method: 'get',
    data,
  })
}
/**
 * @description 分页查询代码合并操作记录
 * @param {object} data
 */
export function getMergeHistoryPageLogs(data) {
  return request({
    url: '/api/codem/codeBranchOperateHistory/getMergeRevokeHistory/page',
    method: 'post',
    data,
  })
}
/**
 * @desc 代码合并前校验
 * @param {string} privateToken 令牌
 * @param {stirng} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} orignBranchName 目标分支
 * @param {string} branchName 合并分支
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function validateBeforeMerge(data) {
  return request({
    url: '/api/codem/codeBranch/chargeBeforeMerge',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询分支最近一天的提交记录
 * @param {string} privateToken 令牌
 * @param {stirng} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} branchName 分支名称
 * @param {string} url 地址
 * @param {string} updateTime 更新时间
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getDayCommitLogs(data, num) {
  return request({
    url: `/api/codem/codeBranch/branchCommits?pageNumber=${num}`,
    method: 'post',
    data,
  })
}
/**
 * @desc 查询分支所有提交记录
 * @param {string} privateToken 令牌
 * @param {stirng} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} branchName 分支名称
 * @param {string} url 地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getBranchCommitLogs(data) {
  return request({
    url: '/api/codem/codeBranch/allBranchCommits',
    method: 'post',
    data,
  })
}
/**
 * @desc 查询请求合并的commit记录
 * @param {string} privateToken 令牌
 * @param {stirng} codeRepositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} sourceBranch 起源分支
 * @param {string} targetBranch 目标分支
 * @param {string} url 地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getCommitList(data) {
  return request({
    url: '/api/codem/codeBranch/mergeCommitsAndversionNumList',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询commit的变更信息
 * @param {string} privateToken 令牌
 * @param {stirng} codeRepositoryId 代码库id
 * @param {string} repositoryType 代码库类型
 * @param {string} sha 散列值
 * @param {string} url 地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function getCommitDetail(data) {
  return request({
    url: '/api/codem/codeBranch/commitDetail',
    method: 'get',
    data,
  })
}
/**
 * @desc 修改文件内容
 * @param {string} branchName 分支名称
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} commitMsg 提交信息
 * @param {string} newContent 修改后的内容
 * @param {string} path 文件路径
 * @param {string} privateToken 令牌
 * @param {string} url 代码库地址
 * @param {string} engineId 引擎id
 * @param {string} account 用户名
 */
export function editFileContent(data) {
  return request({
    url: '/api/codem/codem/fileContent/editFileContent',
    method: 'put',
    data,
  })
}
/**
 * @desc 新增代码库分支文件
 * @param {string} branchName 分支名称
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} commitMsg 提交文件标题
 * @param {string} newContent 文件内容
 * @param {string} path 文件路径
 * @param {string} privateToken 令牌
 * @param {string} url 地址
 */
export function createFileContent(data) {
  return request({
    url: '/api/codem/codem/fileContent/createFileContent',
    method: 'post',
    data,
  })
}
/**
 * @desc 删除代码库分支文件
 * @param {string} branchName 分支名称
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} commitMsg 提交文件标题
 * @param {string} newContent 文件内容
 * @param {string} path 文件路径
 * @param {string} privateToken 令牌
 * @param {string} url 地址
 */
export function deleteLibraryFile(data) {
  return request({
    url: '/api/codem/codem/fileContent/deleteFileContent',
    method: 'delete',
    data,
  })
}
/**
 * @desc 查询commit具体信息
 * @param {string} codeRepositoryId 代码库id
 * @param {string} privateToken 令牌
 * @param {string} repositoryType 代码库类型
 * @param {string} sha 散列值
 * @param {string} url 地址
 * @param {string} account 账号
 * @param {string} engineId 引擎id
 */
export function getCurCommitDetail(data) {
  return request({
    url: '/api/codem/codeBranch/commitDetail',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询某次commit记录文件目录
 * @param {string} repositoryType 代码库类型
 * @param {string} codeRepositoryId 代码库ld
 * @param {string} sha commit的id
 * @param {string} url 地址
 * @param {string} filePath 文件路径
 * @param {string} privateToken 令牌
 * @param {string} account 用户
 * @param {string} engineId 引擎id
 */
export function getCurrentLogFiles(data) {
  return request({
    url: '/api/codem/codeBranch/queryCommitFile',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询某次commit记录文件内容
 * @param {string} repositoryType 代码库类型
 * @param {string} repositoryId 代码库ld
 * @param {string} sha commit的id
 * @param {string} filePath 查询文件路径
 * @param {string} url 地址
 * @param {string} privateToken 令牌
 * @param {string} account 用户
 * @param {string} engineId 引擎id
 */
export function getCurrentLogFileContent(data) {
  return request({
    url: '/api/codem/codeBranch/queryHistoryCommitFile',
    method: 'get',
    data,
  })
}
/**
 * @desc 查询某次commit记录前的所有commit
 * @param {string} repositoryType 代码库类型
 * @param {string} repositoryId 代码库ld
 * @param {string} branchName commit的id
 * @param {string} url 地址
 * @param {string} sinceDate 起始日期
 * @param {string} untilDate commit日期
 * @param {string} privateToken 令牌
 * @param {string} account 用户
 * @param {string} engineId 引擎id
 */
export function getCurrentLogCommits(data) {
  return request({
    url: '/api/codem/codeBranch/queryHistoryCommits',
    method: 'get',
    data,
  })
}

/**
 * @desc 根据需求id查询代码库信息
 * @param {string} id 代码库类型

 */
export function queryIssuerelatedCode(params) {
  return request({
    url:
      '/api/alm/alm/requirement/queryCorrelatedCodeRepository?requirementId=' +
      params,
    method: 'get',
  })
}

/**
 * @desc 需求关联代码库
 * @param {string}

 */
export function saveIssueRelatedCode(data) {
  return request({
    url: '/api/alm/alm/requirement/correlatedCodeRepository',
    method: 'post',
    data,
  })
}
/**
 * @desc 需求关联已有的代码库分支
 * @param {string} branchId 分支id
 * @param {stirng} repositoryBranch 分支名
 * @param {stirng} repositoryId 代码库id
 * @param {string} repositoryName 代码库名称
 * @param {string} repositoryType 代码库类型
 * @param {string} requirementId 需求id
 */
export function releaseExistedBranch(data) {
  return request({
    url: '/api/alm/alm/issueRequirementCode',
    method: 'post',
    data,
  })
}
/**
 * @desc 根据需求id创建代码库关联分支
 * @param {}
 */
/**
 * @desc
 * @param {string} branchName 分支名称
 * @param {string} codeRepositoryId 代码库id
 * @param {string} codeRepositoryType 代码库类型
 * @param {string} orignBranchName 源分支名称
 * @param {string} requirementId 需求id
 */
export function releaseNewBranchByRequireId(requirementId, data) {
  return request({
    url: `/api/alm/alm/issueRequirementCode/createBranch/${requirementId}`,
    method: 'post',
    data,
  })
}
/**
 * @desc 取消关联的代码库分支
 * @param {array} data 删除的代码库分支id
 */
export function disconnectLibraryBranch(data) {
  return request({
    url: '/api/alm/alm/issueRequirementCode',
    method: 'delete',
    data,
  })
}

// 修改代码库流程状态
// export function changeCodeStatus(data) {
//   return request({
//     url: `/api/codem/codeRepository/updateCodeRepositoryStatus`,
//     method: 'post',
//     data
//   })
// }
// 根据分支-时间-需求查询commit列表信息
export function getBranchCommitsList(data) {
  return request({
    url: `/api/codem/codeBranch/getBranchCommitsList`,
    method: 'get',
    data,
  })
}
// 代码库合并操作按钮新增
export function codeRepositoryOperationButton(data) {
  return request({
    url: `/api/codem/codeRepositoryOperationButton`,
    method: 'post',
    data,
  })
}
// 代码库合并操作按钮查询
export function getcodeRepositoryOperationButton(data) {
  return request({
    url: `/api/codem/codeRepositoryOperationButton/query`,
    method: 'post',
    data,
  })
}
// 代码库合并操作按钮查询分页
export function getcodeRepositoryOperationButtonPage(data) {
  return request({
    url: `/api/codem/codeRepositoryOperationButton/page`,
    method: 'post',
    data,
  })
}
// 代码库合并操作按钮删除
export function deleteOperationButton(data) {
  return request({
    url: `/api/codem/codeRepositoryOperationButton`,
    method: 'DELETE',
    data,
  })
}
// 批量CherryPick操作
export function batchCherryPick(data) {
  return request({
    url: `/api/codem/codeBranch/batchCherryPick`,
    method: 'post',
    data,
  })
}

// 根据需求code和分支id查询commit列表信息
export function apiBranchIssueCommits(branchId, requirementCode) {
  return request({
    url: `/api/codem/codeBranch/commit/${branchId}/${requirementCode}?engineId=1470948336532979712`,
    method: 'get',
  })
}
