import request from '@/utils/axios-api'

// 查询用户列表
export function getMemberList(data) {
  return request({
    url: '/api/codem/codem/user/page',
    method: 'post',
    data,
  })
}
// 新增代码库成员
export function addCodeMember(data) {
  return request({
    url: '/api/codem/codem/user',
    method: 'post',
    data,
  })
}
// 修改代码库成员
export function updateCodeMember(data) {
  return request({
    url: '/api/codem/codem/user',
    method: 'put',
    data,
  })
}
// 删除代码库成员
export function deleteCodeMember(data) {
  return request({
    url: `/api/codem/codem/user`,
    method: 'delete',
    data,
  })
}
// 查询代码库成员详情
export function getCodeMemberInfo(id) {
  return request({
    url: `/api/codem/codem/user/${id}`,
    method: 'get',
  })
}
// 查询当前用户可绑定代码库用户
/**
 * @description 查询当前用户可绑定代码库用户
 * @param {string} engineId 引擎id
 * @param {string} keyword 用户名称，账号或邮箱
 */
export function getLibraryUsers({ engineId, keyword }) {
  return request({
    url: `/api/codem/codem/user/bindable/codeUser/${engineId}`,
    method: 'get',
    data: { keyword },
  })
}
/**
 * 查询当前用户绑定的代码库账户
 */
export function getLibraryAccounts() {
  return request({
    url: `/api/codem/codem/user/bind/codeUser`,
    method: 'get',
  })
}
/**
 * @description 查询代码库用户可绑定的平台用户
 */
export function getPlatformUsers({ id, keyword }) {
  return request({
    url: `/api/codem/codem/user/bindable/user/${id}`,
    method: 'get',
    data: { keyword },
  })
}
/**
 * @description 代码库用户绑定平台用户
 */
export function bindPlatformUser(id, userId) {
  return request({
    url: `/api/codem/codem/user/binding/${id}/${userId}`,
    method: 'put',
  })
}
/**
 * @description 代码库用户解除绑定平台用户
 * @param {string} id 代码库用户id
 */
export function unbindPlatformUser(id) {
  return request({
    url: `/api/codem/codem/user/unbind/${id}`,
    method: 'put',
  })
}
/**
 * @description 同步代码库中用户信息
 * @param {string} engineId 引擎id
 */
export function syncCodeUsers(engineId) {
  return request({
    url: `/api/codem/codem/user/sync/${engineId}`,
    method: 'put',
  })
}
/**
 * @description 代码库用户绑定平台用户
 * @param {string} id 代码库用户id
 * @param {string} userId 平台用户id
 */
export function bindingOfuserId(e) {
  return request({
    url: `/api/codem/codem/user/binding/${e.id}/${e.userId}`,
    method: 'put',
  })
}
/**
 * @description 查询代码库用户可绑定的平台用户
 * @param {string} id 代码库用户id
 * @param {string} keyword 关键字，模糊匹配
 */
export function getBindableUser(id, data) {
  return request({
    url: `/api/codem/codem/user/bindable/user/${id}`,
    method: 'get',
    data,
  })
}
/**
 * @description 新增用户时可添加GitLab账户的的平台用户
 * @param {string} engineId	 引擎id
 * @param {string} keyword 关键字，模糊匹配
 */
export function getGitlabUser(engineId, data) {
  return request({
    url: `/api/codem/codem/user/bindable/${engineId}/user`,
    method: 'get',
    data,
  })
}
/**
 * @description 同步代码库下用户信息
 * @param {string} engineId	 引擎id
 */
export function syncUserList(engineId) {
  return request({
    url: `/api/codem/codem/user/sync/${engineId}`,
    method: 'put',
  })
}
