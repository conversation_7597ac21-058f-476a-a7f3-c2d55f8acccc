import request from '@/utils/axios-api'

// 查询我的消息全量
export function getMymsg(data) {
  return request({
    // /api/base/base/myMsg/findQuery
    url: '/api/base/msgHistory/queryLetterMsg',
    method: 'post',
    data,
  })
}

// 标记消息为已读 /api/base/base/myMsg/mark
export function markMsg(data) {
  return request({
    url: '/api/base/msgHistory/updateReadAll',
    method: 'PUT',
    data,
  })
}

// 分页查询 /api/base/base/myMsg
export function getMymsgPage(data) {
  return request({
    url: '/api/base/base/myMsg',
    method: 'post',
    data,
  })
}
// 根据项目id验证是否在该项目中
export function getProjecCheck(data) {
  return request({
    url: '/api/alm/alm/project/auth/check',
    method: 'get',
    data,
  })
}
