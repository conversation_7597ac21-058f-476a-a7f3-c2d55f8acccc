import request from '@/utils/axios-api'

// 查询空间
export function searchSpace(data) {
  return request({
    url: '/api/wiki/wiki/space/query',
    method: 'post',
    data,
  })
}

// 查询空间分页
export function searchSpacePage(data) {
  return request({
    url: '/api/wiki/wiki/space/page',
    method: 'post',
    data,
  })
}
// 操作空间
export function operationSpace(data, method) {
  return request({
    url: '/api/wiki/wiki/space',
    method: method,
    data,
  })
}

// 查看空间类型

export function searchSpaceType(data) {
  return request({
    url: '/api/wiki/wiki/spaceType/query',
    method: 'post',
    data,
  })
}

// 查询文档列表

export function searchDoucment(spaceId) {
  return request({
    url: `/api/wiki/wiki/document/myDocumentBySpaceId/${spaceId}`,
    method: 'get',
  })
}

// 操作文档
export function operationDoc(data, method) {
  return request({
    url: '/api/wiki/wiki/document',
    method: method,
    data,
  })
}

// 根据文档id查询内容
export function getDoc(id) {
  return request({
    url: `/api/wiki/wiki/document/${id}`,
    method: 'get',
  })
}

export function addIssue(data) {
  return request({
    url: '/api/alm/alm/requirement',
    method: 'post',
    data,
  })
}

// 项目下角色-不分页
export function apiAlmProjectRoleNoPage(data) {
  return request({
    url: '/api/alm/alm/projectUser/page',
    method: 'post',
    data,
  })
}

// 查询我的空间
export function getMyWikiSpace(id) {
  return request({
    url: `/api/wiki/wiki/space/my`,
    method: 'get',
  })
}

// 搜索有权限的空间和文档，普通用户使用
export function apiAlmProjectDocument(data) {
  return request({
    url: '/api/wiki/wiki/document/search',
    method: 'post',
    data,
  })
}
