import request from '@/utils/axios-api'

// 查询公共筛选器
export function getFilterPage(data) {
  return request({
    url: '/api/base/base/tableView/page',
    method: 'post',
    data,
  })
}
// 查询收藏的筛选器
export function getCollectFilterPage(data) {
  return request({
    url: '/api/base/base/tableView/collect',
    method: 'post',
    data,
  })
}
// 查询我的筛选器
export function getMyFilterPage(data) {
  return request({
    url: '/api/base/base/tableView/my',
    method: 'post',
    data,
  })
}
// 查询被分享的筛选器
export function getSharedFilterPage(data) {
  return request({
    url: '/api/base/base/tableView/shared',
    method: 'post',
    data,
  })
}
// 新增/编辑筛选器
export function opeartionFilter(data, method) {
  return request({
    url: '/api/base/base/tableView',
    method: method,
    data,
  })
}
// 收藏筛选器
export function collectFilter(id, flag) {
  const url = flag ? 'collect' : 'uncollect'
  return request({
    url: `/api/base/base/tableView/${id}/${url}`,
    method: 'put',
  })
}
// 删除筛选器
export function delFilter(data) {
  return request({
    url: '/api/base/base/tableView',
    method: 'delete',
    data,
  })
}
// 分享筛选器
export function sharedFilter(data) {
  return request({
    url: '/api/base/base/tableView/share',
    method: 'post',
    data,
  })
}
// 删除分享的筛选器
export function delsSaredFilter(data) {
  return request({
    url: '/api/base/base/tableView/unshare',
    method: 'post',
    data,
  })
}
// 复制筛选器
export function copyFilterApi(viewId, name) {
  return request({
    url: `/api/base/base/tableView/${viewId}/copy?name=${name}`,
    method: 'post',
  })
}
// 根据用户查询项目
export function queryprojectData(data) {
  return request({
    url: `/api/alm/alm/project/query`,
    method: 'post',
    data,
  })
}
// 查询可使用的筛选条件 查询项目/标签等接口
export function queryFilterConditions(type) {
  return request({
    url: `/api/alm/alm/issueItem/availableFields/${type}`,
    method: 'get',
  })
}
// 查询通用数据
export function querySelectData(data) {
  return request({
    url: `/api/alm/entity/query/collection`,
    method: 'post',
    data,
  })
}
// 预览筛选的数据
export function previewData(data) {
  return request({
    url: `/api/alm/alm/issueItem/searchPage`,
    method: 'post',
    data,
  })
}
// 查询筛选器详情
export function queryDetail(id) {
  return request({
    url: `/api/base/base/tableView/${id}`,
    method: 'get',
  })
}

// 查询工作项状态
export function apFilterFindState(projectIds, typeClassifies, typeCodes) {
  return request({
    url: `/api/alm/alm/state/findStateBath?projectIds=${projectIds}&typeClassifies=${typeClassifies}&typeCodes=${typeCodes}`,
    method: 'get',
  })
}
