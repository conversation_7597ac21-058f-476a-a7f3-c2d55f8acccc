import request from '@/utils/axios-api'

// 生成测试报告
export function addtestReport(data) {
  return request({
    url: '/api/testm/testReport/report',
    method: 'post',
    data,
  })
}
// list测试报告
export function listtestReport(data) {
  return request({
    url: '/api/testm/testReport/getPageTestReport',
    method: 'put',
    data,
  })
}
// list测试报告
export function deltestReport(data) {
  return request({
    url: '/api/testm/testReport',
    method: 'delete',
    data,
  })
}
// list测试报告
export function detailtestReport(id) {
  return request({
    url: `/api/testm/testReport/${id}`,
    method: 'get',
  })
}
// 多维度查看测试报告
export function testReportSummary(data) {
  return request({
    url: `/api/testm/testReport/testReportSummary`,
    method: 'get',
    data,
  })
}
