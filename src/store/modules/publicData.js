import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
const state = {
  dictDataMap: {},
  userData: [],
}
const mutations = {
  set_userData: (state, data) => {
    state.userData = data
  },
}

const actions = {
  // 缓存用户列表
  cacheUserData({ commit, state, dispatch }, that) {
    apiBaseAllUserNoPage().then((res) => {
      if (res.isSuccess) {
        commit('set_userData', res.data)
        if (that) that.$forceUpdate()
      }
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
