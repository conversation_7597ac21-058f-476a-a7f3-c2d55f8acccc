import { getLocalState, setLocalState } from '@/utils/auth'
// import router from '@/router'

const state = getLocalState()

const getters = {
  getChatHistoryByCurrentActive(state) {
    const index = state.history.findIndex((item) => item.uuid === state.active)
    if (index !== -1) {
      return state.history[index]
    }
    return null
  },

  getChatByUuid(state) {
    return (uuid) => {
      if (uuid) {
        return state.chat.find((item) => item.uuid === uuid)?.data ?? []
      }
      return state.chat.find((item) => item.uuid === state.active)?.data ?? []
    }
  },
}

const mutations = {}

const actions = {
  setUsingContext({ dispatch, state }, context) {
    state.usingContext = context
    dispatch('recordState')
  },
  addHistory({ dispatch, state }, { history, chatData = [] }) {
    state.history.unshift(history)
    state.chat.unshift({ uuid: history.uuid, data: chatData })
    state.active = history.uuid
    dispatch('reloadRoute', history.uuid)
  },
  updateHistory({ dispatch, state }, { uuid, edit, editName }) {
    const index = state.history.findIndex((item) => item.uuid === uuid)
    if (index !== -1) {
      state.history[index] = { ...state.history[index], isEdit: edit }
      editName && (state.history[index].title = editName)
      dispatch('recordState')
    }
  },
  async deleteHistory({ dispatch, state }, index) {
    state.history.splice(index, 1)
    state.chat.splice(index, 1)

    if (state.history.length === 0) {
      state.active = null
      dispatch('reloadRoute')
      return
    }

    if (index > 0 && index <= state.history.length) {
      const uuid = state.history[index - 1].uuid
      state.active = uuid
      dispatch('reloadRoute', uuid)
      return
    }

    if (index === 0) {
      if (state.history.length > 0) {
        const uuid = state.history[0].uuid
        state.active = uuid
        dispatch('reloadRoute', uuid)
      }
    }

    if (index > state.history.length) {
      const uuid = state.history[state.history.length - 1].uuid
      state.active = uuid
      dispatch('reloadRoute', uuid)
    }
  },
  async setActive({ dispatch, state }, uuid) {
    state.active = uuid
    return await dispatch('reloadRoute', uuid)
  },

  getChatByUuidAndIndex({ dispatch, state }, { uuid, index }) {
    if (!uuid || uuid === 0) {
      if (state.chat.length) {
        return state.chat[0].data[index]
      }
      return null
    }
    const chatIndex = state.chat.findIndex((item) => item.uuid === uuid)
    if (chatIndex !== -1) {
      return state.chat[chatIndex].data[index]
    }
    return null
  },
  addChatByUuid({ dispatch, state }, { uuid, chat }) {
    if (!uuid || uuid === 0) {
      if (state.history.length === 0) {
        const uuid = Date.now()
        state.history.push({ uuid, title: chat.text, isEdit: false })
        state.chat.push({ uuid, data: [chat] })
        state.active = uuid
        dispatch('recordState')
      } else {
        state.chat[0].data.push(chat)
        if (state.history[0].title === 'New Chat') {
          state.history[0].title = chat.text
        }
        dispatch('recordState')
      }
    }

    const index = state.chat.findIndex((item) => item.uuid === uuid)
    if (index !== -1) {
      state.chat[index].data.push(chat)
      if (state.history[index].title === 'New Chat') {
        state.history[index].title = chat.text
      }
      dispatch('recordState')
    }
  },

  updateChatByUuid({ dispatch, state }, { uuid, index, chat }) {
    if (!uuid || uuid === 0) {
      if (state.chat.length) {
        state.chat[0].data[index] = chat
        dispatch('recordState')
      }
      return
    }

    const chatIndex = state.chat.findIndex((item) => item.uuid === uuid)
    if (chatIndex !== -1) {
      state.chat[chatIndex].data[index] = chat
      dispatch('recordState')
    }
  },
  updateChatSomeByUuid({ dispatch, state }, { uuid, index, chat }) {
    if (!uuid || uuid === 0) {
      if (state.chat.length) {
        state.chat[0].data[index] = { ...state.chat[0].data[index], ...chat }
        dispatch('recordState')
      }
      return
    }

    const chatIndex = state.chat.findIndex((item) => item.uuid === uuid)
    if (chatIndex !== -1) {
      state.chat[chatIndex].data[index] = {
        ...state.chat[chatIndex].data[index],
        ...chat,
      }
      dispatch('recordState')
    }
  },

  deleteChatByUuid({ dispatch, state }, { uuid, index }) {
    if (!uuid || uuid === 0) {
      if (state.chat.length) {
        state.chat[0].data.splice(index, 1)
        dispatch('recordState')
      }
      return
    }

    const chatIndex = state.chat.findIndex((item) => item.uuid === uuid)
    if (chatIndex !== -1) {
      state.chat[chatIndex].data.splice(index, 1)
      dispatch('recordState')
    }
  },

  clearChatByUuid({ dispatch, state }, uuid) {
    if (!uuid || uuid === 0) {
      if (state.chat.length) {
        state.chat[0].data = []
        dispatch('recordState')
      }
      return
    }

    const index = state.chat.findIndex((item) => item.uuid === uuid)
    if (index !== -1) {
      state.chat[index].data = []
      dispatch('recordState')
    }
  },
  recordState({ state }) {
    setLocalState(state)
  },
  async reloadRoute({ dispatch }, uuid) {
    dispatch('recordState')
    // await router.push({ name: 'chat', params: { uuid }})
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
