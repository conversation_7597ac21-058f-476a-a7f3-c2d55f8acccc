// import variables from '@/styles/element-variables.scss'

const state = {
  // theme: variables.theme,
  // themeCode: 'custom-theme-light'
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (Object.prototype.hasOwnProperty.call(state, key)) {
      state[key] = value
    }
  },
  CHANGE_THEME: (state, data) => {
    state.themeCode = data
  },
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  },
  changeTheme({ commit }, data) {
    commit('CHANGE_THEME', data)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
