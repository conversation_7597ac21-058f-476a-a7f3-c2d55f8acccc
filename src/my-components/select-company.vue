<template>
  <el-select
    v-model="companyId"
    v-bind="$attrs"
    filterable
    clearable
    :placeholder="placeholder"
    style="width: 100%"
    @change="change"
    @focus="setOptionWidth"
  >
    <el-option
      v-for="item in companyList"
      :key="item.id"
      :label="item.name"
      :value="item.id"
      :style="{ width: selectOptionWidth }"
    />
  </el-select>
</template>

<script>
import { apiBaseCompanyListNoPage } from '@/api/vone/base/company'
export default {
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      companyId: '',
      companyList: [],
      selectOptionWidth: '',
    }
  },
  watch: {
    value(newVal) {
      this.companyId = newVal
    },
  },
  created() {
    this.companyId = this.value
    apiBaseCompanyListNoPage().then((res) => {
      this.companyList = res.data
    })
  },
  methods: {
    setOptionWidth(event) {
      // 下拉框弹出时，设置弹框的宽度
      this.$nextTick(() => {
        this.selectOptionWidth = event.srcElement.offsetWidth + 'px'
      })
    },
    change() {
      this.$emit('input', this.companyId)
      this.$emit('change', this.companyId, this.companyList)
    },
  },
}
</script>
