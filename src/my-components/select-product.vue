<template>
  <el-select
    v-model="productId"
    v-bind="$attrs"
    filterable
    clearable
    :placeholder="placeholder"
    style="width: 100%"
    @change="change"
  >
    <el-option
      v-for="item in productList"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script>
import { apiProductNoPage } from '@/api/vone/product/index'
export default {
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      productId: '',
      productList: [],
    }
  },
  watch: {
    value(newVal) {
      this.productId = newVal
    },
  },
  created() {
    this.productId = this.value
    apiProductNoPage().then((res) => {
      this.productList = res.data
    })
  },
  methods: {
    change() {
      this.$emit('input', this.productId)
    },
  },
}
</script>
