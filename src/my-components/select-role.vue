<template>
  <el-select
    v-model="roleId"
    v-bind="$attrs"
    filterable
    clearable
    :placeholder="placeholder"
    style="width: 100%"
    :multiple="multiple"
    @change="change"
  >
    <el-option
      v-for="item in roleList"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script>
import { batchQuery } from '@/api/vone/base/role'
export default {
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    filterValue: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      roleId: '',
      roleList: [],
    }
  },
  watch: {
    value(newVal) {
      this.roleId = newVal
    },
  },
  created() {
    this.roleId = this.value
    batchQuery().then((res) => {
      if (this.filterValue && this.filterValue.length) {
        const hJson = res.data.filter(
          (i, j) => this.filterValue.indexOf(i.code) !== -1
        )

        this.roleList = hJson
      } else {
        this.roleList = res.data
      }
      this.$emit('list', this.roleList)
    })
  },
  methods: {
    change(val) {
      this.$emit('input', this.roleId)
      this.$emit('list', this.roleList, val)
    },
  },
}
</script>
