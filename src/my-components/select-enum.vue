<template>
  <!-- 下拉框-枚举 -->
  <el-select
    v-model="id"
    clearable
    filterable
    :placeholder="placeholder"
    style="width: 100%"
    @change="change"
  >
    <el-option
      v-for="(item, index) in list"
      :key="index"
      :label="item[labelKey]"
      :value="item[valueKey]"
    />
  </el-select>
</template>

<script>
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

export default {
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    /**
     * 枚举key
     */
    type: {
      type: String,
      default: '',
    },
    labelKey: {
      type: String,
      default: 'label',
    },
    valueKey: {
      type: String,
      default: 'value',
    },
  },
  data() {
    return {
      id: '',
      list: [],
    }
  },
  created() {
    this.id = this.value
    apiBaseDictEnumList([this.type]).then((res) => {
      this.list = res.data[this.type]
    })
  },
  methods: {
    change() {
      this.$emit('input', this.id)
    },
  },
}
</script>
